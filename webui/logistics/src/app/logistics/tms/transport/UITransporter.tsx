import React from 'react';
import { input, bs, entity } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';

import { T } from '../backend';
import { VehicleFleetURL } from '../RestURL';
import { UIDriverLicenseTypeListEditor } from './UIDriverLicenseTypeListEditor';
import { TransporterType } from '../models';

interface UITransporterFormProps extends entity.AppDbComplexEntityProps {
  isNewRecord?: boolean;
}

export class UITransporterForm extends entity.AppDbEntity<UITransporterFormProps> {
  onModify = (bean: any) => {
    let { observer } = this.props;
    observer.replaceBeanProperty('driverLicenseTypes', bean);
    this.forceUpdate();
  }

  onPostAccount = (bean: any, val: any) => {
    let { observer } = this.props;
    let transporter = observer.getMutableBean();
    transporter.odooPartnerCode = bean.loginId;
    transporter.accountId = bean.id;
    this.forceUpdate();
  }

  render() {
    let { appContext, pageContext, observer } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let modCap = pageContext.hasUserModeratorCapability();
    let transporter = observer.getMutableBean();
    return (
      <bs.VSplit updateOnResize>
        <bs.VSplitPane width={450} title={T('Transporter')}>
          <input.BBStringField label={T('Code')} inputObserver={observer} required
            bean={transporter} field="code" disable={!modCap} />
          <module.account.BBRefAccount
            appContext={appContext} pageContext={pageContext} disable={!writeCap}
            label={T('Account')} placeholder='Enter An Account'
            bean={transporter} accountIdField={'accountId'} accountLabelField={'fullName'} />
          <input.BBStringField label={T('Full Name')} inputObserver={observer} required
            bean={transporter} field={'fullName'} disable={!writeCap} />
          <input.BBStringField inputObserver={observer}
            bean={transporter} field={"odooPartnerCode"} label={T('Odoo Partner Code')} disable={!writeCap} />
          <bs.Row>
            <bs.Col span={6}>
              <input.BBStringField inputObserver={observer}
                bean={transporter} field={"odooJournalCode"} label={T('Odoo Journal Code')} disable={!writeCap} />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBStringField inputObserver={observer}
                bean={transporter} field={"odooCompanyCode"} label={T('Odoo Company Code')} disable={!writeCap} />
            </bs.Col>
          </bs.Row>
          <input.BBStringField label={T('Identification No')} bean={transporter} field={'idCard'} disable={!writeCap} />
          <input.BBStringField label={T('Mobile')} bean={transporter} field={'mobile'} disable={!writeCap} />
          <input.BBStringField label={T('Email')} bean={transporter} field={'email'} disable={!writeCap} />
          <input.BBSelectField bean={transporter} field={"transporterType"} label={T('Employee')}
            options={[TransporterType.Partner, TransporterType.Employee]} />
          <bs.Row>
            <bs.Col span={6}>
              <input.BBCheckboxField bean={transporter} field={"driver"} label={T('Driver')} value disable={!writeCap} />
            </bs.Col>
            <bs.Col span={6} >
              <input.BBCheckboxField bean={transporter} field={"deliver"} label={T('Deliver')} value disable={!writeCap} />
            </bs.Col>
          </bs.Row>
          <input.BBTextField label={T('Description')} style={{ height: '10em' }} bean={transporter} field={'description'} disable={!writeCap} />
        </bs.VSplitPane >
        <bs.VSplitPane>
          <bs.TabPane>
            <bs.Tab name={'driver-license-type'} label={T("Driver License Type")} active={true}>
              <UIDriverLicenseTypeListEditor readOnly={!writeCap}
                plugin={observer.createVGridEntityListEditorPlugin('driverLicenseTypes')}
                appContext={appContext} pageContext={pageContext}
                editorTitle={T('Driver License Types')} dialogEditor={true} />
            </bs.Tab>
          </bs.TabPane>
        </bs.VSplitPane>
      </bs.VSplit >
    );
  }
}

interface UITransporterProps extends entity.AppComplexEntityEditorProps {
  isNewRecord?: boolean;
}

export class UITransporterEditor extends entity.AppDbComplexEntityEditor<UITransporterProps> {
  render() {
    let { appContext, pageContext, observer, isNewRecord } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    return (
      <div className={'flex-vbox'} key={this.viewId}>
        <UITransporterForm {...this.props} readOnly={!writeCap} isNewRecord={isNewRecord} />
        <bs.Toolbar className='border' hide={!writeCap}>
          <entity.ButtonEntityCommit appContext={appContext} pageContext={pageContext} observer={observer}
            onPostCommit={this.onPostCommit} commit={{ service: 'VehicleFleetService', commitMethod: 'saveTransporter', entityLabel: T('Transporter') }} />
          <entity.WButtonEntityReset
            appContext={appContext} pageContext={pageContext} readOnly={!writeCap} observer={observer}
            onPostRollback={(entity) => this.onPostRollback(entity)} />
        </bs.Toolbar>
      </div>
    );
  }
}