import React from 'react';
import { input, bs, entity, app, util } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';

import * as FeatherIcon from 'react-feather';
import { T } from '../backend';

import { UICoordinatorList, UICoordinatorListPlugin } from './UIVehicleFleetCoordinatorList';
import { UITransporterList, UITransporterListPlugin } from './UITransporterList';
import { UIVehicleList, UIVehicleListPlugin } from '../vehicle/UIVehicleList';
import { FleetType } from '../models';
import { EditEmails } from '../utils';
import BBRefAccount = module.account.BBRefAccount;

export interface UIVehicleFleetFormProps extends entity.AppDbEntityEditorProps {
  syncBFSOne?: boolean
}
export class UIVehicleFleetForm extends entity.AppDbEntityEditor<UIVehicleFleetFormProps> {
  onChargeType(_bean: any, _field: string, _oldVal: any, _newVal: any) {
    this.forceUpdate();
  }

  render() {
    let { appContext, pageContext, observer, syncBFSOne } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let vehicleFleet = observer.getMutableBean();
    const webhookConfig = observer.getBeanProperty('webhookConfig', {});
    const SESSION = app.host.DATATP_HOST.session;
    let config: bs.TabPaneConfig = {
      tabs: [
        {
          name: 'info', label: T('Info'), active: true,
          renderContent: (_ctx: bs.UIContext) => {
            return (
              <div className="flex-vbox p-1">
                <input.BBCheckboxField
                  bean={vehicleFleet} field={'allowRequestTrackingApp'} label={T('Allow Request Tracking App')} disable={!writeCap} value={false} />
                <input.BBCheckboxField
                  bean={vehicleFleet} field={'containerShippingSupport'} label={T('Container Shipping Support')} disable={!writeCap} value={false} />
                <input.BBCheckboxField
                  bean={vehicleFleet} field={'truckShippingSupport'} label={T('Truck Shipping Support')} disable={!writeCap} value={false} />
                <input.BBStringField label={T('Label')} bean={vehicleFleet} field="label" disable={!writeCap}
                  inputObserver={observer} required />
                <input.BBStringField label={T('Code')} inputObserver={observer} required
                  bean={vehicleFleet} field="code" disable={!writeCap || !observer.isNewBean()} />
                <input.BBStringField label={T('Tax Code')} inputObserver={observer}
                  bean={vehicleFleet} field="taxCode" disable={!writeCap} />
                <input.BBStringField label={T('BFS One Code')} inputObserver={observer} required
                  bean={vehicleFleet} field="bfsOneCode" disable={!writeCap} onKeyDown={(winput, event, currInput) => {
                    if (syncBFSOne) {
                      if (event.key == 'Enter' || event.key === 'Tab') {
                        appContext.createHttpBackendCall("BFSOnePartnerService", "getBFSOnePartnerByCode", { code: currInput })
                          .withSuccessData((data: any) => {
                            if (data) {
                              vehicleFleet['label'] = data['name'];
                              vehicleFleet['taxCode'] = data['taxCode'];
                              vehicleFleet['bfsOneCode'] = data['bfsonePartnerCode'];
                              let email: string = data['email'];
                              vehicleFleet['emails'] = email
                                .split(";")
                                .map(email => email.trim());
                              this.forceUpdate();
                            }

                          })
                          .call()
                      }
                    }
                  }} />
                <input.BBSelectField bean={vehicleFleet} field='fleetResource' label='Fleet Resource' options={[null, 'EXTERNAL_COMPANY', 'INTERNAL_COMPANY']} />
                <div className='my-2'>
                  <div className='flex-hbox'>
                    <bs.FormLabel>{T('Email')}</bs.FormLabel>
                    <FeatherIcon.Edit className='mx-2 text-warning' style={{ cursor: 'pointer' }} size={12} onClick={() => EditEmails.onShowPopUpProcessEmails(this)} />
                  </div>
                  <input.BBStringArrayField
                    key={this.viewId}
                    bean={vehicleFleet} field={'emails'} disable={!writeCap}
                    validators={[util.validator.EMAIL_VALIDATOR]} onInputChange={(bean, field, oldVal, newVal) => {
                      if (newVal.length == 0) bean[field] = null;
                    }} />
                </div>
                <input.BBIntField label={T('Index')}
                  bean={vehicleFleet} field={'index'}
                  disable={!writeCap} onInputChange={this.onChargeType} />
                <input.BBSelectField label={T('Fleet Type')}
                  bean={vehicleFleet} field={'fleetType'} options={Object.keys(FleetType)}
                  disable={!observer.isNewBean()} onInputChange={this.onChargeType} />
                <label className='form-label'>{T('Owner')}</label>
                {/* <module.account.BBAccountAutoComplete
              appContext={appContext} pageContext={pageContext} bean={vehicleFleet} inputObserver={observer}
              field={'ownerLoginId'} labelField={'ownerLoginLabel'} useSelectBean={false}
              disable={!writeCap || vehicleFleet.ownerLoginId} /> */}
                <BBRefAccount appContext={appContext} pageContext={pageContext} bean={vehicleFleet} inputObserver={observer}
                  accountIdField='ownerAccountId' accountLabelField='ownerFullName'
                  disable={!writeCap} placeholder='Account' />
                <input.BBCheckboxField bean={vehicleFleet} field={'gps'} value={false} disable={!writeCap} label={T('GPS')} />
                <input.BBTextField label={T('Description')} style={{ height: '15em' }} bean={vehicleFleet} field={'description'} disable={!writeCap} />
              </div>
            );
          }
        },
        {
          name: 'webhook-config', label: T('Webhook Config'),
          renderContent: (_ctx: bs.UIContext) => {
            return (
              <div>
                <module.security.BBRefAccessToken
                  appContext={appContext} pageContext={pageContext}
                  label="Internal Token" placeholder='Select a token' accountId={SESSION.getAccountId()}
                  placement="bottom-start" offset={[0, 5]} minWidth={300}
                  bean={webhookConfig} beanIdField="tokenId" beanLabelField="tokenLabel" />
                <input.BBCheckboxField bean={webhookConfig} field={'autoPush'} label={T('Auto Push')} disable={!writeCap} value={false} />
                <input.BBStringField bean={webhookConfig} field={'baseRestUrl'} label={T('Rest Url')} disable={!writeCap} />
                <input.BBStringField bean={webhookConfig} field={'inputCompanyCode'} label={T('Company Code')} disable={!writeCap} />
                <input.BBStringField bean={webhookConfig} field={'module'} label={T('Module')} disable={!writeCap} />
                <input.BBTextField bean={webhookConfig} field={'accessToken'} label={T('Access Token')} style={{ height: '10em' }} disable={!writeCap} />
              </div>
            );
          }
        },
      ]
    };
    return (
      <bs.DefaultTabPane className="flex-vbox" config={config} />
    )
  }
}

export interface UIVehicleFleetEditorProps extends entity.AppDbEntityEditorProps {
  syncBFSOne?: boolean
}

export class UIVehicleFleetEditor extends entity.AppDbEntityEditor<UIVehicleFleetEditorProps> {
  render() {
    let { appContext, pageContext, observer } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    return (
      <div className='flex-vbox'>
        <div className='flex-vbox'>
          <bs.Card header={T('Fleet Info')}>
            <UIVehicleFleetForm {...this.props} readOnly={!writeCap} />
          </bs.Card>
        </div>
        <bs.Toolbar className='border' hide={!writeCap}>
          <entity.ButtonEntityCommit appContext={appContext} pageContext={pageContext} observer={observer}
            onPostCommit={this.onPostCommit}
            commit={{ service: 'VehicleFleetService', commitMethod: 'saveVehicleFleet', entityLabel: T('Transport Fleet') }} />
          <entity.WButtonEntityReset
            appContext={appContext} pageContext={pageContext} readOnly={!writeCap} observer={observer}
            onPostRollback={(entity) => this.onPostRollback(entity)} />
        </bs.Toolbar>
      </div>
    )
  }
}

export interface UIVehicleFleetListProps extends entity.DbEntityList {
  fleetCode?: string;
}

export class UIVehicleFleet extends entity.AppDbComplexEntityEditor {

  render() {
    let { appContext, pageContext, observer } = this.props;
    const fleet = observer.getMutableBean();
    const writeCap = pageContext.hasUserWriteCapability();
    return (
      <bs.VSplit updateOnResize key={this.viewId}>
        <bs.VSplitPane width={420} title={T('Vehicle Fleets')}>
          <UIVehicleFleetEditor {...this.props} onPostCommit={this.onPostCommit} />
        </bs.VSplitPane>
        <bs.VSplitPane>
          <bs.TabPane >
            <bs.Tab name={'transporter'} label={T('Transporter')} active={true}>
              <UITransporterList
                plugin={new UITransporterListPlugin().withVehicleFleetId(fleet.id)}
                appContext={appContext} pageContext={pageContext}
                vehicleFleet={fleet} type='page' readOnly={!writeCap} />
            </bs.Tab>
            {!(fleet.fleetType == FleetType.Motorbike) &&
              <bs.Tab name={'vehicle'} label={T('Vehicle')}>
                <UIVehicleList
                  appContext={appContext} pageContext={pageContext}
                  plugin={new UIVehicleListPlugin().withVehicleFleetId(fleet.id)}
                  vehicleFleet={fleet} type='page' readOnly={!writeCap} />
              </bs.Tab>}
            <bs.Tab name={'coordinator'} label={T('Coordinator')}>
              <UICoordinatorList
                appContext={appContext} pageContext={pageContext}
                plugin={new UICoordinatorListPlugin().withVehicleFleetId(fleet.id)}
                vehicleFleetId={fleet.id} vehicleFleetLabel={fleet.label} type='selector' readOnly={!writeCap} />
            </bs.Tab>
          </bs.TabPane>
        </bs.VSplitPane>
      </bs.VSplit>
    );
  }
}