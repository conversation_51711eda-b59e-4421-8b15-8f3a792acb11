
import React from "react";
import { app, util, bs, grid, entity } from "@datatp-ui/lib";
import { T } from "../backend";
import { UIVehicleEditor, UIVehicle } from "./UIVehicle";
import { UIVehicleRefuelEditor } from "./UIVehicleRefuel";
import { TMSBillTransportationModeTools } from "../utils";
import { UIVehicleList } from "./UIVehicleList";

type ShowUIVehicleByCodeProps = {
  uiSource: app.AppComponent;
  code: string;
  onPostCommit?: entity.EntityOnPostCommit;
  readOnly?: boolean;
}

type ShowUIVehicleProps = {
  uiSource: app.AppComponent;
  vehicle: any;
  onPostCommit?: entity.EntityOnPostCommit;
  readOnly?: boolean;
}

export class UIVehicleUtils {
  static showVehicleByCode({ uiSource, code, onPostCommit, readOnly }: ShowUIVehicleByCodeProps) {
    const { appContext, pageContext } = uiSource.props;
    if (!readOnly) readOnly = !pageContext.hasUserWriteCapability();
    appContext
      .createHttpBackendCall('VehicleService', 'getVehicle', { code: code })
      .withSuccessData((vehicle: any) => {
        this.showVehicle({ uiSource, onPostCommit, vehicle, readOnly });
      })
      .call()
  }

  static showVehicle({ uiSource, vehicle, onPostCommit, readOnly }: ShowUIVehicleProps) {
    const { appContext, pageContext } = uiSource.props;
    let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIVehicle
          appContext={appCtx} pageContext={pageCtx}
          observer={new entity.ComplexBeanObserver(vehicle)} readOnly={readOnly}
          onPostCommit={onPostCommit} />
      );
    }
    pageContext.addPageContent(`vehicle-${vehicle.code}`, T(`Vehicle: ${vehicle.label}`), createPageContent);
  }

  static onNewVehicle({ uiSource, vehicle: vehicle, onPostCommit, readOnly }: ShowUIVehicleProps) {
    const { appContext, pageContext } = uiSource.props;
    const html = (
      <UIVehicleEditor
        appContext={appContext} pageContext={pageContext}
        observer={new entity.ComplexBeanObserver(vehicle)} readOnly={readOnly}
        onPostCommit={(entity: any) => {
          bs.dialogHideById(dialogId);
          if (onPostCommit) {
            onPostCommit(entity);
          }
        }} />
    );
    let dialogId = bs.dialogShow(T('New Vehicle'), html);
  }

  static cloneGoodsTracking = (_ctx: grid.VGridContext, tracking: any): any => {
    let clone = entity.EntityUtil.clone(tracking, true);
    delete clone.code;
    delete clone.vehicleTripId;
    delete clone.jobTrackingId;
    delete clone.tmsBillId;
    delete clone.responsibleAccountId;
    delete clone.responsibleFullName;
    delete clone.userName;
    delete clone.status;
    clone['uikey'] = `new/${util.IDTracker.next()}`;
    clone['rowHeight'] = 45;
    clone['isBillOwner'] = true;
    clone['updateTMSBill'] = true;
    if ('container' === clone['typeOfTransport']) {
      if (TMSBillTransportationModeTools.isExport(clone['mode'])) {
        clone['receiverAddress'] = clone['returnContainerLocation'];
        clone['receiverLocationId'] = clone['returnContainerLocationId'];
      } else if (TMSBillTransportationModeTools.isImport(clone['mode'])) {
        clone['senderAddress'] = clone['pickupContainerLocation'];
        clone['senderLocationId'] = clone['pickupContainerLocationId'];
      }
    }
    if (!clone['pickupAddress']) clone['pickupAddress'] = clone['senderAddress'];
    if (!clone['pickupInvAddress']) clone['pickupInvAddress'] = clone['senderInvAddress'];
    if (!clone['pickupLocationId']) clone['pickupLocationId'] = clone['senderLocationId'];
    if (!clone['pickupPartnerAddressId']) clone['pickupPartnerAddressId'] = clone['senderPartnerAddressId'];

    if (!clone['deliveryAddress']) clone['deliveryAddress'] = clone['receiverAddress'];
    if (!clone['deliveryInvAddress']) clone['deliveryInvAddress'] = clone['receiverInvAddress'];
    if (!clone['deliveryLocationId']) clone['deliveryLocationId'] = clone['receiverLocationId'];
    if (!clone['deliveryPartnerAddressId']) clone['deliveryPartnerAddressId'] = clone['receiverPartnerAddressId'];

    return clone;
  }
}

type ShowUIVehicleRefuelByCodeProps = {
  uiSource: app.AppComponent;
  id: string;
  onPostCommit?: entity.EntityOnPostCommit;
  readOnly?: boolean;
  popup?: boolean;
}

type ShowUIVehicleRefuelProps = {
  uiSource: app.AppComponent;
  vehicleRefuel: any;
  onPostCommit?: entity.EntityOnPostCommit;
  readOnly?: boolean;
  popup?: boolean;
}

export class UIVehicleRefuelUtils {
  static showVehicleRefuelByID({ uiSource, id, onPostCommit, readOnly, popup }: ShowUIVehicleRefuelByCodeProps) {
    const { appContext, pageContext } = uiSource.props;
    if (!readOnly) readOnly = !pageContext.hasUserWriteCapability();
    appContext
      .createHttpBackendCall('VehicleService', 'getVehicleRefuel', { id: id })
      .withSuccessData((vehicleRefuel: any) => {
        this.showVehicleRefuel({ uiSource, onPostCommit, vehicleRefuel, readOnly, popup });
      })
      .call();
  }

  static showVehicleRefuel({ uiSource, vehicleRefuel, onPostCommit, readOnly, popup }: ShowUIVehicleRefuelProps) {
    const { appContext, pageContext } = uiSource.props;
    let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIVehicleRefuelEditor
          appContext={appCtx} pageContext={pageCtx}
          observer={new entity.ComplexBeanObserver(vehicleRefuel)} readOnly={readOnly}
          onPostCommit={onPostCommit} />
      );
    }
    pageContext.addPageContent(
      `vehicle-refuel-${vehicleRefuel.vehicleCode}`,
      T(vehicleRefuel.vehicleCode
        ? `Vehicle Refuel: ${vehicleRefuel.vehicleCode}`
        : "Create Vehicle Refuel"), createPageContent);
  }
}

export class VehicleUtils {
  static initVehicle() {
    let vehicle: any = {
      code: `vehicle-${util.TimeUtil.toDateTimeIdFormat(new Date())}`,
      label: `New vehicle`,
      storageState: entity.StorageState.ACTIVE,
      editMode: entity.EditMode.VALIDATED,
    }
    return vehicle;
  }
  static onAddRow = (ctx: grid.VGridContext) => {
    let uiList = ctx.uiRoot as UIVehicleList;
    let { plugin } = uiList.props;
    let vehicle: any = {
        'uikey':`new/${util.IDTracker.next()}`,
        'code': `vehicle-${util.TimeUtil.toDateTimeIdFormat(new Date())}`
    }
    plugin.getRecords().unshift(vehicle);
    grid.initRecordStates([vehicle]);
    plugin.getListModel().filter();
  
    let state = grid.getRecordState(vehicle);
    state.markModified();
    ctx.getVGrid().forceUpdateView();
    }

}

export class VehicleRefuelUtils {
  static initVehicleRefuel(vehicle?: any) {
    let vehicleRefuel: any = {
      code: `New vehicle refuel-${util.TimeUtil.toDateTimeIdFormat(new Date())}`,
      vehicleCode: vehicle?.code,
      storageState: entity.StorageState.ACTIVE,
      editMode: entity.EditMode.VALIDATED,
    }
    return vehicleRefuel;
  }
}

//TODO: Chien - clean
// export class VehicleTripUtils {
//   static onSaveModified(context: grid.VGridContext, restURL: string) {
//     let uiRoot = context.uiRoot as UIVehicleTripList;
//     let { appContext } = uiRoot.props;
//     let config = context.config.record;
//     let record = context.model.getModifiedRecords();
//     let dynamicEntities: Array<any> = entity.EntityListUtil.toDynamicEntities(config, record);
//     let callback = (response: server.BackendResponse) => {
//       uiRoot.reloadData();
//       appContext.addOSNotification("success", `Edit Vehicle Trip Success`);
//     }
//     appContext.DO_NOT_USE_serverPUT(restURL, dynamicEntities, callback);
//   }
// }

