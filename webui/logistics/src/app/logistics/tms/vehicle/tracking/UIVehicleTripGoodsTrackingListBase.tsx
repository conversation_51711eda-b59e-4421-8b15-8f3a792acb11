import React, { Component } from 'react';
import * as FeatherIcon from 'react-feather';
import { grid, bs, app, util, input, entity } from '@datatp-ui/lib';

import { T } from '../../backend';
import { UIVehicleTrip } from '../../../vehicletrip/UIVehicleTrip';
import { updateRowByIdOrTripIds } from './UIVehicleTripGoodsTrackingPageControl';
import { UIVehicleTripList, UIVehicleTripListPlugin } from '../../../vehicletrip/UIVehicleTripList';
import { UIVehicleTripGoodsTracking } from './UIVehicleTripGoodsTracking';

import { TMSBillTransportationModeTools } from '../../utils';
import {
  VehicleTripGoodsTrackingStatus,
  VehicleTripGoodsTrackingChargeItemType,
} from '../../models';
import { UISplitVehicleTripGoodsTracking } from './UISplitGoodsTrackingList';
import { UITMSReceiptOfDeliveryPrint } from '../../bill/UITMSBillPrint';
import { UISendMessage } from './UISendMessage';
import { VehicleTripGoodsTrackingChargeEditor } from './VehicleTripGoodsTrackingChargeItemEditor';
import { setLocalStorageRouteType, UIVehicleTripGoodsTrackingListPlugin } from './UIVehicleTripGoodsTrackingList';

function isValidTime(timeStr: string) {
  const regex = /^([01]?[0-9]|2[0-3]:([0-5][0-9])$)/;
  return regex.test(timeStr);
}
class LockTrip extends entity.AppDbEntityEditor {
  hourSelected: any = {
    planStartTime: 0,
    planEndTime: 0
  }
  minuteSelected: any = {
    planStartTime: 0,
    planEndTime: 30
  }

  constructor(props: entity.AppDbEntityEditorProps) {
    super(props);
    let { observer } = this.props;
    const regex = /(\d{2}):(\d{2}):(\d{2})/;
    const timeFields = ['planStartTime', 'planEndTime'];
    let bean = observer.getMutableBean();
    for (let field of timeFields) {
      let dateTime = bean[field];
      let date = util.TimeUtil.parseCompactDateTimeFormat(dateTime);
      let localTime = util.TimeUtil.javaCompactDateTimeFormat(date);
      if (localTime) {
        const time = localTime.match(regex);
        if (time) {
          this.hourSelected[field] = Number(time[1]);
          this.minuteSelected[field] = Number(time[2]);
        }
      }
    }
    this.forceUpdate();
  }

  buildTimeSelect = (fieldName: string) => {
    let { observer } = this.props;
    let bean = observer.getMutableBean();
    let hourOptions = [0, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18];
    let hourContents: Array<any> = [];
    for (let option of hourOptions) {
      let cssClass = 'text-secondary';
      if (this.hourSelected[fieldName] == option) cssClass = 'text-primary';
      hourContents.push(
        <bs.Button laf='link' className={`border rounded-0 p-0 me-1 ${cssClass}`} onClick={() => {
          let mm: number = this.minuteSelected[fieldName] ? this.minuteSelected[fieldName] : 0;
          let time = `${option.toString().padStart(2, '0')}:${mm.toString().padStart(2, '0')}`;
          let dateTime = bean[fieldName];
          if (!dateTime) dateTime = util.TimeUtil.javaCompactDateTimeFormat(new Date());
          if (isValidTime(time)) {
            dateTime = dateTime.replace(/\d{2}:\d{2}:\d{2}\+\d{4}/, `${time}:00`);
            let date = util.TimeUtil.parseCompactDateTimeFormat(dateTime);
            bean[fieldName] = util.TimeUtil.javaCompactDateTimeFormat(date);
          }
          this.hourSelected[fieldName] = option;
          this.forceUpdate();
        }}>
          {`${option.toString().padStart(2, '0')}h`}
        </bs.Button>
      )
    }

    let mmOptions = [0, 30];
    let mmContents: Array<any> = [];
    for (let option of mmOptions) {
      let cssClass = 'text-secondary';
      if (this.minuteSelected[fieldName] == option) cssClass = 'text-primary';
      let hour: number = this.hourSelected[fieldName] ? this.hourSelected[fieldName] : 0;
      let time = `${hour.toString().padStart(2, '0')}:${option.toString().padStart(2, '0')}`;
      mmContents.push(
        <bs.Button laf='link' className={`border rounded-0 p-0 me-1 ${cssClass}`} onClick={() => {
          let dateTime = bean[fieldName];
          if (!dateTime) dateTime = util.TimeUtil.javaCompactDateTimeFormat(new Date());
          if (isValidTime(time)) {
            dateTime = dateTime.replace(/\d{2}:\d{2}:\d{2}\+\d{4}/, `${time}:00`);
            let date = util.TimeUtil.parseCompactDateTimeFormat(dateTime);
            bean[fieldName] = util.TimeUtil.javaCompactDateTimeFormat(date);
          }
          this.minuteSelected[fieldName] = option;
          this.forceUpdate();
        }}>
          {time}
        </bs.Button>
      )
    }
    return (
      <div className='flex-hbox justify-content-between'>
        <div className='flex-hbox flex-grow-0'>
          {hourContents}
        </div>
        <div className='flex-hbox flex-grow-0'>
          {mmContents}
        </div>
      </div>
    )
  }
  render(): React.ReactNode {
    let { appContext, pageContext, observer } = this.props;
    let trip = observer.getMutableBean();
    let writeCap = pageContext.hasUserWriteCapability();
    return (
      <div className='flex-vbox'>
        <div className='flex-vbox flex-grow-1'>
          <bs.Row>
            <bs.Col span={6}>
              <input.BBStringField label={'Tuyến'}
                field='route' bean={trip} disable={!writeCap} />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBDoubleField label={'Tổng Km'}
                field='estimateDistanceInKm' bean={trip} disable={!writeCap} />
            </bs.Col>
          </bs.Row>
          <div className='flex-vbox my-1'>
            <bs.FormLabel>{'Bắt đầu chạy'} </bs.FormLabel>
            {this.buildTimeSelect('planStartTime')}
            <input.BBDateInputMask key={`start-time-${util.IDTracker.next()}`}
              bean={trip} field='planStartTime' format='DD/MM/YYYY' disabled={!writeCap} timeFormat />
          </div>
          <div className='flex-vbox my-1'>
            <bs.FormLabel>{'Kết thúc'} </bs.FormLabel>
            {this.buildTimeSelect('planEndTime')}
            <input.BBDateInputMask key={`end-time-${util.IDTracker.next()}`}
              bean={trip} field='planEndTime' format='DD/MM/YYYY' disabled={!writeCap} timeFormat />
          </div>
          <input.BBDoubleField label={'Dự kiến chạy trong (giờ)'}
            bean={trip} field='estimateTimeInHour' disable />
        </div>
        <bs.Toolbar>
          <entity.ButtonEntityCommit
            appContext={appContext} pageContext={pageContext} hide={!writeCap} observer={observer}
            commit={{
              entityLabel: T('Trip'), context: 'company',
              service: 'VehicleService', commitMethod: 'saveVehicleTrip'
            }}
            onPostCommit={this.onPostCommit} />
        </bs.Toolbar>
      </div>
    )
  }
}

interface UIVehicleTripGoodsTrackingProps extends entity.DbEntityListProps {
  summaryMode?: boolean;
  viewMode?: 'aggregation' | 'table';
}

type VehicleTripGoodsTrackingConfig = {
  showFuelCol: boolean;
  showRevenueCol: boolean;
  showExpenseCol: boolean;
  inputDataCreator: boolean;
}
interface UIVehicleTripGoodsTrackingListProps extends UIVehicleTripGoodsTrackingProps {
  typeOfTransport?: 'container' | 'truck';
  screen?: 'driverReportView' | 'profitReportView';
  aggregationField?: string;
  configView?: VehicleTripGoodsTrackingConfig;
}
let entityFocus: any = {};
export abstract class VehicleTripGoodsTrackingListBase extends entity.DbEntityList<UIVehicleTripGoodsTrackingListProps> {
  idTracker: number = 0;
  idTrackerNext() { return ++this.idTracker; }
  vehicleInfoModifiedFields: Array<any> = [
    'vehicleLabel', 'driverFullName', 'identificationNo', 'mobile'
  ];
  trackingInfoModifiedFields: Array<any> = [
    'transportType', 'vehicleType'
  ];
  modifiedFields: Array<any> = [...this.vehicleInfoModifiedFields, ...this.trackingInfoModifiedFields];
  entityState = entityFocus;

  getTableName() {
    return 'lgc_fleet_vehicle_trip_goods_tracking';
  }

  groupTrip: Array<any> = [];
  firstGroupTripTrackingIds: Array<any> = [];
  clearGroupIds = () => {
    this.groupTrip = [];
    this.firstGroupTripTrackingIds = [];
  }
  //build
  getStatusColor = (subBill: any) => {
    let status = subBill.taskStatus;
    switch (status) {
      case 'PLAN': return 'info';
      case 'TRANSPORTING': return 'warning';
      case 'DONE': return 'success';
      default:
        return 'secondary';
    }
  }

  getColorByTrip = (tracking: any) => {
    let trackingId = tracking['id'];
    let totalFileOnTrip = tracking['totalFileOnTrip'];
    let cssClass = '';
    let hightLight = '';
    if (totalFileOnTrip > 1) {
      let tripId = tracking['vehicleTripId'];
      let idx = 0;

      if (!this.groupTrip.includes(tripId)) {
        this.firstGroupTripTrackingIds.push(trackingId);
        this.groupTrip.push(tripId);
      }
      if (this.firstGroupTripTrackingIds.includes(trackingId)) {
        cssClass = `${cssClass} fw-bolder`
      }
      this.groupTrip.forEach((value, index) => {
        if (value === tripId) {
          idx = index + 1;
        }
      });
      if (idx % 2 == 0) {
        cssClass = `${cssClass} text-success`;
      } else {
        cssClass = `${cssClass}  ${hightLight} text-warning`;
      }
    }
    return cssClass;
  }

  buildTripEditMode(tracking: any) {
    if (!tracking['vehicleTripId']) return <></>
    if (tracking['editMode'] === entity.EditMode.LOCKED) {
      return <FeatherIcon.Lock style={{ cursor: 'pointer' }} size={15} color='red' onClick={() => this.updateTripStatus(tracking, entity.EditMode.DRAFT)} />
    }
    return <FeatherIcon.Unlock style={{ cursor: 'pointer' }} size={15} color='green' onClick={() => this.lockTrip(tracking)} />
  }

  buildTruckIcon = (tracking: any) => {
    let totalFileCollectTrip = tracking['totalFileOnTrip'];
    if (!this.firstGroupTripTrackingIds.includes(tracking['id'])) return;
    let countIcon = (
      <div className={'text-center'}
        style={{
          height: 12, width: 12, borderRadius: 50, fontSize: 12,
          backgroundColor: 'red', color: 'white',
          marginTop: -6, marginLeft: -4
        }}>
        {totalFileCollectTrip}
      </div>
    );
    return (
      <bs.Button style={{ width: 30 }} className={`text-primary`} onClick={() => { }} laf='link'>
        <div className='flex-hbox'>
          <FeatherIcon.Truck size={12} />
          {countIcon}
        </div>
      </bs.Button>
    );
  }

  buildCombineMainFieldIcon = (fieldSelected: grid.FieldConfig, dTracking: grid.DisplayRecord, cssClass: any) => {
    let { plugin } = this.props;
    let tracking = dTracking.record;
    if (this.firstGroupTripTrackingIds.includes(tracking['id'])) {
      let icon = (
        <bs.Button laf='link' onClick={() => {
          let dRecordFilters: Array<grid.DisplayRecord> = [];
          for (let dRec of plugin.getListModel().getDisplayRecordList().getDisplayRecords()) {
            let rec = dRec.record;
            if (rec['vehicleTripId'] === tracking['vehicleTripId']) {
              dRecordFilters.push(dRec);
            }
          }
          plugin.getListModel().getRecords().filter(sel => sel['vehicleTripId'] === tracking['vehicleTripId']);
          let cursorStatus = dTracking.getRecordState();
          let selected = cursorStatus.selected;
          if (selected) {
            selected = false;
          } else {
            selected = true;
          }
          for (let rec of dRecordFilters) {
            let status = rec.getRecordState();
            status.selected = selected;
          }
          let startRow = dTracking.row;
          let totalCollectField = tracking['totalFileOnTrip'];
          let endRow = dTracking.row + totalCollectField;
          if (selected) {
            for (let sel of dRecordFilters) {
              if (sel.row >= startRow && sel.row < endRow) continue;
              let moveRecord = sel.cloneRecord();
              this.getVGridContext().model.insertDisplayRecordAt(startRow, moveRecord);
              this.getVGridContext().model.getDisplayRecordList().updateDisplayRecords();
              plugin.getListModel().removeRecord(sel.record);
              moveRecord['_state'] = sel.getRecordState();
            }
            plugin.getListModel().getRecordFilter().withPattern(tracking['vehicleTripCode']);
            plugin.getListModel().filter();
            this.vgridContext.getVGrid().forceUpdateView();
          } else {
            plugin.getListModel().getRecordFilter().withPattern('');
            plugin.getListModel().filter();
            this.vgridContext.getVGrid().forceUpdateView();
          }
          this.getVGridContext().broadcastCellEvent({
            row: 0, field: fieldSelected, event: 'Modified'
          })
        }}>
          <FeatherIcon.ChevronDown size={12} className={`${cssClass}`} />
        </bs.Button>
      );
      return icon;
    } else if (this.groupTrip.includes(tracking['vehicleTripId'])) {
      return <div style={{ width: 12 }}></div>
    }
  }

  buildOpsStatus = (goodsTracking: any) => {
    let opsStatus = goodsTracking['opsStatus'];
    let opsAccountFullName = goodsTracking['opsAccountFullName'];
    let opsAccountMobile = goodsTracking['opsAccountMobile'];
    let opsIdentificationNo = goodsTracking['opsIdentificationNo'];

    let opsNote = goodsTracking['opsNote'];
    if (!opsStatus) return;

    let onShowInfo = () => {
      let content = (
        <div>
          {`${opsAccountFullName} Mobile:${opsAccountMobile} ID:${opsIdentificationNo}`}
          <div>
            {`${opsNote ? 'Note: ' + opsNote : ''}`}
          </div>
        </div>

      )
      bs.dialogShow(T(`OPS INFO: ${opsStatus}`), content);
    }

    let renderStatus = (icon: any, laf: bs.ButtonLaf, backgroundColor?: string) => {
      return (
        <div className='flex-grow-1 align-items-center'>
          <bs.Button laf={laf} style={{ height: 15, width: 15, backgroundColor: backgroundColor }}
            className='p-0' onClick={onShowInfo}>
            {icon}
          </bs.Button>
        </div>
      )
    }

    if (opsStatus === 'NEED_CONFIRM') return renderStatus('C', 'danger')
    if (opsStatus === 'PROCESSING') return renderStatus('P', 'warning', '#FFC125')
    if (opsStatus === 'DONE') return renderStatus('D', 'success')
  }

  lockTrip(tracking: any) {
    let { appContext, pageContext, plugin } = this.props;
    let vehicleTripId = tracking['vehicleTripId'];

    let trackingOnTrips = plugin.getRecords().filter(sel => sel['vehicleTripId'] === vehicleTripId);
    let totalKm = 0;
    trackingOnTrips.forEach(tracking => { totalKm += tracking['estimateDistanceInKm'] });

    const v = 40;
    const timingError = 0.20;
    if (!vehicleTripId) {
      appContext.addOSNotification('danger', 'Trip Is Not Created!');
      return;
    }
    let successCB = (trip: any) => {
      let mode = tracking['mode'];
      let deliveryPlan: string = tracking['deliveryPlan'];
      let time = tracking['time'];
      if (isValidTime(time) && deliveryPlan) {
        deliveryPlan = deliveryPlan.replace(/\d{2}:\d{2}:\d{2}/, `${time}:00`)
      }
      let timeInHour = totalKm / v;
      timeInHour += timeInHour * timingError;
      let date = util.TimeUtil.parseCompactDateTimeFormat(deliveryPlan);
      if (!trip['planStartTime'] && !trip['planEndTime']) {
        if (TMSBillTransportationModeTools.isImport(mode)) {
          let range = new util.TimeRange();
          range.fromSetDate(date);
          range.toSetDate(date);
          range.fromAdd(-timeInHour, 'h');
          let planStartTime = util.TimeUtil.toCompactDateTimeFormat(range.fromAsDate());
          let planEndTime = util.TimeUtil.toCompactDateTimeFormat(range.toAsDate());
          trip['planStartTime'] = planStartTime;
          trip['planEndTime'] = planEndTime;
        } else {
          let range = new util.TimeRange();
          range.fromSetDate(date);
          range.toSetDate(date);
          range.toAdd(timeInHour, 'h');
          let planStartTime = util.TimeUtil.toCompactDateTimeFormat(range.fromAsDate());
          let planEndTime = util.TimeUtil.toCompactDateTimeFormat(range.toAsDate());
          trip['planStartTime'] = planStartTime;
          trip['planEndTime'] = planEndTime;
        }
      }
      trip['editMode'] = entity.EditMode.LOCKED;
      trip['estimateDistanceInKm'] = totalKm;
      trip['estimateTimeInHour'] = timeInHour;
      trip['transporterStatus'] = 'NEED_CONFIRM';
      let createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (
          <LockTrip appContext={appCtx} pageContext={pageCtx} observer={new entity.BeanObserver(trip)}
            onPostCommit={(update) => {
              pageCtx.back();
              trackingOnTrips.forEach(sel => sel['editMode'] = update['editMode']);
              this.getVGridContext().getVGrid().forceUpdateView();
            }}
          />
        )
      }
      pageContext.createPopupPage('trip', T('Vehicle Trip'), createContent, { size: 'md' });
    }

    appContext
      .createHttpBackendCall('VehicleService', 'getVehicleTrip', { id: vehicleTripId })
      .withSuccessData(successCB)
      .call();
  }

  buildTripTransportStatusIcon = (tracking: any) => {
    let tripTransporterStatus = tracking.tripTransporterStatus;
    if (tripTransporterStatus === 'CONFIRMED') {
      return (
        <div className='flex-vbox justify-content-center flex-grow-0 px-1'>
          <FeatherIcon.CheckCircle className='text-primary' size={12} />
        </div>
      )
    }
    if (tripTransporterStatus === 'REJECT') {
      return (
        <bs.Badge laf='danger'>
          {T('Transporter Reject')}
        </bs.Badge>
      )
    }
    return <div style={{ width: 18 }}></div>;
  }
  //
  showTrackingsByBillId = (dTracking: grid.DisplayRecord) => {
    let { pageContext, plugin } = this.props;
    let tracking = dTracking.record;
    let title = `Màn hình chia cung đường`;
    let onCommit = (_records: Array<any>, _uiEditor?: app.AppComponent) => {
      for (let updateRecord of _records) {
        let id = updateRecord['id'];
        let findTracking = plugin.getListModel().getRecords().find(sel => sel['id'] === id);
        if (findTracking) {
          for (let propertyName in updateRecord) {
            findTracking[propertyName] = updateRecord[propertyName];
            findTracking['_state']['selected'] = true;
          }
        } else {
          this.vgridContext.model.insertDisplayRecordAt(dTracking.row, updateRecord);
          this.vgridContext.model.getDisplayRecordList().updateDisplayRecords();
          updateRecord['_state']['newCreated'] = false;
          updateRecord['_state']['selected'] = true;
        }
      }
      this.vgridContext.getVGrid().forceUpdateView();
    }
    let createPopup = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let trackingGeneralInfo = {
        ...tracking,
        id: null,
      }
      return (
        <UISplitVehicleTripGoodsTracking currentTrackingId={tracking.id} observer={new entity.BeanObserver(trackingGeneralInfo)}
          appContext={appCtx} pageContext={pageCtx} onPostCommit={onCommit} />
      )
    }
    pageContext.createPopupPage('split-vehicle-trip-goods-tracking', title, createPopup, { size: 'xl' })
  }

  showChargeItems(goodsTracking: any, type: VehicleTripGoodsTrackingChargeItemType) {
    let { appContext, pageContext } = this.props
    appContext.createHttpBackendCall('VehicleService', 'getVehicleTripGoodsTrackingById', { id: goodsTracking.id })
      .withSuccessData((data: any) => {
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          let sellingFields = ['fixedCharge', 'extraCharge', 'liftOnLiftOffCharge', 'totalCharge'];
          let buyingFields = ['fixedCost', 'extraCost', 'totalCost', 'travelCost', 'driverSalary', 'vetc'];
          let observer = new entity.ComplexBeanObserver(data);
          return (
            <VehicleTripGoodsTrackingChargeEditor appContext={appCtx} pageContext={pageCtx}
              type={type}
              observer={observer}
              onPostCommit={(_bean) => {
                pageCtx.back();
                sellingFields.forEach(field => goodsTracking[field] = _bean['trackingCharge'][field]);
                buyingFields.forEach(field => goodsTracking[field] = _bean['trackingCharge'][field]);
                this.getVGridContext().getVGrid().forceUpdateView();
              }} />
          )
        }
        pageContext.createPopupPage('goods-tracking-charge-items', 'Charge Items', createAppPage, { size: 'lg' })
      })
      .call();
  }

  updateTripStatus(tracking: any, editMode: entity.EditMode) {
    let { appContext, plugin } = this.props;
    let context = this.getVGridContext();
    appContext
      .createHttpBackendCall('VehicleService', 'updateVehicleTripEditMode', { vehicleTripId: tracking.vehicleTripId, 'editMode': editMode })
      .withSuccessData((data: any) => {
        let vehicleTripId = tracking['vehicleTripId'];
        for (let record of plugin.getRecords()) {
          if (record['vehicleTripId'] === vehicleTripId) {
            record['editMode'] = editMode;
          }
        }
        context.getVGrid().forceUpdateView();
      })
      .withSuccessNotification('success', editMode === entity.EditMode.LOCKED ? T('Locked Success!') : T('Unlocked Success!'))
      .call();
  }

  loadPrintPODByBillIds(tmsBillIds: Array<any>) {
    let { pageContext } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UITMSReceiptOfDeliveryPrint key={`print-${util.IDTracker.next()}`}
          appContext={appCtx} pageContext={pageCtx} billIds={tmsBillIds} />
      )
    }
    pageContext.createPopupPage('receipt-of-delivery', T('Receipt Of Delivery'), createAppPage, { size: 'xl' });
  }

  printPOD(tracking: any) {
    let { appContext } = this.props;
    let tmsBillIds: Set<any> = new Set();
    if (tracking.vehicleTripId) {
      appContext.createHttpBackendCall('VehicleRestCallService', 'findByVehicleTripId', { id: tracking.vehicleTripId })
        .withSuccessData((data: any) => {
          let trackingResponse: Array<any> = data;
          for (let tracking of trackingResponse) {
            tmsBillIds.add(tracking.tmsBillId)
          }
          this.loadPrintPODByBillIds(Array.from(tmsBillIds));
        })
        .call();
    } else {
      tmsBillIds.add(tracking.tmsBillId);
      this.loadPrintPODByBillIds(Array.from(tmsBillIds));
    }
  }

  confirmGoodsTracking = (tracking: any, status: VehicleTripGoodsTrackingStatus) => {
    let { appContext, pageContext, onModifyBean, typeOfTransport } = this.props;
    let params = {
      trackingId: tracking.id,
      status: status,
      typeOfTransport: typeOfTransport
    }
    let onCommit = () => {
      appContext.createHttpBackendCall('VehicleRestCallService', 'confirmVehicleTripTracking', { params })
        .withSuccessData((data: any) => {
          let goodsTracking = data;
          tracking['status'] = goodsTracking['status'];
          tracking['issue'] = goodsTracking['issue'];
          tracking['version'] = goodsTracking['version'];
          tracking['idx'] = goodsTracking['idx'];
          if (onModifyBean) onModifyBean(tracking, entity.ModifyBeanActions.MODIFY);
          this.getVGridContext().getVGrid().forceUpdateView();
          appContext.addOSNotification("success", T(`${status} Tracking Success!`));
        })
        .call();
    }

    if (status === VehicleTripGoodsTrackingStatus.CONFIRMED) return onCommit();

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <div className='flex-vbox'>
          <input.BBTextField bean={params} field={'issue'} label={T('Note')} style={{ height: '10em' }} />
          <bs.Toolbar className='border'>
            <entity.WButtonEntityNew
              label={'Commit'} appContext={appCtx} pageContext={pageCtx}
              onClick={() => {
                onCommit();
                pageCtx.back();
              }} />
          </bs.Toolbar>
        </div>
      )
    }
    pageContext.createPopupPage('issue', "Issue", createAppPage);
  }

  selectVehicleTrip = (trackingIds: Array<any>) => {
    let { pageContext, plugin } = this.props;
    let onSelected = (appCtx: app.AppContext, pageCtx: app.PageContext, entity: any) => {
      const dataCallback = (trackingModel: any) => {
        let trackingResults = trackingModel.trackingMapResults;
        let trackings = plugin.getListModel().getRecords();
        for (let trackingResult of trackingResults) {
          let tracking = trackings.find(sel => sel.id === trackingResult.id);
          if (!tracking) continue;
          for (const propertyName in trackingResult) {
            tracking[propertyName] = trackingResult[propertyName];
          }
        }
        pageCtx.back();
        this.getVGridContext().getVGrid().forceUpdateView();
        appCtx.addOSNotification("success", T('Add Vehicle Trip Bill Success!'));
      };

      let trackingModel = {
        vehicleTripId: entity.id,
        trackingIds: trackingIds,
      }
      appCtx
        .createHttpBackendCall('VehicleService', 'createVehicleTripForTracking', { model: trackingModel })
        .withSuccessData(dataCallback)
        .withFailNotification('danger', T('Add Vehicle Trip Bill Fail!'))
        .call();
    }

    let createUI = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIVehicleTripList type='selector'
          appContext={appCtx} pageContext={pageCtx} plugin={new UIVehicleTripListPlugin()}
          onSelect={onSelected} />)
    }
    pageContext.createPopupPage('vehicle-trip', T('Vehicle Trip'), createUI, { backdrop: 'static', size: 'xl' });
  }

  showVehicleTrip = (tracking: any, isNewTrip: boolean) => {
    if (isNewTrip) {
      this.onNewVehicleTrip(tracking);
      return;
    }
    let { appContext, pageContext, plugin } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();

    let dataCallback = (data: any) => {
      let createUI = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        let onPostCommit = (entity: any) => {
          for (let trackingResult of entity.trackingMapResults) {
            let tracking = plugin.getListModel().getRecords().find(sel => trackingResult.id === sel.id);
            for (const propertyName in trackingResult) {
              tracking[propertyName] = trackingResult[propertyName];
            }
          }
          pageCtx.back();
          this.getVGridContext().getVGrid().forceUpdateView();
        }
        return (
          <UIVehicleTrip
            key={`vehicle-trip`}
            appContext={appCtx} pageContext={pageCtx} readOnly={!writeCap} allowSaveAndUpdateTracking
            observer={new entity.ComplexBeanObserver(data)} onPostCommit={onPostCommit} />)
      }
      pageContext.createPopupPage('vehicle-trip', T('Vehicle Trip'), createUI, { backdrop: 'static', size: 'xl' });
    }
    appContext
      .createHttpBackendCall('VehicleService', 'getVehicleTrip', { id: tracking.vehicleTripId })
      .withSuccessData(dataCallback)
      .withFailNotification("danger", T(`Get Vehicle Trip: ${tracking.fromLocationCode} - ${tracking.toLocationCode} Fail`))
      .call()
  }

  sendMessage(trackings?: Array<any>) {
    let { plugin, appContext, pageContext } = this.props;
    let records = plugin.getListModel().getSelectedRecords();
    if (trackings) {
      records = trackings;
    }
    if (!records || records.length === 0) {
      bs.notificationShow("danger", T("Goods Tracking is not selected"), T("You should select at least 1 Record!!!"));
      return;
    }
    let successCB = (data: any) => {
      let createPopupContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        let onModify = (messages: Array<any>) => {
          for (let record of records) {
            for (let message of messages) {
              if (record.vehicleTripId === message.vehicleTripId) {
                record.messageStatus = message.status;
              }
            }
          }
          this.getVGridContext().getVGrid().forceUpdateView();
        }
        return (
          <UISendMessage appContext={appCtx} pageContext={pageCtx} messages={data} onModify={onModify} />
        )

      };
      pageContext.createPopupPage("message", T('Message'), createPopupContent, { size: 'lg', backdrop: "static" });

    }
    appContext
      .createHttpBackendCall('VehicleService', 'generateMessage', { models: records })
      .withSuccessData(successCB)
      .call();
  }

  createTotalRecords(_ctx: grid.VGridContext) {
    let { plugin } = this.props;
    let records = plugin.getListModel().getSelectedRecords();
    let footerRecords = new Array<any>();
    let actives = util.CollectionMath.sum(
      records, [], { billLabel: 'Total Selected' });
    footerRecords.push(actives);
    return footerRecords;
  }

  onRemoveVehicleTrip = (tracking: any) => {
    let { appContext, plugin } = this.props;
    let onConfirm = () => {
      appContext.createHttpBackendCall('VehicleService', 'removeVehicleTrip', { id: tracking.id })
        .withSuccessData((_data: any) => {
          let trackingOnTrip = plugin.getRecords().filter(sel => sel['vehicleTripId'] === tracking['vehicleTripId']);
          updateRowByIdOrTripIds(this.getVGridContext(), trackingOnTrip, () => {
            this.clearGroupIds();
            this.getVGridContext().getVGrid().forceUpdateView();
          });
        })
        .call();
    }
    bs.dialogConfirmMessage(T('You want to remove vehicle trip for this tracking'), T('Remove vehicle trip'), onConfirm)
  }

  onNewVehicleTrip = (tracking: any) => {
    let { appContext, pageContext } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let dataCallback = (data: any) => {
      let createUI = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        let onPostCommit = (_vehicleTrip: any) => {
          pageCtx.back();
          this.getVGridContext().getVGrid().forceUpdateView();
        }
        return (
          <UIVehicleTrip
            key={`vehicle-trip`}
            appContext={appCtx} pageContext={pageCtx} readOnly={!writeCap} trackings={[tracking]}
            observer={new entity.ComplexBeanObserver(data)} allowSaveAndUpdateTracking onPostCommit={onPostCommit} />)
      }
      pageContext.createPopupPage('vehicle-trip', T('Vehicle Trip'), createUI, { backdrop: 'static', size: 'xl' });
    }

    appContext
      .createHttpBackendCall('VehicleService', 'newVehicleTrip', { trackingId: tracking.id })
      .withSuccessData(dataCallback)
      .withFailNotification("danger", T(`New Vehicle Trip Fail`))
      .call();
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) {
    let { appContext, pageContext, readOnly } = this.props;
    let tracking = dRecord.record;
    appContext.createHttpBackendCall('VehicleRestCallService', 'findPodAndVehicleTrip', { gTrackingId: tracking['id'] })
      .withSuccessData((data: any) => {
        let createUI = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          let onCommit = (entity: any, ui: app.AppComponent) => {
            this.reloadData();
          }
          return (
            <UIVehicleTripGoodsTracking appContext={appCtx} pageContext={pageCtx} sqlRecordTracking={tracking}
              observer={new entity.ComplexBeanObserver(data)} readOnly={readOnly} onPostCommit={onCommit} />
          );
        }
        pageContext.createPopupPage('vehicle-trip-goods-tracking', T('Vehicle Trip Goods Tracking'), createUI, { size: 'xl', backdrop: 'static' })
      })
      .call();
  }

  onDeleteAction(): void {
    let { appContext, plugin } = this.props;
    let trackings = plugin.getListModel().getSelectedRecords();
    let ids: Array<any> = [];
    trackings.forEach(sel => { if (sel.id) ids.push(sel.id) });
    let callbackConfirm = () => {

      appContext
        .createHttpBackendCall('VehicleService', 'deleteVehicleTripGoodsTrackings', { ids: ids })
        .withSuccessData((data: any) => {
          appContext.addOSNotification("success", T('Delete Success'));
          trackings.forEach(sel => plugin.getListModel().removeRecord(sel));
          this.getVGridContext().getVGrid().forceUpdateView();
        })
        .withFailNotification("danger", T('Delete Fail!'))
        .call()
    }
    bs.dialogConfirmMessage('Confirm Message', T('Confirm Delete Trackings'), callbackConfirm);
  }
}
interface EntityFocusProps extends grid.VGridContextProps {
  entityFocus: { type: 'unfocus' | 'focus' };
  entityType: string;
}
export class EntityFocus extends Component<EntityFocusProps> {
  constructor(props: EntityFocusProps) {
    super(props);
    if (props.entityFocus.type == 'focus') {
      this.focus();
    }
  }
  static createFocus(appContext: app.AppContext, params: any, successCB?: (data: any) => void) {
    appContext
      .createHttpBackendCall('EntityFocusService', 'create', params)
      .withSuccessData(successCB ? successCB : (_data: any) => { })
      .withSuccessNotification('success', T('Add Records To Focus Screen Success!!!'))
      .call();
  }

  static removeFocus(appContext: app.AppContext, params: any, successCB?: (data: any) => void) {
    appContext
      .createHttpBackendCall('EntityFocusService', 'remove', params)
      .withSuccessData(successCB ? successCB : (_data: any) => { })
      .withSuccessNotification('success', T('Remove Records From Focus Screen Success!!!'))
      .call();
  }
  createAndFocus = () => {
    let { context, entityType } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { appContext, plugin } = uiRoot.props;
    let selectedIds = plugin.getListModel().getSelectedRecordIds();
    if (selectedIds.length === 0) {
      this.focus();
      return;
    }
    let params = {
      entityType: entityType,
      entityIds: selectedIds,
    }
    let successCB = (_data: any) => {
      this.focus();
    }
    EntityFocus.createFocus(appContext, params, successCB);
  }

  addSearchParam(plugin: entity.DbEntityListPlugin, name: string, value: any) {
    if (!plugin.getSearchParams()) throw new Error("search params is not available");
    if (!plugin.getSearchParams().params) {
      plugin.getSearchParams().params = {};
    }
    if (!value) {
      delete plugin.getSearchParams().params[name];
    } else {
      plugin.getSearchParams().params[name] = value;
    }
    return plugin;
  }

  focus = () => {
    let { context, entityFocus, entityType } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { plugin } = uiRoot.props;
    entityFocus.type = 'focus';
    this.addSearchParam(plugin, 'focus', true);
    uiRoot.reloadData();
  }

  unfocus = () => {
    let { context, entityFocus } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { plugin } = uiRoot.props;
    entityFocus.type = 'unfocus';
    this.addSearchParam(plugin, 'focus', false);
    uiRoot.reloadData();
  }

  removeFocus = () => {
    let { context, entityType } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { appContext, plugin } = uiRoot.props;
    let selectedIds = plugin.getListModel().getSelectedRecordIds();
    if (selectedIds.length === 0) {
      return;
    }
    let params = {
      entityType: entityType,
      entityIds: selectedIds,
    }
    let successCB = (_data: any) => {
      plugin.getListModel().removeSelectedDisplayRecords();
      context.getVGrid().forceUpdateView();
    }
    EntityFocus.removeFocus(appContext, params, successCB);
  }
  render(): React.ReactNode {
    let { entityFocus } = this.props;
    let html = <></>;
    if (entityFocus.type === 'focus') {
      html = (
        <>
          {/* <bs.Button laf='link' className='text-primary' onClick={this.removeFocus}>
            <FeatherIcon.Trash2 className='text-danger' size={14} />
          </bs.Button> */}
          <bs.Button laf='link' className='text-primary flex-grow-1' onClick={this.unfocus}>
            <FeatherIcon.Lock className='text-danger mx-1' size={14} />{T('FOCUS')}
          </bs.Button>
        </>
      )
    } else {
      html = (
        <>
          {/* <bs.Button laf='link' className='text-primary' onClick={this.createAndFocus}>
            <FeatherIcon.Plus className='text-primary' size={14} />
          </bs.Button> */}
          <bs.Button laf='link' className='text-primary flex-grow-1' onClick={this.createAndFocus}>
            <FeatherIcon.Unlock className='text-success mx-1' size={14} />{T('UNFOCUS')}
          </bs.Button>
        </>
      )
    }
    return (
      <div className='flex-hbox' style={{ width: 110 }}>
        {html}
      </div>
    )
  }
}

interface ToolbarFilterOptionsProps extends grid.VGridContextProps {
  options: any[];
  optionLabels: any[];
  selectOption: any;
  onChange(val: string): void;
}
export class ToolbarFilterOptions extends Component<ToolbarFilterOptionsProps> {
  render(): React.ReactNode {
    let { onChange } = this.props;
    return (
      <div className='flex-hbox border border-300 mx-1'>
        <input.BBRadioInputField bean={{ selectOption: this.props.selectOption }} field='selectOption'
          options={this.props.options} optionLabels={this.props.optionLabels}
          onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
            onChange(newVal);
          }} />
      </div>
    )
  }
}

interface WrapTextProps extends grid.VGridContextProps {
  checked: boolean;
  onChange(val: any): void;
}
export class WrapText extends Component<WrapTextProps> {
  render(): React.ReactNode {
    let { onChange, checked } = this.props;
    return (
      <div className='flex-hbox mx-1 flex-grow-0 flex-hbox align-items-center'>
        <div className="form-check form-switch py-0 m-0">
          <input className="form-check-input p-0" type="checkbox" style={{ cursor: 'pointer' }}
            role="switch" id="rawDataToggle" checked={checked}
            onChange={(_event: any) => {
              onChange(_event.target.checked);
            }}
          />
        </div>
        <label className="form-check-label fs--1 d-flex align-items-center" htmlFor="rawDataToggle" >
          <span>{T('Wrap Text')}</span>
        </label>
      </div>
    )
  }
}

