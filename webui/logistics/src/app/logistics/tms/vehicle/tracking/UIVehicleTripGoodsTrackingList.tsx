import React from 'react';
import * as FeatherIcon from 'react-feather';
import { grid, sql, bs, app, util, input, entity } from '@datatp-ui/lib';
import { module } from "@datatp-ui/erp";

import { T } from '../../backend';
import { VehicleFleetURL } from "../../RestURL";
import { UIVehicleTripGoodsTrackingPageControl } from './UIVehicleTripGoodsTrackingPageControl';
import { CustomSelectorHeaderCell, TMSBillTransportationModeTools, TMSUtils, TMSVGridConfigTool } from '../../utils';
import {
  OilPriceType, MessageStatus, TMSBillProcessStatus, TMSBillTransportationMode,
  TMSBillType, VehicleTripGoodsTrackingStatus,
  VehicleTripGoodsTrackingChargeItemType,
} from '../../models';
import { UITMSBillUtils } from '../../bill/UITMSBillUtils';
import { UIVehicleTripGoodsTrackingSummary, VehicleTripGoodsTrackingSendMessageIcon } from './VehicleTripGoodsTrackingSummary';
import { UIVehicleTripGoodsTrackingGenerateCode } from './UIVehicleTripGoodsTrackingGenerateCode';
import { BBRefVehicle } from '../BBRefVehicle';
import { BBRefVehicleFleet } from '../BBRefVehicleFleet';
import { BBRefTransporter } from '../../transport/BBRefTransporter';
import { showVehicleTripGoodsTrackingConfig } from './UIVehicleTripGoodsTrackingConfig';
import { VehicleTripGoodsTrackingListSummaryView } from './UIVehicleTripGoodsTrackingListSummaryView';
import { UIVehicleUtils } from '../VehicleUtils';
import { WTMSGridInsertRow } from '../../bill/TMSBillRecordConfig';
import { WButtonTMSBillPODPrint } from '../../api/TMSBillPrintPlugin';
import { BBRefTMSCustomer } from '../../partner/BBRefTMSCustomer';
import { BBRefTMSCarrier } from '../../partner/BBRefTMSCarrier';
import { ToolbarFilterOptions, EntityFocus, VehicleTripGoodsTrackingListBase, WrapText } from './UIVehicleTripGoodsTrackingListBase';

import { UIVehicleTripList, UIVehicleTripListPlugin } from 'app/logistics/vehicletrip/UIVehicleTripList';
import { TMSBillStopLocationList } from '../../bill/UITMSBillList';
import { BBRefTMSPartnerAddress } from '../../partner/BBRefTMSPartnerAddress';
import { WBtnMail } from '../../bill/UITMSBillMessage';
import { WHIcon } from '../../housebill/TMSBillRecordConfig';
import { UICoordinatorList, UICoordinatorListPlugin } from '../../transport/UIVehicleFleetCoordinatorList';
import { VTGTrackingManager } from './VehicleTripGoodsTrackingPlugin';
import BBRefLocation = module.settings.BBRefLocation;

const compactDate = (val: string) => {
  if (!val) return;
  let dateTime = util.text.formater.compactDate(val);
  return dateTime;
}
const formatCurrency = (val: any) => { return util.text.formater.currency(val, 0) };

export function getLocalStorageRouteType(): string {
  let rt = localStorage.getItem('tms_vehicle_trip_goods_tracking:route_type');
  if (!rt) rt = '';
  return rt;
}
export function setLocalStorageRouteType(val: string) {
  localStorage.setItem('tms_vehicle_trip_goods_tracking:route_type', val);
}
export class UIVehicleTripGoodsTrackingListPlugin extends entity.DbEntityListPlugin {
  routeType: any = '';
  constructor(searchDateRange?: number | util.TimeRange) {
    super([]);
    let rt = getLocalStorageRouteType();
    if (rt) this.routeType = rt;

    let dateRange = new util.TimeRange();
    if (searchDateRange) {
      if (searchDateRange instanceof util.TimeRange) {
        dateRange = searchDateRange;
      } else {
        dateRange.fromSetDate(new Date());
        dateRange.fromSubtract(searchDateRange, 'day');
      }
    }

    this.backend = {
      context: 'company',
      service: 'VehicleRestCallService',
      searchMethod: 'searchVehicleTripGoodsTrackings',
    }

    this.searchParams = {
      "params": { routeType: this.routeType },
      "filters": [
        ...sql.createSearchFilter(),
      ],
      "rangeFilters": [
        { ...sql.createDateTimeFilterNew("dateTime", "Date", searchDateRange ? dateRange : null) }
      ],
      "optionFilters": [
        {
          name: 'dataScope', label: 'Scope', type: 'STRING', required: false,
          options: [app.AppDataScope.OWNER.scope, app.AppDataScope.COMPANY.scope],
          optionLabels: [T('User'), T('Company')],
          selectOption: app.AppDataScope.COMPANY.scope,
        },
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED]),
      ],
      maxReturn: 3000,
    }
  }

  withDataScope = (dataScope?: app.AppDataScope) => {
    if (this.searchParams && this.searchParams.optionFilters) {
      let filterScope = this.searchParams.optionFilters.find((f: any) => f.name === 'dataScope');
      if (filterScope) filterScope.selectOption = dataScope?.scope;
    }
    return this;
  }

  loadData(uiList: entity.DbEntityList<any>) {
    let params = { 'params': this.searchParams }
    this.createBackendSearch(uiList, params).call();
  }

  withRouteType(type: string) {
    setLocalStorageRouteType(type);
    this.routeType = type;
    this.addSearchParam('routeType', type);
    return this;
  }

  withTMSBillId(id: number) {
    this.addSearchParam('tmsBillId', id);
    return this;
  }

  withVehicleTripId(id: number) {
    this.addSearchParam('vehicleTripId', id);
    return this;
  }

  withFleetId(id: number) {
    this.addSearchParam('fleetId', id);
    return this;
  }

  withDriverReportId(id: number) {
    this.addSearchParam('driverReportId', id);
    return this;
  }

  withTypeOfTransport(type: 'container' | 'truck' | null) {
    this.addSearchParam('typeOfTransport', type);
    return this;
  }

  withInputDataApp(val: boolean) {
    this.addSearchParam('inputDataApp', val);
    return this;
  }

  withIds(ids: Array<number>) {
    this.addSearchParam('ids', ids);
    return this;
  }

  withExcludeNeedConfirmStatus(value: boolean) {
    this.addSearchParam('excludeNeedConfirmStatus', value);
    return this;
  }
}
export class UIVehicleTripGoodsTrackingList extends VehicleTripGoodsTrackingListBase {
  buildConfigItems(allow: boolean): Array<grid.RecordAction> {
    if (allow) return [];
    return [
      {
        name: 'del', hint: 'Delete', icon: FeatherIcon.Trash2,
        customRender: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          const chargeState = () => {
            let tracking = dRecord.record;
            ctx.model.getDisplayRecordList().toggleDeletedDisplayRecord(dRecord.row);
            if (dRecord.getRecordState().isMarkDeleted()) {
              tracking['editState'] = 'DELETED';
            } else {
              dRecord.getRecordState().selected = false;
              delete tracking['editState'];
            }
            ctx.getVGrid().forceUpdateView();
          }
          const onClick = (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
            let tracking = dRecord.record;
            if (tracking['editState'] == 'DELETED') {
              chargeState();
            } else {
              bs.dialogConfirmMessage(T('Remove'), 'Bạn có chắc muốn xóa dòng này?', chargeState);
            }
          }
          return (
            <bs.Button laf='link' className='text-danger'
              onClick={() => onClick(ctx, dRecord)} >
              <FeatherIcon.Trash2 size={12} />
            </bs.Button >
          )
        }
      },
      {
        name: 'divide', hint: 'Divide', icon: FeatherIcon.Divide,
        customRender: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          let uiList = ctx.uiRoot as UIVehicleTripGoodsTrackingList;
          let tracking = dRecord.record;
          let allowSplit = tracking.processStatus === TMSBillProcessStatus.PENDING ? true : false;
          return (
            <bs.Button laf='link' disabled={allowSplit} onClick={() => uiList.showTrackingsByBillId(dRecord)} >
              <FeatherIcon.Divide size={12} />
            </bs.Button >
          )
        }
      },
      {
        name: 'edit', hint: 'Edit', icon: FeatherIcon.Edit,
        customRender(ctx, dRecord) {
          let uiTracking = ctx.uiRoot as UIVehicleTripGoodsTrackingList;
          return (
            <bs.Button laf='link' onClick={() => uiTracking.onDefaultSelect(dRecord)}>
              <FeatherIcon.Edit size={12} className='text-warning' />
            </bs.Button>
          )
        },
      },
      {
        name: 'copy', hint: 'Copy', icon: FeatherIcon.Plus,
        customRender: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          let create = (_ctx: grid.VGridContext, atRecord: grid.DisplayRecord) => {
            return { ...UIVehicleUtils.cloneGoodsTracking(ctx, atRecord.record), typeOfTransport: this.props.typeOfTransport };
          }
          let allowInsert = (_ctx: grid.VGridContext, atRecord: grid.DisplayRecord) => {
            let recState = atRecord.getRecordState(false);
            if (!recState) return false;
            if (recState.isMarkDeleted()) {
              return false;
            }
            return true;
          }
          return (<WTMSGridInsertRow style={{ marginRight: 5 }} key={'add'} color='link' context={ctx} row={dRecord.row}
            createInsertRecord={create} allowInsert={allowInsert} />);
        },
      }
    ];
  }

  buildFuelColConfig(onInputChange?: (ctx: grid.FieldContext, oldVal: any, newVal: any) => void): grid.FieldConfig[] {
    const { configView } = this.props;
    const showFuelCol = configView?.showFuelCol;
    if (!showFuelCol) return [];
    return [
      {
        name: 'litersOfFuel', label: T('Dầu'), dataType: 'double', hint: T('Fuel'),
        width: 80,
      },
      {
        name: 'extraLitersOfFuel', label: T('Dầu Ngoài'), dataType: 'double', hint: T('Extra Fuel'),
        width: 80,
        editor: {
          type: 'double',
          onInputChange: onInputChange
        }
      },
      {
        name: 'totalLitersOfFuel', label: T('Tổng Dầu'), dataType: 'double', hint: T('Total Fuel'),
        width: 80
      },
    ]
  }

  buildRevenueColConfig(onInputChange?: (ctx: grid.FieldContext, oldVal: any, newVal: any) => void): grid.FieldConfig[] {
    const { pageContext, configView } = this.props;
    let showRevenueCol = configView?.showRevenueCol;
    showRevenueCol = showRevenueCol ? true : false;
    const modCap = pageContext.hasUserModeratorCapability();
    let uiList = this;
    return [
      {
        name: 'fixedCharge', label: T('Cước'), format: formatCurrency, dataType: 'double', width: 100,
        state: { visible: modCap && showRevenueCol },
        editor: {
          type: 'currency',
          onInputChange: onInputChange,
        }
      },
      {
        name: 'extraCharge', label: T('Phát Sinh'), format: formatCurrency, dataType: 'currency', width: 100,
        state: { visible: modCap && showRevenueCol },
      },
      {
        name: 'totalCharge', label: T('Tổng'), hint: T('Total Charge'), dataType: 'double', format: formatCurrency,
        state: { visible: modCap && showRevenueCol },
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && (event.field.name === 'fixedCharge' || event.field.name === 'extraCharge')) {
              cell.forceUpdate();
            }
          },
        },
        editor: {
          type: 'currency',
          renderCustom(fieldCtx: grid.FieldContext, _onInputChange: grid.OnInputChange) {
            let dRecord = fieldCtx.displayRecord;
            let field = fieldCtx.fieldConfig;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let goodsTracking = dRecord.record;
            return (
              <div className='flex-hbox align-items-center'>
                <input.BBCurrencyField tabIndex={tabIndex} focus={focus} disable
                  bean={goodsTracking} field={field.name} />
                <FeatherIcon.Edit className='m-1' size={15} color='orange' style={{ cursor: 'pointer' }}
                  onClick={() => { uiList.showChargeItems(goodsTracking, VehicleTripGoodsTrackingChargeItemType.SELLING) }} />
              </div>
            )
          },
        }
      },
    ]
  }
  buildExpenseColConfig(onInputChange?: (ctx: grid.FieldContext, oldVal: any, newVal: any) => void): grid.FieldConfig[] {
    const { pageContext, configView, typeOfTransport } = this.props;
    let showExpenseCol = configView?.showExpenseCol;
    const isContainerView = typeOfTransport === 'container';
    const modCap = pageContext.hasUserModeratorCapability();
    showExpenseCol = showExpenseCol ? true : false;

    let uiList = this;
    return [
      {
        name: 'fixedCost', label: T('Cước Mua'), width: 100, dataType: 'currency',
        state: { visible: modCap && showExpenseCol },
        editor: {
          type: 'currency',
          onInputChange: onInputChange
        }
      },
      {
        name: 'extraCost', label: T('Phát Sinh'), width: 100, dataType: 'currency',
        state: { visible: modCap && showExpenseCol },
        editor: {
          type: 'currency',
          onInputChange: onInputChange
        }
      },
      {
        name: 'liftOnLiftOffCharge', label: T('Nâng/Hạ'), format: formatCurrency, dataType: 'double', width: 100,
        state: { visible: modCap && isContainerView && showExpenseCol },
        editor: {
          type: 'currency',
          onInputChange: onInputChange,
        }
      },
      {
        name: 'vetc', label: T('VETC'), width: 100, dataType: 'currency', state: { visible: false },
      },
      {
        name: 'travelCost', label: T('Tiền Đường'), format: formatCurrency, width: 100, dataType: 'currency',
        state: { visible: false },
        editor: {
          type: 'currency',
          onInputChange: onInputChange
        }
      },
      {
        name: 'driverSalary', label: T('Lương Lái Xe'),
        width: 100, dataType: 'currency', hint: 'Driver Salary', state: { visible: false },
      },
      {
        name: 'totalCost', label: T('Tổng Chi Phí'), state: { visible: modCap && showExpenseCol },
        width: 120, dataType: 'currency',
        editor: {
          type: 'currency',
          renderCustom(fieldCtx: grid.FieldContext, _onInputChange: grid.OnInputChange) {
            let dRecord = fieldCtx.displayRecord;
            let field = fieldCtx.fieldConfig;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let goodsTracking = dRecord.record;
            return (
              <div className='flex-hbox align-items-center'>
                <input.BBCurrencyField tabIndex={tabIndex} focus={focus} disable
                  bean={goodsTracking} field={field.name} />
                <FeatherIcon.Edit className='m-1' size={15} color='orange' style={{ cursor: 'pointer' }}
                  onClick={() => { uiList.showChargeItems(goodsTracking, VehicleTripGoodsTrackingChargeItemType.BUYING) }} />
              </div>
            )
          },
        }
      },
    ]
  }
  _updatePickupAddress = (tracking: any, address: any) => {
    const locStorageState = address['locStorageState'];
    if (locStorageState && locStorageState != 'ACTIVE') {
      bs.dialogShow('Inactive Address', <h4 className='text-danger'>{'Địa chỉ đã dừng hoạt động!!!'}</h4>);
    }
    tracking['pickupAddress'] = address['address'];
    tracking['pickupLocationId'] = address['locationId'];
    tracking["pickupInvAddress"] = address['invAddress'];
    tracking["pickupPartnerAddressId"] = address['id'];
  }

  _updateDeliveryAddress = (tracking: any, address: any) => {
    const locStorageState = address['locStorageState'];
    if (locStorageState && locStorageState != 'ACTIVE') {
      bs.dialogShow('Inactive Address', <h4 className='text-danger'>{'Địa chỉ đã dừng hoạt động!!!'}</h4>);
    }
    tracking['deliveryAddress'] = address['address'];
    tracking['deliveryLocationId'] = address['locationId'];
    tracking["deliveryInvAddress"] = address['invAddress'];
    tracking["deliveryPartnerAddressId"] = address['id'];
  }
  checkRefFileNo = (ctx: grid.VGridContext, row: any, fileNo: any) => {
    let { appContext, pageContext, configView, plugin } = this.props;
    const params: sql.SqlSearchParams = {
      params: { refFileNo: fileNo },
      optionFilters: [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED]),
      ],
    }
    appContext.createHttpBackendCall('VehicleRestCallService', 'searchVehicleTripGoodsTrackings', { params: params })
      .withSuccessData((data: any[]) => {
        if (data && data.length > 0) {
          let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
            return (
              <UIVehicleTripGoodsTrackingList
                configView={configView}
                appContext={appCtx} pageContext={pageCtx} readOnly
                plugin={new entity.DbEntityListPlugin(data)}
                onModifyBean={(bean) => {
                  let state = grid.getRecordState(bean);
                  let filter = plugin.getRecords().find(sel => sel['id'] == bean['id']);
                  if (filter) {
                    for (let propertyName in filter) {
                      filter[propertyName] = bean[propertyName];
                    }
                    let state = grid.getRecordState(filter);
                  } else {
                    ctx.model.insertDisplayRecordAt(row, bean);
                  }
                  state.markModified(true);
                  state.setFocus(false);
                  ctx.getVGrid().forceUpdateView(true);
                }}
              />
            );
          };
          pageContext.createPopupPage('tracking-bill', T('Tracking Bill'), createAppPage, { size: 'lg' });
        }
      })
      .call();
  }

  createVGridConfig() {
    let {
      appContext, pageContext, plugin, type, readOnly, summaryMode, viewMode, screen,
      aggregationField, typeOfTransport, configView, onModifyBean
    } = this.props;
    const inputDataCreator = configView?.inputDataCreator;
    const isContainerView = typeOfTransport === 'container';
    let uiList = this;
    let smallScreen = bs.ScreenUtil.isSmallScreen();
    const writeCap = pageContext.hasUserWriteCapability();
    const modCap = pageContext.hasUserModeratorCapability();
    let onInputChangeFees = (fieldCtx: grid.FieldContext, oldVal: any, newVal: any) => {
      let dRecord = fieldCtx.displayRecord;
      let field = fieldCtx.fieldConfig;
      let ctx = fieldCtx.gridContext;
      let record = dRecord.record;
      let event: grid.VGridCellEvent = {
        row: dRecord.row, field: field, event: 'Modified'
      }
      record.totalCharge = record.fixedCharge + record.extraCharge;
      record['updateFees'] = true;
      ctx.broadcastCellEvent(event);
    };

    let onInputChange = (fieldCtx: grid.FieldContext, oldVal: any, newVal: any) => {
      let dRecord = fieldCtx.displayRecord;
      let field = fieldCtx.fieldConfig;
      let ctx = fieldCtx.gridContext;
      let record = dRecord.record;
      let event: grid.VGridCellEvent = {
        row: dRecord.row, field: field, event: 'Modified'
      }
      if (field.name == 'fileTrucking') {
        record['updateFileTrucking'] = true;
      } else {
        record['updateTracking'] = true;
      }

      if ((field.name == 'pickupLocation' || field.name == 'deliveryLocation') && dRecord.getValue('vehicleTripId')) {
        record['updateVehicleTrip'] = true;
      }

      if (field.name == 'estimateDistanceInKm') {
        record['oil'] = dRecord.record['estimateDistanceInKm'] * dRecord.record['oilConsumption'];
        if (record['oilPriceType'] === OilPriceType.MANUAL && record['oilPrice'] > 0) {
          record['oilAfterVat'] = record['oilPrice'] * record['oil'];
        } else {
          record['oilPriceType'] = OilPriceType.AUTO;
          record['oilAfterVat'] = Math.round(record['doPriceAfterVatToday'] * record['oil']);
          record['oilBeforeVat'] = Math.round(record['doPriceBeforeVatToday'] * record['oil']);
          record['oilPrice'] = record['doPriceBeforeVatToday'];
        }
        record['updateCost'] = true;
      }

      if (field.name == 'pickupAddress' || field.name == 'deliveryAddress') {
        record["updateAddress"] = true;
        record['updateTMSBill'] = true;
      }

      onChangeVehicleInfo(dRecord, field.name, oldVal, newVal);
      ctx.broadcastCellEvent(event);
    };

    const company = app.host.DATATP_HOST.session.getAccountAcl().getCompanyAcl();
    const companyCode = company.companyCode;
    const trackingPlugin = VTGTrackingManager.getPlugin(companyCode);
    const onInputChangeBill = (fieldCtx: grid.FieldContext, oldVal: any, newVal: any) => {
      let dRecord = fieldCtx.displayRecord;
      let field = fieldCtx.fieldConfig;
      let ctx = fieldCtx.gridContext;
      let event: grid.VGridCellEvent = {
        row: dRecord.row, field: field, event: 'Modified'
      }
      let record = dRecord.record;
      if (field.name == 'billLabel') {
        this.checkRefFileNo(ctx, dRecord.row, newVal);
        if (trackingPlugin.genOffice) {
          record['office'] = trackingPlugin.genOffice(record);
          ctx.getVGrid().forceUpdateView();
        }
      }
      record['updateTMSBill'] = true;
      ctx.broadcastCellEvent(event);
    };
    let onInputChangeVehicleTrip = (fieldCtx: grid.FieldContext, oldVal: any, newVal: any) => {
      let dRecord = fieldCtx.displayRecord;
      let field = fieldCtx.fieldConfig;
      let ctx = fieldCtx.gridContext;
      let record = dRecord.record;
      let event: grid.VGridCellEvent = {
        row: dRecord.row, field: field, event: 'Modified'
      }
      onChangeVehicleInfo(dRecord, field.name, oldVal, newVal);
      record['updateVehicleTrip'] = true;
      if (field.name === 'vehicleLabel') {
        record['oil'] = record['estimateDistanceInKm'] * record['oilConsumption'];
        if (record['oilPriceType'] === OilPriceType.MANUAL && record['oilPrice'] > 0) {
          record['oilAfterVat'] = record['oilPrice'] * record['oil'];
        } else {
          record['oilPriceType'] = OilPriceType.AUTO;
          record['oilAfterVat'] = Math.round(record['doPriceAfterVatToday'] * record['oil']);
          record['oilBeforeVat'] = Math.round(record['doPriceBeforeVatToday'] * record['oil']);
          record['oilPrice'] = record['doPriceBeforeVatToday'];
        }
      }
      ctx.broadcastCellEvent(event);
    };

    let onChangeVehicleInfo = (dRecord: grid.DisplayRecord, fieldName: string, oldVal: any, newVal: any) => {
      if (this.modifiedFields.includes(fieldName) && oldVal != newVal) {
        if (!dRecord.record['vehicleInfoModified']) dRecord.record['vehicleInfoModified'] = {};
        dRecord.record['vehicleInfoModified'][fieldName] = oldVal;
      }
    }

    let sumFooter = (cell: grid.VGridCell) => {
      let fields = ['quantity', 'weight', 'tmsBillQuantity', 'tmsBillWeight'];
      let revenueFields = ['fixedCharge', 'extraCharge', 'totalCharge']
      let fieldsCost = ['oilAfterVat', 'oilBeforeVat', 'transportationCost', 'oilExtra', 'driverSalary', 'totalCost', 'profit'];
      let allFields = [...fields, ...revenueFields, ...fieldsCost];
      let records = plugin.getListModel().getSelectedRecords();
      let sum = util.CollectionMath.sum(records, allFields);
      let record = cell.getDisplayRecord().record;
      let billLabel = "Total Selected" + ` (${records.length})`
      record['billLabel'] = billLabel;
      let volume = 0;
      let tmsVolume = 0;
      for (let rec of records) {
        let tmsBillVolumeAsText = rec['tmsBillVolumeAsText'];
        if (tmsBillVolumeAsText && TMSUtils.isDecimal(tmsBillVolumeAsText)) tmsVolume += Number(tmsBillVolumeAsText)

        let volumeAsText = rec['volumeAsText'];
        if (volumeAsText && TMSUtils.isDecimal(volumeAsText)) volume += Number(volumeAsText)
      }
      record['tmsBillVolumeAsText'] = tmsVolume;
      record['volumeAsText'] = volume;
      for (let field of allFields) {
        record[field] = sum[field];
      }
      this.vgridContext.getVGrid().forceUpdateView();
    }

    const dataCellHeight = 40;
    let fieldSelected: grid.FieldConfig = {
      ...grid.createSelector('sel', 30), name: 'selected',
      customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
        if (!dRecord.isDataRecord()) return;
        let _state = dRecord.getRecordState();
        return (
          <div onKeyDown={(event) => {
            if (event.key == 'Enter' || event.key == 'Shift') {
              let fieldCtx: grid.FieldContext = {
                fieldConfig: field,
                gridContext: _ctx,
                displayRecord: dRecord,
                tabIndex: 0,
                focus: false
              }
              onInputChange(fieldCtx, !dRecord.record[field.name], dRecord.record[field.name]);
            }
          }}>
            <input.BBCheckboxField bean={_state} field={'selected'} value={false} />
          </div>
        )
      },
      customHeaderRender: (ctx: grid.VGridContext, field: grid.FieldConfig, _headerEle: any) => {
        return <CustomSelectorHeaderCell context={ctx} field={field} onSelect={() =>
          ctx.broadcastCellEvent({
            row: 0, field: field, event: 'Modified'
          })
        } />
      },
      listener: {
        onFooterDataCellEvent(cell: grid.VGridCell, event: grid.VGridCellEvent) {
          if (event.field.name === 'selected') {
            sumFooter(cell);
          }
        },
      },
    };
    let config: grid.VGridConfig = {
      title: 'Vehicle Trip Goods Tracking',
      record: {
        computeDataRowHeight: (_ctx: grid.VGridContext, dRec: grid.DisplayRecord) => {
          let rec = dRec.record;
          if (dRec.isDataRecord()) {
            let stopLocations = rec['stopLocations'];
            if (stopLocations && stopLocations.length > 1) {
              let stopCount = stopLocations.length;
              let height = stopCount * 25;
              return height;
            }
            return dataCellHeight;
          }
          return 20;
        },
        editor: {
          supportViewMode: ['aggregation', 'table'],
          enable: true
        },
        control: {
          width: 65,
          items: this.buildConfigItems(readOnly || type === 'selector'),
        },
        fields: [
          { ...fieldSelected },
          {
            ...entity.DbEntityListConfigTool.FIELD_INDEX(), width: 60,
            computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              if (!dRecord.isDataRecord()) return '';
              if (!dRecord.getValue('id')) {
                return 'bg-success bg-opacity-10'
              }
              if (dRecord.getValue('editMode') === 'LOCKED') {
                return 'bg-danger bg-opacity-10'
              }
              return this.getColorByTrip(dRecord.record);
            },
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
              if (!dRecord.isDataRecord()) return;
              let _state = dRecord.getRecordState();
              let tracking = dRecord.record;
              let icon = <FeatherIcon.Package size={14} className='text-secondary mx-1' />;
              if (dRecord.getValue('editMode') === entity.EditMode.LOCKED) {
                icon = (
                  <bs.Button laf='link' className='text-danger mx-1 p-0' onClick={() => this.updateTripStatus(tracking, entity.EditMode.DRAFT)}>
                    <FeatherIcon.Lock size={14} />
                  </bs.Button>
                )
              }
              if (dRecord.getValue('editMode') === entity.EditMode.DRAFT) {
                icon = (
                  <bs.Button laf='link' className='text-success mx-1 p-0' onClick={() => this.lockTrip(tracking)}>
                    <FeatherIcon.Unlock size={14} />
                  </bs.Button>
                )
              }
              if (_state.isMarkDeleted()) {
                icon = <FeatherIcon.Trash2 size={14} className='text-danger mx-1' />
              } else if (_state.isMarkModified()) {
                icon = <FeatherIcon.Edit size={14} className='text-warning mx-1' />
              }
              return (
                <div className='flex-hbox align-items-center'>
                  {icon}
                  <div className='text-end'>
                    {dRecord.getDisplayRow()}
                  </div>
                </div>
              )
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                let dRec = cell.getDisplayRecord();
                if (event.row == cell.getRow()) {
                  dRec.getRecordState().markModified();
                  cell.forceUpdate()
                }
              },
            },
          },
          {
            name: 'focus', label: T(""), width: 30, container: 'fixed-left',
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
              let color = 'gray';
              let goodsTracking = dRecord.record;
              if (goodsTracking['focus']) {
                color = 'orange';
              }

              let onClick = () => {
                let params = {
                  entityType: uiList.getTableName(),
                  entityIds: [goodsTracking['id']],
                }
                if (!goodsTracking['focus']) {
                  EntityFocus.createFocus(appContext, params, (data: any) => {
                    goodsTracking['focus'] = true;
                    this.getVGridContext().getVGrid().forceUpdateView()
                  });
                } else {
                  EntityFocus.removeFocus(appContext, params, (data: any) => {
                    goodsTracking['focus'] = false;
                    if (this.entityState.type === 'focus') {
                      this.getVGridContext().model.removeRecord(goodsTracking);
                    }
                    this.getVGridContext().getVGrid().forceUpdateView()
                  });
                }
              }
              return (
                <FeatherIcon.Star width={16} color={color} onClick={onClick} />
              )
            },
          },
          {
            name: 'billLabel', label: T('Ref File No.'), width: 160, container: 'fixed-left', hint: 'File Văn Phòng', state: { showRecordState: true },
            filterableType: 'string', filterable: true,
            fieldDataGetter: (record: any) => {
              return record['billLabel'];
            },
            computeCssClasses: (_ctx, dRecord) => {
              let tracking = dRecord.record;
              let cssClass = this.getColorByTrip(tracking) + this.getBgColor(tracking);
              if (!dRecord.isDataRecord()) {
                return cssClass;
              }
              return cssClass
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                let dRec = cell.getDisplayRecord();
                if (event.row == cell.getRow()) {
                  dRec.getRecordState().markModified();
                  cell.forceUpdate()
                }
              },
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
              let rec = dRecord.record;
              let cssClass = field.cssClass;
              if (field.computeCssClasses) cssClass = field.computeCssClasses(_ctx, dRecord);
              let label = rec[field.name];
              let formatLabel = label;
              if (field.width) {
                formatLabel = util.text.formater.uiTruncate(label, field.width + 140, true);
              }
              let countBillId = rec['countBillId'];
              let countButton;
              if (countBillId > 1) {
                countButton = (
                  <bs.Button laf='link' style={{ width: 20 }}
                    onClick={() => uiList.showTrackingsByBillId(dRecord)}>
                    {`(${countBillId})`}
                  </bs.Button>
                )
              }
              let width = field.width ? field.width : 120;
              return (
                <div className={`flex-hbox align-items-center`}>
                  <div className='flex-hbox flex-grow-0' style={{ width: width - 50 }}>
                    {this.buildCombineMainFieldIcon(fieldSelected, dRecord, `${cssClass} my-1`)}
                    <div className={`text-truncate${cssClass}`}>
                      {formatLabel}
                    </div>
                  </div>
                  {countButton}
                  {this.buildTruckIcon(rec)}
                </div>
              );
            },
            editor: {
              type: 'string', onInputChange: onInputChangeBill,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tracking = dRecord.record;
                let countBillId = tracking['countBillId'];
                let countButton;
                if (countBillId > 1) {
                  countButton = (
                    <bs.Button laf='link' style={{ width: 20 }}
                      onClick={() => uiList.showTrackingsByBillId(dRecord)}>
                      {`(${countBillId})`}
                    </bs.Button>
                  )
                }
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = field.computeCssClasses(uiList.getVGridContext(), dRecord);
                let width = field.width ? field.width : 120;
                let isBillOwner = tracking['isBillOwner'];
                return (
                  <div className='flex-hbox'>
                    {uiList.buildCombineMainFieldIcon(fieldSelected, dRecord, `${cssClass} my-2`)}
                    <input.BBStringField style={{ width: width - 100 }} className={cssClass} disable={!isBillOwner}
                      bean={tracking} field={field.name} tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
                    {countButton}
                    {uiList.buildTruckIcon(tracking)}
                  </div>
                )
              },
            },
          },
          { name: 'code', label: T('Code'), width: 150, state: { visible: false } },
          {
            name: '_message', label: T('Message'), width: 30, container: 'fixed-left',
            customHeaderRender(_ctx, _field, _headerEle) {
              return <div className='flex-hbox justify-content-center'>
                <FeatherIcon.Bell size={15} />
              </div>
            },
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              if (!dRecord.isDataRecord()) return;
              let tracking = dRecord.record;
              let tripId = tracking['vehicleTripId'];
              let trackings = plugin.getRecords().filter((r: any) => r['vehicleTripId'] === tripId);
              if (!tripId) trackings = [tracking];
              let color = tracking['messageStatus'] === MessageStatus.SEND_SUCCESS ? 'green' : 'red';
              return (
                <div className='flex-hbox justify-content-center'>
                  <VehicleTripGoodsTrackingSendMessageIcon
                    appContext={appContext} pageContext={pageContext} color={color} trackings={trackings}
                    onPostCommit={() => _ctx.getVGrid().forceUpdateView()} />
                  {/* {this.buildOpsStatus(tracking)} */}
                </div>
              )
            }
          },
          {
            name: '_print', label: T('Print'), width: 40, container: 'fixed-left', state: { visible: screen ? false : true },
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let tracking = dRecord.record;
              return (
                <WButtonTMSBillPODPrint size={15} laf='link' color='text-info'
                  appContext={appContext} pageContext={pageContext} dataType='id'
                  initData={() => {
                    let vehicleTripId = tracking['vehicleTripId'];
                    if (vehicleTripId) {
                      let trackings = plugin.getRecords().filter(sel => sel['vehicleTripId'] === vehicleTripId);
                      let billIds: Array<any> = [];
                      trackings.forEach(sel => billIds.push(sel['tmsBillId']));
                      return billIds;
                    } else {
                      return [tracking['tmsBillId']]
                    }
                  }} />
              )
            }
          },
          {
            name: 'customerFullName', label: T('Customer'), width: 150, container: screen ? 'default' : 'fixed-left', dataTooltip: true, hint: 'Khách Hàng',
            sortable: true, filterableType: 'string', filterable: true,
            editor: {
              type: 'string',
              onInputChange: onInputChangeBill,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tripGoods = dRecord.record;
                const oldVal = tripGoods[field.name];
                let isBillOwner = tripGoods['isBillOwner'];
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiList.getVGridContext(), dRecord)}`
                return (
                  <BBRefTMSCustomer minWidth={400} disable={!writeCap || !isBillOwner} allowUserInput
                    className={cssClass} appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                    bean={tripGoods} beanIdField={'customerId'} beanLabelField={'customerFullName'} placeholder={'Customer'}
                    onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                      onInputChange(bean, field.name, oldVal, bean[field.name])
                    }}
                  />
                )
              },
            }
          },
          {
            name: 'time', label: T('Time'), width: 50, dataTooltip: true, hint: 'Giờ', container: screen ? 'default' : 'fixed-left',
            editor: {
              type: 'string',
              onInputChange: onInputChangeBill,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tripGoods = dRecord.record;
                let isBillOwner = tripGoods['isBillOwner'] || modCap;
                if (!isBillOwner) {
                  return (field.customRender ?
                    field.customRender(uiList.getVGridContext(), field, dRecord, focus)
                    :
                    <div>{tripGoods[field.name]}</div>
                  )
                }
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiList.getVGridContext(), dRecord)}`
                return (
                  <input.BBTimeInputMask tabIndex={tabIndex} focus={focus} className={cssClass}
                    bean={tripGoods} field={field.name} onInputChange={onInputChange} />
                )
              },
            }
          },
          {
            name: 'deliveryPlan', label: T('Delivery Plan'), width: 100, cssClass: 'flex-grow-1 text-end', hint: 'Ngày',
            sortable: true, filterableType: 'date', filterable: true,
            computeCssClasses: (_ctx, dRecord) => {
              let rec = dRecord.record;
              let trackingDate = rec['trackingDate'];
              let tmsDeliveryPlan = rec['tmsDeliveryPlan'];
              let deliveryPlan = rec['deliveryPlan'];
              let cssClass = '';
              if (deliveryPlan != tmsDeliveryPlan) cssClass = 'text-danger text-decoration-underline';
              if (trackingDate != tmsDeliveryPlan) cssClass += ' bg-danger bg-opacity-25';
              return cssClass + this.getBgColor(rec);
            },
            dataType: 'date', format: compactDate,
            editor: {
              type: 'date',
              onInputChange: onInputChangeBill,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tripGoods = dRecord.record;
                let isBillOwner = tripGoods['isBillOwner'] || modCap;
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiList.getVGridContext(), dRecord)}`
                if (!isBillOwner) {
                  return (
                    <div tabIndex={tabIndex} className={cssClass}>
                      {compactDate(tripGoods[field.name])}
                    </div>
                  )
                }
                return (
                  <input.BBDateInputMask className={cssClass} tabIndex={tabIndex} focus={focus} format='DD/MM/YYYY'
                    bean={tripGoods} field={field.name} onInputChange={onInputChange} />
                )
              },
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && event.field.name === 'deliveryPlan') {
                  cell.forceUpdate()
                }
              },
            },
          },
          {
            name: 'label', label: T('File No.'), hint: T('File No.'),
            sortable: true, width: 140, state: { visible: false },
            editor: { type: 'string', onInputChange: onInputChange }
          },
          {
            name: 'bfsHbl', label: T('Hbl Bfs'), dataTooltip: true, state: { visible: isContainerView },
          },
          {
            name: 'fileTrucking', label: T('File Trucking'), hint: T('File Trucking'),
            sortable: true, width: 140, state: { visible: false },
            editor: { type: 'string', onInputChange: onInputChange }
          },
          {
            name: 'tmsBookingDate', label: T('Booking Date'), width: 100, state: { visible: false },
            cssClass: 'flex-grow-1 text-end', hint: 'Ngày Booking',
            sortable: true, filterableType: 'date', filterable: true,
            dataType: 'date', format: compactDate,
            editor: {
              type: 'date',
              onInputChange: onInputChangeBill,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tripGoods = dRecord.record;
                let isBillOwner = tripGoods['isBillOwner'] || modCap;
                if (!isBillOwner) {
                  return (
                    <div tabIndex={tabIndex}>
                      {compactDate(tripGoods[field.name])}
                    </div>
                  )
                }
                return (
                  <input.BBDateInputMask tabIndex={tabIndex} focus={focus} format='DD/MM/YYYY'
                    bean={tripGoods} field={field.name} onInputChange={onInputChange} />
                )
              },
            },
          },
          {
            name: 'hblNo', label: T('Hbl No'), width: 100, cssClass: 'px-1',
            editor: {
              type: 'string',
              onInputChange: onInputChangeBill,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tracking = dRecord.record;
                let isBillOwner = tracking['isBillOwner'] || modCap;
                if (!isBillOwner) {
                  return (
                    <div className={`flex-hbox`} tabIndex={tabIndex}>
                      {tracking[field.name]}
                    </div>
                  )
                }
                return (
                  <input.BBStringField
                    bean={tracking} field={field.name} tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
                )
              },
            }
          },
          {
            name: 'office', label: T('Office'), width: 100, hint: 'Văn Phòng', filterableType: 'Options', filterable: true, cssClass: 'px-1',
            editor: {
              type: 'string',
              onInputChange: onInputChangeBill,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tracking = dRecord.record;
                let isBillOwner = tracking['isBillOwner'] || modCap;
                if (!isBillOwner) {
                  return (
                    <div className={`flex-hbox`} tabIndex={tabIndex}>
                      {tracking[field.name]}
                    </div>
                  )
                }
                return (
                  <input.BBStringField
                    bean={tracking} field={field.name} tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
                )
              },
            }
          },
          {
            name: "responsibleFullName", label: T('PIC.'), width: 100, cssClass: 'pe-1', hint: 'Người Chịu Trách Nhiệm',
            filterable: true, filterableType: 'options',
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = ctx.uiRoot as entity.DbEntityList
              const { appContext, pageContext } = uiList.props;
              let html = (
                <div className='flex-hbox'>
                  <module.account.WAvatars className='px-2'
                    appContext={appContext} pageContext={pageContext} avatarIds={[record['responsibleAccountId']]}
                    avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                  {record['userName']}
                </div>
              )
              return (
                TMSUtils.renderTMSGridTooltip(html, record['userName'], _field.width, dRecord.row, { className: 'flex-vbox text-start' })
              )
            },
            // editor: {
            //   type: 'string',
            //   onInputChange: onInputChange,
            //   renderCustom(ctx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            //     let dRecord = ctx.displayRecord;
            //     let field = ctx.fieldConfig;
            //     let tracking = dRecord.record;
            //     let isBillOwner = tracking['isBillOwner'];
            //     if (isBillOwner) {
            //       return <input.BBStringField bean={tracking} field={field.name} onInputChange={onInputChange} placeholder='PIC' />
            //     } else {
            //       return ctx.fieldConfig.customRender ? ctx.fieldConfig.customRender(ctx.gridContext, ctx.fieldConfig, ctx.displayRecord, ctx.focus) : tracking[ctx.fieldConfig.name];
            //     }
            //   },
            // }
          },
          // pickupDeliveryAddress
          {
            name: 'coordinatorFullName', label: T('Coordinator'), width: 50,
            customHeaderRender(_ctx, _field, _headerEle) {
              return (
                <div className='flex-hbox justify-content-center'>
                  <FeatherIcon.Monitor size={15} />
                </div>
              )
            },
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              if (!dRecord.isDataRecord()) return;
              let record = dRecord.record;
              let uiList = ctx.uiRoot as entity.DbEntityList
              const { appContext, pageContext } = uiList.props;
              let html = <div> {T('N/A')}</div>;
              if (record['coordinatorAccountId']) {
                html = (
                  <div className='flex-hbox'>
                    <module.account.WAvatars className='px-2'
                      appContext={appContext} pageContext={pageContext} avatarIds={[record['coordinatorAccountId']]}
                      avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                  </div>
                )
              }
              let btn = (
                <bs.Button laf='link' className='justify-content-center align-items-center'
                  onClick={() => {
                    let createContent = (_appCtx: app.AppContext, _pageCtx: app.PageContext) => {
                      return (
                        <UICoordinatorList type='selector'
                          appContext={_appCtx} pageContext={_pageCtx} plugin={new UICoordinatorListPlugin()}
                          onSelect={(appContext: app.AppContext, pageContext: app.PageContext, coordinator: any) => {
                            appContext.createHttpBackendCall('VehicleRestCallService', 'allocationForCoordinator', {
                              coordinatorAccountId: coordinator['accountId'],
                              trackingIds: [record['id']]
                            })
                              .withSuccessData(_data => {
                                record['coordinatorAccountId'] = coordinator['accountId'];
                                record['coordinatorFullName'] = coordinator['fullName'];
                                if (onModifyBean) onModifyBean(record);
                                pageContext.back();
                                ctx.getVGrid().forceUpdateView();
                              })
                              .withSuccessNotification('success', T('Allocation Success'))
                              .call();
                          }} />
                      )
                    }
                    pageContext.createPopupPage('coordinator', "Coordinator", createContent, { size: 'md' })
                  }}>
                  {html}
                </bs.Button>
              )
              return TMSUtils.renderTMSGridTooltip(btn, record['coordinatorFullName'], _field.width, dRecord.row)
            }
          },
          {
            name: 'senderReceiverContact', label: T('Contact'), width: 250, dataTooltip: true, hint: 'Liên Hệ Lấy/Trả Hàng',
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                let fieldName = event.field.name;
                if (cell.getRow() === event.row && (fieldName === 'mode' || fieldName === 'senderReceiverContact')) {
                  cell.forceUpdate();
                }
              },
            },
            computeCssClasses: (_ctx, dRecord) => {
              let tracking = dRecord.record;
              let mode = tracking.mode;
              let senderContact = tracking['senderContact'];
              let receiverContact = tracking['receiverContact'];
              let cssClass = this.getBgColor(tracking);
              if (TMSBillTransportationModeTools.isDomestic(mode) || (senderContact && receiverContact)) {
                cssClass = 'text-warning ' + this.getBgColor(tracking);
              }
              if (uiList.entityState.wrapText) cssClass += " text-wrap";
              return cssClass;
            },
            fieldDataGetter: (record: any) => {
              let mode = record.mode;
              let senderContact = record['senderContact'];
              let receiverContact = record['receiverContact'];
              if (!senderContact && !receiverContact) return '';
              let contact = senderContact;
              if (TMSBillTransportationModeTools.isImport(mode)) {
                contact = receiverContact;
              }
              if (TMSBillTransportationModeTools.isDomestic(mode) || (senderContact && receiverContact)) {
                contact = `-Người Gửi:${senderContact} \n-Người Nhận: ${receiverContact}`;
              }
              record['senderReceiverContact'] = contact;
              return contact;
            },
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              if (!dRecord.isDataRecord()) return;
              let tracking = dRecord.record;
              let contact;
              if (_field.fieldDataGetter) contact = _field.fieldDataGetter(tracking);
              let cssClass = _field.computeCssClasses ? _field.computeCssClasses(_ctx, dRecord) : '';
              let html = (
                <div className={cssClass}>
                  {contact}
                </div>
              )
              return (
                TMSUtils.renderTooltip(contact, html)
              );
            },
            editor: {
              type: 'string',
              onInputChange: onInputChangeBill,
              renderCustom(ctx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = ctx.displayRecord;
                let tracking = dRecord.record;
                let mode = tracking.mode;
                let isBillOwner = tracking['isBillOwner'];
                if (isBillOwner) {
                  let fieldName = 'senderContact';
                  let html = (
                    <input.BBStringField bean={tracking} field={fieldName} onInputChange={onInputChange} />
                  )
                  if (TMSBillTransportationModeTools.isImport(mode)) {
                    fieldName = 'receiverContact';
                  }
                  if (TMSBillTransportationModeTools.isDomestic(mode)) {
                    return (
                      <div className='flex-hbox' tabIndex={ctx.tabIndex} autoFocus={ctx.focus}>
                        <div className='flex-grow-0'>
                          <bs.Button laf='link' className='p-0' onClick={() => {
                            let createContent = (_appCtx: app.AppContext, _pageCtx: app.PageContext) => {
                              return (
                                <div className='flex-vbox'>
                                  <input.BBTextField bean={tracking} field='senderContact' label={T('Sender Contact')} onInputChange={onInputChange} />
                                  <input.BBTextField bean={tracking} field='receiverContact' label={T('Receiver Contact')} onInputChange={onInputChange} />
                                  <bs.Toolbar>
                                    <bs.Button laf='info' onClick={() => _pageCtx.back()}>
                                      {T('Confirm')}
                                    </bs.Button>
                                  </bs.Toolbar>
                                </div>
                              )
                            }
                            pageContext.createPopupPage('contact', "Contact", createContent)
                          }}>
                            <div className='flex-grow-1'>
                              {ctx.fieldConfig.customRender ? ctx.fieldConfig.customRender(ctx.gridContext, ctx.fieldConfig, ctx.displayRecord, ctx.focus) : tracking[ctx.fieldConfig.name]}
                            </div>
                          </bs.Button>
                        </div>
                      </div>
                    )
                  }
                  html = (
                    <input.BBStringField
                      tabIndex={ctx.tabIndex} focus={ctx.focus}
                      bean={tracking} field={fieldName} onInputChange={onInputChange} />
                  )
                  return (
                    TMSUtils.renderTMSGridTooltip(
                      html, tracking[fieldName], ctx.fieldConfig.width, dRecord.row, { className: `px-2 flex-hbox justify-content-end` }
                    )
                  )
                } else {
                  return ctx.fieldConfig.customRender ? ctx.fieldConfig.customRender(ctx.gridContext, ctx.fieldConfig, ctx.displayRecord, ctx.focus) : tracking[ctx.fieldConfig.name];
                }
              },
            }
          },
          {
            name: 'senderContact', label: T('Pickup Contact'), width: 175, dataTooltip: true, hint: 'Người Gửi',
            state: { visible: false },
            fieldDataGetter: (record) => {
              return record['senderContact'] ? `Người gửi: ${record['senderContact']}` : '';
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (cell.getRow() === event.row && event.field.name === 'mode') {
                  cell.forceUpdate();
                }
              },
            },
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let tracking = dRecord.record;
              let senderContact = tracking['senderContact'] ? tracking['senderContact'] : '-';
              return TMSUtils.renderTMSGridTooltip(senderContact, senderContact, _field.width, dRecord.row, { className: 'text-center px-1' });
            },
            editor: {
              type: 'string',
              onInputChange: onInputChangeBill,
              renderCustom(ctx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = ctx.displayRecord;
                let field = ctx.fieldConfig;
                let tracking = dRecord.record;
                let isBillOwner = tracking['isBillOwner'];
                if (isBillOwner) {
                  let senderContact = tracking['senderContact'];
                  let width = field.width ? field.width : 120;
                  let senderContext = (
                    <input.BBStringField
                      tabIndex={ctx.tabIndex} focus={ctx.focus}
                      bean={tracking} field='senderContact' onInputChange={onInputChange} placeholder='sender' />
                  )
                  return TMSUtils.renderTMSGridTooltip(senderContext, senderContact, width, dRecord.row)
                } else {
                  return ctx.fieldConfig.customRender ? ctx.fieldConfig.customRender(ctx.gridContext, ctx.fieldConfig, ctx.displayRecord, ctx.focus) : tracking[ctx.fieldConfig.name];
                }
              },
            }
          },
          {
            name: 'receiverContact', label: T('Delivery Contact'), width: 175, dataTooltip: true, hint: 'Người Nhận',
            state: { visible: false },
            fieldDataGetter: (record) => {
              return record['receiverContact'] ? `Người nhận: ${record['receiverContact']}` : null;
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (cell.getRow() === event.row && event.field.name === 'mode') {
                  cell.forceUpdate();
                }
              },
            },
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let tracking = dRecord.record;
              let receiverContact = tracking['receiverContact'] ? tracking['receiverContact'] : '-';
              return TMSUtils.renderTMSGridTooltip(receiverContact, receiverContact, _field.width, dRecord.row, { className: 'text-center px-1' });
            },
            editor: {
              type: 'string',
              onInputChange: onInputChangeBill,
              renderCustom(ctx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = ctx.displayRecord;
                let field = ctx.fieldConfig;
                let tracking = dRecord.record;
                let isBillOwner = tracking['isBillOwner'];
                if (isBillOwner) {
                  let receiverContact = tracking['receiverContact'];
                  let receiverContext = (
                    <input.BBStringField
                      tabIndex={ctx.tabIndex} focus={ctx.focus}
                      bean={tracking} field='receiverContact' onInputChange={onInputChange} placeholder='receiver' />
                  )
                  return TMSUtils.renderTMSGridTooltip(receiverContext, receiverContact, field.width, dRecord.row)
                } else {
                  return ctx.fieldConfig.customRender ? ctx.fieldConfig.customRender(ctx.gridContext, ctx.fieldConfig, ctx.displayRecord, ctx.focus) : tracking[ctx.fieldConfig.name];
                }
              },
            }
          },
          {
            name: 'senderReceiverAddress', label: T('Address'), width: 250, dataTooltip: true, hint: 'Địa Chỉ Lấy/Trả Hàng',
            state: { visible: (isContainerView || companyCode === 'beehph') },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                let fieldName = event.field.name;
                if (cell.getRow() === event.row && (fieldName === 'mode' || fieldName === 'senderReceiverAddress')) {
                  cell.forceUpdate();
                }
              },
            },
            fieldDataGetter: (record) => {
              let mode = record.mode;
              let senderAddress = record['senderAddress'];
              let receiverAddress = record['receiverAddress'];
              let address = receiverAddress;
              if (TMSBillTransportationModeTools.isDomestic(mode)) {
                address = `-Lấy: ${senderAddress} \n` + `-Trả: ${receiverAddress} \n`
              }
              if (TMSBillTransportationModeTools.isExport(mode)) {
                address = senderAddress;
              }
              record['senderReceiverAddress'] = address;
              return address
            },
            editor: {
              type: 'string',
              onInputChange: onInputChangeBill,
              renderCustom(ctx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = ctx.displayRecord;
                let tracking = dRecord.record;
                let mode = tracking.mode;
                let isBillOwner = (tracking['isBillOwner'] || modCap) && inputDataCreator;
                let cssClass = ctx.fieldConfig.cssClass;
                if (ctx.fieldConfig.computeCssClasses) {
                  cssClass = `${cssClass} ${ctx.fieldConfig.computeCssClasses(ctx.gridContext, dRecord)}`;
                }
                if (isBillOwner) {
                  let fieldName = 'senderAddress';
                  let locationIdField = 'senderLocationId'
                  let types: Array<any> = ['Export', 'None']
                  if (TMSBillTransportationModeTools.isImport(mode)) {
                    fieldName = 'receiverAddress';
                    locationIdField = 'receiverLocationId'
                    types = ['Import', 'None']
                  }
                  if (TMSBillTransportationModeTools.isDomestic(mode)) {
                    return (
                      <div className='flex-hbox' tabIndex={ctx.tabIndex} autoFocus={ctx.focus}>
                        <div className='flex-grow-0'>
                          <bs.Button laf='link' className='p-0' onClick={() => {
                            let createContent = (_appCtx: app.AppContext, _pageCtx: app.PageContext) => {
                              return (
                                <div className='flex-vbox'>
                                  <BBRefTMSPartnerAddress minWidth={500} key={`${tracking.customerId}`} className={cssClass}
                                    autofocus={ctx.focus} tabIndex={ctx.tabIndex} allowUserInput
                                    appContext={appContext} pageContext={pageContext} placeholder='Address' bean={tracking} customerId={tracking['customerId']}
                                    partnerId={tracking['customerPartnerId']}
                                    beanIdField={'senderLocationId'} beanLabelField={'senderAddress'} types={['Export', 'None']}
                                    label={T('Sender Address')}
                                    onPostCommit={(partnerAddress: any) => {
                                      uiList._updatePickupAddress(tracking, partnerAddress);
                                      onInputChange(tracking, fieldName, tracking[fieldName], tracking[fieldName]);
                                    }}
                                    onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                                      tracking['pickupAddress'] = _userInput;
                                      if (_selectOpt && _selectOpt.id) uiList._updatePickupAddress(bean, _selectOpt);
                                      onInputChange(bean, fieldName, bean[fieldName], bean[fieldName]);
                                    }}
                                  />
                                  <BBRefTMSPartnerAddress minWidth={500} key={`${tracking.customerId}`} className={cssClass}
                                    autofocus={ctx.focus} tabIndex={ctx.tabIndex} allowUserInput
                                    partnerId={tracking['customerPartnerId']}
                                    appContext={appContext} pageContext={pageContext} placeholder='Address' bean={tracking} customerId={tracking['customerId']}
                                    beanIdField={'receiverLocationId'} beanLabelField={'receiverAddress'} types={['Import', 'None']}
                                    label={T('Receiver Address')}
                                    onPostCommit={(partnerAddress: any) => {
                                      uiList._updateDeliveryAddress(tracking, partnerAddress);
                                      onInputChange(tracking, fieldName, tracking[fieldName], tracking[fieldName]);
                                    }}
                                    onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                                      tracking['deliveryAddress'] = _userInput;
                                      if (_selectOpt && _selectOpt.id) uiList._updateDeliveryAddress(bean, _selectOpt);
                                      onInputChange(bean, fieldName, bean[fieldName], bean[fieldName]);
                                    }}
                                  />
                                  <bs.Toolbar>
                                    <bs.Button laf='info' onClick={() => _pageCtx.back()}>
                                      {T('Confirm')}
                                    </bs.Button>
                                  </bs.Toolbar>
                                </div>
                              )
                            }
                            pageContext.createPopupPage('address', "Address", createContent)
                          }}>
                            <div className='flex-grow-1'>
                              {ctx.fieldConfig.customRender ? ctx.fieldConfig.customRender(ctx.gridContext, ctx.fieldConfig, ctx.displayRecord, ctx.focus) : tracking[ctx.fieldConfig.name]}
                            </div>
                          </bs.Button>
                        </div>
                      </div>
                    )
                  }
                  const oldVal = tracking[fieldName];
                  let html = (
                    <BBRefTMSPartnerAddress minWidth={500} className={cssClass}
                      autofocus={ctx.focus} tabIndex={ctx.tabIndex} allowUserInput
                      appContext={appContext} pageContext={pageContext} placeholder='Address' bean={tracking} customerId={tracking['customerId']}
                      beanIdField={locationIdField} beanLabelField={fieldName} types={types} partnerId={tracking['customerPartnerId']}
                      onPostCommit={(partnerAddress: any) => {
                        if (fieldName === 'senderAddress') {
                          uiList._updatePickupAddress(tracking, partnerAddress);
                        } else {
                          uiList._updateDeliveryAddress(tracking, partnerAddress);
                        }
                        onInputChange(tracking, fieldName, oldVal, tracking[fieldName]);
                      }}
                      onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                        if (fieldName === 'senderAddress') {
                          tracking['pickupAddress'] = _userInput;
                          if (_selectOpt && _selectOpt.id) uiList._updatePickupAddress(bean, _selectOpt);
                        } else {
                          tracking['deliveryAddress'] = _userInput;
                          if (_selectOpt && _selectOpt.id) uiList._updateDeliveryAddress(bean, _selectOpt);
                        }
                        onInputChange(bean, fieldName, oldVal, bean[fieldName]);
                      }}
                    />
                  )
                  return (
                    TMSUtils.renderTooltip(tracking[fieldName], html)
                  )
                } else {
                  let value = ctx.fieldConfig.fieldDataGetter ? ctx.fieldConfig.fieldDataGetter(ctx.displayRecord.record) : tracking[ctx.fieldConfig.name];
                  let html = (
                    <div className={cssClass}>
                      {value}
                    </div>
                  )
                  return TMSUtils.renderTooltip(value, html);
                }
              },
            }
          },
          {
            name: 'pickupAddress', label: T('Pickup Address'), width: 250, dataTooltip: true, hint: 'Địa Chỉ Lấy Hàng',
            state: { visible: !(isContainerView || !inputDataCreator) },
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                const { fieldConfig, displayRecord, tabIndex, focus, gridContext } = fieldCtx;
                let bill = displayRecord.record;
                const pickupLocationId = bill['pickupLocationId'];
                const mode = bill.mode;
                const cssClass = `${fieldConfig.computeCssClasses && fieldConfig.computeCssClasses(gridContext, displayRecord)}`;
                let value = fieldConfig.fieldDataGetter ? fieldConfig.fieldDataGetter(bill) : bill[fieldConfig.name];
                const title = (
                  <ul>
                    <li>{value}</li>
                    <li><FeatherIcon.MapPin size={12} className='text-success me-1' />{bill['pickupLocationAddress']}</li>
                    <li>{bill['pickupInvAddress']}</li>
                  </ul>
                );
                const html = TMSBillTransportationModeTools.isExport(mode) || TMSBillTransportationModeTools.isDomestic(mode) ?
                  <BBRefTMSPartnerAddress key={`pickup-${bill['pickupLocationId']}-${mode}`} className={`flex-grow-1 ${cssClass}`}
                    minWidth={500} allowUserInput partnerId={bill['customerPartnerId']}
                    autofocus={fieldCtx.focus} tabIndex={tabIndex}
                    appContext={appContext} pageContext={pageContext} placeholder='Address' bean={bill} customerId={bill['customerId']}
                    beanIdField='pickupPartnerAddressId' beanLabelField={fieldConfig.name} types={['Export', 'None']}
                    onPostCommit={(partnerAddress: any) => {
                      uiList._updatePickupAddress(bill, partnerAddress);
                      onInputChange(bill, fieldConfig.name, pickupLocationId, bill['pickupLocationId']);
                    }}
                    onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                      if (_selectOpt && _selectOpt.id) uiList._updatePickupAddress(bean, _selectOpt);
                      onInputChange(bean, fieldConfig.name, pickupLocationId, bean['pickupLocationId']);
                    }} />
                  :
                  <div className='flex-hbox'>
                    {WHIcon()}
                    <BBRefLocation
                      className={`flex-grow-1 ${cssClass}`} autofocus={focus} tabIndex={tabIndex} locationTags={['app:tms']} minWidth={500}
                      appContext={appContext} pageContext={pageContext} bean={bill} disable={!writeCap} refLocationBy='id'
                      beanIdField={'pickupLocationId'} beanLabelField={fieldConfig.name} placeholder='Full Return WH'
                      onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                        bean["pickupLocationAddress"] = _selectOpt['address'];
                        bean["pickupInvAddress"] = `${_selectOpt['shortLabel']}, ${_selectOpt['subdistrictLabel']}, ${_selectOpt['stateLabel']}`;
                        onInputChange(bean, fieldConfig.name, pickupLocationId, bean['pickupLocationId']);
                      }} />
                  </div>
                return UITMSBillUtils.renderTooltip(title, html);
              },
            },
          },
          {
            name: 'stopLocations', label: T('Stop Location'), width: 170, state: { visible: !isContainerView },
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let goodTrackings = dRecord.record;
              let stopLocations: Array<any> = goodTrackings['stopLocations'];
              let addresses: Array<string> = [];
              if (stopLocations) {
                for (let stopLocation of stopLocations) {
                  addresses.push(stopLocation.address);
                }
              }

              return <bs.CssTooltip >
                <bs.CssTooltipToggle style={{ textOverflow: 'ellipsis', overflow: 'hidden' }} className='flex-vbox'>
                  {
                    addresses.map((address: string, index: number) =>
                      <div style={{
                        width: '100%',
                        borderBottom: index !== addresses.length - 1 ? '1px solid #ccc' : 'none',
                        padding: '2px 0'
                      }}>
                        {address}
                      </div>)
                  }
                </bs.CssTooltipToggle>
                <bs.CssTooltipContent
                  className={`d-flex align-items-end text-secondary`}
                  style={{ whiteSpace: 'break-spaces', transform: 'translate(-5px, -75px)' }}>
                  {
                    addresses.map((address) =>
                      <div className='p-2'>
                        {address}
                      </div>
                    )
                  }
                </bs.CssTooltipContent>
              </bs.CssTooltip>
            },
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let goodsTracking = dRecord.record;
                let stopLocations: Array<any> = goodsTracking['stopLocations'];
                let addresses: Array<string> = [];
                if (stopLocations) {
                  for (let stopLocation of stopLocations) {
                    addresses.push(stopLocation.address);
                  }
                }
                let disabled = !goodsTracking['isBillOwner'];
                if (!goodsTracking['tmsBillId']) {
                  disabled = true;
                }
                return (
                  <div className={`flex-hbox align-items-center`}>
                    <bs.CssTooltip style={{ width: fieldCtx.fieldConfig.width! - 22 }}>
                      <bs.CssTooltipToggle style={{ textOverflow: 'ellipsis', overflow: 'hidden' }} className='flex-vbox'>
                        {
                          addresses.map((address: string, index: number) =>
                            <div style={{
                              width: '100%',
                              borderBottom: index !== addresses.length - 1 ? '1px solid #ccc' : 'none',
                              padding: '2px 0'
                            }}>
                              {address}
                            </div>)
                        }
                      </bs.CssTooltipToggle>
                      <bs.CssTooltipContent
                        className={`d-flex align-items-end text-secondary`}
                        style={{ whiteSpace: 'break-spaces', transform: 'translate(-5px, -75px)' }}>
                        {
                          addresses ? addresses.map((address) =>
                            <div className='p-2'>
                              {address}
                            </div>
                          ) : <></>
                        }
                      </bs.CssTooltipContent>
                    </bs.CssTooltip>
                    <bs.Button laf='link' className='p-0 me-1' disabled={disabled} onClick={() => uiList.onShowStopLocations(goodsTracking)}>
                      <FeatherIcon.Edit size={12} className='mx-1 state-modified' />
                    </bs.Button>
                  </div>
                )
              },
            }
          },
          {
            name: 'deliveryAddress', label: T('Delivery Address'), width: 250, dataTooltip: true, hint: 'Địa Chỉ Giao Hàng',
            state: { visible: !(isContainerView || !inputDataCreator) },
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                const { fieldConfig, displayRecord, tabIndex, focus, gridContext } = fieldCtx;
                let bill = displayRecord.record;
                const deliveryLocationId = bill['deliveryLocationId'];
                const mode = bill.mode;
                const cssClass = `${fieldConfig.computeCssClasses && fieldConfig.computeCssClasses(gridContext, displayRecord)}`;
                let value = fieldConfig.fieldDataGetter ? fieldConfig.fieldDataGetter(bill) : bill[fieldConfig.name];
                const title = (
                  <ul>
                    <li>{value}</li>
                    <li><FeatherIcon.MapPin size={12} className='text-success me-1' />{bill['deliveryLocationAddress']}</li>
                    <li>{bill['deliveryInvAddress']}</li>
                  </ul>
                );
                const html = TMSBillTransportationModeTools.isImport(mode) || TMSBillTransportationModeTools.isDomestic(mode) ?
                  <BBRefTMSPartnerAddress key={`delivery-${bill['deliveryLocationId']}-${mode}`}
                    minWidth={500} allowUserInput partnerId={bill['customerPartnerId']}
                    className={`flex-grow-1 ${cssClass}`} autofocus={fieldCtx.focus} tabIndex={tabIndex}
                    appContext={appContext} pageContext={pageContext} placeholder='Address' bean={bill} customerId={bill['customerId']}
                    beanIdField='deliveryPartnerAddressId' beanLabelField={fieldConfig.name} types={['Export', 'None']}
                    onPostCommit={(partnerAddress: any) => {
                      uiList._updateDeliveryAddress(bill, partnerAddress);
                      onInputChange(bill, fieldConfig.name, deliveryLocationId, bill['deliveryLocationId']);
                    }}
                    onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                      if (_selectOpt && _selectOpt.id) uiList._updateDeliveryAddress(bean, _selectOpt);
                      onInputChange(bean, fieldConfig.name, deliveryLocationId, bean['deliveryLocationId']);
                    }} />
                  :
                  <div className='flex-hbox'>
                    {WHIcon()}
                    <BBRefLocation
                      className={`flex-grow-1 ${cssClass}`} autofocus={focus} tabIndex={tabIndex} locationTags={['app:tms']} minWidth={500}
                      appContext={appContext} pageContext={pageContext} bean={bill} disable={!writeCap} refLocationBy='id'
                      beanIdField={'deliveryLocationId'} beanLabelField={fieldConfig.name} placeholder='Full Return WH'
                      onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                        bean["deliveryLocationAddress"] = _selectOpt['address'];
                        bean["deliveryInvAddress"] = `${_selectOpt['shortLabel']}, ${_selectOpt['subdistrictLabel']}, ${_selectOpt['stateLabel']}`;
                        onInputChange(bean, fieldConfig.name, deliveryLocationId, bean['deliveryLocationId']);
                      }} />
                  </div>
                return UITMSBillUtils.renderTooltip(title, html);
              },
            },
          },
          {
            name: 'warehouseLabel', label: T('Full Return WH'), hint: T('Full Return WH'), width: 150,
            state: { visible: !inputDataCreator },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (cell.getRow() === event.row && event.field.name === 'mode') {
                  cell.forceUpdate();
                }
              },
            },
            fieldDataGetter(record) {
              let mode = record.mode;
              if (TMSBillTransportationModeTools.isImport(mode)) {
                return record['senderLocationShortLabel'] ? record['senderLocationShortLabel'] : record['senderAddress'];
              }
              if (TMSBillTransportationModeTools.isExport(mode)) {
                return record['receiverLocationShortLabel'] ? record['receiverLocationShortLabel'] : record['receiverAddress'];
              }
              return null;
            },
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let tracking = dRecord.record;
              let mode = tracking.mode;
              let html;
              if (TMSBillTransportationModeTools.isImport(mode)) {
                if (!tracking['senderLocationShortLabel']) tracking['senderLocationShortLabel'] = tracking['senderAddress'];
                html = <BBRefLocation
                  locationTags={['app:tms']} minWidth={500}
                  appContext={appContext} pageContext={pageContext} bean={tracking} disable refLocationBy='id'
                  beanIdField={'senderLocationId'} beanLabelField={'senderLocationShortLabel'} placeholder='Full Return WH' />
              }
              if (TMSBillTransportationModeTools.isExport(mode)) {
                if (!tracking['receiverLocationShortLabel']) tracking['receiverLocationShortLabel'] = tracking['receiverAddress'];
                html = <BBRefLocation
                  locationTags={['app:tms']} minWidth={500}
                  appContext={appContext} pageContext={pageContext} bean={tracking} disable refLocationBy='id'
                  beanIdField={'receiverLocationId'} beanLabelField={'receiverLocationShortLabel'} placeholder='Full Return WH' />
              }
              return html;
            },
            editor: {
              type: 'string',
              onInputChange: onInputChangeBill,
              renderCustom(ctx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = ctx.displayRecord;
                let tracking = dRecord.record;
                let mode = tracking.mode;
                let isBillOwner = tracking['isBillOwner'];
                let key: string = TMSBillTransportationMode.Domestic;
                if (TMSBillTransportationModeTools.isExport(mode)) key = 'EXPORT';
                if (TMSBillTransportationModeTools.isImport(mode)) key = 'IMPORT';
                let html = <div key={key}></div>;
                if (TMSBillTransportationModeTools.isImport(mode)) {
                  if (!tracking['senderLocationShortLabel']) tracking['senderLocationShortLabel'] = tracking['senderAddress'];
                  const oldVal = tracking['senderLocationShortLabel'];
                  html = <BBRefLocation key={key} tabIndex={ctx.tabIndex} autofocus={ctx.focus}
                    locationTags={['app:tms']} minWidth={500} disable={!isBillOwner}
                    appContext={appContext} pageContext={pageContext} bean={tracking} refLocationBy='id'
                    beanIdField={'senderLocationId'} beanLabelField={'senderLocationShortLabel'} placeholder='Full Return WH'
                    onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                      bean['senderAddress'] = _selectOpt['address'];
                      bean["senderInvAddress"] = `${_selectOpt['shortLabel']}, ${_selectOpt['subdistrictLabel']}, ${_selectOpt['stateLabel']}`;
                      onInputChange(bean, 'senderLocationShortLabel', oldVal, bean['senderLocationShortLabel'])
                    }}
                  />
                }
                if (TMSBillTransportationModeTools.isExport(mode)) {
                  if (!tracking['receiverLocationShortLabel']) tracking['receiverLocationShortLabel'] = tracking['receiverAddress'];
                  const oldVal = tracking['receiverLocationShortLabel'];
                  html = <BBRefLocation key={key} tabIndex={ctx.tabIndex} autofocus={ctx.focus}
                    locationTags={['app:tms']} minWidth={500} disable={!isBillOwner}
                    appContext={appContext} pageContext={pageContext} bean={tracking} refLocationBy='id'
                    beanIdField={'receiverLocationId'} beanLabelField={'receiverLocationShortLabel'} placeholder='Full Return WH'
                    onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                      bean['receiverAddress'] = _selectOpt['address'];
                      bean["receiverInvAddress"] = `${_selectOpt['shortLabel']}, ${_selectOpt['subdistrictLabel']}, ${_selectOpt['stateLabel']}`;
                      onInputChange(bean, 'receiverLocationShortLabel', oldVal, bean['receiverLocationShortLabel'])
                    }}
                  />
                }
                return html;
              },
            }
          },
          {
            name: 'pickupContainerLocation', label: T('Pickup Cont'), hint: T('Điểm Lấy Cont'), width: 150,
            state: { visible: isContainerView },
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(ctx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = ctx.displayRecord;
                let tracking = dRecord.record;
                let fieldConfig = ctx.fieldConfig;
                let mode = tracking.mode;
                const oldVal = tracking[fieldConfig.name];
                let html = <BBRefLocation tabIndex={ctx.tabIndex} autofocus={ctx.focus}
                  locationTags={['app:tms']} minWidth={500} allowUserInput
                  appContext={appContext} pageContext={pageContext} bean={tracking} refLocationBy='id'
                  beanIdField={'pickupContainerLocationId'} beanLabelField={'pickupContainerLocation'} placeholder='Pickup Cont'
                  onPostUpdate={(_inputUI, bean, _selectOpt, _userInput) => {
                    onInputChange(bean, fieldConfig.name, oldVal, bean[fieldConfig.name]);
                    if (TMSBillTransportationModeTools.isImport(mode)) {
                      bean["pickupLocationId"] = bean['pickupContainerLocationId'];
                      bean["pickupAddress"] = bean['pickupContainerLocation'];
                      bean["pickupInvAddress"] = `${_selectOpt['shortLabel']}, ${_selectOpt['subdistrictLabel']}, ${_selectOpt['stateLabel']}`;
                      tracking['updateTMSBill'] = true;
                    }
                  }} />
                return html;
              },
            }
          },
          {
            name: 'returnContainerLocation', label: T('Return Cont'), hint: T('Điểm Trả Cont'), width: 150,
            state: { visible: isContainerView },
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(ctx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = ctx.displayRecord;
                let fieldConfig = ctx.fieldConfig;
                let tracking = dRecord.record;
                let mode = tracking.mode;
                const oldVal = tracking[fieldConfig.name];
                let html = <BBRefLocation tabIndex={ctx.tabIndex} autofocus={ctx.focus}
                  locationTags={['app:tms']} minWidth={500} allowUserInput
                  appContext={appContext} pageContext={pageContext} bean={tracking} refLocationBy='id'
                  beanIdField={'returnContainerLocationId'} beanLabelField={'returnContainerLocation'} placeholder='Return Cont'
                  onPostUpdate={(_inputUI, bean, _selectOpt, _userInput) => {
                    if (TMSBillTransportationModeTools.isExport(mode)) {
                      bean["returnLocationId"] = bean['returnContainerLocationId'];
                      bean["returnAddress"] = bean['returnContainerLocation'];
                      bean["returnInvAddress"] = `${_selectOpt['shortLabel']}, ${_selectOpt['subdistrictLabel']}, ${_selectOpt['stateLabel']}`;
                      tracking['updateTMSBill'] = true;
                    }
                    onInputChange(bean, fieldConfig.name, oldVal, bean[fieldConfig.name]);
                  }} />
                return html;
              },
            }
          },
          {
            name: 'routeType', label: T('Route Type'), hint: T('Loại Đường(Ngắn/Dài)'), width: 120, state: { visible: isContainerView },
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(ctx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let { fieldConfig } = ctx;
                let dRecord = ctx.displayRecord;
                return (
                  <input.BBRadioInputField
                    bean={dRecord.record} field={fieldConfig.name} options={['short', 'long']}
                    optionLabels={[T('Ngắn'), T('Dài')]} onInputChange={onInputChange}
                  />
                );
              },
            }
          },
          {
            name: 'mode', label: T('Mode'), hint: 'Loại hàng', width: 80,
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (cell.getRow() === event.row && event.field.name === 'mode') {
                  cell.forceUpdate();
                }
              },
            },
            computeCssClasses: (ctx, dRecord) => {
              return TMSBillTransportationModeTools.getColor(dRecord.record.mode) + this.getBgColor(dRecord.record);
            },
            fieldDataGetter(record) {
              return TMSBillTransportationModeTools.getLabel(record.mode);
            },
            editor: {
              type: 'string',
              onInputChange: onInputChangeBill,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tripGoods = dRecord.record;
                let isBillOwner = tripGoods['isBillOwner'];
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiList.getVGridContext(), dRecord)}`
                if (!isBillOwner) {
                  return (
                    <div tabIndex={tabIndex} className={cssClass}>
                      {TMSBillTransportationModeTools.getLabel(tripGoods[field.name])}
                    </div>
                  )
                }
                let onChange = (bean: any, field: string, oldVal: any, newVal: any) => {
                  tripGoods['groupMode'] = TMSBillTransportationModeTools.group(newVal);
                  if (TMSBillTransportationModeTools.isDomestic(newVal)) {
                    onInputChange(bean, field, oldVal, newVal);
                    return;
                  }

                  let oldValIsExport = TMSBillTransportationModeTools.isExport(oldVal);
                  let newValIsExport = TMSBillTransportationModeTools.isExport(newVal);
                  let oldValIsImport = TMSBillTransportationModeTools.isImport(oldVal);
                  let newValIsImport = TMSBillTransportationModeTools.isImport(newVal);
                  let isDomestic = TMSBillTransportationModeTools.isDomestic(newVal);
                  if (!(oldValIsExport == newValIsExport || oldValIsImport === newValIsImport || isDomestic)) {
                    const receiverContact = tripGoods['receiverContact'];
                    const receiverAddress = tripGoods['receiverAddress'];
                    const receiverLocationId = tripGoods['receiverLocationId'];
                    const receiverLocationAddress = tripGoods['receiverLocationAddress'];
                    const receiverLocationShortLabel = tripGoods['receiverLocationShortLabel'];
                    const senderContact = tripGoods['senderContact'];
                    const senderAddress = tripGoods['senderAddress'];
                    const senderLocationId = tripGoods['senderLocationId'];
                    const senderLocationAddress = tripGoods['senderLocationAddress'];
                    const senderLocationShortLabel = tripGoods['senderLocationShortLabel'];

                    tripGoods['receiverContact'] = senderContact;
                    tripGoods['receiverAddress'] = senderAddress;
                    tripGoods['receiverLocationId'] = senderLocationId;
                    tripGoods['receiverLocationAddress'] = senderLocationAddress;
                    tripGoods['receiverLocationShortLabel'] = senderLocationShortLabel;
                    tripGoods['senderContact'] = receiverContact;
                    tripGoods['senderAddress'] = receiverAddress;
                    tripGoods['senderLocationId'] = receiverLocationId;
                    tripGoods['senderLocationAddress'] = receiverLocationAddress;
                    tripGoods['senderLocationShortLabel'] = receiverLocationShortLabel;
                  }
                  if (onInputChange) onInputChange(bean, field, oldVal, newVal);
                }
                return (
                  <input.BBSelectField bean={tripGoods} field={field.name} tabIndex={tabIndex} focus={focus} className={cssClass}
                    options={[
                      TMSBillTransportationMode.ExportFcl, TMSBillTransportationMode.ExportLcl, TMSBillTransportationMode.ExportAir,
                      TMSBillTransportationMode.ImportFcl, TMSBillTransportationMode.ImportLcl, TMSBillTransportationMode.ImportAir,
                      TMSBillTransportationMode.DomesticFcl, TMSBillTransportationMode.DomesticLcl
                    ]}
                    optionLabels={[
                      'Export Fcl', 'Export Lcl', 'Export Air',
                      'Import Fcl', 'Import Lcl', 'Import Air',
                      'DomesticFcl', 'DomesticLcl'
                    ]} onInputChange={onChange} />
                )
              },
            }
          },

          //
          {
            name: 'containerNo', label: T('Container No'), state: { visible: isContainerView },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (cell.getRow() === event.row && event.field.name === 'containerNo') {
                  cell.forceUpdate();
                }
              },
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let containerNo = record[field.name];
              let width = field.width ? field.width : 120;
              return (
                <div className={`flex-hbox`}>
                  <bs.Tooltip className={`flex-grow-1 text-truncate`} style={{ width: width - 25 }} tooltip={containerNo}>
                    {containerNo}
                  </bs.Tooltip>
                  {UITMSBillUtils.containerValidate(containerNo)}
                </div>
              )
            },
            editor: {
              type: 'string', onInputChange: onInputChangeBill,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tracking = dRecord.record;
                let containerNo = tracking[field.name];
                return (
                  <div className={`flex-hbox`}>
                    <input.BBStringField
                      bean={tracking} field={field.name} tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
                    {UITMSBillUtils.containerValidate(containerNo)}
                  </div>
                )
              },
            }
          },
          {
            name: 'sealNo', label: T('Seal No'), state: { visible: isContainerView },
            editor: {
              type: 'string',
              onInputChange: onInputChangeBill,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tracking = dRecord.record;
                return (
                  <input.BBStringField
                    bean={tracking} field={field.name} tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
                )
              },
            }
          },
          {
            name: 'bookingCode', label: T('Booking Code'), state: { visible: isContainerView },
            editor: {
              type: 'string',
              onInputChange: onInputChangeBill,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tracking = dRecord.record;
                return (
                  <input.BBStringField
                    bean={tracking} field={field.name} tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
                )
              },
            }
          },
          {
            name: 'carrierFullName', label: T('Carrier'), state: { visible: isContainerView },
            editor: {
              type: 'string',
              onInputChange: onInputChangeBill,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let bill = dRecord.record;
                let isBillOwner = bill.isBillOwner;
                const oldVal = bill[field.name];
                return (
                  <BBRefTMSCarrier minWidth={400} disable={!isBillOwner} allowUserInput
                    appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                    bean={bill} beanIdField={'carrierId'} beanLabelField={'carrierFullName'} placeholder={'Carrier'}
                    onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                      onInputChange(bean, field.name, oldVal, bean[field.name])
                    }}
                  />
                )
              },
            }
          },
          //Goods
          {
            name: 'quantity', label: T('Quantity'), width: 80, dataType: 'double', hint: 'Số kiện', state: { visible: false },
            editor: { type: 'double', onInputChange: onInputChange }
          },
          {
            name: 'quantityUnit', label: T('Q.Unit'), hint: "Đơn Vị", width: 120, state: { visible: false },
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let forwarder = dRecord.record;
                const oldVal = forwarder[field.name];
                return (
                  <module.settings.BBRefUnit
                    appContext={appContext} pageContext={pageContext}
                    minWidth={400} placement="left" placeholder="Enter Unit"
                    groupNames={['packing']}
                    bean={forwarder} beanIdField={field.name}
                    tabIndex={tabIndex} autofocus={focus}
                    onPostUpdate={(inputUI, bean, selectOpt, userInput) => onInputChange(bean, field.name, oldVal, bean[field.name])} />
                )
              },
            }
          },
          {
            name: 'weight', label: T('KG'), width: 80, hint: 'Khối Lượng', state: { visible: false },
            editor: { type: 'double', onInputChange: onInputChange }
          },
          {
            name: 'chargeableWeight', label: T('Chargeable Weight'), width: 80, state: { visible: false },
            editor: { type: 'double', onInputChange: onInputChange }
          },
          {
            name: 'weightUnit', label: T('W.Unit'), hint: "Weight Unit", state: { visible: false },
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let forwarder = dRecord.record;
                const oldVal = forwarder[field.name];
                return (
                  <module.settings.BBRefUnit
                    appContext={appContext} pageContext={pageContext}
                    minWidth={300}
                    placement="left"
                    placeholder="Enter Unit"
                    groupNames={['weight']}
                    bean={forwarder} beanIdField={field.name}
                    tabIndex={tabIndex} autofocus={focus}
                    onPostUpdate={(inputUI, bean, selectOpt, userInput) => onInputChange(bean, field.name, oldVal, bean[field.name])} />
                )
              },
            }
          },
          {
            name: 'volumeAsText', label: T('CBM'), cssClass: 'flex-grow-1 text-end', hint: 'Thể Tích', state: { visible: false },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && (event.field.name === 'volumeAsText')) {
                  let bill = cell.getDisplayRecord().record;
                  let volumeAsText = bill['volumeAsText'];
                  if (TMSUtils.isDecimal(volumeAsText)) {
                    bill['volume'] = Number(volumeAsText);
                  }
                }
              },
            },
            editor: {
              type: 'string', onInputChange: onInputChange,
            }
          },
          {
            name: 'volume', label: T('Volume'), width: 80, state: { visible: false },
            editor: { type: 'double', onInputChange: onInputChange }
          },
          {
            name: 'chargeableVolume', label: T('Chargeable Volume'), width: 80, state: { visible: false },
            editor: { type: 'double', onInputChange: onInputChange }
          },
          {
            name: 'volumeUnit', label: T('V.Unit'), hint: "Volume Unit", state: { visible: false },
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let forwarder = dRecord.record;
                const oldVal = forwarder[field.name];
                return (
                  <module.settings.BBRefUnit
                    appContext={appContext} pageContext={pageContext}
                    minWidth={300}
                    placement="left"
                    placeholder="Enter Unit"

                    groupNames={['volume']}
                    bean={forwarder} beanIdField={field.name}
                    tabIndex={tabIndex} autofocus={focus}
                    onPostUpdate={(inputUI, bean, selectOpt, userInput) => onInputChange(bean, field.name, oldVal, bean[field.name])} />
                )
              },
            }
          },
          //
          {
            name: 'tmsBillQuantity', label: T('Quantity'), width: 80, hint: 'Số kiện',
            editor: {
              type: 'double',
              onInputChange: onInputChangeBill,
              enable: false,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tracking = dRecord.record;
                let allowEditBill = tracking['isBillOwner'] || pageContext.hasUserModeratorCapability();
                if (!allowEditBill) {
                  return (
                    <div className={`cell-number w-100 px-2`} tabIndex={tabIndex}>
                      {tracking[field.name]}
                    </div>
                  )
                }
                return (
                  <input.BBDoubleField className='px-2'
                    bean={tracking} field={field.name} tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
                )
              },
            }
          },
          {
            name: 'tmsBillQuantityUnit', label: T('Q.Unit'), hint: "Đơn Vị", width: 80,
            editor: {
              type: 'string',
              onInputChange: onInputChangeBill,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tracking = dRecord.record;
                const oldVal = tracking[field.name];
                let allowEditBill = tracking['isBillOwner'] || pageContext.hasUserModeratorCapability();
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiList.getVGridContext(), dRecord)}`
                if (!allowEditBill) {
                  return (
                    <div className={`flex-hbox ${cssClass}`} tabIndex={tabIndex}>
                      {tracking[field.name]}
                    </div>
                  )
                }
                return (
                  <module.settings.BBRefUnit
                    appContext={appContext} pageContext={pageContext}
                    minWidth={400}
                    placement="left"
                    placeholder="Enter Unit"
                    groupNames={['packing']}
                    bean={tracking} beanIdField={field.name}
                    tabIndex={tabIndex} autofocus={focus}
                    onPostUpdate={(_inputUI, bean, _selectOpt, _userInput) =>
                      onInputChange(bean, field.name, oldVal, bean[field.name])} disable={!allowEditBill} />
                )

              },
              enable: false
            }
          },
          {
            name: 'tmsBillWeight', label: T('KG'), width: 80, hint: 'Khối Lượng',
            editor: {
              type: 'double',
              onInputChange: onInputChangeBill,
              enable: false,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tracking = dRecord.record;
                let isBillOwner = tracking['isBillOwner'] || pageContext.hasUserModeratorCapability();
                if (!isBillOwner) {
                  return (
                    <div className={`cell-number w-100 px-2`} tabIndex={tabIndex}>
                      {tracking[field.name]}
                    </div>
                  )
                }
                return (
                  <input.BBDoubleField className='px-2'
                    bean={tracking} field={field.name} tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
                )
              },

            }
          },
          {
            name: 'tmsBillVolumeAsText', label: T('CBM'), width: 130, cssClass: 'flex-grow-1 text-end', hint: 'Thể Tích',
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && (event.field.name === 'tmsBillVolumeAsText')) {
                  let bill = cell.getDisplayRecord().record;
                  let volumeAsText = bill['tmsBillVolumeAsText'];
                  if (TMSUtils.isDecimal(volumeAsText)) {
                    bill['tmsBillVolume'] = Number(volumeAsText);
                  }
                }
              },
            },
            editor: {
              type: 'string',
              enable: false,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tracking = dRecord.record;
                let isBillOwner = tracking['isBillOwner'] || pageContext.hasUserModeratorCapability();
                if (!isBillOwner) {
                  return (
                    <div className={`cell-number w-100 px-2`} tabIndex={tabIndex}>
                      {tracking[field.name]}
                    </div>
                  )
                }
                return (
                  <input.BBStringField className='text-end px-2'
                    bean={tracking} field={field.name} tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
                )
              },
              onInputChange: onInputChangeBill,
            }
          },
          {
            name: 'goodsDescription', label: T('Commodity'), hint: T('Thông tin hàng hóa'),
            editor: {
              type: 'string',
              onInputChange: onInputChangeBill,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tracking = dRecord.record;
                let isBillOwner = tracking['isBillOwner'];
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiList.getVGridContext(), dRecord)}`
                if (!isBillOwner) {
                  return (
                    <div className={`flex-hbox ${cssClass}`} tabIndex={tabIndex}>
                      {tracking[field.name]}
                    </div>
                  )
                }
                return (
                  <input.BBStringField
                    bean={tracking} field={field.name} tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
                )
              },
            }
          },
          {
            name: 'etaCutOffTime', label: T('ETA/Cut Off'), hint: T('ETA/Cut Off'),
            editor: {
              type: 'string',
              onInputChange: onInputChangeBill,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tracking = dRecord.record;
                let isBillOwner = tracking['isBillOwner'];
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiList.getVGridContext(), dRecord)}`
                if (!isBillOwner) {
                  return (
                    <div className={`flex-hbox ${cssClass}`} tabIndex={tabIndex}>
                      {tracking[field.name]}
                    </div>
                  )
                }
                return (
                  <input.BBStringField
                    bean={tracking} field={field.name} tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
                )
              },
            }
          },
          {
            name: 'tmsBillDescription', label: T('TMS Bill Note'), width: 200, dataTooltip: true, hint: 'Ghi Chú Cus',
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let tracking = dRecord.record;
              return TMSUtils.renderTMSGridTooltip(tracking[_field.name], tracking[_field.name], _field.width, dRecord.row);
            },
            editor:
            {
              type: 'text',
              onInputChange: onInputChangeBill,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tracking = dRecord.record;
                let isBillOwner = tracking['isBillOwner'];
                let currHeight = dataCellHeight;;
                if (!isBillOwner && field.customRender) {
                  return (
                    <div className='flex-hbox' tabIndex={tabIndex}>
                      {field.customRender(uiList.getVGridContext(), field, dRecord, focus)}
                    </div>
                  )
                }
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiList.getVGridContext(), dRecord)}`
                return (
                  <div className='flex-hbox'>
                    <input.BBTextField tabIndex={tabIndex} focus={focus} placeholder='Note'
                      style={{ height: currHeight }}
                      className={cssClass}
                      bean={tracking} field={field.name} onInputChange={onInputChange} />
                  </div>
                );
              },
            },
          },
          {
            name: 'description', label: T('Tracking Note'), width: 200, dataTooltip: true,
            hint: 'Ghi Chú Điều Vận', cssClass: 'text-warning',
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let tracking = dRecord.record;
              let currHeight = dataCellHeight;
              return (
                <div className='flex-hbox' style={{ overflow: 'auto' }}>
                  <bs.Tooltip style={{ whiteSpace: 'pre-line', height: currHeight }}
                    tooltip={tracking[_field.name]}>
                    {tracking[_field.name]}
                  </bs.Tooltip>
                </div>
              )
            },
            editor:
            {
              type: 'text',
              onInputChange: onInputChange,
            },
          },
          {
            name: 'invoiceNote', label: T('Invoice Note'), width: 350,
            editor: {
              type: 'text',
              onInputChange: onInputChange,
            }
          },
          {
            name: 'att', label: T('ATT'), width: 40, state: { visible: false },
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let renderBillAttachments = (tracking: any) => {
                let vehicleTripId = tracking['vehicleTripId'];
                const onShow = () => {
                  if (!vehicleTripId) {
                    bs.notificationShow('warning', 'You need create vehicle trip!!!');
                    return;
                  }
                  const createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
                    return (
                      <module.storage.UIAttachments readOnly={!writeCap}
                        appContext={appContext} pageContext={pageContext}
                        commitURL={VehicleFleetURL.vehicleTrip.saveAttachments(tracking['vehicleTripId'])}
                        loadURL={VehicleFleetURL.vehicleTrip.loadAttachments(tracking['vehicleTripId'])}
                        onChange={(plugin) => {
                          if (tracking['vehicleTripAttachmentCount'] != plugin.getModel().getRecords().length) {
                            tracking['vehicleTripAttachmentCount'] = plugin.getModel().getRecords().length;
                            this.getVGridContext().getVGrid().forceUpdateView();
                          }
                        }} />
                    )
                  }
                  pageContext.createPopupPage('attachments', T("Attachments"), createAppPage, { size: "xl", backdrop: "static" });
                }
                return TMSUtils.renderFileAttachmentsIcon(tracking['vehicleTripAttachmentCount'], onShow);
              }
              return (
                <div className={'flex-hbox'}>
                  {renderBillAttachments(dRecord.record)}
                </div>
              )
            }
          },
          // {
          //   name: 'pickupLocation', label: T('Pickup'), cssClass: 'flex-grow-1 text-center', hint: 'Điểm Lấy Hàng',
          //   width: 60,
          //   editor: {
          //     type: 'string',
          //     onInputChange: onInputChange
          //   }
          // },
          // {
          //   name: 'deliveryLocation', label: T('Delivery'), cssClass: 'flex-grow-1 text-center', hint: 'Điểm Trả Hàng',
          //   width: 65,
          //   editor: {
          //     type: 'string',
          //     onInputChange: onInputChange
          //   }
          // },
          {
            name: 'transportType', label: T('Transport Type'), width: 130,
            editor: {
              type: 'string', onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tripGoods = dRecord.record;
                return (
                  <input.BBSelectField tabIndex={tabIndex} focus={focus}
                    bean={tripGoods} field={field.name} options={['', 'Nguyên chuyến', 'Kết hợp', 'Ghép', 'CK', 'RU']} onInputChange={onInputChange} />
                )
              },
            }
          },
          //Customer/Sender/Receiver
          { name: 'tmsBillType', label: T('Bill Type'), state: { visible: false } },

          //VehicleTrip
          {
            name: 'estimateDistanceInKm', label: T('Km'), dataType: 'double', hint: T('Km'), width: 50,
            editor: {
              type: 'double',
              onInputChange: onInputChange
            }
          },
          {
            name: 'twoWay', label: T('2 Chiều'), container: screen ? 'fixed-right' : 'default', cssClass: 'flex-grow-1 text-center', width: 65,
            editor: {
              type: 'boolean',
              onInputChange: onInputChangeVehicleTrip
            }
          },
          {
            name: 'vehicleType', label: T('Vehicle Type'), hint: 'Loại Xe', width: 90, container: 'fixed-right',
            filterable: true,
            filterableType: 'options',
            fieldDataGetter(record) {
              return record['vehicleType'];
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && (event.field.name === 'vehicleType')) {
                  cell.forceUpdate()
                }
              },
            },
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tracking = dRecord.record;
                const oldVal = tracking[field.name];
                let fileName = field.name;
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiList.getVGridContext(), dRecord)}`;
                return (
                  <module.settings.BBRefUnit
                    className={cssClass}
                    appContext={appContext} pageContext={pageContext}
                    minWidth={300}
                    placement="left"
                    placeholder="Enter Unit"
                    groupNames={['truck']}
                    bean={tracking} beanIdField={fileName}
                    tabIndex={tabIndex} autofocus={focus}
                    onPostUpdate={(inputUI, bean, selectOpt, userInput) => {
                      bean[field.name] = bean[fileName];
                      onInputChange(bean, field.name, oldVal, bean[field.name])
                    }} />
                )
              }
            }
          },
          {
            name: 'fleetLabel', label: T('Fleet'), hint: 'Đội Xe',
            filterable: true, filterableType: 'Options',
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && event.field.name === 'vehicleLabel') {
                  cell.forceUpdate();
                }
              },
            },
            editor: {
              type: 'string',
              onInputChange: onInputChangeVehicleTrip,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tracking = dRecord.record;
                let isLocked = tracking['editMode'] === entity.EditMode.LOCKED;
                const oldVal = tracking[field.name];
                return (
                  <BBRefVehicleFleet key={`${tracking['fleetId']}-ref-vehicle-fleet`}
                    tabIndex={tabIndex} autofocus={focus} placeholder='Fleet' minWidth={400}
                    appContext={appContext} pageContext={pageContext} disable={!writeCap || isLocked}
                    bean={tracking} beanLabelField='fleetLabel' beanIdField='fleetId'
                    onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) =>
                      onInputChange(bean, field.name, oldVal, bean[field.name])
                    } />
                )
              }
            }
          },
          ...trackingPlugin.buildVehicleTripCols(),
          {
            name: 'vehicleLabel', label: T('License plate'), hint: 'Biển Số', filterableType: 'Options', filterable: true, width: 150,
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && event.field.name === 'fleetLabel') {
                  cell.forceUpdate();
                }
              },
            },
            editor: {
              type: 'string',
              onInputChange: onInputChangeVehicleTrip,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tracking = dRecord.record;
                const oldVal = tracking[field.name];
                let isLocked = tracking['editMode'] === entity.EditMode.LOCKED;
                return (
                  <BBRefVehicle disable={!writeCap || isLocked} appContext={appContext} pageContext={pageContext} allowUserInput placeholder='License plate'
                    placement="bottom-start" offset={[0, 5]} minWidth={500} autofocus={focus} tabIndex={tabIndex}
                    bean={tracking} beanIdField={'vehicleId'} beanLabelField={'vehicleLabel'}
                    loadParams={(searchParams: sql.SqlSearchParams) => {
                      searchParams.params = {
                        ...searchParams.params,
                        'vehicleFleetId': tracking['fleetId']
                      };
                    }}
                    onPostUpdate={(_inputUI, _bean, selectOpt, _userInput) => {
                      if (selectOpt) {
                        if (selectOpt['vehicleFleetId']) {
                          tracking['fleetId'] = selectOpt['vehicleFleetId'];
                          tracking['fleetLabel'] = selectOpt['fleetName'];
                        }
                        tracking['trailerNumber'] = selectOpt['trailerNumber'];
                        tracking['oilConsumption'] = selectOpt['oilConsumption'];
                        tracking['identificationNo'] = selectOpt['idCard'];
                        tracking['mobile'] = selectOpt['mobile'];
                        tracking['driverFullName'] = selectOpt['transporterFullName'];
                        tracking['driverId'] = selectOpt['transporterId'];
                      }
                      onInputChange(tracking, field.name, oldVal, tracking[field.name])
                    }}
                  />
                )
              }
            }
          },
          {
            name: 'trailerNumber', label: T('Rơ Moóc'), hint: 'Rơ Moóc', state: { visible: isContainerView },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && event.field.name === 'vehicleLabel') {
                  cell.forceUpdate()
                }
              },
            },
            editor: {
              type: 'string',
              onInputChange: onInputChangeVehicleTrip,
            },
          },
          {
            name: 'driverFullName', label: T('Driver'), hint: 'Lái Xe', width: 175,
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && event.field.name === 'vehicleLabel') {
                  cell.forceUpdate()
                }
              },
            },
            editor: {
              type: 'string',
              onInputChange: onInputChangeVehicleTrip,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tracking = dRecord.record;
                const oldDriver = tracking[field.name];
                const oldMobile = tracking['mobile'];
                const oldId = tracking['identificationNo'];
                const isLocked = tracking['editMode'] === entity.EditMode.LOCKED;;
                return (
                  <BBRefTransporter key={`driver-${tracking['driverId']}`} allowUserInput disable={!writeCap || isLocked}
                    vehicleFleetId={tracking['fleetId']}
                    appContext={appContext} pageContext={pageContext}
                    minWidth={300}
                    placement="left"
                    placeholder="Enter Transporter"
                    bean={tracking} beanIdField={'driverId'}
                    tabIndex={tabIndex} autofocus={focus}
                    beanLabelField={field.name} refTransporterBy={'id'} onPostUpdate={(inputUI, bean, selectOp, userInput) => {
                      if (selectOp) {
                        tracking['mobile'] = selectOp['mobile'];
                        tracking['identificationNo'] = selectOp['idCard'];
                        onChangeVehicleInfo(dRecord, 'mobile', oldMobile, tracking['mobile']);
                        onChangeVehicleInfo(dRecord, 'identificationNo', oldId, tracking['identificationNo']);
                      }
                      onInputChange(bean, field.name, oldDriver, bean[field.name])
                    }} />
                )
              }
            }
          },
          {
            name: 'identificationNo', label: T('Driver ID'), hint: 'Driver ID',
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && (event.field.name === 'driverFullName' || event.field.name === 'vehicleLabel')) {
                  cell.forceUpdate()
                }
              },
            },
            editor: {
              type: 'string',
              onInputChange: onInputChangeVehicleTrip,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tracking = dRecord.record;
                const isLocked = tracking['editMode'] === entity.EditMode.LOCKED;;
                return (
                  <input.BBStringField disable={!writeCap || isLocked}
                    bean={tracking} field={field.name} tabIndex={tabIndex} focus={focus}
                    onInputChange={onInputChange} />
                );
              },
            }
          },
          {
            name: 'mobile', label: T('Mobile'), hint: 'Số Điện Thoại Lái Xe',
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && (event.field.name === 'driverFullName' || event.field.name === 'vehicleLabel')) {
                  cell.forceUpdate()
                }
              },
            },
            editor: {
              type: 'string',
              onInputChange: onInputChangeVehicleTrip,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tracking = dRecord.record;
                const isLocked = tracking['editMode'] === entity.EditMode.LOCKED;;
                return (
                  <input.BBStringField disable={!writeCap || isLocked}
                    bean={tracking} field={field.name} tabIndex={tabIndex} focus={focus}
                    onInputChange={onInputChange} />
                );
              },
            }
          },
          {
            name: 'editMode', label: '', width: 40, state: { visible: false },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
              let tracking = dRecord.record;
              return (
                <div className='flex-hbox justify-content-center'>
                  {this.buildTripEditMode(tracking)}
                </div>
              )
            },
          },
          ...this.buildFuelColConfig(onInputChange),
          ...this.buildRevenueColConfig(onInputChangeFees),
          ...this.buildExpenseColConfig(onInputChangeFees),
          {
            name: 'profit', label: T('Profit'), format: formatCurrency, state: { visible: screen === 'profitReportView' ? true : false }, container: 'fixed-right',
          },
          {
            name: 'cssClass', label: T('Mark'), width: 60,
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                const bgColors =
                  ['', 'bg-info', 'bg-primary', 'bg-secondary', 'bg-success', 'bg-danger', 'bg-warning'];
                let dRecord = fieldCtx.displayRecord;
                let tracking = dRecord.record;
                let color = tracking['cssClass'];
                let btns: any[] = [];
                bgColors.forEach(c => {
                  btns.push(
                    <bs.Button key={c} laf='link' className={`m-1 p-3 ${c} border border-300`}
                      onClick={() => {
                        const oldVal = tracking['cssClass'];
                        tracking['cssClass'] = c;
                        onInputChange(tracking, 'cssClass', oldVal, tracking['cssClass']);
                        fieldCtx.gridContext.getVGrid().forceUpdateView();
                      }}>
                    </bs.Button>
                  )
                });
                return (
                  <bs.Popover className="d-flex flex-center w-100" title={T('Mark')} >
                    <bs.PopoverToggle className={color ? color : 'bg-white'}>
                    </bs.PopoverToggle>
                    <bs.PopoverContent>
                      <div className='flex-vbox gap-2' style={{ width: '200px' }}>
                        {btns}
                      </div>
                    </bs.PopoverContent>
                  </bs.Popover>

                );
              },
            }
          },
          {
            name: 'taskStatus', label: T('Trip Info'), container: 'fixed-right', width: 150, hint: 'Status',
            state: { visible: screen ? false : true },
            filterableType: 'Options', filterable: true,
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && (event.field.name === 'vehicleLabel')) {
                  cell.forceUpdate();
                }
              },
            },
            computeCssClasses: (_ctx, dRecord) => {
              let tracking = dRecord.record;
              return this.getColorByTrip(tracking) + this.getBgColor(tracking);
            },
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let tracking = dRecord.record;
              let processStatus = tracking['status'];
              let thisUI = ctx.uiRoot as UIVehicleTripGoodsTrackingList;
              if (!tracking.id) return <div className='flex-vbox text-center'>-</div>;
              if (processStatus == VehicleTripGoodsTrackingStatus.REJECT) {
                let onConfirm = () => {
                  bs.dialogConfirmMessage('Confirm File', T(`You want to confirm file no. ${tracking['billLabel']}`),
                    () => this.confirmGoodsTracking(tracking, VehicleTripGoodsTrackingStatus.CONFIRMED));
                }
                return (
                  <bs.Button laf='link' className='w-100' onClick={() => onConfirm()}>
                    <bs.Badge className='w-100' laf={'danger'}>
                      <bs.BadgeLabel>
                        {processStatus}
                      </bs.BadgeLabel>
                    </bs.Badge>
                  </bs.Button>
                )
              }
              if (processStatus == VehicleTripGoodsTrackingStatus.NEED_CONFIRM) {
                return (
                  <div className={'flex-hbox flex-grow-0'} >
                    <bs.Button disabled={!writeCap} laf='success' className='my-5 p-1' style={{ width: 63, fontSize: 12 }} outline
                      onClick={() => this.confirmGoodsTracking(tracking, VehicleTripGoodsTrackingStatus.CONFIRMED)} >
                      {T('Confirm')} <FeatherIcon.Check size={10} />
                    </bs.Button>
                    <bs.Button disabled={!writeCap} laf='danger' className='mx-1 my-5 p-1' style={{ width: 63, fontSize: 12 }} outline
                      onClick={() => this.confirmGoodsTracking(tracking, VehicleTripGoodsTrackingStatus.REJECT)} >
                      {T('Reject')} <FeatherIcon.X size={10} />
                    </bs.Button>
                  </div>
                )
              }
              let cssClass = field.cssClass;
              if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiList.getVGridContext(), dRecord)}`
              let label = tracking['vehicleLabel'] ? tracking['vehicleLabel'] : '.........';
              let isNewTrip = false;
              if (tracking.vehicleTripId == null) {
                label = 'Create Trip';
                isNewTrip = true;
              }
              let gps = tracking.gps;
              return (
                <div className='w-100'>
                  <div className={'flex-hbox align-items-center'}>
                    {tracking.vehicleTripId ?
                      <bs.Button disabled={!writeCap} key={'remove-round'} laf='link' onClick={() => thisUI.onRemoveVehicleTrip(tracking)} >
                        <FeatherIcon.Trash2 className={'text-danger'} size={12} />
                      </bs.Button>
                      : null
                    }
                    {tracking.vehicleLabel ?
                      <bs.Button className='m-0 p-0' laf='link'
                        onClick={() => { throw new Error(`Implement Map`) }}>
                        <FeatherIcon.MapPin className={`flex-grow-0 ${gps ? 'text-success' : 'text-secondary'} `} size={12} />
                      </bs.Button>
                      : null
                    }
                    <WBtnMail context={this.getVGridContext()} bill={tracking} />
                    <bs.Button key={'round'} laf='link' className='m-0 p-0'
                      onClick={() => thisUI.showVehicleTrip(tracking, isNewTrip)}>
                      <div className={`flex-hbox align-items-center justify-content-center`}>
                        <module.account.WAvatars
                          appContext={appContext} pageContext={pageContext} avatarIds={[tracking['fleetOwnerAccountId']]}
                          avatarIdType='AccountId' width={15} height={15} borderRadius={10} />
                        {isNewTrip ? <FeatherIcon.Plus className='mx-1' size={12} /> : null}
                        {label}
                      </div>
                    </bs.Button>
                  </div>
                  {tracking.vehicleTripId ?
                    <div className='flex-hbox'>
                      {thisUI.buildTripTransportStatusIcon(tracking)}
                      <bs.Badge className='m-0' style={{ padding: 1 }} laf={thisUI.getStatusColor(tracking)}>
                        <input.BBSelectField className='px-1 py-0' style={{ width: field.width, height: 16, fontSize: 14 }} bean={tracking} field={'taskStatus'}
                          options={['PLAN', 'SUBMITTED_PLAN', 'TRANSPORTING', 'DONE']}
                          optionLabels={['Kế Hoạch', 'Đã Gửi Kế Hoạch', 'Đang Vận Chuyển', 'Hoàn Thành']}
                          onInputChange={
                            (_bean: any, field: any, newVal: any, oldVal: any) => {
                              dRecord.getRecordState().markModified();
                              let fieldCtx: grid.FieldContext = {
                                fieldConfig: field,
                                gridContext: ctx,
                                displayRecord: dRecord,
                                focus: false,
                                tabIndex: 0
                              }
                              onInputChangeVehicleTrip(fieldCtx, newVal, oldVal)
                            }
                          } />
                      </bs.Badge>
                    </div>
                    : null
                  }
                </div>
              );
            },
          },

          ...TMSVGridConfigTool.ENTITY_COLUMNS
        ],
        summary: {
          dataCellHeight: 260,
          computeDataRowHeight(ctx, dRecord) {
            if (dRecord.isDataRecord()) return 260;
            return 30;
          },
          render: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord, _viewMode: grid.ViewMode) => {
            return <VehicleTripGoodsTrackingListSummaryView context={ctx} dRecord={dRecord} />;
          },
        },
        fieldGroups: {
          'plan': {
            label: T('Plan'),
            visible: true,
            fields: [
              'time', 'deliveryPlan', 'bookingDate'
            ]
          },
          'Customer Info': {
            label: T('Customer Info'),
            visible: true,
            fields: [
              'customerFullName'
            ]
          },
          'Pickup/Delivery': {
            label: T('Pickup/Delivery'),
            visible: true,
            fields: [
              'senderContact', 'senderAddress', 'receiverContact', 'receiverAddress', 'warehouseLabel',
              'pickupContainerLocation', 'returnContainerLocation', 'senderReceiverAddress', 'senderReceiverContact',
              'pickupAddress', 'deliveryAddress', 'stopLocations', 'routeType'
            ]
          },
          'TMS Bill Info': {
            label: T('Bill Info'),
            visible: true,
            fields: [
              'tmsBillQuantity', 'tmsBillQuantityUnit', 'tmsBillWeight', 'tmsBillVolumeAsText', 'goodsDescription',
              'mode', 'etaCutOffTime', 'tmsBillDescription', 'containerNo', 'sealNo', 'carrierFullName', 'bookingCode'
            ]
          },
          'Vehicle Trip': {
            label: T('Vehicle Trip'),
            visible: true,
            fields: [
              'tripRoutes', 'actualEndTime', 'round', 'address', 'actualStartTime', 'planEndTime', 'planStartTime',
              'actualDistanceInKm', 'fleetLabel', 'vehicleLabel', 'fleetBfsOneCode',
              'mobile', 'driverFullName', 'identificationNo', 'tripDeliveryPlan', 'editMode', 'trailerNumber'
            ]
          },
          'fuel': {
            label: T('Fuel'),
            visible: false,
            fields: [
              'litersOfFuel', 'extraLitersOfFuel', 'totalLitersOfFuel'
            ]
          },
          'Revenue': {
            label: T('Doanh Thu'),
            visible: false,
            fields: [
              'fixedCharge', 'extraCharge', 'totalCharge',
            ]
          },
          'Expense': {
            label: T('Chi Phí'),
            visible: false,
            fields: [
              'driverSalary', 'extraFuelCost', 'fuelCost', 'travelCost', 'vetc', 'fixedCost', 'extraCost', 'totalCost', 'liftOnLiftOffCharge'
            ]
          },
        }
      },
      toolbar: {
        actions: [
          // {
          //   name: 'focus', label: 'Focus', icon: FeatherIcon.Eye,
          //   createComponent(_ctx) {
          //     return <EntityFocus entityFocus={uiList.entityFocus} entityType={uiList.getTableName()} context={_ctx} />
          //   },
          // },
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION(!writeCap || (readOnly ?? false), {
            name: "summary",
            label: 'Summary',
            icon: FeatherIcon.File,
            onClick(ctx: grid.VGridContext) {
              let uiRoot = ctx.uiRoot as entity.DbEntityList;
              let { pageContext } = uiRoot.props;
              let onCreatePage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
                return (
                  <div className='flex-vbox'>
                    <UIVehicleTripGoodsTrackingSummary context={ctx} />
                    <bs.Toolbar className='border'>
                      <entity.WButtonEntityWrite icon={FeatherIcon.X}
                        label={'Close'} appContext={appCtx} pageContext={pageCtx}
                        onClick={() => pageCtx.back()} />
                    </bs.Toolbar>
                  </div>
                )
              }
              pageContext.createPopupPage('summary', T("Summary"), onCreatePage, { size: "xl" })
            },
          }),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION(!writeCap || (readOnly ?? false), {
            name: "generate-file-trucking",
            label: 'Generate File Trucking',
            icon: FeatherIcon.Book,
            onClick(ctx: grid.VGridContext) {
              let uiRoot = ctx.uiRoot as entity.DbEntityList;
              let { appContext, pageContext, plugin } = uiRoot.props;

              appContext.createHttpBackendCall('VehicleRestCallService', 'getBFSFileTruckingConfig', {})
                .withSuccessData((data: any) => {
                  let config = data;
                  let onCreatePage = (_appCtx: app.AppContext, pageCtx: app.PageContext) => {
                    return (
                      <UIVehicleTripGoodsTrackingGenerateCode
                        appContext={appContext} pageContext={pageCtx}
                        onCommit={(_bean) => {
                          pageCtx.back();
                          uiRoot.reloadData();
                        }}
                        observer={new entity.ComplexBeanObserver({ ...config, autoIncrease: 'byTrip', sortDate: 'ASC' })}
                        records={plugin.getListModel().getSelectedRecords()} />
                    )
                  }
                  pageContext.createPopupPage('config', T("Generate File Trucking Config"), onCreatePage, { size: "xl" });
                })
                .call();
            },
          }),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION(!writeCap || (readOnly ?? false), {
            name: "add-row",
            label: 'Add Row',
            icon: FeatherIcon.Plus,
            onClick(ctx: grid.VGridContext) {
              let uiRoot = ctx.uiRoot as UIVehicleTripGoodsTrackingList;
              let { plugin } = uiRoot.props;
              let tripGoods: any = {
                'billLabel': '/',
                'openedDate': util.TimeUtil.toCompactDateTimeFormat(new Date()),
                'tmsBillType': TMSBillType.FORWARDER,
                'mode': TMSBillTransportationMode.ExportAir,
                'quantityUnit': 'PKG',
                'weightUnit': 'KGM',
                'volumeUnit': 'CBM',
                'uikey': `new/${uiRoot.idTrackerNext()}`,
                'isBillOwner': true,
                'updateTMSBill': true,
                typeOfTransport: uiList.props.typeOfTransport
              }
              tripGoods['groupMode'] = TMSBillTransportationModeTools.group(tripGoods['mode']);
              plugin.getRecords().unshift(tripGoods);
              grid.initRecordStates([tripGoods]);
              plugin.getListModel().filter();
              let state = grid.getRecordState(tripGoods);
              state.markModified();
              uiRoot.forceUpdate();
            },
          }),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!pageContext.hasUserAdminCapability() || (readOnly ?? false), T("Delete")),
          module.settings.createVGridFavButton('vehicle-trip-goods-tracking-config'),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION(!writeCap || (readOnly ?? false), {
            name: "config",
            label: '',
            icon: FeatherIcon.Settings,
            onClick: (ctx: grid.VGridContext) => {
              showVehicleTripGoodsTrackingConfig(ctx, 'md');
            },
          }),
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(plugin.searchParams ? true : false, 'filter'),
        filterActions: [
          {
            name: 'focus', label: T('Focus'),
            createComponent(_ctx) {
              return <EntityFocus entityFocus={uiList.entityState} entityType={uiList.getTableName()} context={_ctx} />
            },
          },
          {
            name: 'route-type', label: T('Route Type'),
            createComponent(_ctx) {
              let listPlugin = plugin as UIVehicleTripGoodsTrackingListPlugin;
              if (!isContainerView) return;
              return (
                <ToolbarFilterOptions
                  context={_ctx}
                  options={['', 'short', 'long', 'unclassified']} optionLabels={['All', 'Ngắn', 'Dài', 'N/A']} selectOption={listPlugin.routeType}
                  onChange={(newVal) => {
                    listPlugin.withRouteType(newVal);
                    uiList.reloadData();
                  }}
                />
              )
            },
          },
          {
            name: 'wrap-text', label: T('Wrap Text'),
            createComponent(_ctx) {
              return (
                <WrapText
                  context={_ctx} checked={uiList.entityState.wrapText}
                  onChange={(newVal) => {
                    uiList.entityState.wrapText = newVal;
                    let recConfig = _ctx.config.record;
                    let recEditor = recConfig.editor;
                    if (!recEditor) return;
                    recEditor.enable = !newVal;
                    uiList.nextViewId();
                    uiList.forceUpdate();
                  }}
                />
              )
            },
          },
          ...entity.DbEntityListConfigTool.TOOLBAR_AUTO_REFRESH('auto-refresh-tracking', T('Refresh')),
        ],
      },
      footer: {
        page: {
          hide: type !== 'page',
          render: (ctx: grid.VGridContext) => {
            return (<UIVehicleTripGoodsTrackingPageControl context={ctx} />);
          }
        },
        selector: entity.DbEntityListConfigTool.FOOTER_MULTI_SELECT(T('Select'), type),
      },
      view: {
        currentViewName: viewMode ? viewMode : bs.ScreenUtil.isSmallScreen() ? 'table' : 'aggregation',
        availables: {
          table: {
            viewMode: 'table',
            footer: {
              createRecords: (ctx: grid.VGridContext) => this.createTotalRecords(ctx)
            }
          },
          aggregation: {
            viewMode: 'aggregation',
            treeWidth: 130,
            createAggregationModel(_ctx: grid.VGridContext) {
              let sumAggregation = (label: string, enable: boolean = false) => {
                let fields = ['quantity', 'weight', 'tmsBillQuantity', 'tmsBillWeight'];
                let revenueFields = ['fixedCharge', 'extraCharge', 'totalCharge']
                let fieldsCost = ['oil', 'oilAfterVat', 'oilBeforeVat', 'transportationCost', 'oilExtra', 'driverSalary', 'totalCost', 'profit'];
                let allFields = [...fields, ...revenueFields, ...fieldsCost, 'averageDriverLaborRatio', 'driverSalary'];
                return new grid.SumAggregationFunction(label, allFields, enable);
              }
              let model = new grid.AggregationDisplayModel(T('All'), true);
              model.addAggregation(new grid.DateValueAggregation(T("Date"), "trackingDate", "YYYY/MM/DD", !aggregationField || aggregationField === 'deliveryPlan')
                .withSortBucket('desc').withAggFunction(sumAggregation('Sum by Date')));
              model.addAggregation(
                new grid.ValueAggregation(T("License Plate"), "vehicleLabel", aggregationField === 'vehicleLabel')
                  .withAggFunction(sumAggregation('Sum', true)));
              return model;
            },
            footer: {
              createRecords: (ctx: grid.VGridContext) => this.createTotalRecords(ctx),
            }
          }
        }
      }
    }
    if (smallScreen) {
      delete config.record.control;
    }
    if (summaryMode) {
      let state = grid.VGridConfigUtil.getRecordConfigState(config);
      state.summaryMode = summaryMode;
    }

    const fields = ['senderReceiverContact', 'senderReceiverAddress', 'pickupAddress', 'deliveryAddress'];
    let checkFields: any[] = [];
    for (let sel of config.record.fields) {
      if ((!sel.state || sel.state.visible) && fields.includes(sel.name)) {
        checkFields.push(sel.name);
      }
      if (sel.editor && config.record.editor) sel.editor.enable = true;
      if (!sel.computeCssClasses) {
        sel.computeCssClasses = (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          let record = dRecord.record;
          let cssClass = this.getBgColor(record);
          if (fields.includes(sel.name) && (uiList.entityState.wrapText)) {
            cssClass = `${cssClass} text-wrap`;
          }
          return cssClass;
        }
      }
    }
    config.record.computeDataRowHeight = (_ctx: grid.VGridContext, dRec: grid.DisplayRecord) => {
      let rec = dRec.record;
      const h = 25;
      let height = dataCellHeight;
      if (dRec.isDataRecord()) {
        let stopLocations = rec['stopLocations'];
        if (stopLocations && stopLocations.length > 1) {
          let stopCount = stopLocations.length;
          height = stopCount * h;
        }
        let wrapText = uiList.entityState.wrapText;
        let maxLength = 0;
        if (wrapText) {
          // checkFields.forEach(f => {
          //   let val = rec[f];
          //   if (val && val.length > maxLength) maxLength = val.length;
          // });
          // height = Math.max(height, Math.ceil(maxLength / 25) * h);
          height = Math.max(height, 7.5 * h);
        }
        return height;
      }
      return 20;
    }

    return config;
  }

  getBgColor = (record: any) => {
    let cssClass = record['cssClass'];
    if (!cssClass) return '';
    return ` ${cssClass} bg-opacity-10`;
  }

  onShowStopLocations(tracking: any) {
    let { appContext, pageContext } = this.props

    appContext.createHttpBackendCall('TMSRestCallService', 'getTMSBillById', { id: tracking['tmsBillId'] })
      .withSuccessData((data: any) => {
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          let observer = new entity.ComplexBeanObserver(data);
          return (
            <div className='flex-vbox'>
              <TMSBillStopLocationList appContext={appCtx} pageContext={pageCtx} style={{ height: 350 }} customerId={tracking['customerId']}
                partnerId={tracking['customerPartnerId']}
                purposeType={TMSBillTransportationModeTools.isImport(tracking['mode']) ? 'ReturnGoods' : 'CollectGoods'}
                plugin={observer.createVGridEntityListEditorPlugin('stopLocations', [])}
                dialogEditor={true} editorTitle={T("Stop Locations")}
              />
              <bs.Toolbar className='border'>
                <entity.ButtonEntityCommit
                  appContext={appCtx} pageContext={pageCtx} observer={observer}
                  onPostCommit={() => {
                    tracking['stopLocations'] = observer.getComplexArrayProperty('stopLocations', []);
                    this.nextViewId();
                    this.forceUpdate()
                    pageCtx.back()
                  }}
                  commit={{ service: 'TMSRestCallService', commitMethod: 'saveTMSBill', entityLabel: "TMS Bill" }} />
              </bs.Toolbar>
            </div>
          )

        }
        pageContext.createPopupPage('stop-locations', T('Stop Locations'), createAppPage, { size: 'lg', backdrop: 'static' })
      })
      .call();
  }
}


interface UIVehicleTripGoodsTrackingProps extends entity.DbEntityListProps {
  summaryMode?: boolean;
  viewMode?: 'aggregation' | 'table';
}
export class UIVehicleTripGoodsTrackingListLoadableConfig extends app.AppComponent<UIVehicleTripGoodsTrackingProps> {
  entity: any = null;

  componentDidMount(): void {
    let callback = (data: any) => {
      if (data) {
        this.entity = data;
      } else {
        this.entity = {};
      }
      this.forceUpdate();
    }
    let { appContext } = this.props;
    appContext
      .createHttpBackendCall('VehicleRestCallService', 'getVehicleTripGoodsTrackingConfig')
      .withSuccessData(callback)
      .call();
  }

  createTabPaneConfig = (config: any) => {
    let { appContext, pageContext, plugin, type } = this.props;
    let listPlugin = plugin as UIVehicleTripGoodsTrackingListPlugin;
    listPlugin.withInputDataApp(config['inputDataCreator']);
    // listPlugin.withExcludeNeedConfirmStatus(true);
    let containerView = config['containerView'];
    let truckView = config['truckView'];
    let tabs: bs.PanelConfig[] = [];
    let containerViewPanelConfig: bs.PanelConfig = {
      name: 'container', label: 'Container', active: true, Icon: FeatherIcon.Truck,
      renderContent: (_ctx: bs.UIContext) => {
        return (
          <UIVehicleTripGoodsTrackingList type={type} key={`cont-tracking-list`}
            typeOfTransport='container' configView={config}
            appContext={appContext} pageContext={pageContext}
            plugin={listPlugin.withTypeOfTransport('container')}
          />
        );
      }
    };
    let truckViewPanelConfig: bs.PanelConfig = {
      name: 'truck', label: 'Truck', Icon: FeatherIcon.Box,
      renderContent: (_ctx: bs.UIContext) => {
        return (
          <UIVehicleTripGoodsTrackingList type={type} key={`truck-tracking-list`}
            typeOfTransport='truck' configView={config}
            appContext={appContext} pageContext={pageContext}
            plugin={listPlugin.withTypeOfTransport('truck')} />
        );
      }
    };

    let genericViewPanelConfig: bs.PanelConfig = {
      name: 'generic-tracking', label: 'Generic Tracking', active: true, Icon: FeatherIcon.Truck,
      renderContent: (_ctx: bs.UIContext) => {
        return (
          <UIVehicleTripGoodsTrackingList type={type} key={`generic-tracking-list`}
            configView={config}
            appContext={appContext} pageContext={pageContext}
            plugin={listPlugin.withTypeOfTransport(null)}
          />
        );
      }
    };

    if (containerView) tabs.push(containerViewPanelConfig);
    if (truckView) tabs.push({ ...truckViewPanelConfig, active: tabs.length == 0 });
    if (tabs.length == 0) {
      tabs.push(genericViewPanelConfig);
    } else if (pageContext.hasUserAdminCapability()) {
      tabs.push({ ...genericViewPanelConfig, active: false });
    }
    tabs.push({
      name: 'vehicle-trip', label: 'Vehicle Trip', Icon: FeatherIcon.Activity,
      renderContent: (_ctx: bs.UIContext) => {
        return (
          <UIVehicleTripList
            type='page'
            appContext={appContext} pageContext={pageContext}
            plugin={new UIVehicleTripListPlugin().withDataScope(app.AppDataScope.COMPANY)}
          />
        );
      }
    }
    )
    return { tabs: tabs };
  }

  renderEntity(entity: any): React.ReactNode {
    return (
      <bs.DefaultTabPane style={{ height: 200 }} config={this.createTabPaneConfig(entity)} />
    )
  };

  render(): React.ReactNode {
    if (!this.entity) return this.renderLoading();
    return this.renderEntity(this.entity);
  }
}