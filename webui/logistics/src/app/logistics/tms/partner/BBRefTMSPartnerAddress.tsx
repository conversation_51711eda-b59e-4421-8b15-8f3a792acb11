import React from "react";
import { T } from "../backend";

import { entity, sql, grid, bs, app } from "@datatp-ui/lib";
import { TMSPartnerAddressEditor } from "./UITMSPartnerList";


export type AddressType = 'Export' | 'Import' | 'None';
interface BBRefTMSPartnerAddressProps extends entity.BBRefEntityProps {
  beanIdField: string;
  beanLabelField: string;
  customerId?: any;
  partnerId?: any;
  onPostCommit?: (partnerAddress: any) => void;
  types?: AddressType[];
}
export class BBRefTMSPartnerAddress extends entity.BBRefEntity<BBRefTMSPartnerAddressProps> {
  createPlugin() {
    let { beanIdField, beanLabelField, customerId, types, appContext, partnerId, pageContext, onPostCommit } = this.props;
    let config: entity.BBRefEntityPluginConfig = {
      backend: {
        context: 'company',
        service: 'TMSPartnerService',
        searchMethod: 'searchPartnerAddresses',
        loadMethod: 'loadPartnerAddress',
        createSearchParams: (searchParams: sql.SqlSearchParams, _userInput: string) => {
          searchParams.params = {
            'customerId': customerId,
          };
          searchParams.optionFilters = [
            {
              "name": "type",
              "label": "Type",
              "type": "STRING",
              "required": true,
              "multiple": true,
              "options": ['Export', 'Import', 'None'],
              "selectOptions": types || []
            },
          ]
          return searchParams;
        },
      },
      bean: {
        idField: beanIdField,
        labelField: beanLabelField,
        mapSelect: (ui: entity.BBRefEntity, bean: any, selectOpt: any, idValue: any, labelValue: any): any => {
          if (selectOpt) {
            bean[beanIdField] = idValue;
            bean[beanLabelField] = labelValue;
            return 'success';
          } else {
            bean[beanIdField] = null;
            bean[beanLabelField] = null;
            const { allowUserInput } = ui.props;
            if (allowUserInput) {
              bean[beanLabelField] = labelValue;
              appContext
                .createHttpBackendCall('TMSPartnerService', 'getNewSubdistrictLocationByOldAddress', { address: labelValue })
                .withSuccessData((data: any) => {
                  if (data) {
                    let partnerAddress = { locationId: data.id, locationLabel: data.address, tmsPartnerId: partnerId, type: "None", address: labelValue, invAddress: data.subdistrictLabel + ', ' + data.stateLabel }
                    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
                      return <TMSPartnerAddressEditor appContext={appCtx} pageContext={pageCtx} observer={new entity.BeanObserver(partnerAddress)} onPostCommit={(partnerAddress: any) => {
                        bean[beanIdField] = partnerAddress['locationId'];
                        bean[beanLabelField] = partnerAddress['address'];
                        if (onPostCommit) {
                          onPostCommit(partnerAddress);
                        }
                        pageCtx.back();
                      }} />
                    }
                    pageContext.createPopupPage('partner-address', 'Lưu Địa Chỉ', createAppPage, { size: 'md' })
                  }
                })
                .call();
              return 'success';
            } else {
              return 'fail';
            }
          }
        }
      },
      refEntity: {
        idField: 'locationId',
        labelField: 'address',
        vgridRecordConfig: {
          fields: [
            entity.DbEntityListConfigTool.FIELD_ON_SELECT('address', T('Address'), 200),
            { name: 'type', label: T('Type') },
            { name: 'locationLabel', label: T('Location'), width: 350 },
            {
              name: 'locStorageState', label: T(''), width: 100, container: 'fixed-right',
              customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
                if (dRecord.record['locStorageState'] === 'ACTIVE') {
                  return <bs.Badge laf="success">{'Hoạt Động'}</bs.Badge>
                }
                return <bs.Badge laf="danger">{'Hết Hạn'}</bs.Badge>
              }
            },
          ]
        }
      },
    }
    return new entity.BBRefEntityPlugin(config);
  }
}