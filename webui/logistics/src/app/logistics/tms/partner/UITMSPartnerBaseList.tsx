import React, { Component } from 'react';
import * as FeatherIcon from 'react-feather'
import { app, server, util, grid, bs, input, entity, sql } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';
import { T } from '../backend';
import { Role } from '../models';
import { UIEntityInfo } from 'app/logistics/vendor/api/TMSTrackingPlugin';
import { UITMSBillList, UITMSBillListPlugin } from '../bill/UITMSBillList';
import { EditEmails, TMSUtils } from '../utils';
const SESSION = app.host.DATATP_HOST.session;

export interface UITMSPartnerBaseListProps extends entity.DbEntityListProps {
  typePartner?: 'Customer' | 'Carrier' | 'Agent',
  onPostCommit?: (data: any) => void;
}

export abstract class UITMSPartnerBaseList<T extends UITMSPartnerBaseListProps = UITMSPartnerBaseListProps> extends entity.DbEntityList<T> {

  abstract renderListPageControl(context: grid.VGridContext): React.ReactElement;

  onInputChange = (fieldCtx: grid.FieldContext, oldVal: any, newVal: any) => {
    let dRecord = fieldCtx.displayRecord;
    let field = fieldCtx.fieldConfig;
    let ctx = fieldCtx.gridContext;
    let event: grid.VGridCellEvent = {
      row: dRecord.row, field: field, event: 'Modified', data: dRecord
    }
    ctx.broadcastCellEvent(event);
  };

  addFieldConfigs(): Array<grid.FieldConfig> {
    return [];
  }

  createHTTPBackEndCallFollow(record: any): server.HttpBackendCall | null {
    return null;
  }

  addActionConfigs(): Array<grid.VGridActionConfig> {
    return [];
  }

  createActionConfigs(): Array<grid.VGridActionConfig> {
    let { pageContext, readOnly } = this.props;
    const writeCap = !readOnly && pageContext.hasUserWriteCapability();
    const modCap = !readOnly && pageContext.hasUserModeratorCapability();
    return [
      ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_STORAGE_STATES(
        [entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED], !writeCap
      ),
      ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!modCap, T('Delete')),
      ...this.addActionConfigs()
    ]
  }

  createFollowFieldConfigs(): Array<grid.FieldConfig> {
    let { appContext, pageContext, onPostCommit } = this.props;
    let thisUI = this;
    let fields: Array<grid.FieldConfig> = [
      {
        name: 'permissions', label: T('Followers'), width: 250, dataTooltip: true,
        state: { visible: true },
        fieldDataGetter: (customer: any) => {
          if (!customer.permissions) return null;
          let permissions: Array<any> = customer.permissions;
          let values: Array<any> = [];
          for (let permission of permissions) {
            if (permission.role == 'SALE_MAN' || permission.role == 'SALE_MANAGER') continue;
            values.push(permission.label);
          }
          return values.join(', ');
        }
      },
      {
        name: 'manager', label: T('Managers'), width: 250, dataTooltip: true,
        state: { visible: true },
        fieldDataGetter: (customer: any) => {
          if (!customer.permissions) return null;
          let permissions: Array<any> = customer.permissions;
          let values: Array<any> = [];
          for (let permission of permissions) {
            if (permission.role != 'SALE_MANAGER') continue;
            values.push(permission.label);
          }
          return values.join(', ');
        }
      },
      {
        name: 'follow', label: T('Follow'), width: 70,
        container: 'fixed-right',
        state: { visible: true },
        customRender(ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) {
          let record = dRecord.record;
          let accountId = SESSION.getAccountAcl().getAccountId();
          let permissions: Array<any> = record.permissions;
          if (!record.id || !permissions) {
            return <></>
          }
          for (let permission of permissions) {
            if (permission.userId === accountId) {
              return <></>
            }
          }
          let onFollow = () => {
            let callbackConfirm = () => {
              let httpBackEndCall = thisUI.createHTTPBackEndCallFollow(record);
              if (httpBackEndCall) {
                httpBackEndCall.withSuccessData((data: any) => {
                  if (data) {
                    permissions.push(data);
                    thisUI.getVGridContext().getVGrid().forceUpdateView();
                    if (onPostCommit) {
                      onPostCommit(data);
                    }
                  }
                })
                  .withSuccessNotification('success', T('Follow success'))
                  .call();
              }

            }
            let message = (<div className="text-danger">Do you want to follow ?</div>);
            bs.dialogConfirmMessage('Confirm Message', message, callbackConfirm);
          }
          return (
            <div className='flex-hbox justify-content-center'>
              <bs.Button laf='link' onClick={onFollow}>
                + Follow
              </bs.Button>
            </div>
          )
        }
      },
    ];
    return fields;
  }

  createVGridConfig() {
    let { type, appContext, pageContext, readOnly, plugin } = this.props;
    let title = 'TMS Partner';
    let backend = plugin.backend;
    if (backend && backend.entityLabel) {
      title = backend.entityLabel;
    }
    const writeCap = !readOnly && pageContext.hasUserWriteCapability();
    let thisUI = this;

    let config: grid.VGridConfig = {
      title: T(title),
      record: {
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          { name: 'id', label: 'ID', state: { visible: true }, container: 'fixed-left', width: 50 },
          {
            name: 'shortName', label: T('Short Name'), width: 220,
            state: { showRecordState: true }, container: 'fixed-left',
            onClick: (ctx: grid.VGridContext, record: grid.DisplayRecord) => {
              this.onSelect(record);
            },
            fieldDataGetter: (record) => {
              return record['shortName'] ? record['shortName'] : '/';
            },
            editor: { type: 'string' },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row === cell.getRow()) {
                  cell.forceUpdate();
                }
              }
            }
          },
          {
            name: 'label',
            label: T('Account'),
            width: 200,
            state: { visible: false },
            editor: {
              type: 'string',
              onInputChange: this.onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let partner = dRecord.record;
                const oldVal = partner['bfsOneCode'];
                return (
                  <module.account.BBRefAccount minWidth={600} allowUserInput tabIndex={tabIndex} autofocus={focus}
                    appContext={appContext} pageContext={pageContext} disable={!writeCap}
                    placeholder='Enter An Account/BFSOne Code'
                    bean={partner} accountIdField={'accountId'} accountLabelField={'label'}
                    onPostUpdate={(_inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => {
                      bean['bfsOneCode'] = userInput;
                      if (selectOpt && selectOpt['legacyLoginId']) {
                        bean['bfsOneCode'] = selectOpt['legacyLoginId'];
                      }
                      onInputChange(bean, field.name, oldVal, bean['bfsOneCode'])
                    }}
                  />
                )
              },
            }
          },
          {
            name: 'bfsOneCode', label: 'BFSOne Code', width: 100,
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row === cell.getRow() && event.field.name === 'label') {
                  cell.forceUpdate();
                }
              }
            },
            editor: {
              type: 'string',
              onInputChange: this.onInputChange,
            }
          },
          {
            name: 'sales', label: T('Sales'), width: 250, dataTooltip: true,
            state: { visible: true },
            fieldDataGetter: (customer: any) => {
              if (!customer.permissions) return null;
              let permissions: Array<any> = customer.permissions;
              let values: Array<any> = [];
              for (let permission of permissions) {
                if (permission.role != 'SALE_MAN') continue;
                values.push(permission.label);
              }
              return values.join(', ');
            },
            customRender(ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) {
              let value = field.fieldDataGetter ? field.fieldDataGetter(dRecord.record) : dRecord.record[field.name];
              const onMultiSelect = (accounts: Array<any>, pageCtx: app.PageContext) => {
                let permissions: Array<any> = [];
                for (let account of accounts) {
                  let permission = {
                    tmsPartnerId: dRecord.record['tmsPartnerId'],
                    role: 'SALE_MAN',
                    type: 'Employee',
                    userId: account.id,
                    label: account.fullName,
                    capability: 'Read'
                  };
                  permissions.push(permission);
                }
                appContext
                  .createHttpBackendCall('TMSPartnerPermissionService', 'savePartnerPermissions', { permissions: permissions })
                  .withSuccessData((data: any) => {
                    pageCtx.back();
                    thisUI.reloadData();
                  })
                  .withSuccessNotification('success', T('Add Permissions Success'))
                  .call()
              }
              return (<div className='flex-hbox'>
                <bs.Button laf='link' onClick={() => {
                  let excludeRecordFilter = new entity.ExcludeRecordFilter(dRecord.record['permissions'], "userId", "userId");
                  let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
                    return (
                      <module.account.UIAccountList
                        type={'selector'}
                        plugin={new module.account.UIAccountListPlugin().withAccountType(module.account.AccountType.USER).withRecordFilter(excludeRecordFilter)}
                        appContext={appCtx} pageContext={pageCtx} readOnly={true}
                        onSelect={(_appCtx, _pageCtx, account) => onMultiSelect([account], _pageCtx)}
                      />
                    );
                  }
                  pageContext.createPopupPage("add-employees", T('Add Accounts'), createAppPage, { size: "lg" });
                }}>
                  <FeatherIcon.Plus size={12} className='text-info' />
                </bs.Button>
                {TMSUtils.renderTooltip(value, value)}
              </div>)
            }
          },
          {
            name: 'emails', label: T('Emails'), width: 210, state: { visible: true }, editor: {
              type: 'string',
              onInputChange: this.onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let partner = dRecord.record;
                let emails: any = partner['emails'];
                let fileWidth = field.width ? field.width : 150;
                let openPopUpEmails = () => {
                  appContext.createHttpBackendCall('TMSPartnerService', 'getTMSPartnerById', { id: partner.tmsPartnerId })
                    .withSuccessData((data: any) => {
                      let createPopupPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
                        return (
                          <EditEmails appContext={appCtx} pageContext={pageCtx} observer={new entity.BeanObserver(data)}
                            commitConfig={{ entityLabel: 'Partner', service: 'TMSPartnerService', commitMethod: 'saveTMSPartner' }}
                            onPostCommit={() => { thisUI.reloadData() }} />
                        )
                      }
                      pageContext.createPopupPage('edit-emails', T('Edit Emails'), createPopupPageContent, { size: 'sm', backdrop: "static" });
                    })
                    .call();
                }
                return (
                  <div className={`flex-hbox align-items-center`}>
                    <div style={{ width: fileWidth - 22, overflow: 'hidden' }}>
                      <bs.Tooltip tooltip={emails} >{emails}</bs.Tooltip>
                    </div>
                    {partner.id && !readOnly ? <FeatherIcon.Edit size={12} className='mx-1 state-modified' onClick={openPopUpEmails} /> : <></>}
                  </div>
                )
              },
            }
          },
          { name: 'code', label: T('Code'), state: { visible: false } },
          {
            name: 'invoiceCompanyLabel', label: T('Company'), width: 200, dataTooltip: true, editor: {
              type: 'string',
              onInputChange: this.onInputChange,
            }
          },
          { name: 'invoiceCompanyTaxCode', label: T('Tax Code'), editor: { type: 'string' } },
          {
            name: 'invoiceCompanyAddress', label: T('Address'), width: 350, dataTooltip: true, editor: { type: 'text' }
          },
          { name: 'email', label: T('Email'), width: 250, state: { visible: false } },
          { name: 'mobile', label: T('Mobile'), width: 250, state: { visible: false } },
          { name: 'odooPartnerCode', label: T('Odoo Partner Code'), width: 220, state: { visible: false } },
          {
            name: 'description', label: T('Description'), width: 250, editor: { type: 'string' },
          },
          ...this.addFieldConfigs(),
          ...entity.DbEntityListConfigTool.FIELD_ENTITY
        ],
        fieldGroups: {
          "Invoice Info": {
            label: T('Invoice Info'),
            visible: true,
            fields: ['invoiceCompanyLabel', 'invoiceCompanyAddress', 'invoiceCompanyTaxCode']
          },
        },
        computeDataRowHeight: (_ctx: grid.VGridContext, dRec: grid.DisplayRecord) => {
          return 35;
        },
        editor: {
          supportViewMode: ['table', 'aggregation'],
          enable: true
        },

      },
      toolbar: {
        actions: this.createActionConfigs(),
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(plugin.searchParams ? true : false, 'filter'),
        filterActions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_AUTO_REFRESH('refresh', T('Refresh')),
        ],
      },
      footer: {
        page: {
          render: (ctx: grid.VGridContext) => {
            return thisUI.renderListPageControl(ctx);
          }
        },
        selector: entity.DbEntityListConfigTool.FOOTER_MULTI_SELECT(T("Select"), type)
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
          aggregation: {
            viewMode: 'aggregation',
            treeWidth: 150,
            createAggregationModel(_ctx: grid.VGridContext) {
              let model = new grid.AggregationDisplayModel(T('All'), false);
              model.addAggregation(
                new grid.ValueAggregation(T("Tax Code"), "invoiceCompanyTaxCode", true)
                  .withSortBucket('asc'));
              return model;
            },
          }
        }
      }
    }
    if (writeCap) {
      config.record.control = {
        width: 20,
        items: [
          {
            name: 'edit', hint: 'Edit', icon: FeatherIcon.Edit,
            customRender(ctx, dRecord) {
              let customer = dRecord.record;
              let uiPartner = ctx.uiRoot as UITMSPartnerBaseList;
              return (
                <bs.Button laf='link' disabled={!customer.id} onClick={() => uiPartner.onDefaultSelect(dRecord)}>
                  <FeatherIcon.Edit size={12} className='text-warning' />
                </bs.Button>
              )
            },
          }
        ]
      };
    }
    return config;

  }

  onNewAction() {
    let { plugin } = this.props;
    let newPartner: any = {
      label: '/',
    };
    let records = plugin.getListModel().getRecords();
    records.unshift(newPartner);
    plugin.update(records);
    grid.getRecordState(newPartner).markModified();
    this.getVGridContext().getVGrid().forceUpdateView();
  }
}

export abstract class UITMSPartnerBaseListPageControl<T extends grid.VGridContextProps = grid.VGridContextProps> extends Component<T> {

  abstract onAdd(): void;

  abstract createHttpBackEndCallSave(records: Array<any>): server.HttpBackendCall;

  onSave = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UITMSPartnerBaseList;
    let { plugin } = uiRoot.props;
    let dRecords = plugin.getListModel().getDisplayRecordList().getDisplayRecords();
    let filterRecBfsOneCodeIsNull: Array<grid.DisplayRecord> =
      dRecords.filter(dRec => dRec.isDataRecord() && !dRec.record['bfsOneCode'] && dRec.getRecordState().isMarkModified());
    if (filterRecBfsOneCodeIsNull.length > 0) {
      let notify: Array<any> = [];
      filterRecBfsOneCodeIsNull.forEach(dRec => {
        let line = dRec.rowInBucket ? dRec.rowInBucket : dRec.row + 1;
        notify.push(
          <div key={`${util.IDTracker.next()}`}>
            {`Line: ${line} | `}
            {`${dRec.record['shortName']} : BFSOne Code Is Not NULL`}
          </div>
        )
      });

      let content = (
        <div>
          {...notify}
          <div className='text-danger fw-bold'>
            - Please Enter BFSOne Code And Try Again!
          </div>
        </div>
      )
      bs.dialogMessage('Warning', 'warning', content, { size: 'md' });
      return;
    }

    let records = plugin.getListModel().getMarkModifiedRecords();
    records.forEach((record) => {
      if (record.emails) {
        record.emails = record.emails.split(';')
      } else {
        delete record.emails;
      }
    });

    let backEndCall = this.createHttpBackEndCallSave(records);
    if (backEndCall) {
      backEndCall
        .withSuccess((_data: any) => {
          uiRoot.reloadData()
        })
        .withSuccessNotification("success", T(`Save Success`))
        .call();
    }

  }

  addButtonToolBars(): Array<any> {
    return [];
  }

  render() {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UITMSPartnerBaseList;
    let { appContext, pageContext, readOnly } = uiRoot.props;
    let writeCap = !readOnly && pageContext.hasUserWriteCapability();

    return (
      <bs.Toolbar className='border' hide={!writeCap}>
        {...this.addButtonToolBars()}
        <entity.WButtonEntityWrite
          appContext={appContext} pageContext={pageContext} icon={FeatherIcon.Plus}
          label={T('New')} onClick={() => this.onAdd()} />
        <entity.XlsxExportButton appContext={appContext} pageContext={pageContext} context={context} />
        <entity.WButtonEntityWrite appContext={appContext} pageContext={pageContext} icon={FeatherIcon.Save}
          label={T('Save Changes')} onClick={() => this.onSave()} />
      </bs.Toolbar>
    );
  }
}

export class UIPartnerForm extends entity.AppDbComplexEntityEditor {

  render() {
    const { observer, appContext, pageContext, readOnly } = this.props;
    const partner = observer.getMutableBean();
    const writeCap = pageContext.hasUserWriteCapability() && !readOnly;
    return (
      <div className='flex-vbox'>
        <input.BBStringField bean={partner} field='label' label={T('Full Name')} disable={!writeCap} />
        <input.BBStringField label={T('Short Name')} bean={partner} field={'shortName'} disable={!writeCap} />
        <input.BBStringField label={T('BFSOne Code')} bean={partner} field={'bfsOneCode'} disable={!writeCap} />
        <bs.FormLabel>{T('Ref Account')}</bs.FormLabel>
        <module.account.BBRefAccount minWidth={600} allowUserInput
          appContext={appContext} pageContext={pageContext} disable={!writeCap}
          placeholder='Enter An Account/BFSOne Code'
          bean={partner} accountIdField={'refAccountId'} accountLabelField={'refAccountFullName'}
          onPostUpdate={(inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => {
            if (selectOpt) {
              bean['bfsOneCode'] = selectOpt['legacyLoginId'];
            } else {
              bean['bfsOneCode'] = userInput;
            }
            this.forceUpdate();
          }}
        />
        <div className='my-2'>
          <div className='flex-hbox'>
            <bs.FormLabel>{T('Email')}</bs.FormLabel>
            <FeatherIcon.Edit className='mx-2 text-warning' style={{ cursor: 'pointer' }} size={12} onClick={() => EditEmails.onShowPopUpProcessEmails(this)} />
          </div>
          <input.BBStringArrayField
            key={this.viewId}
            bean={partner} field={'emails'} disable={!writeCap}
            validators={[util.validator.EMAIL_VALIDATOR]} onInputChange={(bean, field, oldVal, newVal) => {
              if (newVal.length == 0) bean[field] = null;
            }} />
        </div>
        <input.BBStringField bean={partner} field={'invoiceCompanyLabel'} label={'Invoice Company'} disable={!writeCap} />
        <input.BBStringField bean={partner} field={'invoiceCompanyTaxCode'} label={'Invoice Tax Code'} disable={!writeCap} />
        <input.BBTextField bean={partner} field={'invoiceCompanyAddress'} label={'Invoice Address'} style={{ height: '8em' }} disable={!writeCap} />

        <bs.Row>
          <bs.Col span={12}>
            <input.BBTextField label={T('Description')}
              bean={partner} style={{ height: '3em' }} field={'description'} disable={!writeCap} />
          </bs.Col>
        </bs.Row>
      </div>
    )
  }
}

export class UIPartnerEditor extends entity.AppDbComplexEntityEditor {
  tmsPartnerId: number;

  constructor(props: entity.AppComplexEntityEditorProps) {
    super(props);
    let { observer } = this.props;
    let partner = observer.getMutableBean();
    this.tmsPartnerId = partner.id
  }


  createPartnerCommit(): entity.CommitConfig {
    return {
      entityLabel: T(`TMS Partner`), context: 'company',
      service: 'TMSPartnerService', commitMethod: 'saveTMSPartner'
    }
  }

  onPartnerCommit = (entity: any) => {
    this.onPostCommitCallback(entity);
    this.tmsPartnerId = entity.id;
    this.forceUpdate();
  }

  addTabs(): Array<any> {
    return [];
  }

  render() {
    const { appContext, pageContext, observer, readOnly, onPostCommit } = this.props;
    const partner = observer.getMutableBean();
    const writeCap = pageContext.hasUserWriteCapability();
    const webhookConfig = observer.getBeanProperty('webhookConfig', {});
    const SESSION = app.host.DATATP_HOST.session;

    return (
      <div className='flex-vbox'>
        <bs.TabPane>
          <bs.Tab name='partner' label={T('Partner')} active>
            <bs.GreedyScrollable className='flex-vbox'>
              <bs.Card header="Info">
                <UIPartnerForm {...this.props} readOnly={!writeCap || readOnly} />
              </bs.Card>
              <bs.Card header='Webhook Config' collapse>
                <module.security.BBRefAccessToken
                  appContext={appContext} pageContext={pageContext}
                  label="Internal Token" placeholder='Select a token' accountId={SESSION.getAccountId()}
                  placement="bottom-start" offset={[0, 5]} minWidth={300}
                  bean={webhookConfig} beanIdField="tokenId" beanLabelField="tokenLabel" />
                <input.BBCheckboxField bean={webhookConfig} field={'autoPush'} label={T('Auto Push')} disable={!writeCap} value={false} />
                <input.BBStringField bean={webhookConfig} field={'baseRestUrl'} label={T('Rest Url')} disable={!writeCap} />
                <input.BBStringField bean={webhookConfig} field={'inputCompanyCode'} label={T('Company Code')} disable={!writeCap} />
                <input.BBStringField bean={webhookConfig} field={'module'} label={T('Module')} disable={!writeCap} />
                <input.BBTextField bean={webhookConfig} field={'accessToken'} label={T('Access Token')} style={{ height: '10em' }} disable={!writeCap} />
              </bs.Card>
            </bs.GreedyScrollable>
            <bs.Toolbar className='border' hide={!writeCap || readOnly}>
              <entity.ButtonEntityCommit
                appContext={appContext} pageContext={pageContext} hide={!writeCap || readOnly} observer={observer}
                commit={this.createPartnerCommit()}
                onPostCommit={this.onPartnerCommit}
              />
            </bs.Toolbar>
          </bs.Tab>
          {...this.addTabs()}
        </bs.TabPane>
        {partner.id ? < UILoginPermissionList style={{ maxHeight: 400 }} readOnly={!writeCap}
          appContext={appContext} pageContext={pageContext}
          tmsPartnerId={this.tmsPartnerId}
          onPostCommit={onPostCommit}
          plugin={new UILoginPermissionListPlugin().withPartnerId(partner.id)}
        /> : <></>}
      </div>
    )
  }
}

export interface ConvertBFSOnePartnerBaseEditorProps extends entity.AppComplexEntityEditorProps {
}

export abstract class ConvertBFSOnePartnerBaseEditor<T extends ConvertBFSOnePartnerBaseEditorProps = ConvertBFSOnePartnerBaseEditorProps> extends entity.AppDbComplexEntityEditor<T> {

  abstract createHttpBackEndCallPreview(params: any): server.HttpBackendCall;

  abstract onPostPreviewCallBack(data: any): void;

  abstract renderEditor(): JSX.Element;

  abstract createListDuplicated(): JSX.Element;

  onPreviewBFSOnePartner = (code: string) => {
    let { appContext, observer } = this.props;
    let successCB = (data: any) => {
      let bfsOnePartner = data['bfsOnePartner'];
      if (bfsOnePartner) {
        observer.replaceBeanProperty('bfsOneData', bfsOnePartner);
        let email: string = bfsOnePartner.email;
        let emails = email
          .split(";")
          .map(email => email.trim());
        let partner: any = {
          'shortName': bfsOnePartner.name,
          'bfsOneCode': bfsOnePartner.bfsonePartnerCode,
          'label': bfsOnePartner.label,
          'emails': emails,
          'invoiceCompanyLabel': bfsOnePartner.localizedLabel,
          'invoiceCompanyTaxCode': bfsOnePartner.taxCode,
          'invoiceCompanyAddress': bfsOnePartner.localizedAddress,
          'permissions': data.saleManPermission ? [data.saleManPermission] : []
        }
        observer.replaceBeanProperty('partner', partner);
        this.onPostPreviewCallBack(data);
        appContext.addOSNotification('success', 'Find BFS One Partner Success!');
        this.forceUpdate();
      } else {
        bs.dialogMessage('Warning', 'warning', `Not Found BFSOne Partner Data With Id / Tax code: ${code}`);
      }
    }
    let params = {
      code: code
    }
    let backEndCall = this.createHttpBackEndCallPreview(params);
    if (backEndCall) {
      backEndCall.withSuccessData(successCB).call();
    }
  }

  render(): React.ReactNode {
    let { appContext, pageContext, observer } = this.props;
    let bean = observer.getMutableBean();
    let bfsOneData = observer.getBeanProperty('bfsOneData', {});

    return (
      <bs.VSplit>
        <bs.VSplitPane width={550}>
          {this.renderEditor()}
        </bs.VSplitPane>
        <bs.VSplitPane>
          <div className='flex-grow-1'>
            <h4>Input BFS One Code</h4>
            <input.BBStringField bean={bean} field='code' label='BFSOne Code/ Tax Code' />
            <hr className='my-4 text-primary' />
            <h4>BFSOne Loaded...</h4>
            <UIEntityInfo
              appContext={appContext} pageContext={pageContext} widthVal={700}
              vgridRecordConfig={
                {
                  fields: [
                    { name: 'bfsonePartnerCode', label: T('Partner Id'), width: 150 },
                    { name: 'name', label: T('Short Name'), width: 150 },
                    { name: 'localizedLabel', label: T('Company Name'), width: 150 },
                    { name: 'taxCode', label: T('Tax Code'), width: 150 },
                    { name: 'localizedAddress', label: T('Address'), width: 150 },
                    { name: 'email', label: T('Email'), width: 150 },
                    { name: 'groupName', label: T('Group Name'), width: 150 },
                  ]
                }
              } entity={bfsOneData} />
            {this.createListDuplicated()}
          </div>
          <bs.Toolbar>
            <entity.WButtonEntityWrite
              appContext={appContext} pageContext={pageContext} icon={FeatherIcon.Activity}
              label={T('Execute')} onClick={() => this.onPreviewBFSOnePartner(bean['code'])} />
          </bs.Toolbar>
        </bs.VSplitPane>
      </bs.VSplit>
    )
  }
}

export interface UIPartnerProps extends entity.AppComplexEntityEditorProps {
  role?: Role,
  refId: number,
}

export class UIPartner<T extends UIPartnerProps = UIPartnerProps> extends entity.AppDbComplexEntityEditor<T> {
  onPostCloneJTProjectRule = (newJTProjectRule: any) => {
    const { appContext, observer } = this.props;
    const customer = observer.getMutableBean();
    customer.tmsJobTrackingRuleLabel = newJTProjectRule.label;
    customer.tmsJobTrackingRuleId = newJTProjectRule.id
    appContext.createHttpBackendCall('TMSPartnerService', 'saveTMSPartner', { partner: customer })
      .withSuccessData((data: any) => {
        observer.replaceWith(data);
        this.nextViewId();
        this.forceUpdate();
      })
      .call();
  }

  addTabs(): Array<any> {
    return [
    ];
  }

  createTMSBillFilterName(): 'customerId' | 'carrierId' {
    return 'customerId';
  }

  hasRenderTMSBillList(): boolean {
    return true;
  }

  createEditor(): JSX.Element {
    const { appContext, pageContext, observer, role, refId } = this.props;
    const partner = observer.getMutableBean();
    const writeCap = pageContext.hasUserWriteCapability();
    return (
      <UIPartnerEditor key={`info-${this.viewId}`}
        appContext={appContext} pageContext={pageContext} observer={observer} readOnly={!writeCap}
        onPostCommit={() => {
          this.onPostCommit(partner);
        }} />
    )
  }

  render() {
    const { appContext, pageContext, refId } = this.props;

    let filterName = this.createTMSBillFilterName();
    return (
      <bs.VSplit updateOnResize>
        <bs.VSplitPane title={T('Partner Info')} width={550}>
          {this.createEditor()}
        </bs.VSplitPane>
        <bs.VSplitPane title={T('Tabs')}>
          <bs.TabPane key={`tabs-${this.viewId}`}>
            {
              this.hasRenderTMSBillList() ?
                <bs.Tab name={'tms-bills-tab'} label={T('TMS Bills')} active>
                  <UITMSBillList
                    appContext={appContext} pageContext={pageContext}
                    type={'page'} readOnly
                    plugin={
                      new UITMSBillListPlugin()
                        .withTMSPartnerTypeId(filterName, refId)
                        .withDataScope(appContext.getUserDataScope())
                    } />
                </bs.Tab> : <></>
            }
            {...this.addTabs()}
          </bs.TabPane>
        </bs.VSplitPane>
      </bs.VSplit>
    )
  }
}

export class UILoginPermissionListPlugin extends entity.DbEntityListPlugin {
  constructor() {
    super([]);
    this.backend = {
      context: 'company',
      service: 'TMSPartnerPermissionService',
      searchMethod: 'searchPartnerPermissions',
      deleteMethod: 'deletePartnerPermissions',
      entityLabel: 'TMS Partner Permissions'
    }
    this.searchParams = {
      "params": {},
      "filters": [
        ...sql.createSearchFilter()
      ],
      "optionFilters": [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      maxReturn: 3000,
    }
  }

  withPartnerId(partnerId: number) {
    this.addSearchParam("tmsPartnerId", partnerId);
    return this;
  }

  backendDelete(uiList: UILoginPermissionList, permissionIds: Array<number>) {
    let { appContext, onPostCommit } = uiList.props;

    appContext.createHttpBackendCall('TMSPartnerPermissionService', 'deletePartnerPermissions', { ids: permissionIds })
      .withSuccessData((data: any) => {
        appContext.addOSNotification("success", T('Delete Success'));
        uiList.getVGridContext().model.removeSelectedDisplayRecords();
        uiList.forceUpdate();
        if (onPostCommit) {
          onPostCommit(data);
        }
      })
      .call();
  }

  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { 'params': this.searchParams }).call();
  }
}

export interface UILoginPermissionListProps extends entity.DbEntityListProps {
  tmsPartnerId: number,
  onPostCommit?: (data: any) => void
}

export class UILoginPermissionList extends entity.DbEntityList<UILoginPermissionListProps> {

  toolbarAddPermission(readOnly: boolean) {
    if (readOnly) return [];
    let { plugin } = this.props;

    let actions: Array<grid.VGridActionConfig> = [
      {
        name: "add-read", label: T('Read'), icon: FeatherIcon.Eye,
        onClick: (ctx: grid.VGridContext) => {
          let uiRoot = ctx.uiRoot as UILoginPermissionList;
          uiRoot.onAdd("Read", ctx, plugin);
        }
      },
      {
        name: "add-write", label: T('Write'), icon: FeatherIcon.Plus,
        onClick: (ctx: grid.VGridContext) => {
          let uiRoot = ctx.uiRoot as UILoginPermissionList;
          uiRoot.onAdd("Write", ctx, plugin);
        }
      },
      {
        name: "add-moderator", label: T('Moderator'), icon: FeatherIcon.Plus,
        onClick: (ctx: grid.VGridContext) => {
          let uiRoot = ctx.uiRoot as UILoginPermissionList;
          uiRoot.onAdd("Moderator", ctx, plugin);
        }
      },
      {
        name: "add-admin", label: T('Admin'), icon: FeatherIcon.Plus,
        onClick: (ctx: grid.VGridContext) => {
          let uiRoot = ctx.uiRoot as UILoginPermissionList;
          uiRoot.onAdd("Admin", ctx, plugin);
        }
      },
    ]
    return actions;
  }

  createVGridConfig() {
    let { pageContext, readOnly } = this.props;
    let writeCap = pageContext.hasUserWriteCapability() && !readOnly;
    let config: grid.VGridConfig = {
      record: {
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          entity.DbEntityListConfigTool.FIELD_ON_SELECT('label', T('Label'), 150),
          { name: 'capability', label: T('Capability'), width: 110 },
          { name: 'type', label: T('Type'), width: 120, state: { visible: false } },
        ]
      },
      toolbar: {
        actions: [
          ...this.toolbarAddPermission(!writeCap),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!writeCap, T("Remove")),
        ]
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      }
    }
    return config;
  }

  onAdd(capability: any, ctx: grid.VGridContext, plugin: entity.DbEntityListPlugin) {
    let uiRoot = ctx.uiRoot as UILoginPermissionList;
    let { appContext, pageContext, tmsPartnerId, onPostCommit } = uiRoot.props;

    let onMultiSelect = (employees: Array<any>, pageCtx: app.PageContext) => {
      let permissions: Array<any> = [];
      for (let employee of employees) {
        let permission = {
          tmsPartnerId: tmsPartnerId,
          role: 'CUSTOMER_SERVICE',
          type: 'Employee',
          userId: employee.accountId,
          label: employee.label,
          capability: capability
        };
        permissions.push(permission);
      }
      appContext
        .createHttpBackendCall('TMSPartnerPermissionService', 'savePartnerPermissions', { permissions: permissions })
        .withSuccessData((data: any) => {
          plugin.getListModel().addRecords(data);
          ctx.getVGrid().forceUpdateView();
          pageCtx.back();
          if (onPostCommit) {
            onPostCommit(data);
          }
        })
        .withSuccessNotification('success', T('Add Permissions Success'))
        .call()
    }
    let excludeRecordFilter = new entity.ExcludeRecordFilter(plugin.getListModel().getRecords(), "loginId", "loginId");
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <module.company.hr.UIEmployeeList
          type={'selector'}
          plugin={new module.company.hr.UIEmployeeListPlugin().withRecordFilter(excludeRecordFilter)}
          appContext={appCtx} pageContext={pageCtx} readOnly={true}
          onSelect={(_appCtx, _pageCtx, employee) => onMultiSelect([employee], _pageCtx)}
          onMultiSelect={(_appCtx, _pageCtx, employees) => onMultiSelect(employees, _pageCtx)} />
      );
    }
    pageContext.createPopupPage("add-employees", T('Add Employees'), createAppPage, { size: "lg" });
  }
}