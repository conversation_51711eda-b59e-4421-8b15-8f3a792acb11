import React from 'react';
import { entity, sql, bs } from "@datatp-ui/lib";
import * as FeatherIcon from 'react-feather'


import { T } from "./backend";

interface BBRefResourceProps extends entity.BBRefEntityProps {
  resourceType: string;
  beanIdField: string;
  beanLabelField: string;
  refResourceBy: 'id' | 'identifier';
}
export class BBRefResource extends entity.BBRefEntity<BBRefResourceProps> {
  createPlugin() {
    let { resourceType, beanIdField, beanLabelField, refResourceBy } = this.props;
    let loadMethod = 'loadResourceById';
    if (refResourceBy == 'identifier') {
      loadMethod = 'loadResourceByIdentifier';
    }
    let config: entity.BBRefEntityPluginConfig = {
      backend: {
        context: 'system',
        service: 'ResourceService',
        searchMethod: 'searchResourceByType',
        createSearchParams: (searchParams: sql.SqlSearchParams, _userInput: string) => {
          searchParams.params = { 'resourceType': resourceType };
          return searchParams;
        },
        loadMethod: loadMethod,
        createLoadParams: (idValue) => {
          let params = { resourceType: resourceType, id: idValue };
          return params;
        }
      },
      bean: {
        idField: beanIdField,
        labelField: beanLabelField
      },
      refEntity: {
        idField: refResourceBy,
        labelField: 'label',
        labelFunc: (opt: any) => {
          return `${opt['label']}(${opt['identifier']})`
        },

        vgridRecordConfig: {
          fields: [
            entity.DbEntityListConfigTool.FIELD_INDEX(),
            entity.DbEntityListConfigTool.FIELD_ON_SELECT('label', T('Label'), 350),
            { name: 'identifier', label: T('Identifier'), width: 150 },
            { name: 'resourceType', label: T('Resource Type'), width: 150 },
            { name: 'ownerType', label: T('Owner Type'), width: 150 },
            { name: 'ownerIdentifier', label: T('Owner Identifier'), width: 150 },
            { name: 'ownerId', label: T('Owner Id'), width: 150 },
          ]
        }
      },
    }
    return new entity.BBRefEntityPlugin(config);
  }
}

interface BBRefMultiResourceProps extends entity.BBRefMultiEntityProps {
  resourceType: string;
  beanIdField: string;
  beanLabelField: string;
  refResourceBy: 'id' | 'identifier';
}

export class BBRefMultiResource extends entity.BBRefMultiEntity<BBRefMultiResourceProps> {

  renderToggle() {
    let { label, bean, disable } = this.props;
    let beanConfig = this.plugin.config.bean;
    let refEntities = bean as Array<any>;
    let refEntityWidgets = [];
    for (let i = 0; i < refEntities.length; i++) {
      let bean = refEntities[i];
      let label = beanConfig.labelField ? bean[beanConfig.labelField] : bean[beanConfig.idField];

      let widget = (
        <div key={i} className="flex-hbox align-items-center justify-content-between border border-dashed rounded px-2 py-1 w-100 mb-1" style={{ minWidth: "150px" }}>
          <div className='flex-vbox'>
            <bs.Button laf='link' className="px-2 text-truncate text-start py-0"
              onClick={() => this.showRefEntityInfo(bean)} >
              <div className="flex-hbox">
                <span className="text-secondary">{label || 'N/A'} </span>
              </div>
            </bs.Button>
          </div>
          <bs.Button hidden={disable} laf='link' className="text-danger px-2 border-start py-0"
            onClick={() => this.onRemove(i)} >
            <FeatherIcon.X size={14} />
          </bs.Button>
        </div>
      );
      refEntityWidgets.push(widget);
    }

    let inputUI = (
      <div className='flex-hbox flex-wrap'>
        {refEntityWidgets}
        <div className='flex-hbox flex-nowrap my-1 py-0' id={`popover-trigger-${this.popoverId}`}>
          {this.renderInput()}
          {this.renderSearchRefEntities()}
        </div>
      </div>
    );

    if (!label) return inputUI;

    return (
      <div className='bb-field' >
        <label className='form-label'>
          {label}
        </label>
        {inputUI}
      </div>
    );
  }


  protected createPlugin() {
    let { resourceType, beanIdField, beanLabelField, refResourceBy } = this.props;
    let loadMethod = 'loadResourceById';
    if (refResourceBy == 'identifier') {
      loadMethod = 'loadResourceByIdentifier';
    }
    let config: entity.BBRefEntityPluginConfig = {
      backend: {
        context: 'system',
        service: 'ResourceService',
        searchMethod: 'searchResourceByType',
        createSearchParams: (searchParams: sql.SqlSearchParams, _userInput: string) => {
          searchParams.params = { 'resourceType': resourceType };
          return searchParams;
        },
        loadMethod: loadMethod,
        createLoadParams: (idValue) => {
          let params = { resourceType: resourceType, id: idValue };
          return params;
        }
      },
      bean: {
        idField: beanIdField,
        labelField: beanLabelField
      },
      refEntity: {
        idField: refResourceBy,
        labelField: 'label',
        labelFunc: (opt: any) => {
          return `${opt['label']}(${opt['identifier']})`
        },

        vgridRecordConfig: {
          fields: [
            entity.DbEntityListConfigTool.FIELD_INDEX(),
            entity.DbEntityListConfigTool.FIELD_ON_SELECT('label', T('Label'), 350),
            { name: 'identifier', label: T('Identifier'), width: 150 },
            { name: 'resourceType', label: T('Resource Type'), width: 150 },
            { name: 'ownerType', label: T('Owner Type'), width: 150 },
            { name: 'ownerIdentifier', label: T('Owner Identifier'), width: 150 },
            { name: 'ownerId', label: T('Owner Id'), width: 150 },
          ]
        }
      },
    }
    return new entity.BBRefMultiEntityPlugin(config);
  }
}