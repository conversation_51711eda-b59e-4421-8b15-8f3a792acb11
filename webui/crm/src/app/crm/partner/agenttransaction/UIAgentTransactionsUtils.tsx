import React from "react";
import * as FeatherIcon from 'react-feather';
import { bs, app, input, entity } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';

export type Space = 'User' | 'Company' | 'System'

export interface ƯAgentTransactionGroupBySelectorProps extends app.AppComponentProps {
  onModify?: entity.EntityOnModify;
  groupBy?: { label: string, value: string };
}

interface WAgentTransactionGroupBySelectorState {
  groupBy: { label: string, value: string };
}
export class WAgentTransactionGroupBySelector extends app.AppComponent<ƯAgentTransactionGroupBySelectorProps, WAgentTransactionGroupBySelectorState> {

  constructor(props: ƯAgentTransactionGroupBySelectorProps) {
    super(props);
    const { groupBy } = this.props;
    if (groupBy) {
      this.state = { groupBy: groupBy }
    } else {
      this.state = {
        groupBy: {
          label: 'Agent',
          value: 'AGENT'
        }
      }
    }
  }

  onInputChange = (label: string, value: string) => {
    const { onModify } = this.props;
    const groupBy: any = { label, value }
    this.setState({ groupBy: groupBy })
    if (onModify) onModify(groupBy, 'groupBy', null, label);
  }

  render(): React.ReactNode {
    const { groupBy } = this.state;

    return (
      <bs.Popover className="flex-vbox flex-grow-0" placement="bottom-start" closeOnTrigger=".btn" offset={[0, 5]}>
        <bs.PopoverToggle laf="secondary" outline className="me-1 p-1" style={{ color: '#6c757d' }}>
          <FeatherIcon.Layers size={14} className="me-1" />
          {groupBy.label}
          <FeatherIcon.ChevronDown size={14} className="ms-1" />
        </bs.PopoverToggle>
        <bs.PopoverContent>
          <div className='flex-vbox align-items-center gap-1' style={{ width: '180px' }}>

            <bs.Button laf="secondary" outline size="sm" className="me-1 py-2 w-100 text-start"
              onClick={() => this.onInputChange('Agent', 'AGENT')}>
              <FeatherIcon.Users size={14} className="me-1" />
              {`Agent`}
            </bs.Button>

            <bs.Button laf="secondary" outline size="sm" className="me-1 py-2 w-100 text-start"
              onClick={() => this.onInputChange('Continent', 'CONTINENT')}>
              <FeatherIcon.MapPin size={14} className="me-1" />
              {`Continent`}
            </bs.Button>

            <bs.Button laf="secondary" outline size="sm" className="me-1 py-2 w-100 text-start"
              onClick={() => this.onInputChange('Country', 'COUNTRY')}>
              <FeatherIcon.MapPin size={14} className="me-1" />
              {`Country`}
            </bs.Button>

            <bs.Button laf="secondary" outline size="sm" className="me-1 py-2 w-100 text-start"
              onClick={() => this.onInputChange('Network', 'NETWORK')}>
              <FeatherIcon.Globe size={14} className="me-1" />
              {`Network`}
            </bs.Button>

          </div>
        </bs.PopoverContent>
      </bs.Popover>
    )
  }
}

export type AgentTransactionReportParams = {
  searchPattern: string;
  agentCode: string;
  country: string;
  continent: string;
  source: string;
  fromLocationCode: string;
  toLocationCode: string;
  companyBranch: string;
  shipmentType: string;
}

export interface WAgentTransactionReportFilterProps extends app.AppComponentProps {
  onModify?: entity.EntityOnModify;
  params?: AgentTransactionReportParams
}

interface WAgentTransactionReportFilterState {
  params: AgentTransactionReportParams
}
export class WAgentTransactionReportFilter extends app.AppComponent<WAgentTransactionReportFilterProps, WAgentTransactionReportFilterState> {

  constructor(props: WAgentTransactionReportFilterProps) {
    super(props);
    const { params } = this.props;
    if (params) {
      this.state = { params: params }
    } else {
      this.state = {
        params: {
          searchPattern: '',
          agentCode: '',
          country: '',
          continent: '',
          source: '',
          fromLocationCode: '',
          toLocationCode: '',
          companyBranch: '',
          shipmentType: ''
        }
      }
    }
  }

  onInputChange = (bean: any, field: string, oldVal: any, newVal: any) => {
    const { onModify } = this.props;
    this.setState({ params: bean })
    if (onModify) onModify(bean, field, oldVal, newVal);
  }

  clearFilter = () => {
    const { params } = this.state;
    params.searchPattern = '';
    params.fromLocationCode = '';
    params.toLocationCode = '';
    this.onInputChange(params, 'searchPattern', '', '');
  }

  render(): React.ReactNode {
    const { appContext, pageContext } = this.props;
    const { params } = this.state;

    let color = '#6c757d';
    if (params.searchPattern || params.fromLocationCode || params.toLocationCode) {
      color = '#e5780b';
    }

    return (
      <bs.Popover className="flex-vbox flex-grow-0" placement="bottom-start" closeOnTrigger=".btn" offset={[-300, 5]}>
        <bs.PopoverToggle laf="secondary" outline className="me-1 p-1" style={{ color: color }}>
          <FeatherIcon.Layers size={14} className="me-1" />
          {'Report Filter'}
          <FeatherIcon.ChevronDown size={14} className="ms-1" />
        </bs.PopoverToggle>
        <bs.PopoverContent>
          <div className='flex-vbox p-1' style={{ width: '500px', minHeight: '100px' }}>
            <div className="flex-vbox">
              <bs.Row>
                <bs.Col span={12}>
                  <input.BBStringField
                    bean={params} field='searchPattern' label={("Partner Name Or Code")} onInputChange={this.onInputChange}
                    onKeyDown={(_winput: input.WInput, event: React.KeyboardEvent, _currInput: any) => {
                      if (event.key === 'Enter') {
                        params.searchPattern = _currInput;
                        this.onInputChange(params, 'searchPattern', '', _currInput);
                      }
                    }} />
                </bs.Col>
              </bs.Row>

              <div className="bb-field">
                <label className="form-label">Route</label>
                <div className="flex-hbox">

                  <div className="w-50">
                    <module.settings.BBRefLocation label={''} placeholder={'From Location Code'} minWidth={400}
                      appContext={appContext} pageContext={pageContext} bean={params}
                      beanIdField={'fromLocationCode'}
                      beanLabelField={'fromLocationCode'}
                      locationTypes={['Port', 'Airport']}
                      refLocationBy='code'
                      placement='bottom-start'
                      offset={[0, 10]}
                      onPostUpdate={(_inputUI: React.Component, bean: any, selOpt: any, _userInput: string) => {
                        bean['fromLocationCode'] = selOpt['code'];
                        this.onInputChange(params, 'fromLocationCode', '', selOpt['code']);
                      }}
                    />
                  </div>
                  <div className="w-50">
                    <module.settings.BBRefLocation label={''} placeholder={'To Location Code'} minWidth={400}
                      appContext={appContext} pageContext={pageContext} bean={params}
                      beanIdField={'toLocationCode'}
                      beanLabelField={'toLocationCode'}
                      locationTypes={['Port', 'Airport']}
                      refLocationBy='code'
                      placement='bottom-start'
                      offset={[0, 10]}
                      onPostUpdate={(_inputUI: React.Component, bean: any, selOpt: any, _userInput: string) => {
                        bean['toLocationCode'] = selOpt['code'];
                        this.onInputChange(params, 'toLocationCode', '', selOpt['code']);
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="flex-hbox flex-grow-0 justify-content-end align-items-center mt-2 py-1 border-top">
              <bs.Button laf='secondary' outline className="px-2 py-1 mx-1" style={{ width: 100 }}
                onClick={this.clearFilter}>
                <FeatherIcon.Trash2 size={12} /> Clear
              </bs.Button>
            </div>
          </div>
        </bs.PopoverContent>
      </bs.Popover >
    )
  }
}
