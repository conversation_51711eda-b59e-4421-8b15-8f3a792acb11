import { grid } from '@datatp-ui/lib';

export type Space = 'User' | 'Company' | 'System'

export class AgentTransactionsTreePlugin extends grid.TreeDisplayModelPlugin {
  override setCollapse(record: grid.TreeRecord) {
    record.collapse = true;
  }
}

export class CountryContinentTreePlugin extends AgentTransactionsTreePlugin {

  buildTreeRecords(records: Array<any>): Array<grid.TreeRecord> {
    let treeRecords: Array<any> = [];
    let idCounter = 1;

    // Group by Continent
    const continentGroups = records.reduce((acc, record) => {
      const continent = record['continent'] || 'OTHERS';
      if (!acc[continent]) acc[continent] = [];
      acc[continent].push(record);
      return acc;
    }, {} as Record<string, Array<any>>);

    const sortedCountries = Object.entries(continentGroups)
      .sort(([a], [b]) => a.localeCompare(b));

    // Build tree structure
    for (let [continent, continentRecords] of sortedCountries as [string, Array<any>][]) {
      // Sum value from continentRecords
      const total = continentRecords.reduce((acc: {
        freehandCount: number;
        nominatedCount: number;
        freehandCbm: number;
        freehandCw: number;
        freehandCont20: number;
        freehandCont40: number;
        freehandCont45: number;
        freehandTeus: number;
        nominatedCbm: number;
        nominatedCw: number;
        nominatedCont20: number;
        nominatedCont40: number;
        nominatedCont45: number;
        nominatedTeus: number;
      }, record: any) => {
        acc.freehandCount += Number(record['freehandCount']) || 0;
        acc.nominatedCount += Number(record['nominatedCount']) || 0;
        acc.freehandCbm += Number(record['freehandCbm']) || 0;
        acc.freehandCw += Number(record['freehandCw']) || 0;
        acc.freehandCont20 += Number(record['freehandCont20']) || 0;
        acc.freehandCont40 += Number(record['freehandCont40']) || 0;
        acc.freehandCont45 += Number(record['freehandCont45']) || 0;
        acc.freehandTeus += Number(record['freehandTeus']) || 0;
        acc.nominatedCbm += Number(record['nominatedCbm']) || 0;
        acc.nominatedCw += Number(record['nominatedCw']) || 0;
        acc.nominatedCont20 += Number(record['nominatedCont20']) || 0;
        acc.nominatedCont40 += Number(record['nominatedCont40']) || 0;
        acc.nominatedCont45 += Number(record['nominatedCont45']) || 0;
        acc.nominatedTeus += Number(record['nominatedTeus']) || 0;
        return acc;
      }, {
        freehandCount: 0,
        nominatedCount: 0,
        freehandCbm: 0,
        freehandCw: 0,
        freehandCont20: 0,
        freehandCont40: 0,
        freehandCont45: 0,
        freehandTeus: 0,
        nominatedCbm: 0,
        nominatedCw: 0,
        nominatedCont20: 0,
        nominatedCont40: 0,
        nominatedCont45: 0,
        nominatedTeus: 0,
      });

      let continentNode: any = {
        id: idCounter++,
        parentId: undefined,
        groupType: 'CONTINENT',
        label: continent,
        agentCode: undefined,
        freehandCount: total.freehandCount,
        nominatedCount: total.nominatedCount,
        freehandTeus: total.freehandTeus,
        freehandCont20: total.freehandCont20,
        freehandCont40: total.freehandCont40,
        freehandCont45: total.freehandCont45,
        freehandCbm: total.freehandCbm,
        freehandCw: total.freehandCw,
        nominatedTeus: total.nominatedTeus,
        nominatedCont20: total.nominatedCont20,
        nominatedCont40: total.nominatedCont40,
        nominatedCont45: total.nominatedCont45,
        nominatedCbm: total.nominatedCbm,
        nominatedCw: total.nominatedCw,
      }
      treeRecords.push(continentNode);

      // Group by Country
      const countryGroups = continentRecords.reduce((acc, record) => {
        const country = record['countryLabel'] || 'N/A';
        if (!acc[country]) acc[country] = [];
        acc[country].push(record);
        return acc;
      }, {} as Record<string, Array<any>>);

      // Calculate total counts for each country and sort by freehandCount + nominatedCount (descending)
      const sortedCountries = Object.entries(countryGroups)
        .map(([country, countryRecords]) => {
          const total = (countryRecords as Array<any>).reduce((acc: { freehandCount: number; nominatedCount: number }, record: any) => {
            acc.freehandCount += Number(record['freehandCount']) || 0;
            acc.nominatedCount += Number(record['nominatedCount']) || 0;
            return acc;
          }, { freehandCount: 0, nominatedCount: 0 });

          return {
            country,
            countryRecords: countryRecords as Array<any>,
            totalCount: total.freehandCount + total.nominatedCount
          };
        })
        .sort((a, b) => b.totalCount - a.totalCount); // Sort descending by total count

      for (let { country, countryRecords } of sortedCountries) {
        // Sum value from countryRecords
        const total = countryRecords.reduce((acc: {
          freehandCount: number;
          nominatedCount: number;
          freehandCbm: number;
          freehandCw: number;
          freehandCont20: number;
          freehandCont40: number;
          freehandCont45: number;
          freehandTeus: number;
          nominatedCbm: number;
          nominatedCw: number;
          nominatedCont20: number;
          nominatedCont40: number;
          nominatedCont45: number;
          nominatedTeus: number;
        }, record: any) => {
          acc.freehandCount += Number(record['freehandCount']) || 0;
          acc.nominatedCount += Number(record['nominatedCount']) || 0;
          acc.freehandCbm += Number(record['freehandCbm']) || 0;
          acc.freehandCw += Number(record['freehandCw']) || 0;
          acc.freehandCont20 += Number(record['freehandCont20']) || 0;
          acc.freehandCont40 += Number(record['freehandCont40']) || 0;
          acc.freehandCont45 += Number(record['freehandCont45']) || 0;
          acc.freehandTeus += Number(record['freehandTeus']) || 0;
          acc.nominatedCbm += Number(record['nominatedCbm']) || 0;
          acc.nominatedCw += Number(record['nominatedCw']) || 0;
          acc.nominatedCont20 += Number(record['nominatedCont20']) || 0;
          acc.nominatedCont40 += Number(record['nominatedCont40']) || 0;
          acc.nominatedCont45 += Number(record['nominatedCont45']) || 0;
          acc.nominatedTeus += Number(record['nominatedTeus']) || 0;
          return acc;
        }, {
          freehandCount: 0,
          nominatedCount: 0,
          freehandCbm: 0,
          freehandCw: 0,
          freehandCont20: 0,
          freehandCont40: 0,
          freehandCont45: 0,
          freehandTeus: 0,
          nominatedCbm: 0,
          nominatedCw: 0,
          nominatedCont20: 0,
          nominatedCont40: 0,
          nominatedCont45: 0,
          nominatedTeus: 0,
        });

        let countryNode: any = {
          id: idCounter++,
          parentId: continentNode.id,
          groupType: 'COUNTRY',
          label: country,
          agentCode: undefined,
          freehandCount: total.freehandCount,
          nominatedCount: total.nominatedCount,
          freehandTeus: total.freehandTeus,
          freehandCont20: total.freehandCont20,
          freehandCont40: total.freehandCont40,
          freehandCont45: total.freehandCont45,
          freehandCbm: total.freehandCbm,
          freehandCw: total.freehandCw,
          nominatedTeus: total.nominatedTeus,
          nominatedCont20: total.nominatedCont20,
          nominatedCont40: total.nominatedCont40,
          nominatedCont45: total.nominatedCont45,
          nominatedCbm: total.nominatedCbm,
          nominatedCw: total.nominatedCw,
        }
        treeRecords.push(countryNode);

        // Sort countryRecords by agentName before creating nodes
        const sortedAgentRecords = countryRecords.sort((a, b) => {
          const agentNameA = a['agentName'] || '';
          const agentNameB = b['agentName'] || '';
          return agentNameA.localeCompare(agentNameB);
        });

        for (let record of sortedAgentRecords) {
          let agentNode: any = {
            id: idCounter++,
            parentId: countryNode.id,
            groupType: 'AGENT',
            label: record['agentName'],
            agentCode: record['agentCode'],
            freehandCount: record['freehandCount'],
            nominatedCount: record['nominatedCount'],
            freehandTeus: record['freehandTeus'],
            freehandCont20: record['freehandCont20'],
            freehandCont40: record['freehandCont40'],
            freehandCont45: record['freehandCont45'],
            freehandCbm: record['freehandCbm'],
            freehandCw: record['freehandCw'],
            nominatedTeus: record['nominatedTeus'],
            nominatedCont20: record['nominatedCont20'],
            nominatedCont40: record['nominatedCont40'],
            nominatedCont45: record['nominatedCont45'],
            nominatedCbm: record['nominatedCbm'],
            nominatedCw: record['nominatedCw'],
            latestContact: record['latestContact'],
            latestTransactionId: record['latestTransactionId'],
            dateCreated: record['dateCreated'],
            workPhone: record['workPhone'],
            fax: record['fax'],
            address: record['address'],
            source: record['source'],
            continent: record['continent'],
            countryLabel: record['countryLabel'],
          }
          treeRecords.push(agentNode);
        }
      }
    }

    grid.initRecordStates(treeRecords);
    return super.buildTreeRecords(treeRecords);
  }
}

export class NetworkTreePlugin extends AgentTransactionsTreePlugin {

  buildTreeRecords(records: Array<any>): Array<grid.TreeRecord> {
    let treeRecords: Array<any> = [];
    let idCounter = 1;

    // Group by Network
    const networkGroups = records.reduce((acc, record) => {
      const network = record['source'] || 'N/A';
      if (!acc[network]) acc[network] = [];
      acc[network].push(record);
      return acc;
    }, {} as Record<string, Array<any>>);

    const sortedNetworks = Object.entries(networkGroups)
      .sort(([a], [b]) => a.localeCompare(b));

    // Build tree structure
    for (let [network, networkRecords] of sortedNetworks as [string, Array<any>][]) {
      // Sum value from networkRecords
      const total = networkRecords.reduce((acc: {
        freehandCount: number;
        nominatedCount: number;
        freehandCbm: number;
        freehandCw: number;
        freehandCont20: number;
        freehandCont40: number;
        freehandCont45: number;
        freehandTeus: number;
        nominatedCbm: number;
        nominatedCw: number;
        nominatedCont20: number;
        nominatedCont40: number;
        nominatedCont45: number;
        nominatedTeus: number;
      }, record: any) => {
        acc.freehandCount += Number(record['freehandCount']) || 0;
        acc.nominatedCount += Number(record['nominatedCount']) || 0;
        acc.freehandCbm += Number(record['freehandCbm']) || 0;
        acc.freehandCw += Number(record['freehandCw']) || 0;
        acc.freehandCont20 += Number(record['freehandCont20']) || 0;
        acc.freehandCont40 += Number(record['freehandCont40']) || 0;
        acc.freehandCont45 += Number(record['freehandCont45']) || 0;
        acc.freehandTeus += Number(record['freehandTeus']) || 0;
        acc.nominatedCbm += Number(record['nominatedCbm']) || 0;
        acc.nominatedCw += Number(record['nominatedCw']) || 0;
        acc.nominatedCont20 += Number(record['nominatedCont20']) || 0;
        acc.nominatedCont40 += Number(record['nominatedCont40']) || 0;
        acc.nominatedCont45 += Number(record['nominatedCont45']) || 0;
        acc.nominatedTeus += Number(record['nominatedTeus']) || 0;
        return acc;
      }, {
        freehandCount: 0,
        nominatedCount: 0,
        freehandCbm: 0,
        freehandCw: 0,
        freehandCont20: 0,
        freehandCont40: 0,
        freehandCont45: 0,
        freehandTeus: 0,
        nominatedCbm: 0,
        nominatedCw: 0,
        nominatedCont20: 0,
        nominatedCont40: 0,
        nominatedCont45: 0,
        nominatedTeus: 0,
      });

      let networkNode: any = {
        id: idCounter++,
        parentId: undefined,
        groupType: 'NETWORK',
        label: network,
        agentCode: undefined,
        freehandCount: total.freehandCount,
        nominatedCount: total.nominatedCount,
        freehandTeus: total.freehandTeus,
        freehandCont20: total.freehandCont20,
        freehandCont40: total.freehandCont40,
        freehandCont45: total.freehandCont45,
        freehandCbm: total.freehandCbm,
        freehandCw: total.freehandCw,
        nominatedTeus: total.nominatedTeus,
        nominatedCont20: total.nominatedCont20,
        nominatedCont40: total.nominatedCont40,
        nominatedCont45: total.nominatedCont45,
        nominatedCbm: total.nominatedCbm,
        nominatedCw: total.nominatedCw,
      }
      treeRecords.push(networkNode);

      // Sort networkRecords by agentName before creating nodes
      const sortedAgentRecords = networkRecords.sort((a, b) => {
        const agentNameA = a['agentName'] || '';
        const agentNameB = b['agentName'] || '';
        return agentNameA.localeCompare(agentNameB);
      });

      for (let record of sortedAgentRecords) {
        let agentNode: any = {
          id: idCounter++,
          parentId: networkNode.id,
          groupType: 'AGENT',
          label: record['agentName'],
          agentCode: record['agentCode'],
          freehandCount: record['freehandCount'],
          nominatedCount: record['nominatedCount'],
          freehandTeus: record['freehandTeus'],
          freehandCont20: record['freehandCont20'],
          freehandCont40: record['freehandCont40'],
          freehandCont45: record['freehandCont45'],
          freehandCbm: record['freehandCbm'],
          freehandCw: record['freehandCw'],
          nominatedTeus: record['nominatedTeus'],
          nominatedCont20: record['nominatedCont20'],
          nominatedCont40: record['nominatedCont40'],
          nominatedCont45: record['nominatedCont45'],
          nominatedCbm: record['nominatedCbm'],
          nominatedCw: record['nominatedCw'],
          latestContact: record['latestContact'],
          latestTransactionId: record['latestTransactionId'],
          dateCreated: record['dateCreated'],
          workPhone: record['workPhone'],
          fax: record['fax'],
          address: record['address'],
          source: record['source'],
          continent: record['continent'],
          countryLabel: record['countryLabel'],
        }
        treeRecords.push(agentNode);
      }
    }

    grid.initRecordStates(treeRecords);
    return super.buildTreeRecords(treeRecords);
  }
}

export class AgentTreePlugin extends AgentTransactionsTreePlugin {
  override setCollapse(record: grid.TreeRecord) {
    record.collapse = false;
  }

  buildTreeRecords(records: Array<any>): Array<grid.TreeRecord> {
    let treeRecords: Array<any> = [];
    let idCounter = 1;

    const sortedAgentRecords = records.sort((a, b) => {
      const agentNameA = a['agentName'] || '';
      const agentNameB = b['agentName'] || '';
      return agentNameA.localeCompare(agentNameB);
    });

    for (let record of sortedAgentRecords) {
      let agentNode: any = {
        id: idCounter++,
        parentId: undefined,
        groupType: 'AGENT',
        label: record['agentName'],
        agentCode: record['agentCode'],
        freehandCount: record['freehandCount'],
        nominatedCount: record['nominatedCount'],
        freehandTeus: record['freehandTeus'],
        freehandCont20: record['freehandCont20'],
        freehandCont40: record['freehandCont40'],
        freehandCont45: record['freehandCont45'],
        freehandCbm: record['freehandCbm'],
        freehandCw: record['freehandCw'],
        nominatedTeus: record['nominatedTeus'],
        nominatedCont20: record['nominatedCont20'],
        nominatedCont40: record['nominatedCont40'],
        nominatedCont45: record['nominatedCont45'],
        nominatedCbm: record['nominatedCbm'],
        nominatedCw: record['nominatedCw'],
        latestContact: record['latestContact'],
        latestTransactionId: record['latestTransactionId'],
        dateCreated: record['dateCreated'],
        workPhone: record['workPhone'],
        fax: record['fax'],
        address: record['address'],
        source: record['source'],
        continent: record['continent'],
        countryLabel: record['countryLabel'],
      }
      treeRecords.push(agentNode);

    }

    grid.initRecordStates(treeRecords);
    return super.buildTreeRecords(treeRecords);
  }
}

export function getTreePlugin(groupedBy: string): AgentTransactionsTreePlugin {
  if (groupedBy === 'COUNTRY') {
    return new CountryContinentTreePlugin();
  } else if (groupedBy === 'NETWORK') {
    return new NetworkTreePlugin();
  }
  return new AgentTreePlugin();
}