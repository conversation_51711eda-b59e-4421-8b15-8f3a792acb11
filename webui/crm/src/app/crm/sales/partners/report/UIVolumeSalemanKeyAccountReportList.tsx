import React from "react";

import * as FeatherIcon from 'react-feather'
import { util, grid, bs, entity, input, sql } from '@datatp-ui/lib';
import { T } from "../../backend";

export class UIVolumeSalemanKeyAccountReportPlugin extends entity.DbEntityListPlugin {
  constructor() {
    super([]);

    this.backend = {
      service: 'PartnerReportService',
      searchMethod: 'searchVolumeSalemanKeyAccountReport',
    }
    this.searchParams = {
      params: {
      },
      filters: [...sql.createSearchFilter()],
      maxReturn: 1000
    }
  }

  loadData(uiList: entity.DbEntityList<any>): void {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }

  withReportedDate(reportedDateFrom: string, reportedDateTo: string) {
    this.addSearchParam("fromDate", reportedDateFrom);
    this.addSearchParam("toDate", reportedDateTo);
    return this;
  }
}
export interface UIVolumeSalemanKeyAccountReportListProps extends entity.DbEntityListProps {
  hideSubtotalColumns?: boolean;
}

export class UIVolumeSalemanKeyAccountReportList extends entity.DbEntityList<UIVolumeSalemanKeyAccountReportListProps> {

  createVGridConfig() {
    let { hideSubtotalColumns = false } = this.props;
    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 40,
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'transactionId', label: T('Transaction ID'), width: 160, filterable: true,
          },
          {
            name: 'transactionDate', label: T('Created Date'), width: 130, filterable: true,
            format: util.text.formater.compactDate
          },
          {
            name: 'reportDate', label: T('ETD/ ETA'), width: 130, filterable: true,
            format: util.text.formater.compactDate
          },
          { name: 'shipmentType', label: T('Shipment Type'), width: 150, filterable: true, },
          { name: 'typeOfService', label: T('Type of Service'), width: 200, filterable: true, },
          { name: 'hawbNo', label: T('Hawb No'), width: 200, filterable: true, },
          { name: 'customerName', label: T('Customer Name'), width: 200, filterable: true, },

          { name: 'hawbGw', label: T('G.W'), width: 120, },
          { name: 'hawbCw', label: T('C.W'), width: 120, },
          { name: 'hawbCbm', label: T('CBM'), width: 120, },
          { name: 'containerSize', label: T('Container Size'), width: 200, },
          ...(!hideSubtotalColumns ? [
            { name: 'subtotalSellingVnd', label: T('SubTotal Selling (VND)'), width: 200, },
            { name: 'subtotalBuyingVnd', label: T('SubTotal Buying (VND)'), width: 200, },
            { name: 'subtotalOtherDebitVnd', label: T('Other Debit (VND)'), width: 200, },
            { name: 'subtotalOtherCreditVnd', label: T('Other Credit (VND)'), width: 200, },
          ] : []),
          { name: 'agentCode', label: T('Agent Code'), width: 100, filterable: true, },
          { name: 'agentName', label: T('Agent Name'), width: 200, },
        ],
      },
      toolbar: {
        // hide: true,
        actions: [
          {
            name: "export-xlsx", label: 'Export Xlsx',
            createComponent: function (ctx: grid.VGridContext) {
              let uiRoot = ctx.uiRoot as any;
              const { appContext, pageContext } = uiRoot.props;
              return (<entity.XlsxExportButton appContext={appContext} pageContext={pageContext} context={ctx} />)
            }
          },
        ],
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
        }
      }
    }
    return config;
  }

  render(): React.JSX.Element {
    if (this.isLoading()) return this.renderLoading();
    return this.renderUIGrid();
  }

}