import { util } from '@datatp-ui/lib';

import {
  BookingChargeType, FreightTerm, TransportationMode
} from "../../common/model";

import { ContainerTypeUnit } from '../../common/ContainerTypeUtil';
import { InquiryUtil } from '../inquiry';
import { TransportationTool } from 'app/crm/common';

export class BookingBuilder {
  private inquiry: any;

  private seaQuote: any;
  private airQuote: any;
  private sellingRates: any[] = [];

  constructor(inquiry: any) {
    this.inquiry = inquiry;
  }

  public processQuote(quote: any): void {
    let mode: TransportationMode = quote['mode'];
    if (TransportationTool.isSea(mode)) {
      this.processSeaQuote(quote);
    } else if (TransportationTool.isAir(mode)) {
      this.processAirQuote(quote);
    }
  }

  public processSeaQuote(quote: any): void {
    this.seaQuote = quote;
    let freeTime = quote['freeTime'] || ''
    let containers: any[] = this.inquiry.containers || [];
    let ofTemplate: any = this.buildSeaFreightTemplate(quote);

    if (TransportationTool.isSeaFCL(quote.mode)) {
      for (let container of containers) {
        let qty = container.quantity || 0;
        if (qty > 0) {
          let containerType = ContainerTypeUnit.match(container.containerType);
          if (containerType) {
            let priceLevel = containerType.toFCLPriceLevel();
            let noteFieldName = priceLevel ? `ref${priceLevel.charAt(0).toUpperCase() + priceLevel.slice(1)}Note` : '';
            let noteVal: string = (quote.priceGroup[noteFieldName] || '')
            if (noteVal) noteVal += `/ ${freeTime}`
            else noteVal = `${freeTime}`

            if (priceLevel) {
              let cloneFreight = { ...ofTemplate };
              cloneFreight.quantity = qty;
              cloneFreight.unit = containerType.label;
              cloneFreight.unitPrice = quote.priceGroup[priceLevel] || 0;
              cloneFreight.totalAmount = cloneFreight.unitPrice * qty;
              cloneFreight.note = noteVal;
              this.sellingRates.push(cloneFreight);
            }
          }
        }
      }
    } else {
      let price = quote.priceGroup['selectedPrice'] || 0;
      if (price > 0) {
        let qty = this.inquiry['chargeableVolume'] || this.inquiry['volumeCbm'] || 1;
        ofTemplate.quantity = qty;
        ofTemplate.unit = 'CBM';
        ofTemplate.unitPrice = quote.priceGroup['selectedPrice'] || 0;
        ofTemplate.totalAmount = quote.priceGroup['selectedPrice'] * qty;
        this.sellingRates.push(ofTemplate);
      }
    }
  }

  private buildSeaFreightTemplate(quote: any) {
    return {
      type: 'SEAFREIGHT',
      group: this.inquiry.mode,
      payerPartnerId: this.inquiry.clientPartnerId,
      payerPartnerLabel: this.inquiry.clientLabel,
      code: 'S_OF',
      name: 'SEAFREIGHT',
      taxRate: quote.taxRate,
      currency: quote.currency || "USD",
      note: quote['note'],
      domesticCurrency: 'VND',
      domesticUnitPrice: 0,
      domesticTotalAmount: 0
    };
  }

  public processAirQuote(quote: any): void {
    this.airQuote = quote;
    let qty: number = this.inquiry['chargeableWeight'] || this.inquiry['grossWeightKg'] || 1;

    let unitPrice: number = quote.priceGroup['selectedPrice'] || 0;
    if (unitPrice > 0) {
      this.sellingRates.push({
        group: this.inquiry.mode,
        type: 'AIRFREIGHT',
        payerPartnerId: this.inquiry.clientPartnerId,
        payerPartnerLabel: this.inquiry.clientLabel,
        code: 'S_AF',
        name: 'AIRFREIGHT',
        currency: quote.currency || "USD",
        quantity: qty,
        unit: 'KGS',
        unitPrice: unitPrice,
        totalAmount: unitPrice * qty,
        note: quote['note'],
        domesticCurrency: 'VND',
        domesticUnitPrice: 0,
        domesticTotalAmount: 0
      });
    }
  }

  public buildLocalCharge(type: 'LOCAL_CHARGE' | `CUSTOM`, item: any, containers: any[]) {
    let quoteRate: any = item['quoteRate'] || {}
    let unitPrice: number = item['unitPrice'] || 0

    if (unitPrice !== 0) {
      let qty: number = item['quantity'] || 1;
      let addCharge: any = {
        group: this.inquiry.mode,
        type: type,
        payerPartnerId: this.inquiry.clientPartnerId,
        payerPartnerLabel: this.inquiry.clientLabel,
        code: item.code,
        name: item.name,
        currency: item.currency || "USD",
        taxRate: item.taxRate || 0,
        unit: item.unit,
        quantity: qty,
        unitPrice: unitPrice,
        totalAmount: unitPrice * qty,
        note: item['note'],
        domesticCurrency: 'VND',
        domesticUnitPrice: 0,
        domesticTotalAmount: 0
      };
      this.sellingRates.push(addCharge)
    } else {
      for (let sel of containers) {
        let containerType = ContainerTypeUnit.match(sel['containerType']);
        if (containerType) {
          let unitPrice: number = quoteRate[containerType.label] || quoteRate[containerType.name] || 0;
          if (unitPrice === 0) {
            continue;
          }

          let qty: number = sel['quantity'] || 1;
          let addCharge: any = {
            group: this.inquiry.mode,
            type: type,
            payerPartnerId: this.inquiry.clientPartnerId,
            payerPartnerLabel: this.inquiry.clientLabel,
            code: item.code,
            name: item.name,
            currency: item.currency || "USD",
            taxRate: item.taxRate || 0,
            unit: containerType.label,
            quantity: qty,
            unitPrice: unitPrice,
            totalAmount: unitPrice * qty,
            note: item['note'],
            domesticCurrency: 'VND',
            domesticUnitPrice: 0,
            domesticTotalAmount: 0
          };
          this.sellingRates.push(addCharge)
        }
      }
    }
  }

  buildBooking(quotation: any): any {
    let booking: any = {
      inquiry: InquiryUtil.cloneSpecificInquiry(this.inquiry, true),
      bookingDate: util.TimeUtil.javaCompactDateTimeFormat(new Date()),
      shipmentType: "FREE-HAND",
      paymentTerm: FreightTerm.PREPAID,
      planTimeArrival: this.inquiry.cargoReadyDate,
      planTimeDeparture: this.inquiry.cargoReadyDate,

      // /* -------------------------------Association/ Reference Entity------------------------------*/
      specificQuotationChargeId: quotation.id,
      chargeType: TransportationTool.isSea(this.inquiry.mode) ? BookingChargeType.SEA : BookingChargeType.AIR,
      sellingRates: this.sellingRates,
    };

    /* -------------------------------Customer ID, Coloader ID, Agent ID------------------------------*/
    let quote = this.seaQuote || this.airQuote || {};

    if (quote['note']) {
      booking['note'] = quote['note'];
    }

    if (quote['carrierPartnerId']) {
      booking['carrierPartnerId'] = quote['carrierPartnerId'];
      booking['carrierLabel'] = quote['carrierLabel'];
    }
    /* -------------------------------Flight/ Vessel - Voyage------------------------------*/
    booking['transportMethod'] = quote['transportMethod'];
    booking['transportNo'] = quote['transportNo'];
    booking['transportDate'] = util.TimeUtil.javaCompactDateTimeFormat(new Date());

    if (quote['handlingAgentPartnerId']) {
      this.inquiry['handlingAgentPartnerId'] = quote['handlingAgentPartnerId'];
      this.inquiry['handlingAgentPartnerLabel'] = quote['handlingAgentPartnerLabel'];
    }

    return booking;
  }
}