import { util, app } from '@datatp-ui/lib';

import { ClientPartnerType, FreightTerm, ImportExportPurpose, TransportationMode, TransportationTermOfService, TypeOfService } from "../../common/model";

import { ContainerTypeUnit } from '../../common/ContainerTypeUtil';
import { InquiryUtil } from '../inquiry';
import { TransportationTool } from 'app/crm/common';

import TimeUtil = util.TimeUtil;

const SESSION = app.host.DATATP_HOST.session;

export class BookingModelBuilder {
  private inquiry: any;

  private seaQuote: any;
  private airQuote: any;
  private sellingRates: any[] = [];

  constructor(inquiry: any) {
    this.inquiry = inquiry;
  }

  public processQuote(quote: any): void {
    let mode: TransportationMode = quote['mode'];
    if (TransportationTool.isSea(mode)) {
      this.processSeaQuote(quote);
    } else if (TransportationTool.isAir(mode)) {
      this.processAirQuote(quote);
    }
  }

  public processSeaQuote(seaQuote: any): void {
    this.seaQuote = seaQuote;
    let freeTime = seaQuote['freeTime'] || ''
    let containers: any[] = this.inquiry.containers || [];
    let ofTemplate: any = this.buildSeaFreightTemplate(seaQuote);

    if (TransportationTool.isSeaFCL(seaQuote.mode)) {
      for (let container of containers) {
        let qty = container.quantity || 0;
        if (qty > 0) {
          let containerType = ContainerTypeUnit.match(container.containerType);
          if (containerType) {
            let priceLevel = containerType.toFCLPriceLevel();
            let noteFieldName = priceLevel ? `ref${priceLevel.charAt(0).toUpperCase() + priceLevel.slice(1)}Note` : '';
            let noteVal: string = (seaQuote.priceGroup[noteFieldName] || '')
            if (noteVal) noteVal += `/ ${freeTime}`
            else noteVal = `${freeTime}`

            if (priceLevel) {
              let cloneFreight = { ...ofTemplate };
              cloneFreight.quantity = qty;
              cloneFreight.unit = containerType.label;
              cloneFreight.unitPrice = seaQuote.priceGroup[priceLevel] || 0;
              cloneFreight.totalAmount = cloneFreight.unitPrice * qty;
              cloneFreight.note = noteVal;
              this.sellingRates.push(cloneFreight);
            }
          }
        }
      }
    } else {
      let price = seaQuote.priceGroup['selectedPrice'] || 0;
      if (price > 0) {
        let qty = this.inquiry['chargeableVolume'] || this.inquiry['volumeCbm'] || 1;
        ofTemplate.quantity = qty;
        ofTemplate.unit = 'CBM';
        ofTemplate.unitPrice = seaQuote.priceGroup['selectedPrice'] || 0;
        ofTemplate.totalAmount = seaQuote.priceGroup['selectedPrice'] * qty;
        this.sellingRates.push(ofTemplate);
      }
    }
  }

  private buildSeaFreightTemplate(quote: any) {
    return {
      type: 'SEAFREIGHT',
      group: this.inquiry.mode,
      payerPartnerId: this.inquiry.clientPartnerId,
      payerPartnerLabel: this.inquiry.clientLabel,
      code: 'S_OF',
      name: 'SEAFREIGHT',
      taxRate: quote.taxRate,
      currency: quote.currency || "USD",
      note: quote['note'],
      domesticCurrency: 'VND',
      domesticUnitPrice: 0,
      domesticTotalAmount: 0
    };
  }

  public buildLocalCharge(type: 'LOCAL_CHARGE' | `CUSTOM`, item: any, containers: any[]) {
    let quoteRate: any = item['quoteRate'] || {}
    let unitPrice: number = item['unitPrice'] || 0

    if (unitPrice !== 0) {
      let qty: number = item['quantity'] || 1;
      let addCharge: any = {
        group: this.inquiry.mode,
        type: type,
        payerPartnerId: this.inquiry.clientPartnerId,
        payerPartnerLabel: this.inquiry.clientLabel,
        code: item.code,
        name: item.name,
        currency: item.currency || "USD",
        taxRate: item.taxRate || 0,
        unit: item.unit,
        quantity: qty,
        unitPrice: unitPrice,
        totalAmount: unitPrice * qty,
        note: item['note'],
        domesticCurrency: 'VND',
        domesticUnitPrice: 0,
        domesticTotalAmount: 0
      };
      this.sellingRates.push(addCharge)
    } else {
      for (let sel of containers) {
        let containerType = ContainerTypeUnit.match(sel['containerType']);
        if (containerType) {
          let unitPrice: number = quoteRate[containerType.label] || quoteRate[containerType.name] || 0;
          if (unitPrice === 0) {
            continue;
          }

          let qty: number = sel['quantity'] || 1;
          let addCharge: any = {
            group: this.inquiry.mode,
            type: type,
            payerPartnerId: this.inquiry.clientPartnerId,
            payerPartnerLabel: this.inquiry.clientLabel,
            code: item.code,
            name: item.name,
            currency: item.currency || "USD",
            taxRate: item.taxRate || 0,
            unit: containerType.label,
            quantity: qty,
            unitPrice: unitPrice,
            totalAmount: unitPrice * qty,
            note: item['note'],
            domesticCurrency: 'VND',
            domesticUnitPrice: 0,
            domesticTotalAmount: 0
          };
          this.sellingRates.push(addCharge)
        }
      }
    }
  }


  public processAirQuote(quote: any): void {
    this.airQuote = quote;

    let qty: number = this.inquiry['chargeableWeight'] || this.inquiry['grossWeightKg'] || 1;

    let unitPrice: number = quote.priceGroup['selectedPrice'] || 0;
    if (unitPrice > 0) {
      this.sellingRates.push({
        group: this.inquiry.mode,
        type: 'AIRFREIGHT',
        payerPartnerId: this.inquiry.clientPartnerId,
        payerPartnerLabel: this.inquiry.clientLabel,
        code: 'S_AF',
        name: 'AIRFREIGHT',
        currency: quote.currency || "USD",
        quantity: qty,
        unit: 'KGS',
        unitPrice: unitPrice,
        totalAmount: unitPrice * qty,
        note: quote['note'],
        domesticCurrency: 'VND',
        domesticUnitPrice: 0,
        domesticTotalAmount: 0
      });
    }
  }

  public buildBookingModel(): any {
    let bookingModel: any = {
      inquiry: InquiryUtil.cloneSpecificInquiry(this.inquiry, true),
      senderAccountId: SESSION.getAccountId(),
      senderLabel: SESSION.getAccountAcl().getFullName(),
      requestReference: this.inquiry['referenceCode'],
      airQuote: this.airQuote,
      seaQuote: this.seaQuote,
      bookingDate: TimeUtil.javaCompactDateTimeFormat(new Date()),
      paymentTerm: FreightTerm.PREPAID,
      shipmentType: "FREE-HAND",
      fromLocationCode: this.inquiry.fromLocationCode,
      fromLocationLabel: this.inquiry.fromLocationLabel,
      toLocationCode: this.inquiry.toLocationCode,
      toLocationLabel: this.inquiry.toLocationLabel,
      cargoPickupAt: this.inquiry.pickupAddress,
      cargoDeliveryAt: this.inquiry.deliveryAddress,
      unitOfPackage: this.inquiry.containerTypes,
      grossWeight: this.inquiry.grossWeightKg,
      volume: this.inquiry.volumeCbm,
      packages: this.inquiry.packageQty,
      commodity: this.inquiry.commodity,
      descriptionOfGoods: this.inquiry.descOfGoods,
      planTimeArrival: this.inquiry.cargoReadyDate,
      planTimeDeparture: this.inquiry.cargoReadyDate,
      bookingNote: this.seaQuote ? this.seaQuote['note'] : this.airQuote['note'] || '',
      sellingRates: this.sellingRates,
    };

    let quote = this.seaQuote || this.airQuote || {};
    if (quote['carrierPartnerId']) {
      bookingModel['carrierPartnerId'] = quote['carrierPartnerId'];
      bookingModel['carrierLabel'] = quote['carrierLabel'];
    }

    if (quote['handlingAgentPartnerId']) {
      this.inquiry['handlingAgentPartnerId'] = quote['handlingAgentPartnerId'];
      this.inquiry['handlingAgentPartnerLabel'] = quote['handlingAgentPartnerLabel'];
    }


    return bookingModel;
  }

}


export class Inquiry {
  referenceCode?: string;
  requestDate?: Date;
  mode?: TransportationMode;
  purpose?: ImportExportPurpose;
  typeOfService?: TypeOfService;

  /* -------------- Customer/ Agent ------------------- */
  clientPartnerType?: ClientPartnerType;
  clientPartnerId?: number;
  clientLabel?: string;
  attention?: string;
  handlingAgentPartnerId?: number;
  handlingAgentLabel?: string;

  /* -------------- Saleman------------------- */
  salemanAccountId?: number;
  salemanLabel?: string;

  /* -------------- Port/ Airport/ Location------------------- */
  fromLocationCode?: string;
  fromLocationLabel?: string;
  toLocationCode?: string;
  toLocationLabel?: string;
  finalDestination?: string;
  pickupAddress?: string;
  deliveryAddress?: string;

  /* -------------- Shipment Info------------------- */
  termOfService?: TransportationTermOfService;
  incoterms?: string;
  estimatedTimeDeparture?: Date;
  cargoReadyDate?: Date;
  targetRateAndCharges?: string;
  containerTypes?: string;
  packagingType?: string;
  packageQty?: number;
  descOfGoods?: string;
  commodity?: string;
  grossWeightKg?: number;
  volumeCbm?: number;
  chargeableWeight?: number;
  chargeableVolume?: number;
  termsAndConditions?: string;
  feedback?: string;
  note?: string;
  signatureNote?: string;
}

export class Booking {
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "booking_date")
  private Date bookingDate;

  @Column(name = "booking_number")
  private String bookingNumber;

  @Column(name = "bfsone_reference")
  private String bfsoneReference;

  @Column(name = "hawb_no")
  private String hawbNo;

  @Column(name = "mawb_no")
  private String mawbNo;

  @Column(name = "shipment_type")
  private String shipmentType;

  @Enumerated(EnumType.STRING)
  @Column(name = "payment_term")
  private FreightTerm paymentTerm = FreightTerm.PREPAID;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "eta")
  private Date planTimeArrival;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "etd")
  private Date planTimeDeparture;

  @Column(name = "note", length = 1024 * 32)
  private String note;

  /* ------------ Sender (Saleman) / Receiver (Cus/ Docs) -------------- */
  @NotNull
  @Column(name = "receiver_account_id")
  private Long receiverAccountId;

  @NotNull
  @Column(name = "receiver_bfsone_code")
  private String  receiverBFSOneCode;

  @Column(name = "receiver_label")
  private String receiverLabel;

  //@NotNull
  @Column(name = "sender_account_id")
  private Long senderAccountId;

  //@NotNull
  @Column(name = "sender_bfsone_code")
  private String  senderBFSOneCode;

  @Column(name = "sender_label")
  private String senderLabel;

  /* -------------------------------Customer ID, Coloader ID, Agent ID------------------------------*/
  @Column(name = "handling_agent_partner_id")
  private Long handlingAgentPartnerId;

  @Column(name = "handling_agent_label")
  private String handlingAgentLabel;

  @Column(name = "carrier_partner_id")
  private Long carrierPartnerId;

  @Column(name = "carrier_label")
  private String carrierLabel;

  /* -------------------------------Flight/ Vessel - Voyage------------------------------*/
  @Column(name = "transport_name")
  private String transportMethod;

  @Column(name = "transport_no")
  private String transportNo;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "transport_date")
  private Date transportDate;

  /* -------------------------------Association/ Reference Entity------------------------------*/
  @Column(name = "specific_quotation_charge_id")
  private Long sQuotationChargeId;

  @Enumerated(EnumType.STRING)
  @Column(name = "charge_type")
  private ChargeType chargeType = ChargeType.SEA;

  @Column(name = "charge_id")
  private Long chargeId;

  @Column(name = "inquiry_id", nullable = false, updatable = false, insertable = false)
  private Long inquiryId;

  @OneToOne(optional = false, cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "inquiry_id", referencedColumnName = "id")
  private SpecificServiceInquiry inquiry;

  /* -------------------------------DTO------------------------------*/
  @Transient
  private List<SellingRate> sellingRates = new ArrayList<>();

}