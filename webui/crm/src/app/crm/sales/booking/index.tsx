import { util, app } from '@datatp-ui/lib';

import { FreightTerm, TransportationMode } from "../../common/model";

import { ContainerTypeUnit } from '../../common/ContainerTypeUtil';
import { InquiryUtil } from '../inquiry';
import { TransportationTool } from 'app/crm/common';

import TimeUtil = util.TimeUtil;

const SESSION = app.host.DATATP_HOST.session;

export class BookingModelBuilder {
  private inquiry: any;

  private seaQuote: any;
  private airQuote: any;
  private sellingRates: any[] = [];

  constructor(inquiry: any) {
    this.inquiry = inquiry;
  }

  public processQuote(quote: any): void {
    let mode: TransportationMode = quote['mode'];
    if (TransportationTool.isSea(mode)) {
      this.processSeaQuote(quote);
    } else if (TransportationTool.isAir(mode)) {
      this.processAirQuote(quote);
    }
  }

  public processSeaQuote(seaQuote: any): void {
    this.seaQuote = seaQuote;
    let freeTime = seaQuote['freeTime'] || ''
    let containers: any[] = this.inquiry.containers || [];
    let ofTemplate: any = this.buildSeaFreightTemplate(seaQuote);

    if (TransportationTool.isSeaFCL(seaQuote.mode)) {
      for (let container of containers) {
        let qty = container.quantity || 0;
        if (qty > 0) {
          let containerType = ContainerTypeUnit.match(container.containerType);
          if (containerType) {
            let priceLevel = containerType.toFCLPriceLevel();
            let noteFieldName = priceLevel ? `ref${priceLevel.charAt(0).toUpperCase() + priceLevel.slice(1)}Note` : '';
            let noteVal: string = (seaQuote.priceGroup[noteFieldName] || '')
            if (noteVal) noteVal += `/ ${freeTime}`
            else noteVal = `${freeTime}`

            if (priceLevel) {
              let cloneFreight = { ...ofTemplate };
              cloneFreight.quantity = qty;
              cloneFreight.unit = containerType.label;
              cloneFreight.unitPrice = seaQuote.priceGroup[priceLevel] || 0;
              cloneFreight.totalAmount = cloneFreight.unitPrice * qty;
              cloneFreight.note = noteVal;
              this.sellingRates.push(cloneFreight);
            }
          }
        }
      }
    } else {
      let price = seaQuote.priceGroup['selectedPrice'] || 0;
      if (price > 0) {
        let qty = this.inquiry['chargeableVolume'] || this.inquiry['volumeCbm'] || 1;
        ofTemplate.quantity = qty;
        ofTemplate.unit = 'CBM';
        ofTemplate.unitPrice = seaQuote.priceGroup['selectedPrice'] || 0;
        ofTemplate.totalAmount = seaQuote.priceGroup['selectedPrice'] * qty;
        this.sellingRates.push(ofTemplate);
      }
    }
  }

  private buildSeaFreightTemplate(quote: any) {
    return {
      type: 'SEAFREIGHT',
      group: this.inquiry.mode,
      payerPartnerId: this.inquiry.clientPartnerId,
      payerPartnerLabel: this.inquiry.clientLabel,
      code: 'S_OF',
      name: 'SEAFREIGHT',
      taxRate: quote.taxRate,
      currency: quote.currency || "USD",
      note: quote['note'],
      domesticCurrency: 'VND',
      domesticUnitPrice: 0,
      domesticTotalAmount: 0
    };
  }

  public buildLocalCharge(type: 'LOCAL_CHARGE' | `CUSTOM`, item: any, containers: any[]) {
    let quoteRate: any = item['quoteRate'] || {}
    let unitPrice: number = item['unitPrice'] || 0

    if (unitPrice !== 0) {
      let qty: number = item['quantity'] || 1;
      let addCharge: any = {
        group: this.inquiry.mode,
        type: type,
        payerPartnerId: this.inquiry.clientPartnerId,
        payerPartnerLabel: this.inquiry.clientLabel,
        code: item.code,
        name: item.name,
        currency: item.currency || "USD",
        taxRate: item.taxRate || 0,
        unit: item.unit,
        quantity: qty,
        unitPrice: unitPrice,
        totalAmount: unitPrice * qty,
        note: item['note'],
        domesticCurrency: 'VND',
        domesticUnitPrice: 0,
        domesticTotalAmount: 0
      };
      this.sellingRates.push(addCharge)
    } else {
      for (let sel of containers) {
        let containerType = ContainerTypeUnit.match(sel['containerType']);
        if (containerType) {
          let unitPrice: number = quoteRate[containerType.label] || quoteRate[containerType.name] || 0;
          if (unitPrice === 0) {
            continue;
          }

          let qty: number = sel['quantity'] || 1;
          let addCharge: any = {
            group: this.inquiry.mode,
            type: type,
            payerPartnerId: this.inquiry.clientPartnerId,
            payerPartnerLabel: this.inquiry.clientLabel,
            code: item.code,
            name: item.name,
            currency: item.currency || "USD",
            taxRate: item.taxRate || 0,
            unit: containerType.label,
            quantity: qty,
            unitPrice: unitPrice,
            totalAmount: unitPrice * qty,
            note: item['note'],
            domesticCurrency: 'VND',
            domesticUnitPrice: 0,
            domesticTotalAmount: 0
          };
          this.sellingRates.push(addCharge)
        }
      }
    }
  }


  public processAirQuote(quote: any): void {
    this.airQuote = quote;

    let qty: number = this.inquiry['chargeableWeight'] || this.inquiry['grossWeightKg'] || 1;

    let unitPrice: number = quote.priceGroup['selectedPrice'] || 0;
    if (unitPrice > 0) {
      this.sellingRates.push({
        group: this.inquiry.mode,
        type: 'AIRFREIGHT',
        payerPartnerId: this.inquiry.clientPartnerId,
        payerPartnerLabel: this.inquiry.clientLabel,
        code: 'S_AF',
        name: 'AIRFREIGHT',
        currency: quote.currency || "USD",
        quantity: qty,
        unit: 'KGS',
        unitPrice: unitPrice,
        totalAmount: unitPrice * qty,
        note: quote['note'],
        domesticCurrency: 'VND',
        domesticUnitPrice: 0,
        domesticTotalAmount: 0
      });
    }
  }

  public buildBookingModel(): any {
    let bookingModel: any = {
      inquiry: InquiryUtil.cloneSpecificInquiry(this.inquiry, true),
      senderAccountId: SESSION.getAccountId(),
      senderLabel: SESSION.getAccountAcl().getFullName(),
      requestReference: this.inquiry['referenceCode'],
      airQuote: this.airQuote,
      seaQuote: this.seaQuote,
      bookingDate: TimeUtil.javaCompactDateTimeFormat(new Date()),
      paymentTerm: FreightTerm.PREPAID,
      shipmentType: "FREE-HAND",
      fromLocationCode: this.inquiry.fromLocationCode,
      fromLocationLabel: this.inquiry.fromLocationLabel,
      toLocationCode: this.inquiry.toLocationCode,
      toLocationLabel: this.inquiry.toLocationLabel,
      cargoPickupAt: this.inquiry.pickupAddress,
      cargoDeliveryAt: this.inquiry.deliveryAddress,
      unitOfPackage: this.inquiry.containerTypes,
      grossWeight: this.inquiry.grossWeightKg,
      volume: this.inquiry.volumeCbm,
      packages: this.inquiry.packageQty,
      commodity: this.inquiry.commodity,
      descriptionOfGoods: this.inquiry.descOfGoods,
      planTimeArrival: this.inquiry.cargoReadyDate,
      planTimeDeparture: this.inquiry.cargoReadyDate,
      bookingNote: this.seaQuote ? this.seaQuote['note'] : this.airQuote['note'] || '',
      sellingRates: this.sellingRates,
    };

    let quote = this.seaQuote || this.airQuote || {};
    if (quote['carrierPartnerId']) {
      bookingModel['carrierPartnerId'] = quote['carrierPartnerId'];
      bookingModel['carrierLabel'] = quote['carrierLabel'];
    }

    if (quote['handlingAgentPartnerId']) {
      this.inquiry['handlingAgentPartnerId'] = quote['handlingAgentPartnerId'];
      this.inquiry['handlingAgentPartnerLabel'] = quote['handlingAgentPartnerLabel'];
    }


    return bookingModel;
  }

}


export class Inquiry {
  @Column(name = "reference_code")
  private String referenceCode;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "request_date")
  private Date requestDate;

  @Enumerated(EnumType.STRING)
  @Column(name = "mode")
  private TransportationMode mode;

  @Enumerated(EnumType.STRING)
  private Purpose purpose = Purpose.IMPORT;

  @Enumerated(EnumType.STRING)
  @Column(name = "type_of_service")
  private TypeOfService typeOfService = TypeOfService.InlandTrucking;

  /* -------------- Customer/ Agent ------------------- */
  @Enumerated(EnumType.STRING)
  @Column(name = "client_partner_type")
  private ClientPartnerType clientPartnerType;

  @Column(name = "client_partner_id")
  private Long clientPartnerId;

  @Column(name = "client_label")
  private String clientLabel;

  @Column(name = "attention")
  private String attention;

  @Column(name = "handling_agent_partner_id")
  private Long handlingAgentPartnerId;

  @Column(name = "handling_agent_label")
  private String handlingAgentLabel;

  /* -------------- Saleman------------------- */
  @Column(name = "saleman_account_id")
  private Long salemanAccountId;

  @Column(name = "saleman_label")
  private String salemanLabel;

  /* -------------- Port/ Airport/ Location------------------- */
  @Column(name = "from_location_code")
  private String fromLocationCode;

  @Column(name = "from_location_label")
  private String fromLocationLabel;

  @Column(name = "to_location_code")
  private String toLocationCode;

  @Column(name = "to_location_label")
  private String toLocationLabel;

  @Column(name = "final_destination")
  private String finalDestination;

  @Column(name = "pickup_address", length = 2 * 1024)
  private String pickupAddress;

  @Column(name = "delivery_address", length = 2 * 1024)
  private String deliveryAddress;

  /* -------------- Shipment Info------------------- */
  @Enumerated(EnumType.STRING)
  @Column(name = "term_of_service")
  private TermOfService termOfService;

  @Column(name = "incoterms")
  private String incoterms;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "estimated_time_departure")
  private Date estimatedTimeDeparture;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "cargo_ready_date")
  private Date cargoReadyDate;

  @Column(name = "target_rate_and_charges")
  private String targetRateAndCharges;

  @Column(name = "container_types")
  private String containerTypes;

  @Column(name = "packaging_type")
  private String packagingType = "PK";

  @Column(name = "package_quantity")
  private int packageQty;

  @Column(name = "desc_of_goods", length = 1024 * 32)
  private String descOfGoods = "";

  @Column(name = "commodity", length = 1024 * 32)
  private String commodity;

  @Column(name = "gross_weight_kg")
  private double grossWeightKg;

  @Column(name = "volume_cbm")
  private double volumeCbm;

  @Column(name = "chargeable_weight")
  private double chargeableWeight;

  @Column(name = "chargeable_volume")
  private double chargeableVolume;

  @Column(name = "terms_and_conditions", length = 1024 * 32)
  private String termsAndConditions;

  @Column(name = "feedback", length = 2 * 1024)
  private String feedback;

  @Column(length = 1024 * 32)
  private String note;

  @Column(name = "signature_note", length = 1024 * 32)
  private String signatureNote;
}

export class Booking {

}