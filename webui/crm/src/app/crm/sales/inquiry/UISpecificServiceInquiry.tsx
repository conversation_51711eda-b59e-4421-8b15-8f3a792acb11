import React from 'react';
import * as FeatherIcon from 'react-feather'

import { util, bs, input, entity } from '@datatp-ui/lib';
import { module } from "@datatp-ui/erp";

import { T } from '../backend'
import { BBRefUserCustomer } from '../partners/BBRefUserCustomer';
import { BBContainerType, ContainerTypeUnit } from 'app/crm/common';

import LocationType = module.settings.LocationType;
import BBRefLocation = module.settings.BBRefLocation;
import { IncotermUtils } from 'app/crm/price';
import {
  TransportationMode, TransportationTool, ImportExportPurpose,
  TransportationTermOfService as TermOfService, mapToTypeOfShipment
} from 'app/crm/common';

class UILogisticsTruckingService extends entity.AppDbComplexEntityEditor {

  onModify = (bean: any, field: string, _oldVal: any, newVal: any) => {
    let { observer, onModify } = this.props;
    if (field.length > 0) {
      observer.replaceBeanProperty(field, newVal);
    } else {
      observer.setMutableBean(bean);
    }
    observer.commitAndGet();
    let inquiry = observer.getMutableBean();
    if (onModify) onModify(inquiry, field, null, newVal);
    else this.forceUpdate()
  }

  render(): React.ReactNode {
    let { appContext, pageContext, observer } = this.props;
    let inquiry = observer.getMutableBean();
    let mode: TransportationMode = inquiry['mode'];
    const writeCap = pageContext.hasUserWriteCapability();

    if (TransportationTool.isTruck(mode) || TransportationTool.isUnknown(mode)) {
      return (
        <div className="flex-vbox py-1">
          <p className="fw-bold border-bottom">Trucking</p>
          <BBRefLocation appContext={appContext} pageContext={pageContext} bean={inquiry} hideMoreInfo required
            beanIdField={'fromLocationCode'} beanLabelField={'fromLocationLabel'} locationTypes={['State']}
            label='Pickup Province' placeholder='Pickup Province' refLocationBy='code' style={{ minWidth: 300 }}
            onPostUpdate={(_inputUi, bean, selectOpt, _userInput) => {
              bean['fromLocationCode'] = selectOpt['code'];
              bean['fromLocationLabel'] = selectOpt['label'];
            }} />
          <input.BBTextField bean={inquiry} label={T('Pickup Address')} field={'pickupAddress'} disable={!writeCap}
            onInputChange={this.onModify} />

          <BBRefLocation appContext={appContext} pageContext={pageContext} bean={inquiry} hideMoreInfo required
            beanIdField={'toLocationCode'} beanLabelField={'toLocationLabel'} locationTypes={['State']}
            label='Delivery Province' placeholder='Delivery Province' refLocationBy='code' style={{ minWidth: 300 }}
            onPostUpdate={(_inputUi, bean, selectOpt, _userInput) => {
              bean['toLocationCode'] = selectOpt['code'];
              bean['toLocationLabel'] = selectOpt['label'];
            }} />
          <input.BBTextField bean={inquiry} label={T('Delivery Address')} field={'deliveryAddress'} disable={!writeCap}
            onInputChange={this.onModify} />
        </div>
      )
    } else {
      let termOfService = inquiry['termOfService'];
      if (TermOfService.PortToPort === termOfService) return <></>
      let hasPickup: boolean = TermOfService.DoorToDoor === termOfService || TermOfService.DoorToPort === termOfService;
      let hasDelivery: boolean = TermOfService.DoorToDoor === termOfService || TermOfService.PortToDoor === termOfService;
      if (!hasPickup && !hasDelivery) return <></>

      return (
        <div className="flex-vbox py-1">
          <p className="fw-bold border-bottom">Trucking</p>
          {hasPickup &&
            <>
              <BBRefLocation appContext={appContext} pageContext={pageContext} bean={inquiry} hideMoreInfo required
                beanIdField={'fromLocationCode'} beanLabelField={'fromLocationLabel'} locationTypes={['State']}
                label='Pickup Province' placeholder='Pickup Province' refLocationBy='code' style={{ minWidth: 300 }}
                onPostUpdate={(_inputUi, bean, selectOpt, _userInput) => {
                  bean['fromLocationCode'] = selectOpt['code'];
                  bean['fromLocationLabel'] = selectOpt['label'];
                }} />
              <input.BBTextField bean={inquiry} label={T('Pickup Address')} field={'pickupAddress'} disable={!writeCap}
                onInputChange={this.onModify} />
            </>
          }
          {
            hasDelivery &&
            <>
              <BBRefLocation appContext={appContext} pageContext={pageContext} bean={inquiry} hideMoreInfo required
                beanIdField={'toLocationCode'} beanLabelField={'toLocationLabel'} locationTypes={['State']}
                label='Delivery Province' placeholder='Delivery Province' refLocationBy='code' style={{ minWidth: 300 }}
                onPostUpdate={(_inputUi, bean, selectOpt, _userInput) => {
                  bean['toLocationCode'] = selectOpt['code'];
                  bean['toLocationLabel'] = selectOpt['label'];
                }} />
              <input.BBTextField bean={inquiry} label={T('Delivery Address')} field={'deliveryAddress'} disable={!writeCap}
                onInputChange={this.onModify} />
            </>
          }
        </div>
      )
    }
  }
}

class UIFreightService extends entity.AppDbComplexEntityEditor {

  onModify = (bean: any, field: string, _oldVal: any, newVal: any) => {
    let { observer, onModify } = this.props;
    if (field.length > 0) {
      observer.replaceBeanProperty(field, newVal);
    } else {
      observer.setMutableBean(bean);
    }
    observer.commitAndGet();
    let inquiry = observer.getMutableBean();
    if (onModify) onModify(inquiry, field, null, newVal);
    else this.forceUpdate()
  }

  render(): React.ReactNode {
    let { appContext, pageContext, observer } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let inquiry = observer.getMutableBean();
    let mode: TransportationMode = inquiry['mode'] || TransportationMode.UNKNOWN
    if (TransportationTool.isTruck(mode) || mode === TransportationMode.UNKNOWN) {
      return <></>
    }
    let locationType: LocationType = 'Port';
    if (TransportationTool.isAir(mode)) locationType = 'Airport'
    else if (TransportationTool.isRail(mode)) locationType = 'Address'

    return (
      <div className="flex-vbox py-1">
        <p className="fw-bold border-bottom my-0">Routing</p>
        <bs.Row>
          <bs.Col span={12}>
            <BBRefLocation label='Port/ Airport of Loading' placeholder='Port/ Airport of Loading' hideMoreInfo
              appContext={appContext} pageContext={pageContext} required disable={!writeCap}
              bean={inquiry} beanIdField={'fromLocationCode'} beanLabelField={"fromLocationLabel"} locationTypes={[locationType]}
              inputObserver={observer} refLocationBy='code'
              onPostUpdate={(_inputUI, bean, _selectOpt, _userInput) => this.onModify(bean, 'fromLocationCode', '', bean['fromLocationCode'])
              } />
          </bs.Col>
        </bs.Row>

        <bs.Row>
          <bs.Col span={12}>
            <BBRefLocation label='Port/ Airport of discharge' placeholder='Port/ Airport of discharge' hideMoreInfo
              appContext={appContext} pageContext={pageContext} required disable={!writeCap}
              bean={inquiry} beanIdField={'toLocationCode'} beanLabelField={"toLocationLabel"} locationTypes={[locationType]}
              inputObserver={observer} refLocationBy='code'
              onPostUpdate={(_inputUI, bean, _selectOpt, _userInput) => this.onModify(bean, 'toLocationCode', '', bean['toLocationCode'])
              } />
          </bs.Col>
        </bs.Row>
        <input.BBStringField label={T('Final Dest')} disable={!writeCap} bean={inquiry} field={'finalDestination'} />
      </div>
    )
  }
}

export interface UISpecificInquiryInfoProps extends entity.AppDbComplexEntityProps { }

export class UISpecificInquiryForm extends entity.AppDbComplexEntityEditor<UISpecificInquiryInfoProps> {
  displayService: string = '';

  constructor(props: UISpecificInquiryInfoProps) {
    super(props);
    const { observer } = this.props;
    let inquiry = observer.getMutableBean();
    let purpose: ImportExportPurpose = inquiry['purpose']
    let mode: TransportationMode = inquiry['mode']

    if (TransportationTool.isTruck(mode)) {
      this.displayService = ''
    } else {
      this.displayService = `(${mapToTypeOfShipment(purpose, mode)})`
    }
  }

  onModifyInquiry = (inquiry: any, field: string, _oldVal: any, newVal: any) => {
    let { observer, onModify } = this.props;
    this.nextViewId();
    if (field.length > 0) {
      observer.replaceBeanProperty(field, newVal);
    } else {
      observer.setMutableBean(inquiry);
    }
    let updatedInquiry = observer.updateMutableBean();
    if (onModify) onModify(updatedInquiry, field, _oldVal, newVal)
    else this.forceUpdate()
  }

  onChangeContainer = (bean: any, field: string, oldVal: any, newVal: any) => {
    if (!newVal || newVal === '') {
      bean['containers'] = [];
      bean['reportVolume'] = 0;
      bean['reportVolumeUnit'] = 'TEU';
      this.onModifyInquiry(bean, field, oldVal, newVal);
      return;
    }

    bean['reportVolume'] = ContainerTypeUnit.calculateTUE(newVal);
    bean['reportVolumeUnit'] = 'TEU';
    bean['containers'] = ContainerTypeUnit.textToContainerList(newVal);
    this.onModifyInquiry(bean, field, oldVal, newVal);
  }

  render(): React.ReactNode {

    let { appContext, pageContext, observer } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    let inquiry = observer.getMutableBean();
    let mode: TransportationMode = inquiry['mode']

    const lightColor = '#D6EFD8';
    const borderColor = '#80AF81';

    const hoverStyle: any = {
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      transform: 'translateY(-2px)',
      backgroundColor: lightColor
    };

    let incotermOptions: string[] = IncotermUtils.getIncotermOptions();
    let incotermOptionLabels: string[] = IncotermUtils.getIncotermOptionLabels();

    const typeOfServiceOptions = ['InlandTrucking', 'CustomsLogistics', 'LogisticsCrossBorder', 'RoundUseTrucking', 'WarehouseService']

    return (
      <div className='flex-vbox h-100 pt-1'>

        <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center"
          style={{
            borderColor: borderColor,
            transition: 'all 0.3s ease',
            marginBottom: '10px'
          }}
          onMouseEnter={(e) => Object.assign(e.currentTarget.style, hoverStyle)}
          onMouseLeave={(e) => {
            e.currentTarget.style.boxShadow = 'none';
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.backgroundColor = '#fff';
          }}>
          <div className="flex-vbox py-2 bg-white rounded-md w-100 h-100">

            <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">
              <h5 style={{ color: '#6c757d' }}><FeatherIcon.HelpCircle className="me-2" size={16} />{`Inquiry Request ${this.displayService}`}</h5>
              <div className="flex-hbox justify-content-end align-items-center">
              </div>
            </div>

            <div className="flex-vbox" >
              <bs.GreedyScrollable className='border-top p-1' style={{ width: '100%', height: '100%' }}>

                <bs.Row>
                  <bs.Col span={6}>
                    <input.BBStringField label={T('Reference Code')} disable bean={inquiry} field={'referenceCode'} />
                  </bs.Col>
                  <bs.Col span={6}>
                    <input.BBDateTimeField
                      bean={inquiry} label={T('Request Date')} field={'requestDate'}
                      timeFormat={false} disable={!writeCap} onInputChange={this.onModifyInquiry} />
                  </bs.Col>
                </bs.Row>

                {
                  (!TransportationTool.isAir(mode) && !TransportationTool.isSea(mode)) &&
                  <bs.Row>
                    <bs.Col span={12}>
                      <input.BBSelectField bean={inquiry} label={'Type Of Service'} field={'typeOfService'}
                        options={typeOfServiceOptions} optionLabels={typeOfServiceOptions} onInputChange={this.onModifyInquiry} />
                    </bs.Col>
                  </bs.Row>
                }

                <bs.Row>
                  <bs.Col span={12}>
                    <BBRefUserCustomer placeholder='Client...' label='Client' allowUserInput hideMoreInfo
                      appContext={appContext} pageContext={pageContext} disable={!writeCap} minWidth={500}
                      bean={inquiry} beanIdField={'clientPartnerId'} beanLabelField={'clientLabel'}
                      partnerTypes={['CUSTOMERS', 'AGENTS', 'CUSTOMER_LEAD', 'AGENTS_APPROACHED']}
                      onPostUpdate={(_input: any, bean: any, selectOpt: any) => {
                        bean['clientPartnerType'] = selectOpt['type'];
                        if (selectOpt['type'] === 'AGENTS' || selectOpt['type'] === 'AGENTS_APPROACHED') {
                          bean['handlingAgentPartnerId'] = selectOpt['id'];
                          bean['handlingAgentLabel'] = selectOpt['partnerName'];
                        } else {
                          bean['clientPartnerId'] = selectOpt['id'];
                          bean['clientLabel'] = selectOpt['partnerName'];
                          bean['attention'] = selectOpt['personalContact'];
                        }
                        this.onModifyInquiry(bean, 'clientPartnerId', '', selectOpt['id'])
                      }} />
                  </bs.Col>
                </bs.Row>

                <input.BBStringField label={T('Attn')} bean={inquiry} field={'attention'} />

                <bs.Row>
                  <bs.Col span={12} md={6}>
                    <input.BBSelectField bean={inquiry} label={T('Incoterms')} disable={!writeCap} field={'incoterms'}
                      options={incotermOptions} optionLabels={incotermOptionLabels} onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                        bean[field] = newVal;
                        if (newVal) {
                          let term = IncotermUtils.getInfo(newVal);
                          bean['termOfService'] = term.typeOfService;
                        }
                        this.onModifyInquiry(bean, field, oldVal, newVal);
                      }} />
                  </bs.Col>

                  <bs.Col span={12} md={6}>
                    <input.BBDateTimeField
                      label={T('Cargo Ready Date')}
                      bean={inquiry} field={'cargoReadyDate'} dateFormat={'DD/MM/YYYY'} timeFormat={false}
                      onInputChange={this.onModifyInquiry} disable={!writeCap} />
                  </bs.Col>
                </bs.Row>

                <bs.Row>
                  <bs.Col span={6}>
                    <input.BBNumberField precision={4} maxPrecision={5}
                      bean={inquiry} label={T("Gross Weight (KGS)")} field={'grossWeightKg'} disable={!writeCap}
                      inputObserver={observer} validators={[util.validator.ZERO_AND_GREATER_VALIDATOR]}
                      onInputChange={this.onModifyInquiry} />
                  </bs.Col>
                  <bs.Col span={6}>
                    <input.BBNumberField precision={4} maxPrecision={5}
                      bean={inquiry} label={T("Volume (CBM)")} field={'volumeCbm'} disable={!writeCap} inputObserver={observer}
                      validators={[util.validator.ZERO_AND_GREATER_VALIDATOR]}
                      onInputChange={this.onModifyInquiry} />
                  </bs.Col>
                </bs.Row>

                {
                  (TransportationTool.isSeaFCL(mode) || TransportationTool.isTruckContainer(mode)) &&
                  <BBContainerType
                    bean={inquiry} label={'Container Types'} field={'containerTypes'} required
                    onInputChange={this.onChangeContainer} />
                }
                {
                  TransportationTool.isAir(mode) &&
                  <>
                    <bs.Row>
                      <bs.Col span={6}>
                        <input.BBNumberField
                          bean={inquiry} label={T("Package Qty")} field={'packageQty'} disable={!writeCap}
                          inputObserver={observer} validators={[util.validator.ZERO_AND_GREATER_VALIDATOR]}
                          onInputChange={this.onModifyInquiry} />
                      </bs.Col>
                      <bs.Col span={6}>
                        <module.settings.BBRefUnit groupNames={['packing']} minWidth={150}
                          appContext={appContext} pageContext={pageContext} hideMoreInfo disable={!writeCap}
                          bean={inquiry} beanIdField={'packagingType'} placeholder='Packing Type' label='Packing Type'
                          onPostUpdate={(_inputUI, _bean, selectOpt, _userInput) => this.onModifyInquiry(inquiry, '', null, selectOpt.name)} />
                      </bs.Col>
                    </bs.Row>
                    <input.BBNumberField precision={3} maxPrecision={3}
                      bean={inquiry} label={T("Charge Weight")} field={'chargeableWeight'} inputObserver={observer}
                      validators={[util.validator.ZERO_AND_GREATER_VALIDATOR]} disable={!writeCap}
                      onInputChange={this.onModifyInquiry} />
                  </>
                }

                {
                  TransportationTool.isSeaLCL(mode) &&
                  <>
                    <bs.Row>
                      <bs.Col span={6}>
                        <input.BBNumberField
                          bean={inquiry} label={T("Package Qty")} field={'packageQty'} disable={!writeCap}
                          inputObserver={observer} validators={[util.validator.ZERO_AND_GREATER_VALIDATOR]}
                          onInputChange={this.onModifyInquiry} />
                      </bs.Col>
                      <bs.Col span={6}>
                        <module.settings.BBRefUnit groupNames={['packing']} minWidth={150}
                          appContext={appContext} pageContext={pageContext} hideMoreInfo disable={!writeCap}
                          bean={inquiry} beanIdField={'packagingType'} placeholder='Packing Type' label='Packing Type'
                          onPostUpdate={(_inputUI, _bean, selectOpt, _userInput) => this.onModifyInquiry(inquiry, '', null, selectOpt.name)} />
                      </bs.Col>
                    </bs.Row>
                  </>
                }

                <bs.Row className='pt-1'>
                  <bs.Col span={12}>
                    <input.BBTextField bean={inquiry} label='Desc Of Goods' field={"descOfGoods"} style={{ height: '4em' }}
                      onInputChange={this.onModifyInquiry} />
                  </bs.Col>
                </bs.Row>

                <UIFreightService {...this.props} onModify={this.onModifyInquiry} />
                <UILogisticsTruckingService {...this.props} onModify={this.onModifyInquiry} />

                <bs.Row>
                  <bs.Col span={12}>
                    <input.BBTextField label='Feedback'
                      bean={inquiry} field={"feedback"} style={{ height: "4rem" }} disable={!writeCap}
                      onInputChange={this.onModifyInquiry} />
                  </bs.Col>
                </bs.Row>

                <bs.Row>
                  <bs.Col span={12}>
                    <input.BBTextField className='p1'
                      bean={inquiry} label={T('Note')} field={"note"} style={{ height: "12rem" }} disable={!writeCap}
                      onInputChange={this.onModifyInquiry} />
                  </bs.Col>
                </bs.Row>

              </bs.GreedyScrollable>
            </div>

          </div>
        </div>
      </div>
    )
  }
}
