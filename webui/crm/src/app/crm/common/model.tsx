export enum TransportationMode {
  AIR = 'AIR',
  SEA_FCL = 'SEA_FCL',
  SEA_LCL = 'SEA_LCL',
  TRUCK_CONTAINER = 'TRUCK_CONTAINER',
  TRUCK_REGULAR = 'TRUCK_REGULAR',
  RAIL = 'RAIL',
  UNKNOWN = "UNKNOWN"
};


export enum TypeOfService {
  AirExpTransactions = 'AirExpTransactions',
  AirImpTransactions = 'AirImpTransactions',
  CustomsLogistics = 'CustomsLogistics',
  InlandTrucking = 'InlandTrucking',
  LogisticsCrossBorder = 'LogisticsCrossBorder',
  RoundUseTrucking = 'RoundUseTrucking',
  WarehouseService = 'WarehouseService',
  SeaExpTransactions_FCL = 'SeaExpTransactions_FCL',
  SeaExpTransactions_LCL = 'SeaExpTransactions_LCL',
  SeaImpTransactions_FCL = 'SeaImpTransactions_FCL',
  SeaImpTransactions_LCL = 'SeaImpTransactions_LCL',
  SeaExpTransactions_CSL = 'SeaExpTransactions_CSL',
  SeaImpTransactions_CSL = 'SeaImpTransactions_CSL',
}

const TransportationModeAbbreviation = {
  [TransportationMode.AIR]: 'AIR',
  [TransportationMode.SEA_FCL]: 'FCL',
  [TransportationMode.SEA_LCL]: 'LCL',
  [TransportationMode.TRUCK_CONTAINER]: 'CONT',
  [TransportationMode.TRUCK_REGULAR]: 'TRUCK',
  [TransportationMode.RAIL]: 'RAIL',
  [TransportationMode.UNKNOWN]: 'UNKNOWN'
};

export const getAbbreviation = (mode: TransportationMode): string => TransportationModeAbbreviation[mode];

export enum TransportPlanStatus {
  CONFIRMED = "CONFIRMED",
  PENDING = "PENDING",
}

export enum TransportationTermOfService {
  PortToPort = "PORT_TO_PORT",
  DoorToPort = "DOOR_TO_PORT",
  PortToDoor = "PORT_TO_DOOR",
  DoorToDoor = "DOOR_TO_DOOR"
}

export enum TruckType {
  REGULAR = 'REGULAR',
  CONTAINER = 'CONTAINER',
};

export enum SeaType {
  LCL = 'LCL',
  FCL = 'FCL'
}

export enum LclType {
  LCL = 'LCL',
  LCL_CONSOL = 'LCL_CONSOL',
}

export enum CustomClearanceType {
  AIR = "AIR",
  SEA_FCL = "SEA_FCL",
  SEA_LCL = "SEA_LCL",
  RAIL = "RAIL",
  TRUCK_CONTAINER = 'TRUCK_CONTAINER',
  TRUCK_REGULAR = 'TRUCK_REGULAR',
};

export enum ImportExportPurpose {
  DOMESTIC = "DOMESTIC",
  IMPORT = "IMPORT",
  EXPORT = "EXPORT"
}

export enum FreightTerm {
  PREPAID = 'PREPAID',
  COLLECT = 'COLLECT',
}


export enum ClientPartnerType {
  CUSTOMER_LEAD = 'CUSTOMER_LEAD',
  CUSTOMERS = 'CUSTOMERS',
  AGENTS = 'AGENTS',
  AGENTS_APPROACHED = 'AGENTS_APPROACHED'
}