#!/usr/bin/env bash
#
source ./common.sh

#PROFILE="dev"
#PROD_OPT=$(has_opt "-pprod" $@ )
#if [ "$PROD_OPT" = "true" ]; then
#  PROFILE="prod"
#fi

if [ -z "${PROFILE}" ]; then
  PROFILE="dev"
fi
echo '----------------'
echo ${PROFILE}

export DATATP_BUILD_PROFILE="$PROFILE"


function run_clean() {
  CLEAN_OPT=$(has_opt "-clean" $@ )
  if [ "$CLEAN_OPT" = "true" ]; then
    rm -rf $ROOT_DIR/working/release-$DATATP_BUILD_PROFILE/server
    rm -rf $ROOT_DIR/working/release-$DATATP_BUILD_PROFILE/server-data
    rm -rf $ROOT_DIR/working/release-$DATATP_BUILD_PROFILE/server-instances
  fi
}

function projects() {
  runInProjects "$@"
  #runInCustomerProjects "$@"
}

function erp_build() {
  echo "Build ERP"
  cd $ROOT_DIR/datatp-core && gradle build publishToMavenLocal -x test
  cd $ROOT_DIR/datatp-erp && gradle build publishToMavenLocal -x test

  BUILD_UI_OPT=$(has_opt "-build-ui" $@ )
  if [ "$BUILD_UI_OPT" = "true" ]; then
    (cd $ROOT_DIR/datatp-erp/webui/lib; pnpm i && pnpm run build)
    (cd $ROOT_DIR/datatp-erp/webui/erp; pnpm i && pnpm run build)
  fi
  cd $ROOT_DIR/datatp-erp/app/release &&  gradle release -x test
}

function document_ie_build() {
  echo "Build Document IE"
  cd $ROOT_DIR/datatp-document-ie && gradle build publishToMavenLocal -x test
  cd $ROOT_DIR/datatp-document-ie/app/release && gradle release

  if [ "$BUILD_UI_OPT" = "true" ]; then
    (cd $ROOT_DIR/datatp-document-ie/webui/document-ie; pnpm i &&  pnpm run build)
  fi
}

function datatp_logistics_build() {
  echo "Build Logistics Core"
  cd $ROOT_DIR/datatp-logistics && gradle build publishToMavenLocal -x test
  cd $ROOT_DIR/datatp-logistics/app/release && gradle release
}

function datatp_crm_build() {
  echo "Build DataTP CRM"
  cd $ROOT_DIR/datatp-crm && gradle build publishToMavenLocal -x test
  cd $ROOT_DIR/datatp-crm/app/release && gradle release
}

function logistics_build() {
  erp_build "$@"

  document_ie_build "$@"
  datatp_crm_build "$@"
  datatp_logistics_build "$@"

  BUILD_UI_OPT=$(has_opt "-build-ui" $@ )

  if [ "$BUILD_UI_OPT" = "true" ]; then
    (cd $ROOT_DIR/datatp-logistics/webui/logistics; pnpm i &&  pnpm run build)
    (cd $ROOT_DIR/datatp-crm/webui/crm; pnpm i &&  pnpm run build)
  fi

}

function host_build() {
  echo "Deploy Host UI"

  BUILD_UI_OPT=$(has_opt "-build-ui" $@ )
  if [ "$BUILD_UI_OPT" = "true" ]; then
    ADD_APPS=$(get_opt --add-apps '' $@)
    (cd $ROOT_DIR/datatp-build/webui/phoenix && APPS="$ADD_APPS" pnpm run build)
  fi
  (cd $ROOT_DIR/datatp-build/webui/phoenix && ./release.sh deploy)
}

COMMAND=$1
shift


if [ "$COMMAND" = "erp:build" ] ; then
  run_clean "$@"
  erp_build "$@" && host_build --add-apps=basic "$@"
elif [ "$COMMAND" = "lgc:build" ] ; then
  run_clean "$@"
  logistics_build "$@" && host_build --add-apps=all "$@"
elif [ "$COMMAND" = "build:basic" ] ; then
  run_clean "$@"
  erp_build "$@" && host_build --add-apps=basic "$@"
elif [ "$COMMAND" = "build:all" ] ; then
  run_clean "$@"
  logistics_build "$@" && host_build --add-apps=all "$@"
elif [ "$COMMAND" = "instances" ] ; then
  (cd $ROOT_DIR/working/release-$PROFILE/server-env; ./instances.sh $@)
elif [ "$COMMAND" = "help" ] ; then
  echo "Usage: "
  echo " erp:build     Run erp build with option clean, update, build, build-ui"
  echo "   options: [-clean [-update] [-build] [-build-ui]   "
  echo " lgc:build     Run logistics build with option clean, update, build, build-ui"
  echo "   options: [-clean [-update] [-build] [-build-ui]   "
else
  runInProjects $COMMAND "$@"
fi
