2025-09-09T14:50:52.992+07:00  INFO 62651 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 62651 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-09T14:50:52.993+07:00  INFO 62651 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-09T14:50:53.774+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:53.840+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 62 ms. Found 22 JPA repository interfaces.
2025-09-09T14:50:53.848+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:53.850+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-09T14:50:53.850+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:53.858+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 10 JPA repository interfaces.
2025-09-09T14:50:53.859+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:53.861+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-09T14:50:53.907+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:53.912+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-09T14:50:53.921+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:53.923+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 2 JPA repository interfaces.
2025-09-09T14:50:53.923+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:53.927+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-09T14:50:53.930+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:53.934+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-09T14:50:53.938+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:53.940+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-09T14:50:53.940+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:53.941+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-09T14:50:53.941+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:53.947+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 10 JPA repository interfaces.
2025-09-09T14:50:53.953+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:53.956+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-09T14:50:53.960+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:53.964+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-09T14:50:53.964+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:53.971+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-09T14:50:53.971+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:53.974+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-09T14:50:53.974+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:53.974+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-09T14:50:53.974+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:53.975+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-09T14:50:53.975+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:53.979+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-09T14:50:53.979+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:53.980+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-09T14:50:53.981+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:53.981+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-09T14:50:53.981+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:53.991+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-09-09T14:50:54.000+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:54.006+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-09T14:50:54.006+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:54.009+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-09T14:50:54.009+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:54.013+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-09T14:50:54.013+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:54.018+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-09-09T14:50:54.019+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:54.024+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-09T14:50:54.024+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:54.033+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 12 JPA repository interfaces.
2025-09-09T14:50:54.033+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:54.042+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-09-09T14:50:54.043+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:54.059+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 15 ms. Found 24 JPA repository interfaces.
2025-09-09T14:50:54.059+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:54.060+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-09T14:50:54.065+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:54.065+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-09T14:50:54.066+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:54.073+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-09T14:50:54.075+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:54.123+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 48 ms. Found 65 JPA repository interfaces.
2025-09-09T14:50:54.123+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:54.125+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-09T14:50:54.129+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:50:54.132+07:00  INFO 62651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-09T14:50:54.415+07:00  INFO 62651 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-09T14:50:54.420+07:00  INFO 62651 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-09T14:50:54.706+07:00  WARN 62651 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-09T14:50:54.894+07:00  INFO 62651 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-09T14:50:54.896+07:00  INFO 62651 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-09T14:50:54.907+07:00  INFO 62651 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-09T14:50:54.907+07:00  INFO 62651 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1756 ms
2025-09-09T14:50:54.957+07:00  WARN 62651 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-09T14:50:54.957+07:00  INFO 62651 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-09T14:50:55.057+07:00  INFO 62651 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@3b30409
2025-09-09T14:50:55.058+07:00  INFO 62651 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-09T14:50:55.063+07:00  WARN 62651 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-09T14:50:55.063+07:00  INFO 62651 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-09T14:50:55.070+07:00  INFO 62651 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@515a897c
2025-09-09T14:50:55.070+07:00  INFO 62651 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-09T14:50:55.070+07:00  WARN 62651 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-09T14:50:55.070+07:00  INFO 62651 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-09T14:50:55.079+07:00  INFO 62651 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@3520d2db
2025-09-09T14:50:55.079+07:00  INFO 62651 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-09T14:50:55.079+07:00  WARN 62651 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-09T14:50:55.079+07:00  INFO 62651 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-09T14:50:55.086+07:00  INFO 62651 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@31ee5a9
2025-09-09T14:50:55.087+07:00  INFO 62651 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-09T14:50:55.087+07:00  WARN 62651 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-09T14:50:55.087+07:00  INFO 62651 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-09T14:50:55.092+07:00  INFO 62651 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@26415b5e
2025-09-09T14:50:55.093+07:00  INFO 62651 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-09T14:50:55.093+07:00  INFO 62651 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-09T14:50:55.137+07:00  INFO 62651 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-09T14:50:55.139+07:00  INFO 62651 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@74a121fe{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16934664737674866857/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@7c5a7f{STARTED}}
2025-09-09T14:50:55.140+07:00  INFO 62651 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@74a121fe{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16934664737674866857/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@7c5a7f{STARTED}}
2025-09-09T14:50:55.141+07:00  INFO 62651 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@654ab198{STARTING}[12.0.15,sto=0] @2761ms
2025-09-09T14:50:55.247+07:00  INFO 62651 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-09T14:50:55.274+07:00  INFO 62651 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-09T14:50:55.288+07:00  INFO 62651 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-09T14:50:55.413+07:00  INFO 62651 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-09T14:50:55.439+07:00  WARN 62651 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-09T14:50:56.096+07:00  INFO 62651 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-09T14:50:56.106+07:00  INFO 62651 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@4621d245] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-09T14:50:56.218+07:00  INFO 62651 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-09T14:50:56.411+07:00  INFO 62651 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "cloud.datatp.tms", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "cloud.datatp.module.odoo", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-09T14:50:56.412+07:00  INFO 62651 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-09T14:50:56.418+07:00  INFO 62651 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-09T14:50:56.419+07:00  INFO 62651 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-09T14:50:56.445+07:00  INFO 62651 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-09T14:50:56.449+07:00  WARN 62651 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-09T14:50:58.533+07:00  INFO 62651 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-09T14:50:58.534+07:00  INFO 62651 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@5dbff955] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-09T14:50:58.708+07:00  WARN 62651 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-09T14:50:58.708+07:00  WARN 62651 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-09T14:50:58.715+07:00  WARN 62651 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-09T14:50:58.715+07:00  WARN 62651 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-09T14:50:58.728+07:00  WARN 62651 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-09T14:50:58.728+07:00  WARN 62651 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-09T14:50:59.093+07:00  INFO 62651 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-09T14:50:59.098+07:00  INFO 62651 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-09T14:50:59.100+07:00  INFO 62651 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-09T14:50:59.118+07:00  INFO 62651 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-09T14:50:59.120+07:00  WARN 62651 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-09T14:50:59.621+07:00  INFO 62651 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-09T14:50:59.622+07:00  INFO 62651 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@57b018e3] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-09T14:50:59.678+07:00  WARN 62651 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-09T14:50:59.678+07:00  WARN 62651 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-09T14:50:59.986+07:00  INFO 62651 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-09T14:51:00.016+07:00  INFO 62651 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-09T14:51:00.021+07:00  INFO 62651 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-09T14:51:00.021+07:00  INFO 62651 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T14:51:00.028+07:00  WARN 62651 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-09T14:51:00.183+07:00  INFO 62651 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-09T14:51:00.718+07:00  INFO 62651 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-09T14:51:00.721+07:00  INFO 62651 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-09T14:51:00.755+07:00  INFO 62651 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-09T14:51:00.799+07:00  INFO 62651 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-09T14:51:00.858+07:00  INFO 62651 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-09T14:51:00.884+07:00  INFO 62651 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-09T14:51:00.909+07:00  INFO 62651 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 2657614ms : this is harmless.
2025-09-09T14:51:00.916+07:00  INFO 62651 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-09T14:51:00.919+07:00  INFO 62651 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-09T14:51:00.933+07:00  INFO 62651 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 2657604ms : this is harmless.
2025-09-09T14:51:00.934+07:00  INFO 62651 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-09T14:51:00.949+07:00  INFO 62651 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-09T14:51:00.949+07:00  INFO 62651 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-09T14:51:03.079+07:00  INFO 62651 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-09T14:51:03.079+07:00  INFO 62651 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T14:51:03.080+07:00  WARN 62651 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-09T14:51:03.405+07:00  INFO 62651 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 09/09/2025@14:45:00+0700 to 09/09/2025@15:00:00+0700
2025-09-09T14:51:03.405+07:00  INFO 62651 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 09/09/2025@14:45:00+0700 to 09/09/2025@15:00:00+0700
2025-09-09T14:51:03.896+07:00  INFO 62651 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-09T14:51:03.896+07:00  INFO 62651 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T14:51:03.896+07:00  WARN 62651 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-09T14:51:04.171+07:00  INFO 62651 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-09T14:51:04.172+07:00  INFO 62651 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-09T14:51:04.172+07:00  INFO 62651 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-09T14:51:04.172+07:00  INFO 62651 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-09T14:51:04.172+07:00  INFO 62651 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-09T14:51:06.101+07:00  WARN 62651 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 3b6d1214-78fc-45ae-8122-0b4bf73f2f7b

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-09T14:51:06.104+07:00  INFO 62651 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-09T14:51:06.391+07:00  INFO 62651 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-09T14:51:06.391+07:00  INFO 62651 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-09T14:51:06.391+07:00  INFO 62651 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-09T14:51:06.391+07:00  INFO 62651 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-09T14:51:06.391+07:00  INFO 62651 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-09T14:51:06.391+07:00  INFO 62651 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-09T14:51:06.391+07:00  INFO 62651 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-09T14:51:06.391+07:00  INFO 62651 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-09T14:51:06.391+07:00  INFO 62651 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-09T14:51:06.391+07:00  INFO 62651 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-09T14:51:06.391+07:00  INFO 62651 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-09T14:51:06.391+07:00  INFO 62651 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-09T14:51:06.392+07:00  INFO 62651 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-09T14:51:06.392+07:00  INFO 62651 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-09T14:51:06.392+07:00  INFO 62651 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-09T14:51:06.450+07:00  INFO 62651 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-09T14:51:06.450+07:00  INFO 62651 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-09T14:51:06.451+07:00  INFO 62651 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-09T14:51:06.459+07:00  INFO 62651 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@6b429fc{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-09T14:51:06.460+07:00  INFO 62651 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-09T14:51:06.460+07:00  INFO 62651 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-09T14:51:06.487+07:00  INFO 62651 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-09T14:51:06.487+07:00  INFO 62651 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-09T14:51:06.493+07:00  INFO 62651 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.825 seconds (process running for 14.115)
2025-09-09T14:51:14.579+07:00  INFO 62651 --- [qtp1309262768-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-09T14:52:02.493+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T14:52:09.521+07:00  INFO 62651 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-09T14:52:09.544+07:00  INFO 62651 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T14:52:16.306+07:00  INFO 62651 --- [qtp1309262768-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0dp63v9j4eywk1f2wcxr9hb38m1
2025-09-09T14:52:16.306+07:00  INFO 62651 --- [qtp1309262768-36] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0dwo6vbcpw34x1l4jpy8l860rf0
2025-09-09T14:52:16.368+07:00  INFO 62651 --- [qtp1309262768-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0dwo6vbcpw34x1l4jpy8l860rf0, token = 76228d850b3efc0bd6f0238233a52890
2025-09-09T14:52:16.389+07:00  INFO 62651 --- [qtp1309262768-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0dp63v9j4eywk1f2wcxr9hb38m1, token = 76228d850b3efc0bd6f0238233a52890
2025-09-09T14:52:16.722+07:00  INFO 62651 --- [qtp1309262768-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T14:52:16.730+07:00  INFO 62651 --- [qtp1309262768-37] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T14:53:05.653+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T14:54:06.754+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T14:54:08.786+07:00  INFO 62651 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-09T14:54:08.809+07:00  INFO 62651 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T14:55:04.898+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T14:55:04.901+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T14:55:32.845+07:00  INFO 62651 --- [qtp1309262768-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0dp63v9j4eywk1f2wcxr9hb38m1, token = 76228d850b3efc0bd6f0238233a52890
2025-09-09T14:55:32.852+07:00  INFO 62651 --- [qtp1309262768-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0dp63v9j4eywk1f2wcxr9hb38m1, token = 76228d850b3efc0bd6f0238233a52890
2025-09-09T14:55:32.865+07:00  INFO 62651 --- [qtp1309262768-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T14:55:32.865+07:00  INFO 62651 --- [qtp1309262768-61] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T14:56:07.002+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T14:56:13.031+07:00  INFO 62651 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-09T14:56:13.045+07:00  INFO 62651 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T14:57:04.126+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T14:58:06.224+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T14:58:13.263+07:00  INFO 62651 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-09T14:58:13.283+07:00  INFO 62651 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T14:59:03.390+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:00:06.496+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at 15 PM every day
2025-09-09T15:00:06.500+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-09T15:00:06.513+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 3 hour
2025-09-09T15:00:06.514+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-09T15:00:06.514+07:00  INFO 62651 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 09/09/2025@15:00:06+0700
2025-09-09T15:00:06.551+07:00  INFO 62651 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 09/09/2025@15:00:00+0700 to 09/09/2025@15:15:00+0700
2025-09-09T15:00:06.552+07:00  INFO 62651 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 09/09/2025@15:00:00+0700 to 09/09/2025@15:15:00+0700
2025-09-09T15:00:06.553+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T15:00:06.553+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:00:12.565+07:00  INFO 62651 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T15:00:12.569+07:00  INFO 62651 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T15:00:55.142+07:00  INFO 62651 --- [Scheduler-1553053521-1] n.d.m.session.AppHttpSessionListener     : The session node0dwo6vbcpw34x1l4jpy8l860rf0 is destroyed.
2025-09-09T15:01:02.666+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:01:33.955+07:00  INFO 62651 --- [qtp1309262768-61] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-09T15:02:05.776+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:02:12.840+07:00  INFO 62651 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-09T15:02:12.865+07:00  INFO 62651 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T15:02:35.016+07:00  INFO 62651 --- [qtp1309262768-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0dp63v9j4eywk1f2wcxr9hb38m1, token = 76228d850b3efc0bd6f0238233a52890
2025-09-09T15:02:35.017+07:00  INFO 62651 --- [qtp1309262768-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0dp63v9j4eywk1f2wcxr9hb38m1, token = 76228d850b3efc0bd6f0238233a52890
2025-09-09T15:02:35.034+07:00  INFO 62651 --- [qtp1309262768-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T15:02:35.034+07:00  INFO 62651 --- [qtp1309262768-39] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T15:03:06.943+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:04:05.047+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:04:12.079+07:00  INFO 62651 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-09T15:04:12.096+07:00  INFO 62651 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T15:04:37.693+07:00  INFO 62651 --- [qtp1309262768-88] n.d.m.c.a.CompanyAuthenticationService   : User dan logout successfully 
2025-09-09T15:04:39.310+07:00  INFO 62651 --- [qtp1309262768-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0dp63v9j4eywk1f2wcxr9hb38m1, token = d03c8c88f18f1988e37ace07cebb2c46
2025-09-09T15:04:39.318+07:00  INFO 62651 --- [qtp1309262768-34] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T15:05:06.048+07:00  INFO 62651 --- [qtp1309262768-96] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T15:05:06.048+07:00  INFO 62651 --- [qtp1309262768-90] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T15:05:06.133+07:00  INFO 62651 --- [qtp1309262768-90] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-09T15:05:06.133+07:00  INFO 62651 --- [qtp1309262768-96] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-09T15:05:06.175+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T15:05:06.176+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:06:04.256+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:06:11.307+07:00  INFO 62651 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 1
2025-09-09T15:06:11.336+07:00  INFO 62651 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-09T15:07:06.433+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:08:03.543+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:08:10.605+07:00  INFO 62651 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 0
2025-09-09T15:08:10.613+07:00  INFO 62651 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T15:09:06.701+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:10:02.804+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T15:10:02.806+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:10:09.835+07:00  INFO 62651 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-09T15:10:09.843+07:00  INFO 62651 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T15:11:05.930+07:00  INFO 62651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:11:11.972+07:00  INFO 62651 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@6b429fc{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-09T15:11:11.972+07:00  INFO 62651 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-09T15:11:11.973+07:00  INFO 62651 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-09T15:11:11.973+07:00  INFO 62651 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-09T15:11:11.973+07:00  INFO 62651 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-09T15:11:11.973+07:00  INFO 62651 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-09T15:11:11.973+07:00  INFO 62651 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-09T15:11:11.973+07:00  INFO 62651 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-09T15:11:11.973+07:00  INFO 62651 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-09T15:11:11.973+07:00  INFO 62651 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-09T15:11:11.973+07:00  INFO 62651 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-09T15:11:11.973+07:00  INFO 62651 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-09T15:11:11.973+07:00  INFO 62651 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-09T15:11:11.973+07:00  INFO 62651 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-09T15:11:11.974+07:00  INFO 62651 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-09T15:11:11.974+07:00  INFO 62651 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-09T15:11:11.989+07:00  INFO 62651 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T15:11:12.063+07:00  INFO 62651 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-09T15:11:12.067+07:00  INFO 62651 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-09T15:11:12.089+07:00  INFO 62651 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-09T15:11:12.090+07:00  INFO 62651 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-09T15:11:12.095+07:00  INFO 62651 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-09T15:11:12.095+07:00  INFO 62651 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-09T15:11:12.097+07:00  INFO 62651 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-09T15:11:12.097+07:00  INFO 62651 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-09T15:11:12.098+07:00  INFO 62651 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-09T15:11:12.098+07:00  INFO 62651 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-09T15:11:12.098+07:00  INFO 62651 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-09T15:11:12.098+07:00  INFO 62651 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-09T15:11:12.099+07:00  INFO 62651 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-09T15:11:12.099+07:00  INFO 62651 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-09T15:11:12.100+07:00  INFO 62651 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-09T15:11:12.102+07:00  INFO 62651 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@654ab198{STOPPING}[12.0.15,sto=0]
2025-09-09T15:11:12.103+07:00  INFO 62651 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-09T15:11:12.105+07:00  INFO 62651 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@74a121fe{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16934664737674866857/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@7c5a7f{STOPPED}}
