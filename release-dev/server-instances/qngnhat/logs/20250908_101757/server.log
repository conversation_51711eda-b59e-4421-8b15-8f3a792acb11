2025-09-08T10:17:57.799+07:00  INFO 20640 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 20640 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-08T10:17:57.800+07:00  INFO 20640 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-08T10:17:58.557+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.625+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 64 ms. Found 22 JPA repository interfaces.
2025-09-08T10:17:58.633+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.635+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-08T10:17:58.635+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.642+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-09-08T10:17:58.643+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.645+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-08T10:17:58.695+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.700+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 1 JPA repository interface.
2025-09-08T10:17:58.709+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.710+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-08T10:17:58.711+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.714+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-08T10:17:58.717+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.721+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-08T10:17:58.725+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.727+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-08T10:17:58.727+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.727+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-08T10:17:58.727+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.733+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 10 JPA repository interfaces.
2025-09-08T10:17:58.740+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.742+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-08T10:17:58.745+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.749+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-08T10:17:58.749+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.756+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-08T10:17:58.757+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.760+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-08T10:17:58.760+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.760+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-08T10:17:58.760+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.761+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-08T10:17:58.761+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.765+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-08T10:17:58.765+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.767+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-08T10:17:58.767+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.767+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-08T10:17:58.767+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.777+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-09-08T10:17:58.787+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.793+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-08T10:17:58.793+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.796+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-08T10:17:58.796+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.800+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-08T10:17:58.800+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.805+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 9 JPA repository interfaces.
2025-09-08T10:17:58.805+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.808+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-08T10:17:58.809+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.816+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-08T10:17:58.816+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.825+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-09-08T10:17:58.825+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.839+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 24 JPA repository interfaces.
2025-09-08T10:17:58.839+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.840+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-08T10:17:58.845+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.846+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-08T10:17:58.846+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.853+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-08T10:17:58.855+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.891+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 35 ms. Found 65 JPA repository interfaces.
2025-09-08T10:17:58.891+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.892+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-08T10:17:58.896+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:17:58.899+07:00  INFO 20640 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-08T10:17:59.093+07:00  INFO 20640 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-08T10:17:59.097+07:00  INFO 20640 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-08T10:17:59.661+07:00  WARN 20640 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-08T10:17:59.925+07:00  INFO 20640 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-08T10:17:59.928+07:00  INFO 20640 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-08T10:17:59.942+07:00  INFO 20640 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-08T10:17:59.942+07:00  INFO 20640 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1975 ms
2025-09-08T10:18:00.004+07:00  WARN 20640 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T10:18:00.004+07:00  INFO 20640 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-08T10:18:00.109+07:00  INFO 20640 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@72f4232
2025-09-08T10:18:00.110+07:00  INFO 20640 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-08T10:18:00.114+07:00  WARN 20640 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T10:18:00.115+07:00  INFO 20640 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-08T10:18:00.120+07:00  INFO 20640 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@3ef97efc
2025-09-08T10:18:00.121+07:00  INFO 20640 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-08T10:18:00.121+07:00  WARN 20640 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T10:18:00.121+07:00  INFO 20640 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-08T10:18:00.136+07:00  INFO 20640 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@77ef7b2b
2025-09-08T10:18:00.136+07:00  INFO 20640 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-08T10:18:00.136+07:00  WARN 20640 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T10:18:00.136+07:00  INFO 20640 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-08T10:18:00.148+07:00  INFO 20640 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@5f92ef40
2025-09-08T10:18:00.148+07:00  INFO 20640 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-08T10:18:00.148+07:00  WARN 20640 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T10:18:00.148+07:00  INFO 20640 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-08T10:18:00.162+07:00  INFO 20640 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@7f5a5ef3
2025-09-08T10:18:00.162+07:00  INFO 20640 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-08T10:18:00.162+07:00  INFO 20640 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-08T10:18:00.207+07:00  INFO 20640 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-08T10:18:00.209+07:00  INFO 20640 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@5a7e0d68{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.10129161455911977720/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@10f03ab0{STARTED}}
2025-09-08T10:18:00.210+07:00  INFO 20640 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@5a7e0d68{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.10129161455911977720/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@10f03ab0{STARTED}}
2025-09-08T10:18:00.211+07:00  INFO 20640 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@196f9b75{STARTING}[12.0.15,sto=0] @2917ms
2025-09-08T10:18:00.310+07:00  INFO 20640 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-08T10:18:00.338+07:00  INFO 20640 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-08T10:18:00.352+07:00  INFO 20640 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-08T10:18:00.476+07:00  INFO 20640 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-08T10:18:00.554+07:00  WARN 20640 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-08T10:18:01.202+07:00  INFO 20640 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-08T10:18:01.223+07:00  INFO 20640 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@48bff6ff] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-08T10:18:01.410+07:00  INFO 20640 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T10:18:01.628+07:00  INFO 20640 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.company.tmpl", "cloud.datatp.tms", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-08T10:18:01.631+07:00  INFO 20640 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-08T10:18:01.637+07:00  INFO 20640 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-08T10:18:01.639+07:00  INFO 20640 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-08T10:18:01.666+07:00  INFO 20640 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-08T10:18:01.672+07:00  WARN 20640 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-08T10:18:03.732+07:00  INFO 20640 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-08T10:18:03.733+07:00  INFO 20640 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@75f6d74e] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-08T10:18:03.932+07:00  WARN 20640 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-08T10:18:03.932+07:00  WARN 20640 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-08T10:18:03.939+07:00  WARN 20640 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-08T10:18:03.939+07:00  WARN 20640 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-08T10:18:03.952+07:00  WARN 20640 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-08T10:18:03.952+07:00  WARN 20640 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-08T10:18:04.392+07:00  INFO 20640 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T10:18:04.398+07:00  INFO 20640 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-08T10:18:04.399+07:00  INFO 20640 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-08T10:18:04.418+07:00  INFO 20640 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-08T10:18:04.428+07:00  WARN 20640 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-08T10:18:04.989+07:00  INFO 20640 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-08T10:18:04.990+07:00  INFO 20640 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@446a4ce3] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-08T10:18:05.089+07:00  WARN 20640 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-08T10:18:05.089+07:00  WARN 20640 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-08T10:18:05.412+07:00  INFO 20640 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T10:18:05.444+07:00  INFO 20640 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-08T10:18:05.449+07:00  INFO 20640 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-08T10:18:05.449+07:00  INFO 20640 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-08T10:18:05.457+07:00  WARN 20640 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-08T10:18:05.602+07:00  INFO 20640 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-08T10:18:06.088+07:00  INFO 20640 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-08T10:18:06.091+07:00  INFO 20640 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-08T10:18:06.128+07:00  INFO 20640 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-08T10:18:06.175+07:00  INFO 20640 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-08T10:18:06.251+07:00  INFO 20640 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-08T10:18:06.278+07:00  INFO 20640 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-08T10:18:06.306+07:00  INFO 20640 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 430339654ms : this is harmless.
2025-09-08T10:18:06.315+07:00  INFO 20640 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-08T10:18:06.318+07:00  INFO 20640 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-08T10:18:06.331+07:00  INFO 20640 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 437463216ms : this is harmless.
2025-09-08T10:18:06.333+07:00  INFO 20640 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-08T10:18:06.346+07:00  INFO 20640 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-08T10:18:06.347+07:00  INFO 20640 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-08T10:18:07.513+07:00  INFO 20640 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-08T10:18:07.513+07:00  INFO 20640 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-08T10:18:07.514+07:00  WARN 20640 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-08T10:18:08.259+07:00  INFO 20640 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 08/09/2025@10:15:00+0700 to 08/09/2025@10:30:00+0700
2025-09-08T10:18:08.259+07:00  INFO 20640 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 08/09/2025@10:15:00+0700 to 08/09/2025@10:30:00+0700
2025-09-08T10:18:09.356+07:00  INFO 20640 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-08T10:18:09.356+07:00  INFO 20640 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-08T10:18:09.356+07:00  WARN 20640 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-08T10:18:09.636+07:00  INFO 20640 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-08T10:18:09.636+07:00  INFO 20640 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-08T10:18:09.636+07:00  INFO 20640 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-08T10:18:09.636+07:00  INFO 20640 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-08T10:18:09.636+07:00  INFO 20640 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-08T10:18:11.533+07:00  WARN 20640 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 7a6cb036-31d9-4601-8f82-dc26abfe545e

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-08T10:18:11.537+07:00  INFO 20640 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-08T10:18:11.852+07:00  INFO 20640 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-08T10:18:11.853+07:00  INFO 20640 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-08T10:18:11.853+07:00  INFO 20640 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-08T10:18:11.853+07:00  INFO 20640 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-08T10:18:11.853+07:00  INFO 20640 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-08T10:18:11.853+07:00  INFO 20640 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-08T10:18:11.853+07:00  INFO 20640 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-08T10:18:11.853+07:00  INFO 20640 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-08T10:18:11.853+07:00  INFO 20640 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-08T10:18:11.853+07:00  INFO 20640 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-08T10:18:11.853+07:00  INFO 20640 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-08T10:18:11.853+07:00  INFO 20640 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-08T10:18:11.856+07:00  INFO 20640 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-08T10:18:11.856+07:00  INFO 20640 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-08T10:18:11.856+07:00  INFO 20640 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-08T10:18:11.906+07:00  INFO 20640 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-08T10:18:11.906+07:00  INFO 20640 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-08T10:18:11.907+07:00  INFO 20640 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-08T10:18:11.924+07:00  INFO 20640 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@78709532{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-08T10:18:11.925+07:00  INFO 20640 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-08T10:18:11.926+07:00  INFO 20640 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-08T10:18:11.969+07:00  INFO 20640 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-08T10:18:11.969+07:00  INFO 20640 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-08T10:18:11.975+07:00  INFO 20640 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 14.457 seconds (process running for 14.681)
2025-09-08T10:19:06.993+07:00  INFO 20640 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:19:15.029+07:00  INFO 20640 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:19:15.052+07:00  INFO 20640 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:20:04.130+07:00  INFO 20640 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:20:04.140+07:00  INFO 20640 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-08T10:21:06.238+07:00  INFO 20640 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:21:14.266+07:00  INFO 20640 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:21:14.280+07:00  INFO 20640 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:22:03.371+07:00  INFO 20640 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:23:06.478+07:00  INFO 20640 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:23:18.496+07:00  INFO 20640 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:23:18.526+07:00  INFO 20640 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:24:02.614+07:00  INFO 20640 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:24:42.995+07:00  INFO 20640 --- [qtp1390401525-41] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01frmrsvscdeabmcbpgyhiiic10
2025-09-08T10:24:43.298+07:00  INFO 20640 --- [qtp1390401525-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01frmrsvscdeabmcbpgyhiiic10, token = c7246a861fd4ba5f878e1ba28ddea76c
2025-09-08T10:24:43.714+07:00  INFO 20640 --- [qtp1390401525-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-08T10:24:43.817+07:00  INFO 20640 --- [qtp1390401525-35] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-09-08T10:24:43.818+07:00  INFO 20640 --- [qtp1390401525-35] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/SyncBFSUsernameToLoginId.groovy"
}
2025-09-08T10:24:44.134+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 1/15: 10 records
2025-09-08T10:24:44.337+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 2/15: 10 records
2025-09-08T10:24:44.422+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 3/15: 10 records
2025-09-08T10:24:44.503+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 4/15: 10 records
2025-09-08T10:24:44.576+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 5/15: 10 records
2025-09-08T10:24:44.644+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 6/15: 10 records
2025-09-08T10:24:44.706+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 7/15: 10 records
2025-09-08T10:24:44.768+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 8/15: 10 records
2025-09-08T10:24:44.824+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 9/15: 10 records
2025-09-08T10:24:44.879+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 10/15: 10 records
2025-09-08T10:24:44.938+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 11/15: 10 records
2025-09-08T10:24:44.995+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 12/15: 10 records
2025-09-08T10:24:45.046+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 13/15: 10 records
2025-09-08T10:24:45.064+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 14/15: 10 records
2025-09-08T10:24:45.077+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 15/15: 2 records
2025-09-08T10:25:05.723+07:00  INFO 20640 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:25:05.725+07:00  INFO 20640 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-08T10:25:18.800+07:00  INFO 20640 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-08T10:25:18.904+07:00  INFO 20640 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:25:23.940+07:00  INFO 20640 --- [qtp1390401525-36] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01fnlzxjmdmqcm2ejgpr42d4bg1
2025-09-08T10:25:24.054+07:00  INFO 20640 --- [qtp1390401525-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01fnlzxjmdmqcm2ejgpr42d4bg1, token = e9e1ea093cc91af28c78da43ae08abf2
2025-09-08T10:25:24.059+07:00  INFO 20640 --- [qtp1390401525-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-08T10:25:24.077+07:00  INFO 20640 --- [qtp1390401525-35] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-09-08T10:25:24.077+07:00  INFO 20640 --- [qtp1390401525-35] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/SyncBFSUsernameToLoginId.groovy"
}
2025-09-08T10:25:24.206+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 1/66: 10 records
2025-09-08T10:25:24.301+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 2/66: 10 records
2025-09-08T10:25:24.379+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 3/66: 10 records
2025-09-08T10:25:24.447+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 4/66: 10 records
2025-09-08T10:25:24.519+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 5/66: 10 records
2025-09-08T10:25:24.575+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 6/66: 10 records
2025-09-08T10:25:24.633+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 7/66: 10 records
2025-09-08T10:25:24.687+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 8/66: 10 records
2025-09-08T10:25:24.742+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 9/66: 10 records
2025-09-08T10:25:24.795+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 10/66: 10 records
2025-09-08T10:25:24.849+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 11/66: 10 records
2025-09-08T10:25:24.904+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 12/66: 10 records
2025-09-08T10:25:24.959+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 13/66: 10 records
2025-09-08T10:25:25.015+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 14/66: 10 records
2025-09-08T10:25:25.073+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 15/66: 10 records
2025-09-08T10:25:25.129+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 16/66: 10 records
2025-09-08T10:25:25.185+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 17/66: 10 records
2025-09-08T10:25:25.240+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 18/66: 10 records
2025-09-08T10:25:25.292+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 19/66: 10 records
2025-09-08T10:25:25.349+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 20/66: 10 records
2025-09-08T10:25:25.403+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 21/66: 10 records
2025-09-08T10:25:25.456+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 22/66: 10 records
2025-09-08T10:25:25.527+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 23/66: 10 records
2025-09-08T10:25:25.581+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 24/66: 10 records
2025-09-08T10:25:25.634+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 25/66: 10 records
2025-09-08T10:25:25.687+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 26/66: 10 records
2025-09-08T10:25:25.741+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 27/66: 10 records
2025-09-08T10:25:25.793+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 28/66: 10 records
2025-09-08T10:25:25.844+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 29/66: 10 records
2025-09-08T10:25:25.897+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 30/66: 10 records
2025-09-08T10:25:25.938+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 31/66: 10 records
2025-09-08T10:25:25.988+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 32/66: 10 records
2025-09-08T10:25:26.042+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 33/66: 10 records
2025-09-08T10:25:26.101+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 34/66: 10 records
2025-09-08T10:25:26.163+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 35/66: 10 records
2025-09-08T10:25:26.227+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 36/66: 10 records
2025-09-08T10:25:26.288+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 37/66: 10 records
2025-09-08T10:25:26.336+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 38/66: 10 records
2025-09-08T10:25:26.393+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 39/66: 10 records
2025-09-08T10:25:26.447+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 40/66: 10 records
2025-09-08T10:25:26.506+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 41/66: 10 records
2025-09-08T10:25:26.573+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 42/66: 10 records
2025-09-08T10:25:26.633+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 43/66: 10 records
2025-09-08T10:25:26.692+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 44/66: 10 records
2025-09-08T10:25:26.757+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 45/66: 10 records
2025-09-08T10:25:26.820+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 46/66: 10 records
2025-09-08T10:25:26.876+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 47/66: 10 records
2025-09-08T10:25:26.934+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 48/66: 10 records
2025-09-08T10:25:26.991+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 49/66: 10 records
2025-09-08T10:25:27.041+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 50/66: 10 records
2025-09-08T10:25:27.083+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 51/66: 10 records
2025-09-08T10:25:27.130+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 52/66: 10 records
2025-09-08T10:25:27.186+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 53/66: 10 records
2025-09-08T10:25:27.234+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 54/66: 10 records
2025-09-08T10:25:27.260+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 55/66: 10 records
2025-09-08T10:25:27.296+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 56/66: 10 records
2025-09-08T10:25:27.334+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 57/66: 10 records
2025-09-08T10:25:27.380+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 58/66: 10 records
2025-09-08T10:25:27.423+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 59/66: 10 records
2025-09-08T10:25:27.480+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 60/66: 10 records
2025-09-08T10:25:27.543+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 61/66: 10 records
2025-09-08T10:25:27.604+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 62/66: 10 records
2025-09-08T10:25:27.668+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 63/66: 10 records
2025-09-08T10:25:27.725+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 64/66: 10 records
2025-09-08T10:25:27.790+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 65/66: 10 records
2025-09-08T10:25:27.842+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 66/66: 5 records
2025-09-08T10:26:06.975+07:00  INFO 20640 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:27:05.079+07:00  INFO 20640 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:27:18.102+07:00  INFO 20640 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:27:18.109+07:00  INFO 20640 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:28:06.187+07:00  INFO 20640 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:28:24.573+07:00  INFO 20640 --- [qtp1390401525-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0189rvv602x0xe1ogvx3taqz58z2
2025-09-08T10:28:24.696+07:00  INFO 20640 --- [qtp1390401525-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0189rvv602x0xe1ogvx3taqz58z2, token = d15bfd9b3fd97ab102bf896308a4ffa0
2025-09-08T10:28:24.708+07:00  INFO 20640 --- [qtp1390401525-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-08T10:28:24.719+07:00  INFO 20640 --- [qtp1390401525-35] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-09-08T10:28:24.719+07:00  INFO 20640 --- [qtp1390401525-35] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/SyncBFSUsernameToLoginId.groovy"
}
2025-09-08T10:28:24.833+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 1/66: 10 records
2025-09-08T10:28:24.897+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 2/66: 10 records
2025-09-08T10:28:24.956+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 3/66: 10 records
2025-09-08T10:28:25.019+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 4/66: 10 records
2025-09-08T10:28:25.071+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 5/66: 10 records
2025-09-08T10:28:25.121+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 6/66: 10 records
2025-09-08T10:28:25.166+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 7/66: 10 records
2025-09-08T10:28:25.216+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 8/66: 10 records
2025-09-08T10:28:25.272+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 9/66: 10 records
2025-09-08T10:28:25.325+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 10/66: 10 records
2025-09-08T10:28:25.377+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 11/66: 10 records
2025-09-08T10:28:25.428+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 12/66: 10 records
2025-09-08T10:28:25.480+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 13/66: 10 records
2025-09-08T10:28:25.533+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 14/66: 10 records
2025-09-08T10:28:25.585+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 15/66: 10 records
2025-09-08T10:28:25.637+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 16/66: 10 records
2025-09-08T10:28:25.767+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 17/66: 10 records
2025-09-08T10:28:25.825+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 18/66: 10 records
2025-09-08T10:28:25.875+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 19/66: 10 records
2025-09-08T10:28:25.927+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 20/66: 10 records
2025-09-08T10:28:25.980+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 21/66: 10 records
2025-09-08T10:28:26.029+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 22/66: 10 records
2025-09-08T10:28:26.085+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 23/66: 10 records
2025-09-08T10:28:26.149+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 24/66: 10 records
2025-09-08T10:28:26.209+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 25/66: 10 records
2025-09-08T10:28:26.268+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 26/66: 10 records
2025-09-08T10:28:26.318+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 27/66: 10 records
2025-09-08T10:28:26.372+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 28/66: 10 records
2025-09-08T10:28:26.430+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 29/66: 10 records
2025-09-08T10:28:26.490+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 30/66: 10 records
2025-09-08T10:28:26.540+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 31/66: 10 records
2025-09-08T10:28:26.601+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 32/66: 10 records
2025-09-08T10:28:26.660+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 33/66: 10 records
2025-09-08T10:28:26.716+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 34/66: 10 records
2025-09-08T10:28:26.775+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 35/66: 10 records
2025-09-08T10:28:26.833+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 36/66: 10 records
2025-09-08T10:28:26.880+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 37/66: 10 records
2025-09-08T10:28:26.939+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 38/66: 10 records
2025-09-08T10:28:26.994+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 39/66: 10 records
2025-09-08T10:28:27.056+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 40/66: 10 records
2025-09-08T10:28:27.114+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 41/66: 10 records
2025-09-08T10:28:27.184+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 42/66: 10 records
2025-09-08T10:28:27.240+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 43/66: 10 records
2025-09-08T10:28:27.301+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 44/66: 10 records
2025-09-08T10:28:27.363+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 45/66: 10 records
2025-09-08T10:28:27.424+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 46/66: 10 records
2025-09-08T10:28:27.481+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 47/66: 10 records
2025-09-08T10:28:27.540+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 48/66: 10 records
2025-09-08T10:28:27.596+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 49/66: 10 records
2025-09-08T10:28:27.641+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 50/66: 10 records
2025-09-08T10:28:27.696+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 51/66: 10 records
2025-09-08T10:28:27.755+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 52/66: 10 records
2025-09-08T10:28:27.815+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 53/66: 10 records
2025-09-08T10:28:27.860+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 54/66: 10 records
2025-09-08T10:28:27.894+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 55/66: 10 records
2025-09-08T10:28:27.932+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 56/66: 10 records
2025-09-08T10:28:27.968+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 57/66: 10 records
2025-09-08T10:28:28.014+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 58/66: 10 records
2025-09-08T10:28:28.060+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 59/66: 10 records
2025-09-08T10:28:28.115+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 60/66: 10 records
2025-09-08T10:28:28.168+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 61/66: 10 records
2025-09-08T10:28:28.221+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 62/66: 10 records
2025-09-08T10:28:28.277+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 63/66: 10 records
2025-09-08T10:28:28.327+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 64/66: 10 records
2025-09-08T10:28:28.385+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 65/66: 10 records
2025-09-08T10:28:28.436+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 66/66: 3 records
2025-09-08T10:29:04.292+07:00  INFO 20640 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:29:18.327+07:00  INFO 20640 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-08T10:29:18.335+07:00  INFO 20640 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:30:06.415+07:00  INFO 20640 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:30:06.418+07:00  INFO 20640 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-08T10:30:06.421+07:00  INFO 20640 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-08T10:30:06.430+07:00  INFO 20640 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 08/09/2025@10:30:06+0700
2025-09-08T10:30:06.449+07:00  INFO 20640 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 08/09/2025@10:30:00+0700 to 08/09/2025@10:45:00+0700
2025-09-08T10:30:06.449+07:00  INFO 20640 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 08/09/2025@10:30:00+0700 to 08/09/2025@10:45:00+0700
2025-09-08T10:30:36.083+07:00  INFO 20640 --- [qtp1390401525-34] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node091koxqpwiker1nb7kebefb1uo3
2025-09-08T10:30:36.207+07:00  INFO 20640 --- [qtp1390401525-34] n.d.module.session.ClientSessionManager  : Add a client session id = node091koxqpwiker1nb7kebefb1uo3, token = caa10ee18c27fc158cf89b38e9df0803
2025-09-08T10:30:36.222+07:00  INFO 20640 --- [qtp1390401525-34] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-08T10:30:36.232+07:00  INFO 20640 --- [qtp1390401525-35] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-09-08T10:30:36.232+07:00  INFO 20640 --- [qtp1390401525-35] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/SyncBFSUsernameToLoginId.groovy"
}
2025-09-08T10:30:36.335+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 1/66: 10 records
2025-09-08T10:30:36.405+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 2/66: 10 records
2025-09-08T10:30:36.472+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 3/66: 10 records
2025-09-08T10:30:36.525+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 4/66: 10 records
2025-09-08T10:30:36.575+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 5/66: 10 records
2025-09-08T10:30:36.625+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 6/66: 10 records
2025-09-08T10:30:36.668+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 7/66: 10 records
2025-09-08T10:30:36.715+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 8/66: 10 records
2025-09-08T10:30:36.766+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 9/66: 10 records
2025-09-08T10:30:36.820+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 10/66: 10 records
2025-09-08T10:30:36.878+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 11/66: 10 records
2025-09-08T10:30:36.936+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 12/66: 10 records
2025-09-08T10:30:36.995+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 13/66: 10 records
2025-09-08T10:30:37.056+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 14/66: 10 records
2025-09-08T10:30:37.106+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 15/66: 10 records
2025-09-08T10:30:37.156+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 16/66: 10 records
2025-09-08T10:30:37.211+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 17/66: 10 records
2025-09-08T10:30:37.268+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 18/66: 10 records
2025-09-08T10:30:37.324+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 19/66: 10 records
2025-09-08T10:30:37.384+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 20/66: 10 records
2025-09-08T10:30:37.445+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 21/66: 10 records
2025-09-08T10:30:37.506+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 22/66: 10 records
2025-09-08T10:30:37.564+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 23/66: 10 records
2025-09-08T10:30:37.623+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 24/66: 10 records
2025-09-08T10:30:37.683+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 25/66: 10 records
2025-09-08T10:30:37.737+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 26/66: 10 records
2025-09-08T10:30:37.806+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 27/66: 10 records
2025-09-08T10:30:37.863+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 28/66: 10 records
2025-09-08T10:30:37.923+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 29/66: 10 records
2025-09-08T10:30:38.014+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 30/66: 10 records
2025-09-08T10:30:38.055+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 31/66: 10 records
2025-09-08T10:30:38.106+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 32/66: 10 records
2025-09-08T10:30:38.156+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 33/66: 10 records
2025-09-08T10:30:38.205+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 34/66: 10 records
2025-09-08T10:30:38.254+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 35/66: 10 records
2025-09-08T10:30:38.306+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 36/66: 10 records
2025-09-08T10:30:38.345+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 37/66: 10 records
2025-09-08T10:30:38.394+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 38/66: 10 records
2025-09-08T10:30:38.437+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 39/66: 10 records
2025-09-08T10:30:38.490+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 40/66: 10 records
2025-09-08T10:30:38.542+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 41/66: 10 records
2025-09-08T10:30:38.592+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 42/66: 10 records
2025-09-08T10:30:38.635+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 43/66: 10 records
2025-09-08T10:30:38.687+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 44/66: 10 records
2025-09-08T10:30:38.736+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 45/66: 10 records
2025-09-08T10:30:38.785+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 46/66: 10 records
2025-09-08T10:30:38.837+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 47/66: 10 records
2025-09-08T10:30:38.892+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 48/66: 10 records
2025-09-08T10:30:38.950+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 49/66: 10 records
2025-09-08T10:30:39.000+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 50/66: 10 records
2025-09-08T10:30:39.052+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 51/66: 10 records
2025-09-08T10:30:39.102+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 52/66: 10 records
2025-09-08T10:30:39.153+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 53/66: 10 records
2025-09-08T10:30:39.195+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 54/66: 10 records
2025-09-08T10:30:39.228+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 55/66: 10 records
2025-09-08T10:30:39.266+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 56/66: 10 records
2025-09-08T10:30:39.307+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 57/66: 10 records
2025-09-08T10:30:39.351+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 58/66: 10 records
2025-09-08T10:30:39.397+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 59/66: 10 records
2025-09-08T10:30:39.464+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 60/66: 10 records
2025-09-08T10:30:39.515+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 61/66: 10 records
2025-09-08T10:30:39.566+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 62/66: 10 records
2025-09-08T10:30:39.616+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 63/66: 10 records
2025-09-08T10:30:39.663+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 64/66: 10 records
2025-09-08T10:30:39.719+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 65/66: 10 records
2025-09-08T10:30:39.772+07:00  INFO 20640 --- [qtp1390401525-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 66/66: 3 records
2025-09-08T10:31:03.603+07:00  INFO 20640 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:31:17.632+07:00  INFO 20640 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:31:17.634+07:00  INFO 20640 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:31:59.023+07:00  INFO 20640 --- [qtp1390401525-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01wrlg8mtiqx4spq4ywe23ldw4
2025-09-08T10:31:59.023+07:00  INFO 20640 --- [qtp1390401525-36] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01wavt3a75c5dnzksjfks6xtzi5
2025-09-08T10:31:59.088+07:00 ERROR 20640 --- [qtp1390401525-36] n.d.m.monitor.call.EndpointCallContext   : Start call with component CompanyAuthenticationService, method validate, arguments
[ {
  "tenantId" : "default",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "anon",
  "accountId" : 0,
  "token" : "anon",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node01wavt3a75c5dnzksjfks6xtzi5",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : null,
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:anon"
}, null, {
  "loginId" : null,
  "authorization" : "MW36LHYH4BTT2J6UPSF46QHSABP3RHHWHJB6L55DTMOEV3HOHWM2SMWPUHKCX3JRYDDJUV2O4PCHQJH7EL4CJD5B3W4V3UVATD3CGZC2JPJZHE7DHIHQACRN2GVPVH3W3SSKOGBGG6PINM4DX2CS3WFFWTOHQEGWYZC3HPIRPOLDRCDGZHLTBUPD5XSIW36IYKEFWFUSFFEO2CVV7KVO3YRJODZZXHRNVBUBN6D7ZJ55GU5QFD2NZT6IMBJMKOKCDSGQSUH5ZQHJPCQUAEKHTHNEXKIYZQFXI2YLCONQN7YTF7TA4DE7VWNJ7JP437RYTLMZOEUHHM3MXWF7X3PPLXBXWMLEANJ7ZLPMIUHI6VLNPNSGWTHRZ77TI5L6VYBRFB2UARGNVPPFT5BFNMBWSI6JWG2N4CVVNHRM4UGXJCQPAAG3PYPQ====",
  "company" : "bee",
  "password" : null,
  "timeToLiveInMin" : 0,
  "accessType" : "Employee"
} ]
2025-09-08T10:31:59.088+07:00 ERROR 20640 --- [qtp1390401525-37] n.d.m.monitor.call.EndpointCallContext   : Start call with component CompanyAuthenticationService, method validate, arguments
[ {
  "tenantId" : "default",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "anon",
  "accountId" : 0,
  "token" : "anon",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node01wrlg8mtiqx4spq4ywe23ldw4",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : null,
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:anon"
}, null, {
  "loginId" : null,
  "authorization" : "MW36LHYH4BTT2J6UPSF46QHSABP3RHHWHJB6L55DTMOEV3HOHWM2SMWPUHKCX3JRYDDJUV2O4PCHQJH7EL4CJD5B3W4V3UVATD3CGZC2JPJZHE7DHIHQACRN2GVPVH3W3SSKOGBGG6PINM4DX2CS3WFFWTOHQEGWYZC3HPIRPOLDRCDGZHLTBUPD5XSIW36IYKEFWFUSFFEO2CVV7KVO3YRJODZZXHRNVBUBN6D7ZJ55GU5QFD2NZT6IMBJMKOKCDSGQSUH5ZQHJPCQUAEKHTHNEXKIYZQFXI2YLCONQN7YTF7TA4DE7VWNJ7JP437RYTLMZOEUHHM3MXWF7X3PPLXBXWMLEANJ7ZLPMIUHI6VLNPNSGWTHRZ77TI5L6VYBRFB2UARGNVPPFT5BFNMBWSI6JWG2N4CVVNHRM4UGXJCQPAAG3PYPQ====",
  "company" : "bee",
  "password" : null,
  "timeToLiveInMin" : 0,
  "accessType" : "Employee"
} ]
2025-09-08T10:31:59.088+07:00 ERROR 20640 --- [qtp1390401525-37] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.authenticateCall(RPCController.java:61)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.AuthorizationInfo.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.validate(CompanyAuthenticationService.java:110)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.company.auth.CompanyAuthenticationService$$SpringCGLIB$$0.validate(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.AuthorizationInfo.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.doLogin(CompanyAuthenticationService.java:145)
	at net.datatp.module.company.auth.CompanyAuthenticationService.validate(CompanyAuthenticationService.java:108)
	... 109 common frames omitted

2025-09-08T10:31:59.088+07:00 ERROR 20640 --- [qtp1390401525-36] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.authenticateCall(RPCController.java:61)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.AuthorizationInfo.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.validate(CompanyAuthenticationService.java:110)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.company.auth.CompanyAuthenticationService$$SpringCGLIB$$0.validate(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.AuthorizationInfo.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.doLogin(CompanyAuthenticationService.java:145)
	at net.datatp.module.company.auth.CompanyAuthenticationService.validate(CompanyAuthenticationService.java:108)
	... 109 common frames omitted

2025-09-08T10:31:59.093+07:00  INFO 20640 --- [qtp1390401525-37] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CompanyAuthenticationService/validate
2025-09-08T10:31:59.093+07:00  INFO 20640 --- [qtp1390401525-36] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CompanyAuthenticationService/validate
2025-09-08T10:32:06.733+07:00  INFO 20640 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:32:15.891+07:00  INFO 20640 --- [qtp1390401525-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01wrlg8mtiqx4spq4ywe23ldw4, token = 46a1a6c7781a31079caa3f2bf14c87d3
2025-09-08T10:32:15.899+07:00  INFO 20640 --- [qtp1390401525-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-08T10:32:18.714+07:00  INFO 20640 --- [qtp1390401525-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le logout successfully 
2025-09-08T10:32:20.863+07:00 ERROR 20640 --- [qtp1390401525-41] net.datatp.module.account.AccountLogic   : User hpbachnx try to login into system, but fail
2025-09-08T10:32:20.865+07:00 ERROR 20640 --- [qtp1390401525-41] n.d.m.monitor.call.EndpointCallContext   : Start call with component CompanyAuthenticationService, method authenticate, arguments
[ {
  "tenantId" : "",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "anon",
  "accountId" : 0,
  "token" : "anon",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node01wrlg8mtiqx4spq4ywe23ldw4",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : null,
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : ":anon"
}, null, {
  "loginId" : "hpbachnx",
  "authorization" : null,
  "company" : "",
  "password" : "hpbachnx",
  "timeToLiveInMin" : 10080,
  "accessType" : "Employee"
} ]
2025-09-08T10:32:20.865+07:00 ERROR 20640 --- [qtp1390401525-41] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.authenticateCall(RPCController.java:61)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.entity.AccessToken.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.authenticate(CompanyAuthenticationService.java:82)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.company.auth.CompanyAuthenticationService$$SpringCGLIB$$0.authenticate(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-08T10:32:20.867+07:00  INFO 20640 --- [qtp1390401525-41] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CompanyAuthenticationService/authenticate
2025-09-08T10:32:32.525+07:00 ERROR 20640 --- [qtp1390401525-37] net.datatp.module.account.AccountLogic   : User ciara.vnsgn try to login into system, but fail
2025-09-08T10:32:32.528+07:00 ERROR 20640 --- [qtp1390401525-37] n.d.m.monitor.call.EndpointCallContext   : Start call with component CompanyAuthenticationService, method authenticate, arguments
[ {
  "tenantId" : "",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "anon",
  "accountId" : 0,
  "token" : "anon",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node01wrlg8mtiqx4spq4ywe23ldw4",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : null,
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : ":anon"
}, null, {
  "loginId" : "ciara.vnsgn",
  "authorization" : null,
  "company" : "",
  "password" : "ciara.vnsgn",
  "timeToLiveInMin" : 10080,
  "accessType" : "Employee"
} ]
2025-09-08T10:32:32.528+07:00 ERROR 20640 --- [qtp1390401525-37] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.authenticateCall(RPCController.java:61)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.entity.AccessToken.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.authenticate(CompanyAuthenticationService.java:82)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.company.auth.CompanyAuthenticationService$$SpringCGLIB$$0.authenticate(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-08T10:32:32.529+07:00  INFO 20640 --- [qtp1390401525-37] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CompanyAuthenticationService/authenticate
2025-09-08T10:32:39.578+07:00  INFO 20640 --- [qtp1390401525-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01wrlg8mtiqx4spq4ywe23ldw4, token = 0bfd3b0de77f48be00e90492cefcb677
2025-09-08T10:32:39.587+07:00  INFO 20640 --- [qtp1390401525-41] n.d.m.c.a.CompanyAuthenticationService   : User lana.vnhph is logged in successfully system
2025-09-08T10:32:48.992+07:00  INFO 20640 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@78709532{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-08T10:32:48.992+07:00  INFO 20640 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-08T10:32:48.992+07:00  INFO 20640 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-08T10:32:48.992+07:00  INFO 20640 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-08T10:32:48.993+07:00  INFO 20640 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-08T10:32:48.993+07:00  INFO 20640 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-08T10:32:48.993+07:00  INFO 20640 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-08T10:32:48.993+07:00  INFO 20640 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-08T10:32:48.993+07:00  INFO 20640 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-08T10:32:48.993+07:00  INFO 20640 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-08T10:32:48.993+07:00  INFO 20640 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-08T10:32:48.993+07:00  INFO 20640 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-08T10:32:48.993+07:00  INFO 20640 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-08T10:32:48.993+07:00  INFO 20640 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-08T10:32:48.993+07:00  INFO 20640 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-08T10:32:48.993+07:00  INFO 20640 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-08T10:32:49.010+07:00  INFO 20640 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:32:49.083+07:00  INFO 20640 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-08T10:32:49.087+07:00  INFO 20640 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-08T10:32:49.109+07:00  INFO 20640 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T10:32:49.111+07:00  INFO 20640 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T10:32:49.112+07:00  INFO 20640 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T10:32:49.113+07:00  INFO 20640 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-08T10:32:49.114+07:00  INFO 20640 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-08T10:32:49.114+07:00  INFO 20640 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-08T10:32:49.114+07:00  INFO 20640 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-08T10:32:49.114+07:00  INFO 20640 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-08T10:32:49.114+07:00  INFO 20640 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-08T10:32:49.114+07:00  INFO 20640 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-08T10:32:49.115+07:00  INFO 20640 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-08T10:32:49.115+07:00  INFO 20640 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-08T10:32:49.115+07:00  INFO 20640 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-08T10:32:49.118+07:00  INFO 20640 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@196f9b75{STOPPING}[12.0.15,sto=0]
2025-09-08T10:32:49.120+07:00  INFO 20640 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-08T10:32:49.122+07:00  INFO 20640 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@5a7e0d68{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.10129161455911977720/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@10f03ab0{STOPPED}}
