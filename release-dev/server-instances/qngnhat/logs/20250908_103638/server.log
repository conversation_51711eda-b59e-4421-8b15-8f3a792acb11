2025-09-08T10:36:39.602+07:00  INFO 23147 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 23147 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-08T10:36:39.605+07:00  INFO 23147 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-08T10:36:40.435+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.501+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 62 ms. Found 22 JPA repository interfaces.
2025-09-08T10:36:40.509+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.510+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-08T10:36:40.510+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.517+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-09-08T10:36:40.518+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.521+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-08T10:36:40.562+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.568+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-08T10:36:40.576+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.578+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-08T10:36:40.578+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.582+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-08T10:36:40.585+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.589+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-09-08T10:36:40.593+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.596+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-08T10:36:40.596+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.596+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-08T10:36:40.596+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.603+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-09-08T10:36:40.607+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.610+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-08T10:36:40.613+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.617+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-08T10:36:40.618+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.625+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-08T10:36:40.625+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.628+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-08T10:36:40.628+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.628+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-08T10:36:40.628+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.629+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-08T10:36:40.629+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.633+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-08T10:36:40.633+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.635+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-08T10:36:40.635+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.635+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-08T10:36:40.635+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.645+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-09-08T10:36:40.654+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.660+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-08T10:36:40.660+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.663+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-08T10:36:40.663+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.667+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-08T10:36:40.667+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.672+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 9 JPA repository interfaces.
2025-09-08T10:36:40.672+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.676+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-08T10:36:40.677+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.685+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 12 JPA repository interfaces.
2025-09-08T10:36:40.685+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.694+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-09-08T10:36:40.694+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.708+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 24 JPA repository interfaces.
2025-09-08T10:36:40.708+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.709+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-08T10:36:40.714+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.714+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-08T10:36:40.715+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.723+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-08T10:36:40.725+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.762+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 37 ms. Found 65 JPA repository interfaces.
2025-09-08T10:36:40.762+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.764+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-08T10:36:40.768+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:36:40.771+07:00  INFO 23147 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-08T10:36:41.011+07:00  INFO 23147 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-08T10:36:41.016+07:00  INFO 23147 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-08T10:36:41.278+07:00  WARN 23147 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-08T10:36:41.499+07:00  INFO 23147 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-08T10:36:41.504+07:00  INFO 23147 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-08T10:36:41.530+07:00  INFO 23147 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-08T10:36:41.531+07:00  INFO 23147 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1805 ms
2025-09-08T10:36:41.624+07:00  WARN 23147 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T10:36:41.624+07:00  INFO 23147 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-08T10:36:41.871+07:00  INFO 23147 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@7ef6a063
2025-09-08T10:36:41.871+07:00  INFO 23147 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-08T10:36:41.878+07:00  WARN 23147 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T10:36:41.878+07:00  INFO 23147 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-08T10:36:41.882+07:00  INFO 23147 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@e822394
2025-09-08T10:36:41.883+07:00  INFO 23147 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-08T10:36:41.883+07:00  WARN 23147 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T10:36:41.883+07:00  INFO 23147 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-08T10:36:41.903+07:00  INFO 23147 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@76e56b17
2025-09-08T10:36:41.903+07:00  INFO 23147 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-08T10:36:41.903+07:00  WARN 23147 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T10:36:41.903+07:00  INFO 23147 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-08T10:36:41.914+07:00  INFO 23147 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@2a7b4b19
2025-09-08T10:36:41.914+07:00  INFO 23147 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-08T10:36:41.914+07:00  WARN 23147 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T10:36:41.914+07:00  INFO 23147 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-08T10:36:41.927+07:00  INFO 23147 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@6f66ab8f
2025-09-08T10:36:41.927+07:00  INFO 23147 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-08T10:36:41.927+07:00  INFO 23147 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-08T10:36:41.969+07:00  INFO 23147 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-08T10:36:41.971+07:00  INFO 23147 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@2a62384{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.6756123924761013677/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@c4f6d5b{STARTED}}
2025-09-08T10:36:41.972+07:00  INFO 23147 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@2a62384{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.6756123924761013677/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@c4f6d5b{STARTED}}
2025-09-08T10:36:41.973+07:00  INFO 23147 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@de06f28{STARTING}[12.0.15,sto=0] @3044ms
2025-09-08T10:36:42.072+07:00  INFO 23147 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-08T10:36:42.102+07:00  INFO 23147 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-08T10:36:42.116+07:00  INFO 23147 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-08T10:36:42.239+07:00  INFO 23147 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-08T10:36:42.273+07:00  WARN 23147 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-08T10:36:42.957+07:00  INFO 23147 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-08T10:36:42.966+07:00  INFO 23147 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@6aed9736] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-08T10:36:43.098+07:00  INFO 23147 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T10:36:43.273+07:00  INFO 23147 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.tms", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-08T10:36:43.275+07:00  INFO 23147 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-08T10:36:43.281+07:00  INFO 23147 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-08T10:36:43.282+07:00  INFO 23147 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-08T10:36:43.307+07:00  INFO 23147 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-08T10:36:43.313+07:00  WARN 23147 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-08T10:36:45.452+07:00  INFO 23147 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-08T10:36:45.453+07:00  INFO 23147 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@33cd3ddc] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-08T10:36:45.649+07:00  WARN 23147 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-08T10:36:45.649+07:00  WARN 23147 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-08T10:36:45.656+07:00  WARN 23147 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-08T10:36:45.656+07:00  WARN 23147 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-08T10:36:45.670+07:00  WARN 23147 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-08T10:36:45.670+07:00  WARN 23147 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-08T10:36:46.180+07:00  INFO 23147 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T10:36:46.186+07:00  INFO 23147 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-08T10:36:46.187+07:00  INFO 23147 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-08T10:36:46.206+07:00  INFO 23147 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-08T10:36:46.216+07:00  WARN 23147 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-08T10:36:46.722+07:00  INFO 23147 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-08T10:36:46.722+07:00  INFO 23147 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@20ca93be] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-08T10:36:46.819+07:00  WARN 23147 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-08T10:36:46.819+07:00  WARN 23147 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-08T10:36:47.089+07:00  INFO 23147 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T10:36:47.118+07:00  INFO 23147 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-08T10:36:47.122+07:00  INFO 23147 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-08T10:36:47.122+07:00  INFO 23147 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-08T10:36:47.129+07:00  WARN 23147 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-08T10:36:47.257+07:00  INFO 23147 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-08T10:36:47.685+07:00  INFO 23147 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-08T10:36:47.688+07:00  INFO 23147 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-08T10:36:47.721+07:00  INFO 23147 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-08T10:36:47.768+07:00  INFO 23147 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-08T10:36:47.835+07:00  INFO 23147 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-08T10:36:47.863+07:00  INFO 23147 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-08T10:36:47.889+07:00  INFO 23147 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 431436438ms : this is harmless.
2025-09-08T10:36:47.897+07:00  INFO 23147 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-08T10:36:47.900+07:00  INFO 23147 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-08T10:36:47.913+07:00  INFO 23147 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 438559999ms : this is harmless.
2025-09-08T10:36:47.914+07:00  INFO 23147 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-08T10:36:47.927+07:00  INFO 23147 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-08T10:36:47.928+07:00  INFO 23147 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-08T10:36:48.930+07:00  INFO 23147 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-08T10:36:48.930+07:00  INFO 23147 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-08T10:36:48.931+07:00  WARN 23147 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-08T10:36:49.679+07:00  INFO 23147 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 08/09/2025@10:30:00+0700 to 08/09/2025@10:45:00+0700
2025-09-08T10:36:49.679+07:00  INFO 23147 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 08/09/2025@10:30:00+0700 to 08/09/2025@10:45:00+0700
2025-09-08T10:36:50.798+07:00  INFO 23147 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-08T10:36:50.798+07:00  INFO 23147 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-08T10:36:50.799+07:00  WARN 23147 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-08T10:36:51.149+07:00  INFO 23147 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-08T10:36:51.149+07:00  INFO 23147 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-08T10:36:51.149+07:00  INFO 23147 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-08T10:36:51.149+07:00  INFO 23147 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-08T10:36:51.149+07:00  INFO 23147 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-08T10:36:52.725+07:00  WARN 23147 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 045ffccb-5bd0-4d68-8d0c-ed0705699caf

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-08T10:36:52.728+07:00  INFO 23147 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-08T10:36:53.016+07:00  INFO 23147 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-08T10:36:53.016+07:00  INFO 23147 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-08T10:36:53.016+07:00  INFO 23147 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-08T10:36:53.016+07:00  INFO 23147 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-08T10:36:53.016+07:00  INFO 23147 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-08T10:36:53.016+07:00  INFO 23147 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-08T10:36:53.016+07:00  INFO 23147 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-08T10:36:53.016+07:00  INFO 23147 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-08T10:36:53.016+07:00  INFO 23147 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-08T10:36:53.016+07:00  INFO 23147 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-08T10:36:53.016+07:00  INFO 23147 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-08T10:36:53.016+07:00  INFO 23147 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-08T10:36:53.019+07:00  INFO 23147 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-08T10:36:53.019+07:00  INFO 23147 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-08T10:36:53.019+07:00  INFO 23147 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-08T10:36:53.082+07:00  INFO 23147 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-08T10:36:53.082+07:00  INFO 23147 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-08T10:36:53.083+07:00  INFO 23147 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-08T10:36:53.091+07:00  INFO 23147 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@2f3618ed{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-08T10:36:53.092+07:00  INFO 23147 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-08T10:36:53.093+07:00  INFO 23147 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-08T10:36:53.127+07:00  INFO 23147 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-08T10:36:53.127+07:00  INFO 23147 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-08T10:36:53.133+07:00  INFO 23147 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.9 seconds (process running for 14.204)
2025-09-08T10:37:06.067+07:00  INFO 23147 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:37:42.988+07:00  INFO 23147 --- [qtp491239265-37] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-08T10:37:56.194+07:00  INFO 23147 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-08T10:37:56.219+07:00  INFO 23147 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:38:03.229+07:00  INFO 23147 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:38:24.967+07:00  INFO 23147 --- [qtp491239265-34] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node015qr76rtpxgi617fco357h6huc0
2025-09-08T10:38:25.133+07:00  INFO 23147 --- [qtp491239265-34] n.d.module.session.ClientSessionManager  : Add a client session id = node015qr76rtpxgi617fco357h6huc0, token = c29fae86ef5f6920d555e869208673b8
2025-09-08T10:38:25.544+07:00  INFO 23147 --- [qtp491239265-34] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-08T10:38:25.603+07:00  INFO 23147 --- [qtp491239265-40] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-09-08T10:38:25.604+07:00  INFO 23147 --- [qtp491239265-40] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/SyncBFSUsernameToLoginId.groovy"
}
2025-09-08T10:38:25.902+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 1/68: 10 records
2025-09-08T10:38:26.012+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 2/68: 10 records
2025-09-08T10:38:26.126+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 3/68: 10 records
2025-09-08T10:38:26.204+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 4/68: 10 records
2025-09-08T10:38:26.272+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 5/68: 10 records
2025-09-08T10:38:26.335+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 6/68: 10 records
2025-09-08T10:38:26.397+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 7/68: 10 records
2025-09-08T10:38:26.458+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 8/68: 10 records
2025-09-08T10:38:26.515+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 9/68: 10 records
2025-09-08T10:38:26.573+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 10/68: 10 records
2025-09-08T10:38:26.631+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 11/68: 10 records
2025-09-08T10:38:26.688+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 12/68: 10 records
2025-09-08T10:38:26.749+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 13/68: 10 records
2025-09-08T10:38:26.865+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 14/68: 10 records
2025-09-08T10:38:26.941+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 15/68: 10 records
2025-09-08T10:38:27.003+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 16/68: 10 records
2025-09-08T10:38:27.059+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 17/68: 10 records
2025-09-08T10:38:27.116+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 18/68: 10 records
2025-09-08T10:38:27.172+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 19/68: 10 records
2025-09-08T10:38:27.234+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 20/68: 10 records
2025-09-08T10:38:27.297+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 21/68: 10 records
2025-09-08T10:38:27.352+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 22/68: 10 records
2025-09-08T10:38:27.410+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 23/68: 10 records
2025-09-08T10:38:27.468+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 24/68: 10 records
2025-09-08T10:38:27.525+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 25/68: 10 records
2025-09-08T10:38:27.580+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 26/68: 10 records
2025-09-08T10:38:27.642+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 27/68: 10 records
2025-09-08T10:38:27.698+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 28/68: 10 records
2025-09-08T10:38:27.756+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 29/68: 10 records
2025-09-08T10:38:27.812+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 30/68: 10 records
2025-09-08T10:38:27.869+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 31/68: 10 records
2025-09-08T10:38:27.920+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 32/68: 10 records
2025-09-08T10:38:27.977+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 33/68: 10 records
2025-09-08T10:38:28.031+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 34/68: 10 records
2025-09-08T10:38:28.084+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 35/68: 10 records
2025-09-08T10:38:28.140+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 36/68: 10 records
2025-09-08T10:38:28.195+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 37/68: 10 records
2025-09-08T10:38:28.250+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 38/68: 10 records
2025-09-08T10:38:28.294+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 39/68: 10 records
2025-09-08T10:38:28.349+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 40/68: 10 records
2025-09-08T10:38:28.397+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 41/68: 10 records
2025-09-08T10:38:28.451+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 42/68: 10 records
2025-09-08T10:38:28.505+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 43/68: 10 records
2025-09-08T10:38:28.560+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 44/68: 10 records
2025-09-08T10:38:28.621+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 45/68: 10 records
2025-09-08T10:38:28.675+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 46/68: 10 records
2025-09-08T10:38:28.735+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 47/68: 10 records
2025-09-08T10:38:28.801+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 48/68: 10 records
2025-09-08T10:38:28.870+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 49/68: 10 records
2025-09-08T10:38:28.935+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 50/68: 10 records
2025-09-08T10:38:28.990+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 51/68: 10 records
2025-09-08T10:38:29.038+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 52/68: 10 records
2025-09-08T10:38:29.088+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 53/68: 10 records
2025-09-08T10:38:29.141+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 54/68: 10 records
2025-09-08T10:38:29.208+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 55/68: 10 records
2025-09-08T10:38:29.266+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 56/68: 10 records
2025-09-08T10:38:29.304+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 57/68: 10 records
2025-09-08T10:38:29.341+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 58/68: 10 records
2025-09-08T10:38:29.385+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 59/68: 10 records
2025-09-08T10:38:29.446+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 60/68: 10 records
2025-09-08T10:38:29.492+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 61/68: 10 records
2025-09-08T10:38:29.551+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 62/68: 10 records
2025-09-08T10:38:29.615+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 63/68: 10 records
2025-09-08T10:38:29.668+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 64/68: 10 records
2025-09-08T10:38:29.728+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 65/68: 10 records
2025-09-08T10:38:29.779+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 66/68: 10 records
2025-09-08T10:38:29.844+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 67/68: 10 records
2025-09-08T10:38:29.894+07:00  INFO 23147 --- [qtp491239265-40] migration.server.hr.SyncBFSOneUsername   : Processing batch 68/68: 1 records
2025-09-08T10:39:06.349+07:00  INFO 23147 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:39:55.466+07:00  INFO 23147 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-08T10:39:55.506+07:00  INFO 23147 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:40:02.521+07:00  INFO 23147 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:40:02.526+07:00  INFO 23147 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-08T10:40:37.192+07:00  INFO 23147 --- [qtp491239265-62] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01s1mkvgzij5gu1doxfrlqvkj4j1
2025-09-08T10:40:37.192+07:00  INFO 23147 --- [qtp491239265-40] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0chy8bpt3yebiuodmf3l7brtb2
2025-09-08T10:40:37.227+07:00 ERROR 23147 --- [qtp491239265-40] n.d.m.monitor.call.EndpointCallContext   : Start call with component CompanyAuthenticationService, method validate, arguments
[ {
  "tenantId" : "default",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "anon",
  "accountId" : 0,
  "token" : "anon",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0chy8bpt3yebiuodmf3l7brtb2",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : null,
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:anon"
}, null, {
  "loginId" : null,
  "authorization" : "MW36LHYH4BTT2J6UPSF46QHSABP3RHHWHJB6L55DTMOEV3HOHWMUJQKFV2J5322DAJ5CZEWTXQTMVB2N2PKABFO5WH6JPJDUU5E36PUM64L7XMO5KS5JQMOZRCXUDDHEOS2DTH47O6T7VYFCE62HVPC6QUE3Y677G7SBTWJYQSFVNL5KKZOLEVW4LXG7B6E4IITMXX573VPQPPDQS4XYMXPZQX3R5HHZWGIT3NV3UHL7MVXNQSCWAHTWRAGLH5RAPR3WNLJA6FUCN53YB4Z7IRPLAIIM2JTOGHGPNLKO6XRQNFXU4GMO53BS2IC5LHEJRPBDUVVCHKVNPZAT5UXPD7BLOU2GWRT5XWEF3D74LE6YMRTQBNVGAC7SN3U2JWGLEILLAD2RJ4JDDVFHT7N3TRFNPM2VNQOKWJINJGBS27FKEY3GILPA====",
  "company" : "beehph",
  "password" : null,
  "timeToLiveInMin" : 0,
  "accessType" : "Employee"
} ]
2025-09-08T10:40:37.227+07:00 ERROR 23147 --- [qtp491239265-40] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.authenticateCall(RPCController.java:61)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.AuthorizationInfo.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.validate(CompanyAuthenticationService.java:110)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.company.auth.CompanyAuthenticationService$$SpringCGLIB$$0.validate(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.AuthorizationInfo.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.doLogin(CompanyAuthenticationService.java:145)
	at net.datatp.module.company.auth.CompanyAuthenticationService.validate(CompanyAuthenticationService.java:108)
	... 109 common frames omitted

2025-09-08T10:40:37.235+07:00  INFO 23147 --- [qtp491239265-40] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CompanyAuthenticationService/validate
2025-09-08T10:40:37.241+07:00 ERROR 23147 --- [qtp491239265-62] n.d.m.monitor.call.EndpointCallContext   : Start call with component CompanyAuthenticationService, method validate, arguments
[ {
  "tenantId" : "default",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "anon",
  "accountId" : 0,
  "token" : "anon",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node01s1mkvgzij5gu1doxfrlqvkj4j1",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : null,
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:anon"
}, null, {
  "loginId" : null,
  "authorization" : "MW36LHYH4BTT2J6UPSF46QHSABP3RHHWHJB6L55DTMOEV3HOHWMUJQKFV2J5322DAJ5CZEWTXQTMVB2N2PKABFO5WH6JPJDUU5E36PUM64L7XMO5KS5JQMOZRCXUDDHEOS2DTH47O6T7VYFCE62HVPC6QUE3Y677G7SBTWJYQSFVNL5KKZOLEVW4LXG7B6E4IITMXX573VPQPPDQS4XYMXPZQX3R5HHZWGIT3NV3UHL7MVXNQSCWAHTWRAGLH5RAPR3WNLJA6FUCN53YB4Z7IRPLAIIM2JTOGHGPNLKO6XRQNFXU4GMO53BS2IC5LHEJRPBDUVVCHKVNPZAT5UXPD7BLOU2GWRT5XWEF3D74LE6YMRTQBNVGAC7SN3U2JWGLEILLAD2RJ4JDDVFHT7N3TRFNPM2VNQOKWJINJGBS27FKEY3GILPA====",
  "company" : "beehph",
  "password" : null,
  "timeToLiveInMin" : 0,
  "accessType" : "Employee"
} ]
2025-09-08T10:40:37.241+07:00 ERROR 23147 --- [qtp491239265-62] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.authenticateCall(RPCController.java:61)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.AuthorizationInfo.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.validate(CompanyAuthenticationService.java:110)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.company.auth.CompanyAuthenticationService$$SpringCGLIB$$0.validate(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 93 common frames omitted
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.AuthorizationInfo.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.doLogin(CompanyAuthenticationService.java:145)
	at net.datatp.module.company.auth.CompanyAuthenticationService.validate(CompanyAuthenticationService.java:108)
	... 104 common frames omitted

2025-09-08T10:40:37.243+07:00  INFO 23147 --- [qtp491239265-62] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CompanyAuthenticationService/validate
2025-09-08T10:40:38.504+07:00  INFO 23147 --- [qtp491239265-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01s1mkvgzij5gu1doxfrlqvkj4j1, token = eebd697fc5870b0e6f7dd5fc5c41cb0b
2025-09-08T10:40:38.511+07:00  INFO 23147 --- [qtp491239265-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-08T10:41:05.793+07:00  INFO 23147 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:41:59.925+07:00  INFO 23147 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-09-08T10:41:59.958+07:00  INFO 23147 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:42:06.971+07:00  INFO 23147 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:43:05.097+07:00  INFO 23147 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:44:00.227+07:00  INFO 23147 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-08T10:44:00.256+07:00  INFO 23147 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-08T10:44:06.267+07:00  INFO 23147 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:45:04.367+07:00  INFO 23147 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:45:04.368+07:00  INFO 23147 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-08T10:45:04.371+07:00  INFO 23147 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-08T10:45:04.377+07:00  INFO 23147 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 08/09/2025@10:45:04+0700
2025-09-08T10:45:04.396+07:00  INFO 23147 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 08/09/2025@10:45:00+0700 to 08/09/2025@11:00:00+0700
2025-09-08T10:45:04.396+07:00  INFO 23147 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 08/09/2025@10:45:00+0700 to 08/09/2025@11:00:00+0700
2025-09-08T10:45:40.315+07:00  INFO 23147 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@2f3618ed{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-08T10:45:40.315+07:00  INFO 23147 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-08T10:45:40.315+07:00  INFO 23147 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-08T10:45:40.315+07:00  INFO 23147 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-08T10:45:40.316+07:00  INFO 23147 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-08T10:45:40.316+07:00  INFO 23147 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-08T10:45:40.316+07:00  INFO 23147 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-08T10:45:40.316+07:00  INFO 23147 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-08T10:45:40.316+07:00  INFO 23147 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-08T10:45:40.316+07:00  INFO 23147 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-08T10:45:40.316+07:00  INFO 23147 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-08T10:45:40.316+07:00  INFO 23147 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-08T10:45:40.316+07:00  INFO 23147 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-08T10:45:40.316+07:00  INFO 23147 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-08T10:45:40.316+07:00  INFO 23147 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-08T10:45:40.316+07:00  INFO 23147 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-08T10:45:40.333+07:00  INFO 23147 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:45:40.386+07:00  INFO 23147 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-08T10:45:40.391+07:00  INFO 23147 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-08T10:45:40.412+07:00  INFO 23147 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T10:45:40.413+07:00  INFO 23147 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T10:45:40.414+07:00  INFO 23147 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T10:45:40.414+07:00  INFO 23147 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-08T10:45:40.415+07:00  INFO 23147 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-08T10:45:40.415+07:00  INFO 23147 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-08T10:45:40.415+07:00  INFO 23147 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-08T10:45:40.415+07:00  INFO 23147 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-08T10:45:40.416+07:00  INFO 23147 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-08T10:45:40.416+07:00  INFO 23147 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-08T10:45:40.416+07:00  INFO 23147 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-08T10:45:40.416+07:00  INFO 23147 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-08T10:45:40.416+07:00  INFO 23147 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-08T10:45:40.418+07:00  INFO 23147 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@de06f28{STOPPING}[12.0.15,sto=0]
2025-09-08T10:45:40.419+07:00  INFO 23147 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-08T10:45:40.420+07:00  INFO 23147 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@2a62384{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.6756123924761013677/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@c4f6d5b{STOPPED}}
