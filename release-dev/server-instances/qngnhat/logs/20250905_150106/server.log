2025-09-05T15:01:07.051+07:00  INFO 57226 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 57226 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-05T15:01:07.052+07:00  INFO 57226 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-05T15:01:07.757+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.822+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 62 ms. Found 22 JPA repository interfaces.
2025-09-05T15:01:07.831+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.833+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-05T15:01:07.833+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.840+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-09-05T15:01:07.841+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.844+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-05T15:01:07.889+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.894+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-05T15:01:07.902+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.904+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-05T15:01:07.904+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.907+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-05T15:01:07.910+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.913+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-05T15:01:07.917+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.919+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 3 JPA repository interfaces.
2025-09-05T15:01:07.919+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.919+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-05T15:01:07.919+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.925+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 10 JPA repository interfaces.
2025-09-05T15:01:07.930+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.932+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-05T15:01:07.935+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.938+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-05T15:01:07.938+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.945+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-05T15:01:07.945+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.948+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-05T15:01:07.948+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.948+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-05T15:01:07.948+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.949+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-05T15:01:07.949+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.953+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-05T15:01:07.953+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.954+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-05T15:01:07.954+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.954+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-05T15:01:07.955+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.964+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-09-05T15:01:07.974+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.980+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-05T15:01:07.980+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.983+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-05T15:01:07.983+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.987+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-05T15:01:07.987+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.992+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 9 JPA repository interfaces.
2025-09-05T15:01:07.992+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:07.996+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-05T15:01:07.997+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:08.004+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-05T15:01:08.004+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:08.013+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-09-05T15:01:08.013+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:08.026+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 24 JPA repository interfaces.
2025-09-05T15:01:08.026+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:08.027+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-05T15:01:08.033+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:08.033+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-05T15:01:08.033+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:08.040+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-05T15:01:08.042+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:08.075+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 32 ms. Found 65 JPA repository interfaces.
2025-09-05T15:01:08.075+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:08.076+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-05T15:01:08.081+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T15:01:08.084+07:00  INFO 57226 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-05T15:01:08.293+07:00  INFO 57226 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-05T15:01:08.296+07:00  INFO 57226 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-05T15:01:08.564+07:00  WARN 57226 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-05T15:01:08.770+07:00  INFO 57226 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-05T15:01:08.772+07:00  INFO 57226 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-05T15:01:08.783+07:00  INFO 57226 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-05T15:01:08.783+07:00  INFO 57226 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1600 ms
2025-09-05T15:01:08.832+07:00  WARN 57226 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-05T15:01:08.833+07:00  INFO 57226 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-05T15:01:08.934+07:00  INFO 57226 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@3aee72d8
2025-09-05T15:01:08.935+07:00  INFO 57226 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-05T15:01:08.939+07:00  WARN 57226 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-05T15:01:08.939+07:00  INFO 57226 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-05T15:01:08.945+07:00  INFO 57226 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@147a8d7c
2025-09-05T15:01:08.945+07:00  INFO 57226 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-05T15:01:08.945+07:00  WARN 57226 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-05T15:01:08.945+07:00  INFO 57226 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-05T15:01:08.955+07:00  INFO 57226 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@a19fe87
2025-09-05T15:01:08.955+07:00  INFO 57226 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-05T15:01:08.955+07:00  WARN 57226 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-05T15:01:08.955+07:00  INFO 57226 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-05T15:01:08.967+07:00  INFO 57226 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@11544ddd
2025-09-05T15:01:08.967+07:00  INFO 57226 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-05T15:01:08.967+07:00  WARN 57226 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-05T15:01:08.967+07:00  INFO 57226 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-05T15:01:08.980+07:00  INFO 57226 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@5ac3d009
2025-09-05T15:01:08.980+07:00  INFO 57226 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-05T15:01:08.980+07:00  INFO 57226 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-05T15:01:09.023+07:00  INFO 57226 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-05T15:01:09.025+07:00  INFO 57226 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@4af32714{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.7992362729608486687/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@794fbf0d{STARTED}}
2025-09-05T15:01:09.025+07:00  INFO 57226 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@4af32714{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.7992362729608486687/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@794fbf0d{STARTED}}
2025-09-05T15:01:09.027+07:00  INFO 57226 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@35be9318{STARTING}[12.0.15,sto=0] @2485ms
2025-09-05T15:01:09.125+07:00  INFO 57226 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-05T15:01:09.153+07:00  INFO 57226 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-05T15:01:09.167+07:00  INFO 57226 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-05T15:01:09.289+07:00  INFO 57226 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-05T15:01:09.357+07:00  WARN 57226 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-05T15:01:09.966+07:00  INFO 57226 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-05T15:01:09.973+07:00  INFO 57226 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@69117714] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-05T15:01:10.100+07:00  INFO 57226 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-05T15:01:10.282+07:00  INFO 57226 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.tms", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-05T15:01:10.284+07:00  INFO 57226 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-05T15:01:10.290+07:00  INFO 57226 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-05T15:01:10.291+07:00  INFO 57226 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-05T15:01:10.316+07:00  INFO 57226 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-05T15:01:10.320+07:00  WARN 57226 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-05T15:01:12.370+07:00  INFO 57226 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-05T15:01:12.371+07:00  INFO 57226 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@6893488d] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-05T15:01:12.566+07:00  WARN 57226 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-05T15:01:12.566+07:00  WARN 57226 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-05T15:01:12.573+07:00  WARN 57226 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-05T15:01:12.573+07:00  WARN 57226 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-05T15:01:12.586+07:00  WARN 57226 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-05T15:01:12.586+07:00  WARN 57226 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-05T15:01:13.026+07:00  INFO 57226 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-05T15:01:13.032+07:00  INFO 57226 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-05T15:01:13.033+07:00  INFO 57226 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-05T15:01:13.052+07:00  INFO 57226 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-05T15:01:13.063+07:00  WARN 57226 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-05T15:01:13.553+07:00  INFO 57226 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-05T15:01:13.553+07:00  INFO 57226 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@6c343471] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-05T15:01:13.621+07:00  WARN 57226 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-05T15:01:13.621+07:00  WARN 57226 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-05T15:01:13.939+07:00  INFO 57226 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-05T15:01:13.971+07:00  INFO 57226 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-05T15:01:13.976+07:00  INFO 57226 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-05T15:01:13.976+07:00  INFO 57226 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-05T15:01:13.983+07:00  WARN 57226 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-05T15:01:14.116+07:00  INFO 57226 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-05T15:01:14.576+07:00  INFO 57226 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-05T15:01:14.579+07:00  INFO 57226 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-05T15:01:14.612+07:00  INFO 57226 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-05T15:01:14.656+07:00  INFO 57226 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-05T15:01:14.705+07:00  INFO 57226 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-05T15:01:14.732+07:00  INFO 57226 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-05T15:01:14.753+07:00  INFO 57226 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 187974290ms : this is harmless.
2025-09-05T15:01:14.761+07:00  INFO 57226 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-05T15:01:14.764+07:00  INFO 57226 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-05T15:01:14.777+07:00  INFO 57226 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 195097851ms : this is harmless.
2025-09-05T15:01:14.778+07:00  INFO 57226 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-05T15:01:14.791+07:00  INFO 57226 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-05T15:01:14.792+07:00  INFO 57226 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-05T15:01:15.866+07:00  INFO 57226 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-05T15:01:15.866+07:00  INFO 57226 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-05T15:01:15.867+07:00  WARN 57226 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-05T15:01:16.535+07:00  INFO 57226 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 05/09/2025@15:00:00+0700 to 05/09/2025@15:15:00+0700
2025-09-05T15:01:16.535+07:00  INFO 57226 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 05/09/2025@15:00:00+0700 to 05/09/2025@15:15:00+0700
2025-09-05T15:01:17.595+07:00  INFO 57226 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-05T15:01:17.595+07:00  INFO 57226 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-05T15:01:17.595+07:00  WARN 57226 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-05T15:01:17.870+07:00  INFO 57226 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-05T15:01:17.871+07:00  INFO 57226 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-05T15:01:17.871+07:00  INFO 57226 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-05T15:01:17.871+07:00  INFO 57226 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-05T15:01:17.871+07:00  INFO 57226 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-05T15:01:19.524+07:00  WARN 57226 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 216993de-2fc6-42c2-8441-32e1ba17e1ec

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-05T15:01:19.527+07:00  INFO 57226 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-05T15:01:19.831+07:00  INFO 57226 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-05T15:01:19.831+07:00  INFO 57226 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-05T15:01:19.831+07:00  INFO 57226 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-05T15:01:19.831+07:00  INFO 57226 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-05T15:01:19.832+07:00  INFO 57226 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-05T15:01:19.832+07:00  INFO 57226 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-05T15:01:19.832+07:00  INFO 57226 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-05T15:01:19.832+07:00  INFO 57226 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-05T15:01:19.832+07:00  INFO 57226 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-05T15:01:19.832+07:00  INFO 57226 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-05T15:01:19.832+07:00  INFO 57226 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-05T15:01:19.832+07:00  INFO 57226 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-05T15:01:19.834+07:00  INFO 57226 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-05T15:01:19.834+07:00  INFO 57226 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-05T15:01:19.834+07:00  INFO 57226 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-05T15:01:19.888+07:00  INFO 57226 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-05T15:01:19.888+07:00  INFO 57226 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-05T15:01:19.890+07:00  INFO 57226 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-05T15:01:19.898+07:00  INFO 57226 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@1a0f8da6{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-05T15:01:19.899+07:00  INFO 57226 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-05T15:01:19.899+07:00  INFO 57226 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-05T15:01:19.934+07:00  INFO 57226 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-05T15:01:19.934+07:00  INFO 57226 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-05T15:01:19.939+07:00  INFO 57226 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.147 seconds (process running for 13.395)
2025-09-05T15:01:35.734+07:00  INFO 57226 --- [qtp907681414-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-05T15:02:06.940+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:02:22.980+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:02:23.005+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:02:38.428+07:00  INFO 57226 --- [qtp907681414-40] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01qoj2nnt0v8fi1ooxfibdwnbs00
2025-09-05T15:02:38.428+07:00  INFO 57226 --- [qtp907681414-61] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01ifyrit9jv0q8qybnyb78k4fi1
2025-09-05T15:02:38.517+07:00 ERROR 57226 --- [qtp907681414-40] n.d.m.monitor.call.EndpointCallContext   : Start call with component CompanyAuthenticationService, method validate, arguments
[ {
  "tenantId" : "default",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "anon",
  "accountId" : 0,
  "token" : "anon",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node01qoj2nnt0v8fi1ooxfibdwnbs00",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : null,
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:anon"
}, null, {
  "loginId" : null,
  "authorization" : "MW36LHYH4BTT2J6UPSF46QHSABP3RHHWHJB6L55DTMOEV3HOHWMV7EUN6S2HPW5FTLEBP245E2J3OXFP5CIOPMUKPMDLGZDBTRAWKSGKDFJIF7GUCX2XC5JKBRTB5ZCSANXRTS3TZWL7Z5Q36M4VDKD4A2CYSXEGJVZRVGREAUCROUKXLELKEJUP6ZWEUAF43UTXK6JOHIFSTTVLM2JYMOZW3RSWNE4NUJHHUMJM5UYZJURUAC7WYB2Q4HEQX7POJU7JVEODSQK44NX3TPZU2S5QJ5FAZ7E6GOEMFLWLTZYYDIX6NDMGHW5HPQWOYUTBOUI5D7NAFXIOMKBAN562OSB7ROTJSLZ2LWCAE5NJF4VHLRGVRYDRRLMDUPMTCUUSFSMZMNINEMPI7RN2NRRRSUZSIB4V2PC5TPORPY34O3UYT7OXWSAA====",
  "company" : "beehcm",
  "password" : null,
  "timeToLiveInMin" : 0,
  "accessType" : "Employee"
} ]
2025-09-05T15:02:38.517+07:00 ERROR 57226 --- [qtp907681414-40] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.authenticateCall(RPCController.java:61)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.AuthorizationInfo.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.validate(CompanyAuthenticationService.java:110)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.company.auth.CompanyAuthenticationService$$SpringCGLIB$$0.validate(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.AuthorizationInfo.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.doLogin(CompanyAuthenticationService.java:145)
	at net.datatp.module.company.auth.CompanyAuthenticationService.validate(CompanyAuthenticationService.java:108)
	... 109 common frames omitted

2025-09-05T15:02:38.520+07:00  INFO 57226 --- [qtp907681414-40] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CompanyAuthenticationService/validate
2025-09-05T15:02:38.526+07:00 ERROR 57226 --- [qtp907681414-61] n.d.m.monitor.call.EndpointCallContext   : Start call with component CompanyAuthenticationService, method validate, arguments
[ {
  "tenantId" : "default",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "anon",
  "accountId" : 0,
  "token" : "anon",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node01ifyrit9jv0q8qybnyb78k4fi1",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : null,
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:anon"
}, null, {
  "loginId" : null,
  "authorization" : "MW36LHYH4BTT2J6UPSF46QHSABP3RHHWHJB6L55DTMOEV3HOHWMV7EUN6S2HPW5FTLEBP245E2J3OXFP5CIOPMUKPMDLGZDBTRAWKSGKDFJIF7GUCX2XC5JKBRTB5ZCSANXRTS3TZWL7Z5Q36M4VDKD4A2CYSXEGJVZRVGREAUCROUKXLELKEJUP6ZWEUAF43UTXK6JOHIFSTTVLM2JYMOZW3RSWNE4NUJHHUMJM5UYZJURUAC7WYB2Q4HEQX7POJU7JVEODSQK44NX3TPZU2S5QJ5FAZ7E6GOEMFLWLTZYYDIX6NDMGHW5HPQWOYUTBOUI5D7NAFXIOMKBAN562OSB7ROTJSLZ2LWCAE5NJF4VHLRGVRYDRRLMDUPMTCUUSFSMZMNINEMPI7RN2NRRRSUZSIB4V2PC5TPORPY34O3UYT7OXWSAA====",
  "company" : "beehcm",
  "password" : null,
  "timeToLiveInMin" : 0,
  "accessType" : "Employee"
} ]
2025-09-05T15:02:38.526+07:00 ERROR 57226 --- [qtp907681414-61] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.authenticateCall(RPCController.java:61)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.AuthorizationInfo.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.validate(CompanyAuthenticationService.java:110)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.company.auth.CompanyAuthenticationService$$SpringCGLIB$$0.validate(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 93 common frames omitted
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.AuthorizationInfo.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.doLogin(CompanyAuthenticationService.java:145)
	at net.datatp.module.company.auth.CompanyAuthenticationService.validate(CompanyAuthenticationService.java:108)
	... 104 common frames omitted

2025-09-05T15:02:38.528+07:00  INFO 57226 --- [qtp907681414-61] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CompanyAuthenticationService/validate
2025-09-05T15:03:05.071+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:03:25.770+07:00  INFO 57226 --- [qtp907681414-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node019j5i1bobgnxq1bhv0ekh8xvlb2
2025-09-05T15:03:25.948+07:00  INFO 57226 --- [qtp907681414-37] n.d.module.session.ClientSessionManager  : Add a client session id = node019j5i1bobgnxq1bhv0ekh8xvlb2, token = 4ba4715c36a8d294fcadecda61766561
2025-09-05T15:03:26.352+07:00  INFO 57226 --- [qtp907681414-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-05T15:03:26.382+07:00  INFO 57226 --- [qtp907681414-40] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-09-05T15:03:26.383+07:00  INFO 57226 --- [qtp907681414-40] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/SyncBFSUsernameToLoginId.groovy"
}
2025-09-05T15:03:26.626+07:00 ERROR 57226 --- [qtp907681414-40] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

groovy.lang.MissingPropertyException: No such property: loginId for class: migration.server.hr.SyncBFSOneUsername$1
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at migration.server.hr.SyncBFSOneUsername$1.propertyMissing(SyncBFSUsernameToLoginId.groovy)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:343)
	at groovy.lang.MetaClassImpl.invokeMissingProperty(MetaClassImpl.java:850)
	at groovy.lang.MetaClassImpl$11.getProperty(MetaClassImpl.java:2170)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at migration.server.hr.SyncBFSOneUsername$1.run(SyncBFSUsernameToLoginId.groovy:87)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at lib.data.ServiceRunnableSet.run(ServiceRunnableSet.groovy:21)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at migration.server.hr.SyncBFSUsernameToLoginId.run(SyncBFSUsernameToLoginId.groovy:140)
	at groovy.util.GroovyScriptEngine.run(GroovyScriptEngine.java:571)
	at net.datatp.module.groovy.GroovyScriptService.run(GroovyScriptService.java:32)
	at net.datatp.module.core.security.http.SystemRPCController.lambda$run$0(SystemRPCController.java:52)
	at net.datatp.module.http.rest.v1.AbstractController$1.doCall(AbstractController.java:65)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.http.rest.v1.AbstractController.execute(AbstractController.java:70)
	at net.datatp.module.core.security.http.SystemRPCController.run(SystemRPCController.java:54)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-09-05T15:03:26.627+07:00  INFO 57226 --- [qtp907681414-40] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint rpc/system/[POST] /system/script/run
2025-09-05T15:03:47.092+07:00  INFO 57226 --- [qtp907681414-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01i2b1b6u2wsvw1k61fy3c8m0y83
2025-09-05T15:03:47.200+07:00  INFO 57226 --- [qtp907681414-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01i2b1b6u2wsvw1k61fy3c8m0y83, token = 31f0ec27afad46cd4cbafd60816351ca
2025-09-05T15:03:47.203+07:00  INFO 57226 --- [qtp907681414-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-05T15:03:47.209+07:00  INFO 57226 --- [qtp907681414-40] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-09-05T15:03:47.209+07:00  INFO 57226 --- [qtp907681414-40] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/SyncBFSUsernameToLoginId.groovy"
}
2025-09-05T15:03:47.337+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:03:47.337+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(nancy.vnhph) already exists.
2025-09-05T15:03:47.756+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:03:47.756+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hpnttrang) already exists.
2025-09-05T15:03:48.195+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:03:48.195+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(katherine.vnhph) already exists.
2025-09-05T15:03:48.201+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:03:48.201+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(derek.vnhph) already exists.
2025-09-05T15:03:48.593+07:00 ERROR 57226 --- [qtp907681414-40] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

org.springframework.dao.IncorrectResultSizeDataAccessException: Query did not return a unique result: 2 results were returned
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:301)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:244)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:560)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:343)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:135)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy226.getByLoginId(Unknown Source)
	at net.datatp.module.account.AccountLogic.getAccountByLoginId(AccountLogic.java:122)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:723)
	at net.datatp.module.account.AccountLogic$$SpringCGLIB$$0.getAccountByLoginId(<generated>)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at migration.server.hr.SyncBFSOneUsername$1.run(SyncBFSUsernameToLoginId.groovy:87)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at lib.data.ServiceRunnableSet.run(ServiceRunnableSet.groovy:21)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at migration.server.hr.SyncBFSUsernameToLoginId.run(SyncBFSUsernameToLoginId.groovy:138)
	at groovy.util.GroovyScriptEngine.run(GroovyScriptEngine.java:571)
	at net.datatp.module.groovy.GroovyScriptService.run(GroovyScriptService.java:32)
	at net.datatp.module.core.security.http.SystemRPCController.lambda$run$0(SystemRPCController.java:52)
	at net.datatp.module.http.rest.v1.AbstractController$1.doCall(AbstractController.java:65)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.http.rest.v1.AbstractController.execute(AbstractController.java:70)
	at net.datatp.module.core.security.http.SystemRPCController.run(SystemRPCController.java:54)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.hibernate.NonUniqueResultException: Query did not return a unique result: 2 results were returned
	at org.hibernate.query.spi.AbstractSelectionQuery.uniqueElement(AbstractSelectionQuery.java:586)
	at org.hibernate.query.spi.AbstractSelectionQuery.getSingleResult(AbstractSelectionQuery.java:570)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$DeferredQueryInvocationHandler.invoke(SharedEntityManagerCreator.java:419)
	at jdk.proxy2/jdk.proxy2.$Proxy290.getSingleResult(Unknown Source)
	at org.springframework.data.jpa.repository.query.JpaQueryExecution$SingleEntityExecution.doExecute(JpaQueryExecution.java:223)
	at org.springframework.data.jpa.repository.query.JpaQueryExecution.execute(JpaQueryExecution.java:92)
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.doExecute(AbstractJpaQuery.java:149)
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.execute(AbstractJpaQuery.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 119 common frames omitted

2025-09-05T15:03:48.595+07:00  INFO 57226 --- [qtp907681414-40] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint rpc/system/[POST] /system/script/run
2025-09-05T15:04:06.185+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:04:13.653+07:00  INFO 57226 --- [qtp907681414-62] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0rdqp5v4a9ycvrym40p2heftt4
2025-09-05T15:04:13.800+07:00  INFO 57226 --- [qtp907681414-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0rdqp5v4a9ycvrym40p2heftt4, token = f73574492d16f183d0996742e7130784
2025-09-05T15:04:13.818+07:00  INFO 57226 --- [qtp907681414-62] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-05T15:04:13.829+07:00  INFO 57226 --- [qtp907681414-40] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-09-05T15:04:13.829+07:00  INFO 57226 --- [qtp907681414-40] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/SyncBFSUsernameToLoginId.groovy"
}
2025-09-05T15:04:13.966+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:04:13.966+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(nancy.vnhph) already exists.
2025-09-05T15:04:14.266+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:04:14.266+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hpnttrang) already exists.
2025-09-05T15:04:14.659+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:04:14.659+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(katherine.vnhph) already exists.
2025-09-05T15:04:14.664+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:04:14.664+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(derek.vnhph) already exists.
2025-09-05T15:04:15.171+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:04:15.172+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(royal.vnhan) already exists.
2025-09-05T15:04:15.737+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:04:15.737+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hpntthuyen) already exists.
2025-09-05T15:04:16.034+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:04:16.034+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(lloyd.vnhph) already exists.
2025-09-05T15:04:16.040+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:04:16.040+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hphungpt) already exists.
2025-09-05T15:04:16.931+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:04:16.931+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hcmloanntt) already exists.
2025-09-05T15:04:17.037+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:04:17.037+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(tt3.log.vnsgn) already exists.
2025-09-05T15:04:17.043+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:04:17.043+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(tt3.log.vnhan) already exists.
2025-09-05T15:04:22.240+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-05T15:04:22.243+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:04:57.474+07:00  INFO 57226 --- [qtp907681414-41] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0yph1pyfhiqq512o1793aqf3bm5
2025-09-05T15:04:57.586+07:00  INFO 57226 --- [qtp907681414-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0yph1pyfhiqq512o1793aqf3bm5, token = 7e78198c01a644ff497d22465f1a1b08
2025-09-05T15:04:57.592+07:00  INFO 57226 --- [qtp907681414-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-05T15:04:57.616+07:00  INFO 57226 --- [qtp907681414-35] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-09-05T15:04:57.616+07:00  INFO 57226 --- [qtp907681414-35] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/SyncBFSUsernameToLoginId.groovy"
}
2025-09-05T15:04:57.846+07:00  WARN 57226 --- [qtp907681414-35] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:04:57.846+07:00 ERROR 57226 --- [qtp907681414-35] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(nancy.vnhph) already exists.
2025-09-05T15:04:58.118+07:00  WARN 57226 --- [qtp907681414-35] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:04:58.118+07:00 ERROR 57226 --- [qtp907681414-35] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hpnttrang) already exists.
2025-09-05T15:04:58.472+07:00  WARN 57226 --- [qtp907681414-35] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:04:58.473+07:00 ERROR 57226 --- [qtp907681414-35] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(katherine.vnhph) already exists.
2025-09-05T15:04:58.478+07:00  WARN 57226 --- [qtp907681414-35] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:04:58.479+07:00 ERROR 57226 --- [qtp907681414-35] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(derek.vnhph) already exists.
2025-09-05T15:04:58.989+07:00  WARN 57226 --- [qtp907681414-35] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:04:58.989+07:00 ERROR 57226 --- [qtp907681414-35] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(royal.vnhan) already exists.
2025-09-05T15:04:59.498+07:00  WARN 57226 --- [qtp907681414-35] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:04:59.498+07:00 ERROR 57226 --- [qtp907681414-35] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hpntthuyen) already exists.
2025-09-05T15:04:59.742+07:00  WARN 57226 --- [qtp907681414-35] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:04:59.742+07:00 ERROR 57226 --- [qtp907681414-35] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(lloyd.vnhph) already exists.
2025-09-05T15:04:59.748+07:00  WARN 57226 --- [qtp907681414-35] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:04:59.748+07:00 ERROR 57226 --- [qtp907681414-35] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hphungpt) already exists.
2025-09-05T15:05:00.626+07:00  WARN 57226 --- [qtp907681414-35] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:05:00.626+07:00 ERROR 57226 --- [qtp907681414-35] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hcmloanntt) already exists.
2025-09-05T15:05:00.730+07:00  WARN 57226 --- [qtp907681414-35] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:05:00.730+07:00 ERROR 57226 --- [qtp907681414-35] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(tt3.log.vnsgn) already exists.
2025-09-05T15:05:00.736+07:00  WARN 57226 --- [qtp907681414-35] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:05:00.736+07:00 ERROR 57226 --- [qtp907681414-35] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(tt3.log.vnhan) already exists.
2025-09-05T15:05:04.323+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:05:04.329+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T15:06:06.424+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:06:26.510+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-05T15:06:26.516+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:07:03.584+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:08:06.678+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:08:26.718+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:08:26.721+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:09:02.777+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:10:05.873+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:10:05.874+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T15:10:26.919+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:10:26.922+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:11:06.982+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:11:09.058+07:00  INFO 57226 --- [Scheduler-773602191-1] n.d.m.session.AppHttpSessionListener     : The session node01i2b1b6u2wsvw1k61fy3c8m0y83 is destroyed.
2025-09-05T15:11:09.059+07:00  INFO 57226 --- [Scheduler-773602191-1] n.d.m.session.AppHttpSessionListener     : The session node0rdqp5v4a9ycvrym40p2heftt4 is destroyed.
2025-09-05T15:11:09.059+07:00  INFO 57226 --- [Scheduler-773602191-1] n.d.m.session.AppHttpSessionListener     : The session node01qoj2nnt0v8fi1ooxfibdwnbs00 is destroyed.
2025-09-05T15:11:09.059+07:00  INFO 57226 --- [Scheduler-773602191-1] n.d.m.session.AppHttpSessionListener     : The session node019j5i1bobgnxq1bhv0ekh8xvlb2 is destroyed.
2025-09-05T15:11:09.060+07:00  INFO 57226 --- [Scheduler-773602191-1] n.d.m.session.AppHttpSessionListener     : The session node0yph1pyfhiqq512o1793aqf3bm5 is destroyed.
2025-09-05T15:11:09.060+07:00  INFO 57226 --- [Scheduler-773602191-1] n.d.m.session.AppHttpSessionListener     : The session node01ifyrit9jv0q8qybnyb78k4fi1 is destroyed.
2025-09-05T15:12:05.070+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:12:26.102+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-05T15:12:26.104+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:13:06.155+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:14:04.245+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:14:25.278+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-05T15:14:25.280+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:15:06.348+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:15:06.350+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T15:15:06.353+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-05T15:15:06.362+07:00  INFO 57226 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 05/09/2025@15:15:06+0700
2025-09-05T15:15:06.385+07:00  INFO 57226 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 05/09/2025@15:15:00+0700 to 05/09/2025@15:30:00+0700
2025-09-05T15:15:06.386+07:00  INFO 57226 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 05/09/2025@15:15:00+0700 to 05/09/2025@15:30:00+0700
2025-09-05T15:16:03.497+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:16:04.830+07:00  INFO 57226 --- [qtp907681414-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01tlrw4404ien5ldtlymxlgk4f6
2025-09-05T15:16:04.940+07:00  INFO 57226 --- [qtp907681414-37] n.d.module.session.ClientSessionManager  : Add a client session id = node01tlrw4404ien5ldtlymxlgk4f6, token = 61a420de1359e791fb530dafbcb022a9
2025-09-05T15:16:04.953+07:00  INFO 57226 --- [qtp907681414-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-05T15:16:04.964+07:00  INFO 57226 --- [qtp907681414-40] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-09-05T15:16:04.964+07:00  INFO 57226 --- [qtp907681414-40] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/SyncBFSUsernameToLoginId.groovy"
}
2025-09-05T15:16:05.098+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:16:05.098+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(nancy.vnhph) already exists.
2025-09-05T15:16:05.339+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:16:05.339+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hpnttrang) already exists.
2025-09-05T15:16:05.714+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:16:05.714+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(katherine.vnhph) already exists.
2025-09-05T15:16:05.721+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:16:05.721+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(derek.vnhph) already exists.
2025-09-05T15:16:06.193+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:16:06.193+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(royal.vnhan) already exists.
2025-09-05T15:16:06.708+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:16:06.709+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hpntthuyen) already exists.
2025-09-05T15:16:06.938+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:16:06.938+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(lloyd.vnhph) already exists.
2025-09-05T15:16:06.943+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:16:06.943+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hphungpt) already exists.
2025-09-05T15:16:07.882+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:16:07.882+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hcmloanntt) already exists.
2025-09-05T15:16:07.991+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:16:07.991+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(tt3.log.vnsgn) already exists.
2025-09-05T15:16:07.997+07:00  WARN 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:16:07.997+07:00 ERROR 57226 --- [qtp907681414-40] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(tt3.log.vnhan) already exists.
2025-09-05T15:16:24.542+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-05T15:16:24.553+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:17:06.622+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:18:02.717+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:18:23.769+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-05T15:18:23.773+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:19:05.834+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:19:56.458+07:00  INFO 57226 --- [qtp907681414-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node08xn2ju9bmxgmmdct5uibrvov7
2025-09-05T15:19:56.553+07:00  INFO 57226 --- [qtp907681414-39] n.d.module.session.ClientSessionManager  : Add a client session id = node08xn2ju9bmxgmmdct5uibrvov7, token = 35ca3622f6dc65905a9302a9473eed7f
2025-09-05T15:19:56.572+07:00  INFO 57226 --- [qtp907681414-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-05T15:19:56.599+07:00  INFO 57226 --- [qtp907681414-37] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-09-05T15:19:56.599+07:00  INFO 57226 --- [qtp907681414-37] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/SyncBFSUsernameToLoginId.groovy"
}
2025-09-05T15:19:56.743+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 1/66: 10 records
2025-09-05T15:19:56.789+07:00  WARN 57226 --- [qtp907681414-37] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:19:56.789+07:00 ERROR 57226 --- [qtp907681414-37] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(nancy.vnhph) already exists.
2025-09-05T15:19:56.823+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 2/66: 10 records
2025-09-05T15:19:56.886+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 3/66: 10 records
2025-09-05T15:19:56.940+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 4/66: 10 records
2025-09-05T15:19:56.990+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 5/66: 10 records
2025-09-05T15:19:57.039+07:00  WARN 57226 --- [qtp907681414-37] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:19:57.040+07:00 ERROR 57226 --- [qtp907681414-37] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hpnttrang) already exists.
2025-09-05T15:19:57.040+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 6/66: 10 records
2025-09-05T15:19:57.088+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 7/66: 10 records
2025-09-05T15:19:57.141+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 8/66: 10 records
2025-09-05T15:19:57.202+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 9/66: 10 records
2025-09-05T15:19:57.262+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 10/66: 10 records
2025-09-05T15:19:57.325+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 11/66: 10 records
2025-09-05T15:19:57.382+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 12/66: 10 records
2025-09-05T15:19:57.416+07:00  WARN 57226 --- [qtp907681414-37] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:19:57.416+07:00 ERROR 57226 --- [qtp907681414-37] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(katherine.vnhph) already exists.
2025-09-05T15:19:57.423+07:00  WARN 57226 --- [qtp907681414-37] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:19:57.423+07:00 ERROR 57226 --- [qtp907681414-37] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(derek.vnhph) already exists.
2025-09-05T15:19:57.442+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 13/66: 10 records
2025-09-05T15:19:57.498+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 14/66: 10 records
2025-09-05T15:19:57.547+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 15/66: 10 records
2025-09-05T15:19:57.597+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 16/66: 10 records
2025-09-05T15:19:57.648+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 17/66: 10 records
2025-09-05T15:19:57.702+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 18/66: 10 records
2025-09-05T15:19:57.758+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 19/66: 10 records
2025-09-05T15:19:57.818+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 20/66: 10 records
2025-09-05T15:19:57.876+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 21/66: 10 records
2025-09-05T15:19:57.926+07:00  WARN 57226 --- [qtp907681414-37] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:19:57.926+07:00 ERROR 57226 --- [qtp907681414-37] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(royal.vnhan) already exists.
2025-09-05T15:19:57.938+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 22/66: 10 records
2025-09-05T15:19:58.002+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 23/66: 10 records
2025-09-05T15:19:58.057+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 24/66: 10 records
2025-09-05T15:19:58.113+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 25/66: 10 records
2025-09-05T15:19:58.165+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 26/66: 10 records
2025-09-05T15:19:58.222+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 27/66: 10 records
2025-09-05T15:19:58.275+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 28/66: 10 records
2025-09-05T15:19:58.325+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 29/66: 10 records
2025-09-05T15:19:58.375+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 30/66: 10 records
2025-09-05T15:19:58.416+07:00  WARN 57226 --- [qtp907681414-37] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:19:58.416+07:00 ERROR 57226 --- [qtp907681414-37] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hpntthuyen) already exists.
2025-09-05T15:19:58.422+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 31/66: 10 records
2025-09-05T15:19:58.474+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 32/66: 10 records
2025-09-05T15:19:58.525+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 33/66: 10 records
2025-09-05T15:19:58.583+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 34/66: 10 records
2025-09-05T15:19:58.644+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 35/66: 10 records
2025-09-05T15:19:58.663+07:00  WARN 57226 --- [qtp907681414-37] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:19:58.663+07:00 ERROR 57226 --- [qtp907681414-37] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(lloyd.vnhph) already exists.
2025-09-05T15:19:58.670+07:00  WARN 57226 --- [qtp907681414-37] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:19:58.670+07:00 ERROR 57226 --- [qtp907681414-37] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hphungpt) already exists.
2025-09-05T15:19:58.709+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 36/66: 10 records
2025-09-05T15:19:58.762+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 37/66: 10 records
2025-09-05T15:19:58.818+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 38/66: 10 records
2025-09-05T15:19:58.871+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 39/66: 10 records
2025-09-05T15:19:58.931+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 40/66: 10 records
2025-09-05T15:19:58.987+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 41/66: 10 records
2025-09-05T15:19:59.040+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 42/66: 10 records
2025-09-05T15:19:59.091+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 43/66: 10 records
2025-09-05T15:19:59.149+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 44/66: 10 records
2025-09-05T15:19:59.222+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 45/66: 10 records
2025-09-05T15:19:59.285+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 46/66: 10 records
2025-09-05T15:19:59.354+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 47/66: 10 records
2025-09-05T15:19:59.426+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 48/66: 10 records
2025-09-05T15:19:59.480+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 49/66: 10 records
2025-09-05T15:19:59.534+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 50/66: 10 records
2025-09-05T15:19:59.578+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 51/66: 10 records
2025-09-05T15:19:59.634+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 52/66: 10 records
2025-09-05T15:19:59.645+07:00  WARN 57226 --- [qtp907681414-37] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:19:59.645+07:00 ERROR 57226 --- [qtp907681414-37] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hcmloanntt) already exists.
2025-09-05T15:19:59.686+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 53/66: 10 records
2025-09-05T15:19:59.727+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 54/66: 10 records
2025-09-05T15:19:59.743+07:00  WARN 57226 --- [qtp907681414-37] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:19:59.743+07:00 ERROR 57226 --- [qtp907681414-37] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(tt3.log.vnsgn) already exists.
2025-09-05T15:19:59.748+07:00  WARN 57226 --- [qtp907681414-37] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:19:59.748+07:00 ERROR 57226 --- [qtp907681414-37] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(tt3.log.vnhan) already exists.
2025-09-05T15:19:59.759+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 55/66: 10 records
2025-09-05T15:19:59.789+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 56/66: 10 records
2025-09-05T15:19:59.823+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 57/66: 10 records
2025-09-05T15:19:59.876+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 58/66: 10 records
2025-09-05T15:19:59.919+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 59/66: 10 records
2025-09-05T15:19:59.976+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 60/66: 10 records
2025-09-05T15:20:00.027+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 61/66: 10 records
2025-09-05T15:20:00.078+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 62/66: 10 records
2025-09-05T15:20:00.128+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 63/66: 10 records
2025-09-05T15:20:00.174+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 64/66: 10 records
2025-09-05T15:20:00.225+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 65/66: 10 records
2025-09-05T15:20:00.274+07:00  INFO 57226 --- [qtp907681414-37] migration.server.hr.SyncBFSOneUsername   : Processing batch 66/66: 1 records
2025-09-05T15:20:06.927+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T15:20:06.929+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:20:22.962+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-05T15:20:22.968+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:21:05.034+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:21:09.064+07:00  INFO 57226 --- [Scheduler-773602191-1] n.d.m.session.AppHttpSessionListener     : The session node01tlrw4404ien5ldtlymxlgk4f6 is destroyed.
2025-09-05T15:22:06.136+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:22:22.172+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:22:22.174+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:23:04.234+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:24:04.760+07:00  INFO 57226 --- [qtp907681414-62] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01fxgycm04c08yncivh4x4hiwx8
2025-09-05T15:24:04.862+07:00  INFO 57226 --- [qtp907681414-62] n.d.module.session.ClientSessionManager  : Add a client session id = node01fxgycm04c08yncivh4x4hiwx8, token = 38158a8f1286ff25f4b7fa87ebefa904
2025-09-05T15:24:04.880+07:00  INFO 57226 --- [qtp907681414-62] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-05T15:24:04.905+07:00  INFO 57226 --- [qtp907681414-39] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-09-05T15:24:04.906+07:00  INFO 57226 --- [qtp907681414-39] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/SyncBFSUsernameToLoginId.groovy"
}
2025-09-05T15:24:05.044+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 1/66: 10 records
2025-09-05T15:24:05.094+07:00  WARN 57226 --- [qtp907681414-39] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:24:05.095+07:00 ERROR 57226 --- [qtp907681414-39] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(nancy.vnhph) already exists.
2025-09-05T15:24:05.126+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 2/66: 10 records
2025-09-05T15:24:05.189+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 3/66: 10 records
2025-09-05T15:24:05.246+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 4/66: 10 records
2025-09-05T15:24:05.299+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 5/66: 10 records
2025-09-05T15:24:05.357+07:00  WARN 57226 --- [qtp907681414-39] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:24:05.357+07:00 ERROR 57226 --- [qtp907681414-39] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hpnttrang) already exists.
2025-09-05T15:24:05.357+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 6/66: 10 records
2025-09-05T15:24:05.415+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 7/66: 10 records
2025-09-05T15:24:05.469+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 8/66: 10 records
2025-09-05T15:24:05.521+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 9/66: 10 records
2025-09-05T15:24:05.570+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 10/66: 10 records
2025-09-05T15:24:05.622+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 11/66: 10 records
2025-09-05T15:24:05.674+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 12/66: 10 records
2025-09-05T15:24:05.707+07:00  WARN 57226 --- [qtp907681414-39] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:24:05.707+07:00 ERROR 57226 --- [qtp907681414-39] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(katherine.vnhph) already exists.
2025-09-05T15:24:05.713+07:00  WARN 57226 --- [qtp907681414-39] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:24:05.714+07:00 ERROR 57226 --- [qtp907681414-39] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(derek.vnhph) already exists.
2025-09-05T15:24:05.732+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 13/66: 10 records
2025-09-05T15:24:05.801+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 14/66: 10 records
2025-09-05T15:24:05.913+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 15/66: 10 records
2025-09-05T15:24:05.966+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 16/66: 10 records
2025-09-05T15:24:06.017+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 17/66: 10 records
2025-09-05T15:24:06.069+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 18/66: 10 records
2025-09-05T15:24:06.120+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 19/66: 10 records
2025-09-05T15:24:06.177+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 20/66: 10 records
2025-09-05T15:24:06.237+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 21/66: 10 records
2025-09-05T15:24:06.282+07:00  WARN 57226 --- [qtp907681414-39] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:24:06.282+07:00 ERROR 57226 --- [qtp907681414-39] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(royal.vnhan) already exists.
2025-09-05T15:24:06.296+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 22/66: 10 records
2025-09-05T15:24:06.340+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:24:06.353+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 23/66: 10 records
2025-09-05T15:24:06.407+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 24/66: 10 records
2025-09-05T15:24:06.463+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 25/66: 10 records
2025-09-05T15:24:06.523+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 26/66: 10 records
2025-09-05T15:24:06.582+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 27/66: 10 records
2025-09-05T15:24:06.641+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 28/66: 10 records
2025-09-05T15:24:06.702+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 29/66: 10 records
2025-09-05T15:24:06.761+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 30/66: 10 records
2025-09-05T15:24:06.809+07:00  WARN 57226 --- [qtp907681414-39] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:24:06.809+07:00 ERROR 57226 --- [qtp907681414-39] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hpntthuyen) already exists.
2025-09-05T15:24:06.819+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 31/66: 10 records
2025-09-05T15:24:06.876+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 32/66: 10 records
2025-09-05T15:24:06.930+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 33/66: 10 records
2025-09-05T15:24:06.985+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 34/66: 10 records
2025-09-05T15:24:07.039+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 35/66: 10 records
2025-09-05T15:24:07.057+07:00  WARN 57226 --- [qtp907681414-39] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:24:07.057+07:00 ERROR 57226 --- [qtp907681414-39] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(lloyd.vnhph) already exists.
2025-09-05T15:24:07.064+07:00  WARN 57226 --- [qtp907681414-39] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:24:07.064+07:00 ERROR 57226 --- [qtp907681414-39] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hphungpt) already exists.
2025-09-05T15:24:07.099+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 36/66: 10 records
2025-09-05T15:24:07.149+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 37/66: 10 records
2025-09-05T15:24:07.203+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 38/66: 10 records
2025-09-05T15:24:07.252+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 39/66: 10 records
2025-09-05T15:24:07.303+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 40/66: 10 records
2025-09-05T15:24:07.355+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 41/66: 10 records
2025-09-05T15:24:07.411+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 42/66: 10 records
2025-09-05T15:24:07.465+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 43/66: 10 records
2025-09-05T15:24:07.524+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 44/66: 10 records
2025-09-05T15:24:07.580+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 45/66: 10 records
2025-09-05T15:24:07.630+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 46/66: 10 records
2025-09-05T15:24:07.682+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 47/66: 10 records
2025-09-05T15:24:07.735+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 48/66: 10 records
2025-09-05T15:24:07.789+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 49/66: 10 records
2025-09-05T15:24:07.841+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 50/66: 10 records
2025-09-05T15:24:07.884+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 51/66: 10 records
2025-09-05T15:24:07.937+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 52/66: 10 records
2025-09-05T15:24:07.948+07:00  WARN 57226 --- [qtp907681414-39] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:24:07.948+07:00 ERROR 57226 --- [qtp907681414-39] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hcmloanntt) already exists.
2025-09-05T15:24:07.992+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 53/66: 10 records
2025-09-05T15:24:08.034+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 54/66: 10 records
2025-09-05T15:24:08.049+07:00  WARN 57226 --- [qtp907681414-39] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:24:08.049+07:00 ERROR 57226 --- [qtp907681414-39] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(tt3.log.vnsgn) already exists.
2025-09-05T15:24:08.054+07:00  WARN 57226 --- [qtp907681414-39] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T15:24:08.054+07:00 ERROR 57226 --- [qtp907681414-39] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(tt3.log.vnhan) already exists.
2025-09-05T15:24:08.065+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 55/66: 10 records
2025-09-05T15:24:08.095+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 56/66: 10 records
2025-09-05T15:24:08.131+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 57/66: 10 records
2025-09-05T15:24:08.181+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 58/66: 10 records
2025-09-05T15:24:08.224+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 59/66: 10 records
2025-09-05T15:24:08.276+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 60/66: 10 records
2025-09-05T15:24:08.329+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 61/66: 10 records
2025-09-05T15:24:08.381+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 62/66: 10 records
2025-09-05T15:24:08.432+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 63/66: 10 records
2025-09-05T15:24:08.476+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 64/66: 10 records
2025-09-05T15:24:08.528+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 65/66: 10 records
2025-09-05T15:24:08.574+07:00  INFO 57226 --- [qtp907681414-39] migration.server.hr.SyncBFSOneUsername   : Processing batch 66/66: 1 records
2025-09-05T15:24:26.373+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-05T15:24:26.380+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:25:03.442+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:25:03.446+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T15:26:06.547+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:26:26.583+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-05T15:26:26.589+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:27:02.649+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:28:05.759+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:28:26.798+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:28:26.800+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:29:06.859+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:30:04.966+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:30:04.966+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T15:30:04.967+07:00  INFO 57226 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 05/09/2025@15:30:04+0700
2025-09-05T15:30:04.982+07:00  INFO 57226 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 05/09/2025@15:30:00+0700 to 05/09/2025@15:45:00+0700
2025-09-05T15:30:04.982+07:00  INFO 57226 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 05/09/2025@15:30:00+0700 to 05/09/2025@15:45:00+0700
2025-09-05T15:30:04.982+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-05T15:30:26.021+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-05T15:30:26.024+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:31:06.077+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:31:09.073+07:00  INFO 57226 --- [Scheduler-773602191-1] n.d.m.session.AppHttpSessionListener     : The session node08xn2ju9bmxgmmdct5uibrvov7 is destroyed.
2025-09-05T15:31:09.074+07:00  INFO 57226 --- [Scheduler-773602191-1] n.d.m.session.AppHttpSessionListener     : The session node01fxgycm04c08yncivh4x4hiwx8 is destroyed.
2025-09-05T15:32:04.164+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:32:25.199+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:32:25.203+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:33:06.283+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:34:03.389+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:34:24.434+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 2
2025-09-05T15:34:24.440+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:35:06.505+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:35:06.507+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T15:36:02.593+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:36:23.636+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:36:23.641+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:37:05.708+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:38:06.796+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:38:22.829+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:38:22.834+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:39:04.905+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:40:07.002+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:40:07.004+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T15:40:22.033+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-05T15:40:22.034+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:41:04.092+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:42:06.190+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:42:26.221+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:42:26.226+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:43:03.281+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:44:06.397+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:44:26.438+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:44:26.440+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:45:02.501+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:45:02.503+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T15:45:02.503+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-05T15:45:02.504+07:00  INFO 57226 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 05/09/2025@15:45:02+0700
2025-09-05T15:45:02.521+07:00  INFO 57226 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 05/09/2025@15:45:00+0700 to 05/09/2025@16:00:00+0700
2025-09-05T15:45:02.521+07:00  INFO 57226 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 05/09/2025@15:45:00+0700 to 05/09/2025@16:00:00+0700
2025-09-05T15:46:05.627+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:46:26.675+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-05T15:46:26.683+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:47:06.747+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:48:04.849+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:48:25.887+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:48:25.890+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:49:06.958+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:50:04.051+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:50:04.053+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T15:50:25.090+07:00  INFO 57226 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:50:25.095+07:00  INFO 57226 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:51:06.143+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:52:03.229+07:00  INFO 57226 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T15:52:07.140+07:00  INFO 57226 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@1a0f8da6{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-05T15:52:07.141+07:00  INFO 57226 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-05T15:52:07.141+07:00  INFO 57226 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-05T15:52:07.141+07:00  INFO 57226 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-05T15:52:07.141+07:00  INFO 57226 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-05T15:52:07.141+07:00  INFO 57226 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-05T15:52:07.141+07:00  INFO 57226 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-05T15:52:07.141+07:00  INFO 57226 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-05T15:52:07.141+07:00  INFO 57226 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-05T15:52:07.141+07:00  INFO 57226 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-05T15:52:07.141+07:00  INFO 57226 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-05T15:52:07.141+07:00  INFO 57226 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-05T15:52:07.141+07:00  INFO 57226 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-05T15:52:07.141+07:00  INFO 57226 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-05T15:52:07.141+07:00  INFO 57226 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-05T15:52:07.141+07:00  INFO 57226 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-05T15:52:07.160+07:00  INFO 57226 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T15:52:07.231+07:00  INFO 57226 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-05T15:52:07.237+07:00  INFO 57226 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-05T15:52:07.265+07:00  INFO 57226 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-05T15:52:07.266+07:00  INFO 57226 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-05T15:52:07.267+07:00  INFO 57226 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-05T15:52:07.268+07:00  INFO 57226 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-05T15:52:07.269+07:00  INFO 57226 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-05T15:52:07.269+07:00  INFO 57226 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-05T15:52:07.270+07:00  INFO 57226 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-05T15:52:07.270+07:00  INFO 57226 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-05T15:52:07.270+07:00  INFO 57226 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-05T15:52:07.270+07:00  INFO 57226 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-05T15:52:07.270+07:00  INFO 57226 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-05T15:52:07.270+07:00  INFO 57226 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-05T15:52:07.270+07:00  INFO 57226 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-05T15:52:07.274+07:00  INFO 57226 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@35be9318{STOPPING}[12.0.15,sto=0]
2025-09-05T15:52:07.277+07:00  INFO 57226 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-05T15:52:07.279+07:00  INFO 57226 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@4af32714{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.7992362729608486687/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@794fbf0d{STOPPED}}
