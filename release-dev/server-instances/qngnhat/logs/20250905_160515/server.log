2025-09-05T16:05:15.827+07:00  INFO 63386 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 63386 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-05T16:05:15.828+07:00  INFO 63386 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-05T16:05:16.591+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.660+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 65 ms. Found 22 JPA repository interfaces.
2025-09-05T16:05:16.668+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.670+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-05T16:05:16.670+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.678+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 10 JPA repository interfaces.
2025-09-05T16:05:16.679+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.682+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-05T16:05:16.725+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.729+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-05T16:05:16.738+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.740+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-05T16:05:16.740+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.743+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-05T16:05:16.746+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.750+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-05T16:05:16.753+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.756+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-05T16:05:16.756+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.757+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-05T16:05:16.757+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.763+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 10 JPA repository interfaces.
2025-09-05T16:05:16.767+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.769+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-05T16:05:16.772+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.775+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-05T16:05:16.775+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.782+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-05T16:05:16.782+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.784+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-05T16:05:16.785+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.785+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-05T16:05:16.785+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.786+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-05T16:05:16.786+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.791+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-05T16:05:16.791+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.792+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-05T16:05:16.792+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.792+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-05T16:05:16.793+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.802+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-09-05T16:05:16.812+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.818+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-05T16:05:16.818+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.821+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-05T16:05:16.822+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.825+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-05T16:05:16.826+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.830+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 9 JPA repository interfaces.
2025-09-05T16:05:16.831+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.834+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-05T16:05:16.835+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.842+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-05T16:05:16.842+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.852+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-09-05T16:05:16.852+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.865+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 24 JPA repository interfaces.
2025-09-05T16:05:16.866+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.867+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-05T16:05:16.872+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.873+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-05T16:05:16.873+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.879+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-05T16:05:16.882+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.914+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 32 ms. Found 65 JPA repository interfaces.
2025-09-05T16:05:16.914+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.916+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-05T16:05:16.920+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05T16:05:16.923+07:00  INFO 63386 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-05T16:05:17.125+07:00  INFO 63386 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-05T16:05:17.129+07:00  INFO 63386 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-05T16:05:17.412+07:00  WARN 63386 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-05T16:05:17.602+07:00  INFO 63386 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-05T16:05:17.604+07:00  INFO 63386 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-05T16:05:17.614+07:00  INFO 63386 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-05T16:05:17.615+07:00  INFO 63386 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1637 ms
2025-09-05T16:05:17.667+07:00  WARN 63386 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-05T16:05:17.668+07:00  INFO 63386 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-05T16:05:17.765+07:00  INFO 63386 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@44bbf5e3
2025-09-05T16:05:17.766+07:00  INFO 63386 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-05T16:05:17.770+07:00  WARN 63386 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-05T16:05:17.771+07:00  INFO 63386 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-05T16:05:17.775+07:00  INFO 63386 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@3f04847e
2025-09-05T16:05:17.776+07:00  INFO 63386 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-05T16:05:17.776+07:00  WARN 63386 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-05T16:05:17.776+07:00  INFO 63386 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-05T16:05:17.782+07:00  INFO 63386 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@4a3d4cd2
2025-09-05T16:05:17.782+07:00  INFO 63386 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-05T16:05:17.782+07:00  WARN 63386 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-05T16:05:17.782+07:00  INFO 63386 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-05T16:05:17.789+07:00  INFO 63386 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@3d1b6816
2025-09-05T16:05:17.789+07:00  INFO 63386 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-05T16:05:17.789+07:00  WARN 63386 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-05T16:05:17.789+07:00  INFO 63386 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-05T16:05:17.798+07:00  INFO 63386 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@4f9871a2
2025-09-05T16:05:17.798+07:00  INFO 63386 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-05T16:05:17.798+07:00  INFO 63386 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-05T16:05:17.844+07:00  INFO 63386 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-05T16:05:17.846+07:00  INFO 63386 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@18c2b4c6{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.14892480253553675056/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@461e9b31{STARTED}}
2025-09-05T16:05:17.847+07:00  INFO 63386 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@18c2b4c6{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.14892480253553675056/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@461e9b31{STARTED}}
2025-09-05T16:05:17.850+07:00  INFO 63386 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@7abc838a{STARTING}[12.0.15,sto=0] @2543ms
2025-09-05T16:05:17.949+07:00  INFO 63386 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-05T16:05:17.975+07:00  INFO 63386 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-05T16:05:17.989+07:00  INFO 63386 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-05T16:05:18.115+07:00  INFO 63386 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-05T16:05:18.163+07:00  WARN 63386 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-05T16:05:18.946+07:00  INFO 63386 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-05T16:05:18.954+07:00  INFO 63386 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@79eaf64] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-05T16:05:19.234+07:00  INFO 63386 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-05T16:05:19.518+07:00  INFO 63386 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.company.tmpl", "cloud.datatp.tms", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-09-05T16:05:19.520+07:00  INFO 63386 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-05T16:05:19.526+07:00  INFO 63386 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-05T16:05:19.527+07:00  INFO 63386 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-05T16:05:19.554+07:00  INFO 63386 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-05T16:05:19.560+07:00  WARN 63386 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-05T16:05:21.817+07:00  INFO 63386 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-05T16:05:21.818+07:00  INFO 63386 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@661d038e] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-05T16:05:22.015+07:00  WARN 63386 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-05T16:05:22.015+07:00  WARN 63386 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-05T16:05:22.023+07:00  WARN 63386 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-05T16:05:22.023+07:00  WARN 63386 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-05T16:05:22.037+07:00  WARN 63386 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-05T16:05:22.037+07:00  WARN 63386 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-05T16:05:22.463+07:00  INFO 63386 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-05T16:05:22.470+07:00  INFO 63386 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-05T16:05:22.471+07:00  INFO 63386 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-05T16:05:22.489+07:00  INFO 63386 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-05T16:05:22.499+07:00  WARN 63386 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-05T16:05:22.999+07:00  INFO 63386 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-05T16:05:22.999+07:00  INFO 63386 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@29df2c88] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-05T16:05:23.108+07:00  WARN 63386 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-05T16:05:23.108+07:00  WARN 63386 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-05T16:05:23.486+07:00  INFO 63386 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-05T16:05:23.514+07:00  INFO 63386 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-05T16:05:23.519+07:00  INFO 63386 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-05T16:05:23.519+07:00  INFO 63386 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-05T16:05:23.525+07:00  WARN 63386 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-05T16:05:23.660+07:00  INFO 63386 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-05T16:05:24.115+07:00  INFO 63386 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-05T16:05:24.117+07:00  INFO 63386 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-05T16:05:24.151+07:00  INFO 63386 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-05T16:05:24.195+07:00  INFO 63386 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-05T16:05:24.292+07:00  INFO 63386 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-05T16:05:24.319+07:00  INFO 63386 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-05T16:05:24.341+07:00  INFO 63386 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 191394584ms : this is harmless.
2025-09-05T16:05:24.349+07:00  INFO 63386 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-05T16:05:24.352+07:00  INFO 63386 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-05T16:05:24.363+07:00  INFO 63386 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 198518149ms : this is harmless.
2025-09-05T16:05:24.365+07:00  INFO 63386 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-05T16:05:24.377+07:00  INFO 63386 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-05T16:05:24.377+07:00  INFO 63386 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-05T16:05:25.500+07:00  INFO 63386 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-05T16:05:25.500+07:00  INFO 63386 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-05T16:05:25.501+07:00  WARN 63386 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-05T16:05:26.168+07:00  INFO 63386 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 05/09/2025@16:00:00+0700 to 05/09/2025@16:15:00+0700
2025-09-05T16:05:26.168+07:00  INFO 63386 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 05/09/2025@16:00:00+0700 to 05/09/2025@16:15:00+0700
2025-09-05T16:05:27.237+07:00  INFO 63386 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-05T16:05:27.237+07:00  INFO 63386 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-05T16:05:27.238+07:00  WARN 63386 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-05T16:05:27.522+07:00  INFO 63386 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-05T16:05:27.522+07:00  INFO 63386 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-05T16:05:27.522+07:00  INFO 63386 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-05T16:05:27.522+07:00  INFO 63386 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-05T16:05:27.522+07:00  INFO 63386 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-05T16:05:29.145+07:00  WARN 63386 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 78c39c4e-27ca-4c27-b9f7-2281b2e3665a

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-05T16:05:29.148+07:00  INFO 63386 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-05T16:05:29.481+07:00  INFO 63386 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-05T16:05:29.482+07:00  INFO 63386 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-05T16:05:29.482+07:00  INFO 63386 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-05T16:05:29.482+07:00  INFO 63386 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-05T16:05:29.482+07:00  INFO 63386 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-05T16:05:29.482+07:00  INFO 63386 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-05T16:05:29.482+07:00  INFO 63386 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-05T16:05:29.482+07:00  INFO 63386 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-05T16:05:29.482+07:00  INFO 63386 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-05T16:05:29.482+07:00  INFO 63386 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-05T16:05:29.482+07:00  INFO 63386 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-05T16:05:29.482+07:00  INFO 63386 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-05T16:05:29.483+07:00  INFO 63386 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-05T16:05:29.483+07:00  INFO 63386 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-05T16:05:29.483+07:00  INFO 63386 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-05T16:05:29.541+07:00  INFO 63386 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-05T16:05:29.542+07:00  INFO 63386 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-05T16:05:29.543+07:00  INFO 63386 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-05T16:05:29.551+07:00  INFO 63386 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@47556cd6{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-05T16:05:29.552+07:00  INFO 63386 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-05T16:05:29.553+07:00  INFO 63386 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-05T16:05:29.579+07:00  INFO 63386 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-05T16:05:29.580+07:00  INFO 63386 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-05T16:05:29.585+07:00  INFO 63386 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 14.035 seconds (process running for 14.278)
2025-09-05T16:06:04.560+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:06:14.113+07:00  INFO 63386 --- [qtp127427798-36] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node013y03tcbfii4r12dy6rr2c4hzy0
2025-09-05T16:06:14.496+07:00  INFO 63386 --- [qtp127427798-36] n.d.module.session.ClientSessionManager  : Add a client session id = node013y03tcbfii4r12dy6rr2c4hzy0, token = e8d0ce84129de1e3585f97b8e8445ef1
2025-09-05T16:06:14.874+07:00  INFO 63386 --- [qtp127427798-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-05T16:06:32.610+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:06:32.625+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:07:06.684+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:08:03.793+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:08:31.900+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-09-05T16:08:31.904+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:09:06.965+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:10:03.052+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:10:03.055+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T16:10:36.137+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-05T16:10:36.143+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:11:06.187+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:11:30.835+07:00  WARN 63386 --- [qtp127427798-76] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T16:11:30.836+07:00 ERROR 63386 --- [qtp127427798-76] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_mobile"
  Detail: Key (mobile)=() already exists.
2025-09-05T16:11:30.857+07:00 ERROR 63386 --- [qtp127427798-76] n.d.m.monitor.call.EndpointCallContext   : Start call with component AccountService, method saveAccount, arguments
[ {
  "tenantId" : "",
  "companyId" : 4,
  "companyParentId" : null,
  "companyCode" : "bee",
  "companyLabel" : "Bee Corp",
  "companyFullName" : "BEE LOGISTICS CORPORATION",
  "loginId" : "nhat.le",
  "accountId" : 2089,
  "token" : "e8d0ce84129de1e3585f97b8e8445ef1",
  "tokenId" : 70842,
  "remoteIp" : "",
  "sessionId" : "node013y03tcbfii4r12dy6rr2c4hzy0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    },
    "beescs" : {
      "companyId" : 12,
      "companyParentId" : 1,
      "companyCode" : "beescs",
      "companyLabel" : "BEESCS",
      "companyFullName" : null
    },
    "beehan" : {
      "companyId" : 16,
      "companyParentId" : 4,
      "companyCode" : "beehan",
      "companyLabel" : "Bee HAN",
      "companyFullName" : null
    },
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    },
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    },
    "beedad" : {
      "companyId" : 18,
      "companyParentId" : 4,
      "companyCode" : "beedad",
      "companyLabel" : "Bee DAD",
      "companyFullName" : null
    },
    "datatp" : {
      "companyId" : 10,
      "companyParentId" : 1,
      "companyCode" : "datatp",
      "companyLabel" : "DataTP Cloud",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 2713,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 13328,
    "appId" : 27,
    "appModule" : "logistics",
    "appName" : "user-partner-logistics-crm",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 13329,
    "appId" : 15,
    "appModule" : "logistics",
    "appName" : "user-logistics-prices",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 8581,
    "appId" : 64,
    "appModule" : "job-tracking",
    "appName" : "job-tracking",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14692,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 6213,
    "appId" : 18,
    "appModule" : "system",
    "appName" : "system",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Read"
  }, {
    "id" : 10447,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Group",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 6142,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 2966,
    "appId" : 5,
    "appModule" : "admin",
    "appName" : "admin-space",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 2967,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 2968,
    "appId" : 9,
    "appModule" : "company",
    "appName" : "company-hr",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 2969,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 3739,
    "appId" : 12,
    "appModule" : "spreadsheet",
    "appName" : "spreadsheet",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 11549,
    "appId" : 70,
    "appModule" : "hr",
    "appName" : "company-kpi",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 12461,
    "appId" : 19,
    "appModule" : "sample",
    "appName" : "reactjs-lib",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 13393,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14846,
    "appId" : 7,
    "appModule" : "company",
    "appName" : "company-asset",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 15054,
    "appId" : 80,
    "appModule" : "logistics",
    "appName" : "company-logistics-sales",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  } ],
  "attributes" : { },
  "clientId" : ":nhat.le"
}, {
  "id" : 11706,
  "editState" : "ORIGIN",
  "loadState" : "FromDB",
  "version" : 0,
  "createdBy" : "tony.nguyen",
  "createdTime" : "24/04/2024@13:23:00+0000",
  "modifiedBy" : "nhat.le",
  "modifiedTime" : "05/09/2025@09:11:30+0000",
  "storageState" : "ACTIVE",
  "loginId" : "derek.vnhph",
  "legacyCompanyLoginId" : "",
  "password" : "$2a$10$fuYv7reUeNY0TI5G2RYnJOD9x0RTzjm9tHAxrPpq9UWzENA/M8.me",
  "accountType" : "USER",
  "email" : "<EMAIL>",
  "mobile" : "",
  "fullName" : "NGUYỄN MINH GIANG",
  "modifiable" : true
} ]
2025-09-05T16:11:30.858+07:00 ERROR 63386 --- [qtp127427798-76] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: duplicate key value violates unique constraint "account_account_mobile"
  Detail: Key (mobile)=() already exists.] [update "account_account" set "account_type"=?,"created_by"=?,"created_time"=?,"email"=?,"full_name"=?,"last_login_time"=?,"legacy_company_login_id"=?,"legacy_login_id"=?,"login_id"=?,"mobile"=?,"modified_by"=?,"modified_time"=?,"move_to_login_id"=?,"password"=?,"storage_state"=?,"version"=? where "id"=? and "version"=?]; SQL [update "account_account" set "account_type"=?,"created_by"=?,"created_time"=?,"email"=?,"full_name"=?,"last_login_time"=?,"legacy_company_login_id"=?,"legacy_login_id"=?,"login_id"=?,"mobile"=?,"modified_by"=?,"modified_time"=?,"move_to_login_id"=?,"password"=?,"storage_state"=?,"version"=? where "id"=? and "version"=?]; constraint [account_account_mobile]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:566)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:795)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:758)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:698)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:416)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.account.AccountService$$SpringCGLIB$$0.saveAccount(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: duplicate key value violates unique constraint "account_account_mobile"
  Detail: Key (mobile)=() already exists.] [update "account_account" set "account_type"=?,"created_by"=?,"created_time"=?,"email"=?,"full_name"=?,"last_login_time"=?,"legacy_company_login_id"=?,"legacy_login_id"=?,"login_id"=?,"mobile"=?,"modified_by"=?,"modified_time"=?,"move_to_login_id"=?,"password"=?,"storage_state"=?,"version"=? where "id"=? and "version"=?]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:197)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.performNonBatchedMutation(AbstractMutationExecutor.java:108)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorSingleNonBatched.performNonBatchedOperations(MutationExecutorSingleNonBatched.java:40)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.execute(AbstractMutationExecutor.java:52)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.doStaticUpdate(UpdateCoordinatorStandard.java:778)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.performUpdate(UpdateCoordinatorStandard.java:324)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.coordinateUpdate(UpdateCoordinatorStandard.java:242)
	at org.hibernate.persister.entity.AbstractEntityPersister.update(AbstractEntityPersister.java:2810)
	at org.hibernate.action.internal.EntityUpdateAction.execute(EntityUpdateAction.java:168)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:632)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:499)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:363)
	at org.hibernate.event.internal.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:41)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.doFlush(SessionImpl.java:1403)
	at org.hibernate.internal.SessionImpl.managedFlush(SessionImpl.java:484)
	at org.hibernate.internal.SessionImpl.flushBeforeTransactionCompletion(SessionImpl.java:2319)
	at org.hibernate.internal.SessionImpl.beforeTransactionCompletion(SessionImpl.java:1976)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.beforeTransactionCompletion(JdbcCoordinatorImpl.java:439)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl.beforeCompletionCallback(JdbcResourceLocalTransactionCoordinatorImpl.java:169)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl$TransactionDriverControlImpl.commit(JdbcResourceLocalTransactionCoordinatorImpl.java:267)
	at org.hibernate.engine.transaction.internal.TransactionImpl.commit(TransactionImpl.java:101)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:562)
	... 107 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: duplicate key value violates unique constraint "account_account_mobile"
  Detail: Key (mobile)=() already exists.
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2725)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2412)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:371)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:502)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:419)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:194)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:155)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:194)
	... 129 common frames omitted

2025-09-05T16:11:30.865+07:00  INFO 63386 --- [qtp127427798-76] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint AccountService/saveAccount
2025-09-05T16:12:02.279+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:12:12.512+07:00  INFO 63386 --- [qtp127427798-76] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le logout successfully 
2025-09-05T16:12:15.329+07:00  INFO 63386 --- [qtp127427798-64] n.d.module.session.ClientSessionManager  : Add a client session id = node013y03tcbfii4r12dy6rr2c4hzy0, token = 5ef2a7729852f98916464275dfa8d056
2025-09-05T16:12:15.334+07:00  INFO 63386 --- [qtp127427798-64] n.d.m.c.a.CompanyAuthenticationService   : User derek.vnhph try to login into system, but fail
2025-09-05T16:12:17.445+07:00  INFO 63386 --- [qtp127427798-60] n.d.module.session.ClientSessionManager  : Add a client session id = node013y03tcbfii4r12dy6rr2c4hzy0, token = 1d84c9b6280a4d83d4a72aea3d297e4a
2025-09-05T16:12:17.446+07:00  INFO 63386 --- [qtp127427798-60] n.d.m.c.a.CompanyAuthenticationService   : User derek.vnhph try to login into system, but fail
2025-09-05T16:12:30.450+07:00  INFO 63386 --- [qtp127427798-69] n.d.module.session.ClientSessionManager  : Add a client session id = node013y03tcbfii4r12dy6rr2c4hzy0, token = ab325d34346baf2c9338176017116584
2025-09-05T16:12:30.460+07:00  INFO 63386 --- [qtp127427798-69] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-05T16:12:36.349+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-09-05T16:12:36.369+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:12:45.385+07:00  INFO 63386 --- [qtp127427798-65] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le logout successfully 
2025-09-05T16:12:47.281+07:00  INFO 63386 --- [qtp127427798-41] n.d.module.session.ClientSessionManager  : Add a client session id = node013y03tcbfii4r12dy6rr2c4hzy0, token = 268a794a3b2b7d6252d92015add8815e
2025-09-05T16:12:47.290+07:00  INFO 63386 --- [qtp127427798-41] n.d.m.c.a.CompanyAuthenticationService   : User giangnm.vnhph is logged in successfully system
2025-09-05T16:13:05.414+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:13:06.531+07:00  INFO 63386 --- [qtp127427798-66] n.d.m.c.a.CompanyAuthenticationService   : User giangnm.vnhph logout successfully 
2025-09-05T16:13:07.874+07:00  INFO 63386 --- [qtp127427798-65] n.d.module.session.ClientSessionManager  : Add a client session id = node013y03tcbfii4r12dy6rr2c4hzy0, token = 85f618646864a8daafaa79ee8f997c8d
2025-09-05T16:13:07.879+07:00  INFO 63386 --- [qtp127427798-65] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-05T16:14:06.511+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:14:36.599+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-09-05T16:14:36.616+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:15:04.661+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:15:04.662+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T16:15:04.664+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-05T16:15:04.668+07:00  INFO 63386 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 05/09/2025@16:15:04+0700
2025-09-05T16:15:04.681+07:00  INFO 63386 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 05/09/2025@16:15:00+0700 to 05/09/2025@16:30:00+0700
2025-09-05T16:15:04.681+07:00  INFO 63386 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 05/09/2025@16:15:00+0700 to 05/09/2025@16:30:00+0700
2025-09-05T16:16:06.799+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:16:35.920+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 19, expire count 3
2025-09-05T16:16:35.947+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-05T16:17:04.001+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:18:06.105+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:18:35.188+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-09-05T16:18:35.208+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:19:03.269+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:20:06.375+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:20:06.377+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T16:20:23.721+07:00  INFO 63386 --- [qtp127427798-77] n.d.module.session.ClientSessionManager  : Add a client session id = node013y03tcbfii4r12dy6rr2c4hzy0, token = 85f618646864a8daafaa79ee8f997c8d
2025-09-05T16:20:23.725+07:00  INFO 63386 --- [qtp127427798-67] n.d.module.session.ClientSessionManager  : Add a client session id = node013y03tcbfii4r12dy6rr2c4hzy0, token = 85f618646864a8daafaa79ee8f997c8d
2025-09-05T16:20:23.751+07:00  INFO 63386 --- [qtp127427798-77] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-05T16:20:23.751+07:00  INFO 63386 --- [qtp127427798-67] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-05T16:20:34.424+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:20:34.426+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 2
2025-09-05T16:21:02.477+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:22:05.592+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:22:33.697+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 1
2025-09-05T16:22:33.719+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:23:06.760+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:23:37.693+07:00  WARN 63386 --- [qtp127427798-75] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-05T16:23:37.693+07:00 ERROR 63386 --- [qtp127427798-75] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "company_hr_employee_bfsone_code"
  Detail: Key (storage_state, company_id, bfsone_code)=(ACTIVE, 16, ) already exists.
2025-09-05T16:23:37.697+07:00 ERROR 63386 --- [qtp127427798-75] n.d.m.monitor.call.EndpointCallContext   : Start call with component HRService, method saveEmployee, arguments
[ {
  "tenantId" : "default",
  "companyId" : 16,
  "companyParentId" : 4,
  "companyCode" : "beehan",
  "companyLabel" : "Bee HAN",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HA NOI BRANCH OFFICE",
  "loginId" : "nhat.le",
  "accountId" : 2089,
  "token" : "85f618646864a8daafaa79ee8f997c8d",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node013y03tcbfii4r12dy6rr2c4hzy0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    },
    "beescs" : {
      "companyId" : 12,
      "companyParentId" : 1,
      "companyCode" : "beescs",
      "companyLabel" : "BEESCS",
      "companyFullName" : null
    },
    "beehan" : {
      "companyId" : 16,
      "companyParentId" : 4,
      "companyCode" : "beehan",
      "companyLabel" : "Bee HAN",
      "companyFullName" : null
    },
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    },
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    },
    "beedad" : {
      "companyId" : 18,
      "companyParentId" : 4,
      "companyCode" : "beedad",
      "companyLabel" : "Bee DAD",
      "companyFullName" : null
    },
    "datatp" : {
      "companyId" : 10,
      "companyParentId" : 1,
      "companyCode" : "datatp",
      "companyLabel" : "DataTP Cloud",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 18225,
    "appId" : 4,
    "appModule" : "communication",
    "appName" : "communication",
    "companyId" : 16,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 16678,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 16,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 1338,
    "appId" : 5,
    "appModule" : "admin",
    "appName" : "admin-space",
    "companyId" : 16,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14161,
    "appId" : 7,
    "appModule" : "company",
    "appName" : "company-asset",
    "companyId" : 16,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1339,
    "appId" : 12,
    "appModule" : "spreadsheet",
    "appName" : "spreadsheet",
    "companyId" : 16,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 11838,
    "appId" : 70,
    "appModule" : "hr",
    "appName" : "company-kpi",
    "companyId" : 16,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1340,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 16,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 6233,
    "appId" : 9,
    "appModule" : "company",
    "appName" : "company-hr",
    "companyId" : 16,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 7657,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 16,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 7063,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 16,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 4543,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 16,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1478,
    "appId" : 17,
    "appModule" : "logistics",
    "appName" : "user-logistics-managements",
    "companyId" : 16,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 2531,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 16,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 5731,
    "appId" : 18,
    "appModule" : "system",
    "appName" : "system",
    "companyId" : 16,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 4384,
    "appId" : 21,
    "appModule" : "tms",
    "appName" : "user-tms-app",
    "companyId" : 16,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 13417,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 16,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:nhat.le"
}, {
  "parentId" : 0,
  "id" : 16,
  "code" : "beehan",
  "label" : "Bee HAN",
  "fullName" : "BEE LOGISTICS CORPORATION - HA NOI BRANCH OFFICE"
}, {
  "id" : 867,
  "uikey" : null,
  "editState" : "ORIGIN",
  "loadState" : "FromDB",
  "version" : 14,
  "createdBy" : "dan",
  "createdTime" : "19/01/2024@04:14:16+0000",
  "modifiedBy" : "nhat.le",
  "modifiedTime" : "05/09/2025@09:23:37+0000",
  "storageState" : "ACTIVE",
  "companyId" : 16,
  "accountId" : 11359,
  "bfsoneUsername" : "",
  "bfsoneCode" : "",
  "employeeCardId" : null,
  "label" : "NGUYỄN THỊ NGỌC",
  "employeeTaxCode" : null,
  "employeeCode" : "",
  "companyBranch" : "C. BEE - Hà Nội",
  "managerEmployeeId" : 819,
  "managerEmployeeLabel" : "NGUYỄN ANH HOÀNG",
  "priority" : 5,
  "description" : "Modified",
  "startDate" : null,
  "endDate" : null,
  "variants" : "nguyenthingoc,hnngocnt"
} ]
2025-09-05T16:23:37.699+07:00 ERROR 63386 --- [qtp127427798-75] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: duplicate key value violates unique constraint "company_hr_employee_bfsone_code"
  Detail: Key (storage_state, company_id, bfsone_code)=(ACTIVE, 16, ) already exists.] [update "company_hr_employee" set "account_id"=?,"bfsone_code"=?,"bfsone_username"=?,"company_branch"=?,"company_id"=?,"created_by"=?,"created_time"=?,"description"=?,"employee_card_id"=?,"employee_code"=?,"employee_tax_code"=?,"end_date"=?,"label"=?,"manager_employee_id"=?,"manager_employee_label"=?,"modified_by"=?,"modified_time"=?,"priority"=?,"start_date"=?,"storage_state"=?,"variants"=?,"version"=? where "id"=? and "version"=?]; SQL [update "company_hr_employee" set "account_id"=?,"bfsone_code"=?,"bfsone_username"=?,"company_branch"=?,"company_id"=?,"created_by"=?,"created_time"=?,"description"=?,"employee_card_id"=?,"employee_code"=?,"employee_tax_code"=?,"end_date"=?,"label"=?,"manager_employee_id"=?,"manager_employee_label"=?,"modified_by"=?,"modified_time"=?,"priority"=?,"start_date"=?,"storage_state"=?,"variants"=?,"version"=? where "id"=? and "version"=?]; constraint [company_hr_employee_bfsone_code]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:566)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:795)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:758)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:698)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:416)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.hr.HRService$$SpringCGLIB$$0.saveEmployee(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: duplicate key value violates unique constraint "company_hr_employee_bfsone_code"
  Detail: Key (storage_state, company_id, bfsone_code)=(ACTIVE, 16, ) already exists.] [update "company_hr_employee" set "account_id"=?,"bfsone_code"=?,"bfsone_username"=?,"company_branch"=?,"company_id"=?,"created_by"=?,"created_time"=?,"description"=?,"employee_card_id"=?,"employee_code"=?,"employee_tax_code"=?,"end_date"=?,"label"=?,"manager_employee_id"=?,"manager_employee_label"=?,"modified_by"=?,"modified_time"=?,"priority"=?,"start_date"=?,"storage_state"=?,"variants"=?,"version"=? where "id"=? and "version"=?]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:197)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.performNonBatchedMutation(AbstractMutationExecutor.java:108)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorSingleNonBatched.performNonBatchedOperations(MutationExecutorSingleNonBatched.java:40)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.execute(AbstractMutationExecutor.java:52)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.doStaticUpdate(UpdateCoordinatorStandard.java:778)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.performUpdate(UpdateCoordinatorStandard.java:324)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.coordinateUpdate(UpdateCoordinatorStandard.java:242)
	at org.hibernate.persister.entity.AbstractEntityPersister.update(AbstractEntityPersister.java:2810)
	at org.hibernate.action.internal.EntityUpdateAction.execute(EntityUpdateAction.java:168)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:632)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:499)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:363)
	at org.hibernate.event.internal.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:41)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.doFlush(SessionImpl.java:1403)
	at org.hibernate.internal.SessionImpl.managedFlush(SessionImpl.java:484)
	at org.hibernate.internal.SessionImpl.flushBeforeTransactionCompletion(SessionImpl.java:2319)
	at org.hibernate.internal.SessionImpl.beforeTransactionCompletion(SessionImpl.java:1976)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.beforeTransactionCompletion(JdbcCoordinatorImpl.java:439)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl.beforeCompletionCallback(JdbcResourceLocalTransactionCoordinatorImpl.java:169)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl$TransactionDriverControlImpl.commit(JdbcResourceLocalTransactionCoordinatorImpl.java:267)
	at org.hibernate.engine.transaction.internal.TransactionImpl.commit(TransactionImpl.java:101)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:562)
	... 107 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: duplicate key value violates unique constraint "company_hr_employee_bfsone_code"
  Detail: Key (storage_state, company_id, bfsone_code)=(ACTIVE, 16, ) already exists.
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2725)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2412)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:371)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:502)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:419)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:194)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:155)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:194)
	... 129 common frames omitted

2025-09-05T16:23:37.702+07:00  INFO 63386 --- [qtp127427798-75] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint HRService/saveEmployee
2025-09-05T16:24:04.866+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:24:32.937+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 25
2025-09-05T16:24:32.944+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:25:06.989+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:25:06.991+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T16:26:04.071+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:26:32.139+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 3
2025-09-05T16:26:32.146+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:27:06.209+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:28:03.307+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:28:36.389+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-05T16:28:36.404+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:29:06.466+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:30:02.543+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:30:02.545+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T16:30:02.546+07:00  INFO 63386 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 05/09/2025@16:30:02+0700
2025-09-05T16:30:02.598+07:00  INFO 63386 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 05/09/2025@16:30:00+0700 to 05/09/2025@16:45:00+0700
2025-09-05T16:30:02.599+07:00  INFO 63386 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 05/09/2025@16:30:00+0700 to 05/09/2025@16:45:00+0700
2025-09-05T16:30:02.600+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-05T16:30:35.665+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 4
2025-09-05T16:30:35.672+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:31:05.723+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:32:06.822+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:32:35.894+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-05T16:32:35.908+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:33:04.948+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:34:06.040+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:34:36.113+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-05T16:34:36.132+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:35:04.176+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:35:04.178+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T16:36:06.282+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:36:35.340+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 6
2025-09-05T16:36:35.343+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:37:03.386+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:38:06.496+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:38:34.553+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-05T16:38:34.568+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:39:02.613+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:40:05.723+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:40:05.724+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T16:40:33.770+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-05T16:40:33.772+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:41:06.832+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:42:04.938+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:42:33.017+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-05T16:42:33.027+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:43:06.086+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:44:04.187+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:44:32.275+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-05T16:44:32.279+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:45:06.333+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:45:06.336+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T16:45:06.337+07:00  INFO 63386 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 05/09/2025@16:45:06+0700
2025-09-05T16:45:06.389+07:00  INFO 63386 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 05/09/2025@16:45:00+0700 to 05/09/2025@17:00:00+0700
2025-09-05T16:45:06.390+07:00  INFO 63386 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 05/09/2025@16:45:00+0700 to 05/09/2025@17:00:00+0700
2025-09-05T16:45:06.391+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-05T16:46:03.503+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:46:36.584+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-05T16:46:36.607+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:47:06.661+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:47:45.619+07:00  INFO 63386 --- [qtp127427798-159] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node04vr9xb04o08c11k6uz3j846ow1
2025-09-05T16:47:45.760+07:00  INFO 63386 --- [qtp127427798-159] n.d.module.session.ClientSessionManager  : Add a client session id = node04vr9xb04o08c11k6uz3j846ow1, token = 0bf811b7203998c8b03437db8c34e92f
2025-09-05T16:47:45.814+07:00  INFO 63386 --- [qtp127427798-159] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-05T16:47:45.858+07:00  INFO 63386 --- [qtp127427798-160] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-09-05T16:47:45.859+07:00  INFO 63386 --- [qtp127427798-160] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/SyncBFSUsernameToLoginId.groovy"
}
2025-09-05T16:47:46.111+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 1/67: 10 records
2025-09-05T16:47:46.114+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 2/67: 10 records
2025-09-05T16:47:46.114+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 3/67: 10 records
2025-09-05T16:47:46.114+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 4/67: 10 records
2025-09-05T16:47:46.115+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 5/67: 10 records
2025-09-05T16:47:46.115+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 6/67: 10 records
2025-09-05T16:47:46.115+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 7/67: 10 records
2025-09-05T16:47:46.115+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 8/67: 10 records
2025-09-05T16:47:46.116+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 9/67: 10 records
2025-09-05T16:47:46.116+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 10/67: 10 records
2025-09-05T16:47:46.116+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 11/67: 10 records
2025-09-05T16:47:46.116+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 12/67: 10 records
2025-09-05T16:47:46.117+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 13/67: 10 records
2025-09-05T16:47:46.117+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 14/67: 10 records
2025-09-05T16:47:46.118+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 15/67: 10 records
2025-09-05T16:47:46.118+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 16/67: 10 records
2025-09-05T16:47:46.118+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 17/67: 10 records
2025-09-05T16:47:46.118+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 18/67: 10 records
2025-09-05T16:47:46.118+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 19/67: 10 records
2025-09-05T16:47:46.119+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 20/67: 10 records
2025-09-05T16:47:46.119+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 21/67: 10 records
2025-09-05T16:47:46.119+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 22/67: 10 records
2025-09-05T16:47:46.119+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 23/67: 10 records
2025-09-05T16:47:46.119+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 24/67: 10 records
2025-09-05T16:47:46.120+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 25/67: 10 records
2025-09-05T16:47:46.120+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 26/67: 10 records
2025-09-05T16:47:46.120+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 27/67: 10 records
2025-09-05T16:47:46.120+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 28/67: 10 records
2025-09-05T16:47:46.120+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 29/67: 10 records
2025-09-05T16:47:46.121+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 30/67: 10 records
2025-09-05T16:47:46.121+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 31/67: 10 records
2025-09-05T16:47:46.121+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 32/67: 10 records
2025-09-05T16:47:46.121+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 33/67: 10 records
2025-09-05T16:47:46.122+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 34/67: 10 records
2025-09-05T16:47:46.122+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 35/67: 10 records
2025-09-05T16:47:46.122+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 36/67: 10 records
2025-09-05T16:47:46.122+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 37/67: 10 records
2025-09-05T16:47:46.122+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 38/67: 10 records
2025-09-05T16:47:46.122+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 39/67: 10 records
2025-09-05T16:47:46.123+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 40/67: 10 records
2025-09-05T16:47:46.123+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 41/67: 10 records
2025-09-05T16:47:46.123+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 42/67: 10 records
2025-09-05T16:47:46.123+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 43/67: 10 records
2025-09-05T16:47:46.123+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 44/67: 10 records
2025-09-05T16:47:46.124+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 45/67: 10 records
2025-09-05T16:47:46.124+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 46/67: 10 records
2025-09-05T16:47:46.124+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 47/67: 10 records
2025-09-05T16:47:46.124+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 48/67: 10 records
2025-09-05T16:47:46.124+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 49/67: 10 records
2025-09-05T16:47:46.124+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 50/67: 10 records
2025-09-05T16:47:46.125+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 51/67: 10 records
2025-09-05T16:47:46.125+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 52/67: 10 records
2025-09-05T16:47:46.125+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 53/67: 10 records
2025-09-05T16:47:46.125+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 54/67: 10 records
2025-09-05T16:47:46.125+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 55/67: 10 records
2025-09-05T16:47:46.125+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 56/67: 10 records
2025-09-05T16:47:46.126+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 57/67: 10 records
2025-09-05T16:47:46.126+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 58/67: 10 records
2025-09-05T16:47:46.126+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 59/67: 10 records
2025-09-05T16:47:46.126+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 60/67: 10 records
2025-09-05T16:47:46.126+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 61/67: 10 records
2025-09-05T16:47:46.126+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 62/67: 10 records
2025-09-05T16:47:46.126+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 63/67: 10 records
2025-09-05T16:47:46.127+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 64/67: 10 records
2025-09-05T16:47:46.127+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 65/67: 10 records
2025-09-05T16:47:46.127+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 66/67: 10 records
2025-09-05T16:47:46.127+07:00  INFO 63386 --- [qtp127427798-160] migration.server.hr.SyncBFSOneUsername   : Processing batch 67/67: 8 records
2025-09-05T16:48:02.756+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:48:35.812+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:48:35.829+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:49:05.876+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:50:06.966+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:50:06.969+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T16:50:36.042+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 1
2025-09-05T16:50:36.047+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:51:05.088+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:52:06.179+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:52:36.233+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 4
2025-09-05T16:52:36.239+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:53:04.278+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:54:06.386+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:54:35.430+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:54:35.434+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:55:03.472+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T16:55:03.473+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:56:06.570+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:56:34.633+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-05T16:56:34.641+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:57:02.679+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:58:05.780+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T16:58:33.873+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 2
2025-09-05T16:58:33.881+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T16:59:06.963+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:00:05.143+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:00:05.144+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T17:00:05.144+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-05T17:00:05.146+07:00  INFO 63386 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 05/09/2025@17:00:05+0700
2025-09-05T17:00:05.162+07:00  INFO 63386 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 05/09/2025@17:00:00+0700 to 05/09/2025@17:15:00+0700
2025-09-05T17:00:05.163+07:00  INFO 63386 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 05/09/2025@17:00:00+0700 to 05/09/2025@17:15:00+0700
2025-09-05T17:00:05.165+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-05T17:00:17.928+07:00  INFO 63386 --- [Scheduler-1744683497-1] n.d.m.session.AppHttpSessionListener     : The session node04vr9xb04o08c11k6uz3j846ow1 is destroyed.
2025-09-05T17:00:33.281+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-05T17:00:33.295+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T17:01:06.393+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:02:04.547+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:02:32.591+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T17:02:32.594+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T17:03:06.650+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:04:03.749+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:04:31.815+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-05T17:04:31.834+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T17:05:06.901+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:05:06.903+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T17:06:03.000+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:06:36.082+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-05T17:06:36.091+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T17:07:06.135+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:08:02.215+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:08:36.283+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-05T17:08:36.295+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T17:09:05.319+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:10:06.421+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T17:10:06.424+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:10:36.511+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 1
2025-09-05T17:10:36.528+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T17:11:04.573+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:12:06.672+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:12:35.729+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T17:12:35.738+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T17:13:03.780+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:14:06.883+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:14:34.954+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-05T17:14:34.969+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T17:15:03.009+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-05T17:15:03.010+07:00  INFO 63386 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 05/09/2025@17:15:03+0700
2025-09-05T17:15:03.022+07:00  INFO 63386 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 05/09/2025@17:15:00+0700 to 05/09/2025@17:30:00+0700
2025-09-05T17:15:03.023+07:00  INFO 63386 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 05/09/2025@17:15:00+0700 to 05/09/2025@17:30:00+0700
2025-09-05T17:15:03.023+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T17:15:03.023+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:16:06.129+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:16:34.188+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-05T17:16:34.196+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T17:17:02.245+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:18:05.356+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:18:33.417+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 2
2025-09-05T17:18:33.429+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T17:19:06.477+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:20:04.574+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:20:04.579+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T17:20:32.649+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-05T17:20:32.662+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T17:21:06.719+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:22:03.817+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:22:31.914+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-05T17:22:31.933+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T17:23:07.006+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:24:03.102+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:24:36.181+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-05T17:24:36.189+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T17:25:06.239+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:25:06.240+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-05T17:26:02.345+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:26:36.423+07:00  INFO 63386 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-05T17:26:36.441+07:00  INFO 63386 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T17:27:05.489+07:00  INFO 63386 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-05T17:27:23.704+07:00  INFO 63386 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@47556cd6{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-05T17:27:23.707+07:00  INFO 63386 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-05T17:27:23.707+07:00  INFO 63386 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-05T17:27:23.707+07:00  INFO 63386 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-05T17:27:23.707+07:00  INFO 63386 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-05T17:27:23.708+07:00  INFO 63386 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-05T17:27:23.708+07:00  INFO 63386 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-05T17:27:23.708+07:00  INFO 63386 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-05T17:27:23.708+07:00  INFO 63386 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-05T17:27:23.708+07:00  INFO 63386 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-05T17:27:23.708+07:00  INFO 63386 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-05T17:27:23.708+07:00  INFO 63386 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-05T17:27:23.708+07:00  INFO 63386 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-05T17:27:23.708+07:00  INFO 63386 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-05T17:27:23.708+07:00  INFO 63386 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-05T17:27:23.708+07:00  INFO 63386 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-05T17:27:23.722+07:00  INFO 63386 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-05T17:27:23.793+07:00  INFO 63386 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-05T17:27:23.799+07:00  INFO 63386 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-05T17:27:23.825+07:00  INFO 63386 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-05T17:27:23.828+07:00  INFO 63386 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-05T17:27:23.829+07:00  INFO 63386 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-05T17:27:23.830+07:00  INFO 63386 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-05T17:27:23.831+07:00  INFO 63386 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-05T17:27:23.832+07:00  INFO 63386 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-05T17:27:23.832+07:00  INFO 63386 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-05T17:27:23.832+07:00  INFO 63386 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-05T17:27:23.832+07:00  INFO 63386 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-05T17:27:23.833+07:00  INFO 63386 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-05T17:27:23.833+07:00  INFO 63386 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-05T17:27:23.833+07:00  INFO 63386 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-05T17:27:23.834+07:00  INFO 63386 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-05T17:27:23.837+07:00  INFO 63386 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@7abc838a{STOPPING}[12.0.15,sto=0]
2025-09-05T17:27:23.841+07:00  INFO 63386 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-05T17:27:23.842+07:00  INFO 63386 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@18c2b4c6{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.14892480253553675056/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@461e9b31{STOPPED}}
