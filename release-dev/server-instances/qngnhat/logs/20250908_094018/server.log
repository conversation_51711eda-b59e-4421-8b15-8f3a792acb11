2025-09-08T09:40:19.456+07:00  INFO 15975 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 15975 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-08T09:40:19.456+07:00  INFO 15975 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-08T09:40:20.195+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.258+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 60 ms. Found 22 JPA repository interfaces.
2025-09-08T09:40:20.267+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.268+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-08T09:40:20.268+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.275+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-09-08T09:40:20.276+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.278+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-08T09:40:20.325+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.331+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 1 JPA repository interface.
2025-09-08T09:40:20.339+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.341+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-08T09:40:20.341+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.345+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-08T09:40:20.347+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.351+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-08T09:40:20.354+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.356+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-08T09:40:20.356+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.357+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-08T09:40:20.357+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.363+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 10 JPA repository interfaces.
2025-09-08T09:40:20.367+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.370+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-08T09:40:20.373+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.377+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-08T09:40:20.377+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.384+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-08T09:40:20.384+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.387+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-08T09:40:20.387+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.387+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-08T09:40:20.387+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.388+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-08T09:40:20.388+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.393+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-08T09:40:20.393+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.394+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-08T09:40:20.394+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.394+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-08T09:40:20.395+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.404+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-09-08T09:40:20.413+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.419+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-08T09:40:20.419+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.422+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-08T09:40:20.423+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.427+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-08T09:40:20.427+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.432+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 9 JPA repository interfaces.
2025-09-08T09:40:20.432+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.436+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-08T09:40:20.436+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.443+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-08T09:40:20.444+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.452+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-09-08T09:40:20.452+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.466+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 24 JPA repository interfaces.
2025-09-08T09:40:20.467+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.467+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-08T09:40:20.473+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.473+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-08T09:40:20.474+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.480+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-08T09:40:20.482+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.515+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 32 ms. Found 65 JPA repository interfaces.
2025-09-08T09:40:20.515+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.516+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-08T09:40:20.521+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:40:20.524+07:00  INFO 15975 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-08T09:40:20.735+07:00  INFO 15975 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-08T09:40:20.738+07:00  INFO 15975 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-08T09:40:21.045+07:00  WARN 15975 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-08T09:40:21.234+07:00  INFO 15975 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-08T09:40:21.237+07:00  INFO 15975 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-08T09:40:21.253+07:00  INFO 15975 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-08T09:40:21.253+07:00  INFO 15975 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1651 ms
2025-09-08T09:40:21.306+07:00  WARN 15975 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T09:40:21.306+07:00  INFO 15975 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-08T09:40:21.400+07:00  INFO 15975 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@2625eb84
2025-09-08T09:40:21.400+07:00  INFO 15975 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-08T09:40:21.405+07:00  WARN 15975 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T09:40:21.405+07:00  INFO 15975 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-08T09:40:21.411+07:00  INFO 15975 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@61761e60
2025-09-08T09:40:21.411+07:00  INFO 15975 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-08T09:40:21.411+07:00  WARN 15975 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T09:40:21.411+07:00  INFO 15975 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-08T09:40:21.417+07:00  INFO 15975 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6bcf2780
2025-09-08T09:40:21.418+07:00  INFO 15975 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-08T09:40:21.418+07:00  WARN 15975 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T09:40:21.418+07:00  INFO 15975 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-08T09:40:21.427+07:00  INFO 15975 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@2f7af3d0
2025-09-08T09:40:21.427+07:00  INFO 15975 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-08T09:40:21.427+07:00  WARN 15975 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T09:40:21.427+07:00  INFO 15975 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-08T09:40:21.433+07:00  INFO 15975 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@4ff7de80
2025-09-08T09:40:21.434+07:00  INFO 15975 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-08T09:40:21.434+07:00  INFO 15975 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-08T09:40:21.475+07:00  INFO 15975 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-08T09:40:21.477+07:00  INFO 15975 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@d5d9d92{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.7874476761267802851/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@126f428e{STARTED}}
2025-09-08T09:40:21.477+07:00  INFO 15975 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@d5d9d92{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.7874476761267802851/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@126f428e{STARTED}}
2025-09-08T09:40:21.479+07:00  INFO 15975 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@5453b15d{STARTING}[12.0.15,sto=0] @2462ms
2025-09-08T09:40:21.576+07:00  INFO 15975 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-08T09:40:21.602+07:00  INFO 15975 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-08T09:40:21.616+07:00  INFO 15975 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-08T09:40:21.743+07:00  INFO 15975 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-08T09:40:21.778+07:00  WARN 15975 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-08T09:40:22.387+07:00  INFO 15975 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-08T09:40:22.396+07:00  INFO 15975 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@4ed507b0] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-08T09:40:22.514+07:00  INFO 15975 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T09:40:22.710+07:00  INFO 15975 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.company.tmpl", "cloud.datatp.tms", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-09-08T09:40:22.713+07:00  INFO 15975 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-08T09:40:22.725+07:00  INFO 15975 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-08T09:40:22.727+07:00  INFO 15975 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-08T09:40:22.778+07:00  INFO 15975 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-08T09:40:22.785+07:00  WARN 15975 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-08T09:40:24.761+07:00  INFO 15975 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-08T09:40:24.762+07:00  INFO 15975 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@1273afe9] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-08T09:40:24.972+07:00  WARN 15975 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-08T09:40:24.973+07:00  WARN 15975 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-08T09:40:24.980+07:00  WARN 15975 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-08T09:40:24.980+07:00  WARN 15975 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-08T09:40:24.993+07:00  WARN 15975 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-08T09:40:24.993+07:00  WARN 15975 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-08T09:40:25.411+07:00  INFO 15975 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T09:40:25.416+07:00  INFO 15975 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-08T09:40:25.417+07:00  INFO 15975 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-08T09:40:25.435+07:00  INFO 15975 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-08T09:40:25.445+07:00  WARN 15975 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-08T09:40:25.948+07:00  INFO 15975 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-08T09:40:25.948+07:00  INFO 15975 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@69f5660b] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-08T09:40:26.038+07:00  WARN 15975 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-08T09:40:26.039+07:00  WARN 15975 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-08T09:40:26.344+07:00  INFO 15975 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T09:40:26.373+07:00  INFO 15975 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-08T09:40:26.377+07:00  INFO 15975 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-08T09:40:26.378+07:00  INFO 15975 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-08T09:40:26.384+07:00  WARN 15975 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-08T09:40:26.513+07:00  INFO 15975 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-08T09:40:26.974+07:00  INFO 15975 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-08T09:40:26.977+07:00  INFO 15975 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-08T09:40:27.011+07:00  INFO 15975 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-08T09:40:27.055+07:00  INFO 15975 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-08T09:40:27.114+07:00  INFO 15975 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-08T09:40:27.141+07:00  INFO 15975 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-08T09:40:27.170+07:00  INFO 15975 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 427731785ms : this is harmless.
2025-09-08T09:40:27.179+07:00  INFO 15975 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-08T09:40:27.182+07:00  INFO 15975 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-08T09:40:27.194+07:00  INFO 15975 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 434855347ms : this is harmless.
2025-09-08T09:40:27.196+07:00  INFO 15975 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-08T09:40:27.208+07:00  INFO 15975 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-08T09:40:27.209+07:00  INFO 15975 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-08T09:40:28.262+07:00  INFO 15975 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-08T09:40:28.262+07:00  INFO 15975 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-08T09:40:28.263+07:00  WARN 15975 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-08T09:40:29.012+07:00  INFO 15975 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 08/09/2025@09:30:00+0700 to 08/09/2025@09:45:00+0700
2025-09-08T09:40:29.012+07:00  INFO 15975 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 08/09/2025@09:30:00+0700 to 08/09/2025@09:45:00+0700
2025-09-08T09:40:30.122+07:00  INFO 15975 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-08T09:40:30.123+07:00  INFO 15975 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-08T09:40:30.123+07:00  WARN 15975 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-08T09:40:30.402+07:00  INFO 15975 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-08T09:40:30.402+07:00  INFO 15975 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-08T09:40:30.402+07:00  INFO 15975 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-08T09:40:30.402+07:00  INFO 15975 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-08T09:40:30.402+07:00  INFO 15975 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-08T09:40:31.926+07:00  WARN 15975 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: cab1465b-6fe3-423e-9883-eade0b1412cc

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-08T09:40:31.930+07:00  INFO 15975 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-08T09:40:32.224+07:00  INFO 15975 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-08T09:40:32.225+07:00  INFO 15975 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-08T09:40:32.225+07:00  INFO 15975 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-08T09:40:32.225+07:00  INFO 15975 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-08T09:40:32.225+07:00  INFO 15975 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-08T09:40:32.225+07:00  INFO 15975 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-08T09:40:32.225+07:00  INFO 15975 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-08T09:40:32.225+07:00  INFO 15975 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-08T09:40:32.225+07:00  INFO 15975 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-08T09:40:32.225+07:00  INFO 15975 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-08T09:40:32.225+07:00  INFO 15975 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-08T09:40:32.225+07:00  INFO 15975 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-08T09:40:32.228+07:00  INFO 15975 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-08T09:40:32.228+07:00  INFO 15975 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-08T09:40:32.228+07:00  INFO 15975 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-08T09:40:32.283+07:00  INFO 15975 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-08T09:40:32.283+07:00  INFO 15975 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-08T09:40:32.285+07:00  INFO 15975 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-08T09:40:32.292+07:00  INFO 15975 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@5653bcf8{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-08T09:40:32.293+07:00  INFO 15975 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-08T09:40:32.294+07:00  INFO 15975 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-08T09:40:32.472+07:00  INFO 15975 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-08T09:40:32.473+07:00  INFO 15975 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-08T09:40:32.484+07:00  INFO 15975 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.288 seconds (process running for 13.468)
2025-09-08T09:41:06.305+07:00  INFO 15975 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:41:35.360+07:00  INFO 15975 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T09:41:35.365+07:00  INFO 15975 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T09:42:03.412+07:00  INFO 15975 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:42:36.275+07:00  INFO 15975 --- [qtp1741445688-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0ykw5k7sc9zkgjxkdm11my0zt0
2025-09-08T09:42:36.746+07:00  INFO 15975 --- [qtp1741445688-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0ykw5k7sc9zkgjxkdm11my0zt0, token = 783bbe1bd8dfe7c9f4cfc74050d8e77d
2025-09-08T09:42:37.125+07:00  INFO 15975 --- [qtp1741445688-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-08T09:42:37.223+07:00  INFO 15975 --- [qtp1741445688-35] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-09-08T09:42:37.223+07:00  INFO 15975 --- [qtp1741445688-35] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/SyncBFSUsernameToLoginId.groovy"
}
2025-09-08T09:42:37.551+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 1/67: 10 records
2025-09-08T09:42:37.672+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 2/67: 10 records
2025-09-08T09:42:37.736+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 3/67: 10 records
2025-09-08T09:42:37.804+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 4/67: 10 records
2025-09-08T09:42:37.869+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 5/67: 10 records
2025-09-08T09:42:37.933+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 6/67: 10 records
2025-09-08T09:42:37.997+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 7/67: 10 records
2025-09-08T09:42:38.056+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 8/67: 10 records
2025-09-08T09:42:38.112+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 9/67: 10 records
2025-09-08T09:42:38.170+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 10/67: 10 records
2025-09-08T09:42:38.227+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 11/67: 10 records
2025-09-08T09:42:38.283+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 12/67: 10 records
2025-09-08T09:42:38.341+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 13/67: 10 records
2025-09-08T09:42:38.399+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 14/67: 10 records
2025-09-08T09:42:38.453+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 15/67: 10 records
2025-09-08T09:42:38.518+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 16/67: 10 records
2025-09-08T09:42:38.574+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 17/67: 10 records
2025-09-08T09:42:38.631+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 18/67: 10 records
2025-09-08T09:42:38.689+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 19/67: 10 records
2025-09-08T09:42:38.744+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 20/67: 10 records
2025-09-08T09:42:38.798+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 21/67: 10 records
2025-09-08T09:42:38.855+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 22/67: 10 records
2025-09-08T09:42:38.912+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 23/67: 10 records
2025-09-08T09:42:38.923+07:00  WARN 15975 --- [qtp1741445688-35] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-08T09:42:38.923+07:00 ERROR 15975 --- [qtp1741445688-35] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(royal.vnhan) already exists.
2025-09-08T09:42:38.973+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 24/67: 10 records
2025-09-08T09:42:39.027+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 25/67: 10 records
2025-09-08T09:42:39.082+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 26/67: 10 records
2025-09-08T09:42:39.139+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 27/67: 10 records
2025-09-08T09:42:39.203+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 28/67: 10 records
2025-09-08T09:42:39.263+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 29/67: 10 records
2025-09-08T09:42:39.319+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 30/67: 10 records
2025-09-08T09:42:39.378+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 31/67: 10 records
2025-09-08T09:42:39.429+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 32/67: 10 records
2025-09-08T09:42:39.484+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 33/67: 10 records
2025-09-08T09:42:39.537+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 34/67: 10 records
2025-09-08T09:42:39.592+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 35/67: 10 records
2025-09-08T09:42:39.645+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 36/67: 10 records
2025-09-08T09:42:39.700+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 37/67: 10 records
2025-09-08T09:42:39.756+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 38/67: 10 records
2025-09-08T09:42:39.800+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 39/67: 10 records
2025-09-08T09:42:39.856+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 40/67: 10 records
2025-09-08T09:42:39.906+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 41/67: 10 records
2025-09-08T09:42:39.964+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 42/67: 10 records
2025-09-08T09:42:40.020+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 43/67: 10 records
2025-09-08T09:42:40.075+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 44/67: 10 records
2025-09-08T09:42:40.123+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 45/67: 10 records
2025-09-08T09:42:40.176+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 46/67: 10 records
2025-09-08T09:42:40.228+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 47/67: 10 records
2025-09-08T09:42:40.280+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 48/67: 10 records
2025-09-08T09:42:40.344+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 49/67: 10 records
2025-09-08T09:42:40.398+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 50/67: 10 records
2025-09-08T09:42:40.452+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 51/67: 10 records
2025-09-08T09:42:40.501+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 52/67: 10 records
2025-09-08T09:42:40.555+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 53/67: 10 records
2025-09-08T09:42:40.610+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 54/67: 10 records
2025-09-08T09:42:40.670+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 55/67: 10 records
2025-09-08T09:42:40.707+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 56/67: 10 records
2025-09-08T09:42:40.754+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 57/67: 10 records
2025-09-08T09:42:40.787+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 58/67: 10 records
2025-09-08T09:42:40.847+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 59/67: 10 records
2025-09-08T09:42:40.901+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 60/67: 10 records
2025-09-08T09:42:40.949+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 61/67: 10 records
2025-09-08T09:42:41.010+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 62/67: 10 records
2025-09-08T09:42:41.064+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 63/67: 10 records
2025-09-08T09:42:41.118+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 64/67: 10 records
2025-09-08T09:42:41.171+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 65/67: 10 records
2025-09-08T09:42:41.217+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 66/67: 10 records
2025-09-08T09:42:41.270+07:00  INFO 15975 --- [qtp1741445688-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 67/67: 8 records
2025-09-08T09:43:06.515+07:00  INFO 15975 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:43:34.570+07:00  INFO 15975 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T09:43:34.578+07:00  INFO 15975 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T09:44:02.629+07:00  INFO 15975 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:45:05.744+07:00  INFO 15975 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:45:05.751+07:00  INFO 15975 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-08T09:45:05.752+07:00  INFO 15975 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-08T09:45:05.768+07:00  INFO 15975 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 08/09/2025@09:45:05+0700
2025-09-08T09:45:05.784+07:00  INFO 15975 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 08/09/2025@09:45:00+0700 to 08/09/2025@10:00:00+0700
2025-09-08T09:45:05.784+07:00  INFO 15975 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 08/09/2025@09:45:00+0700 to 08/09/2025@10:00:00+0700
2025-09-08T09:45:38.903+07:00  INFO 15975 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-08T09:45:38.910+07:00  INFO 15975 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T09:46:06.959+07:00  INFO 15975 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:47:05.051+07:00  INFO 15975 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:47:39.135+07:00  INFO 15975 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-08T09:47:39.140+07:00  INFO 15975 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T09:48:06.189+07:00  INFO 15975 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:49:04.290+07:00  INFO 15975 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:49:39.356+07:00  INFO 15975 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T09:49:39.363+07:00  INFO 15975 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T09:50:06.412+07:00  INFO 15975 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-08T09:50:06.413+07:00  INFO 15975 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:50:21.502+07:00  INFO 15975 --- [Scheduler-392712-1] n.d.m.session.AppHttpSessionListener     : The session node0ykw5k7sc9zkgjxkdm11my0zt0 is destroyed.
2025-09-08T09:51:03.501+07:00  INFO 15975 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:51:38.559+07:00  INFO 15975 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T09:51:38.565+07:00  INFO 15975 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T09:51:55.420+07:00  INFO 15975 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@5653bcf8{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-08T09:51:55.422+07:00  INFO 15975 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-08T09:51:55.422+07:00  INFO 15975 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-08T09:51:55.422+07:00  INFO 15975 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-08T09:51:55.422+07:00  INFO 15975 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-08T09:51:55.423+07:00  INFO 15975 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-08T09:51:55.423+07:00  INFO 15975 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-08T09:51:55.423+07:00  INFO 15975 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-08T09:51:55.423+07:00  INFO 15975 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-08T09:51:55.423+07:00  INFO 15975 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-08T09:51:55.423+07:00  INFO 15975 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-08T09:51:55.423+07:00  INFO 15975 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-08T09:51:55.423+07:00  INFO 15975 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-08T09:51:55.423+07:00  INFO 15975 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-08T09:51:55.423+07:00  INFO 15975 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-08T09:51:55.423+07:00  INFO 15975 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-08T09:51:55.437+07:00  INFO 15975 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T09:51:55.494+07:00  INFO 15975 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-08T09:51:55.499+07:00  INFO 15975 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-08T09:51:55.518+07:00  INFO 15975 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T09:51:55.519+07:00  INFO 15975 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T09:51:55.520+07:00  INFO 15975 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T09:51:55.520+07:00  INFO 15975 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-08T09:51:55.521+07:00  INFO 15975 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-08T09:51:55.522+07:00  INFO 15975 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-08T09:51:55.522+07:00  INFO 15975 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-08T09:51:55.522+07:00  INFO 15975 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-08T09:51:55.522+07:00  INFO 15975 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-08T09:51:55.522+07:00  INFO 15975 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-08T09:51:55.523+07:00  INFO 15975 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-08T09:51:55.523+07:00  INFO 15975 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-08T09:51:55.523+07:00  INFO 15975 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-08T09:51:55.524+07:00  INFO 15975 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@5453b15d{STOPPING}[12.0.15,sto=0]
2025-09-08T09:51:55.526+07:00  INFO 15975 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-08T09:51:55.527+07:00  INFO 15975 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@d5d9d92{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.7874476761267802851/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@126f428e{STOPPED}}
