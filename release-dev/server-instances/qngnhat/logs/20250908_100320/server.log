2025-09-08T10:03:21.057+07:00  INFO 18604 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 18604 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-08T10:03:21.058+07:00  INFO 18604 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-08T10:03:21.775+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:21.841+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 62 ms. Found 22 JPA repository interfaces.
2025-09-08T10:03:21.850+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:21.852+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-08T10:03:21.852+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:21.861+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 10 JPA repository interfaces.
2025-09-08T10:03:21.861+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:21.864+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-08T10:03:21.911+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:21.916+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-08T10:03:21.923+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:21.925+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-08T10:03:21.925+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:21.929+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-08T10:03:21.931+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:21.935+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-08T10:03:21.938+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:21.941+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-08T10:03:21.941+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:21.942+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-08T10:03:21.942+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:21.948+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 10 JPA repository interfaces.
2025-09-08T10:03:21.952+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:21.954+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-08T10:03:21.957+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:21.961+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-08T10:03:21.961+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:21.968+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-08T10:03:21.968+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:21.971+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-08T10:03:21.972+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:21.972+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-08T10:03:21.972+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:21.973+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-08T10:03:21.973+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:21.977+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-08T10:03:21.977+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:21.978+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-08T10:03:21.979+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:21.979+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-08T10:03:21.979+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:21.988+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-09-08T10:03:21.997+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:22.003+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-08T10:03:22.003+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:22.006+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-08T10:03:22.006+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:22.010+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-08T10:03:22.010+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:22.015+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 9 JPA repository interfaces.
2025-09-08T10:03:22.016+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:22.019+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-08T10:03:22.020+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:22.027+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-08T10:03:22.027+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:22.035+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-09-08T10:03:22.036+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:22.049+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 24 JPA repository interfaces.
2025-09-08T10:03:22.049+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:22.050+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-08T10:03:22.055+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:22.056+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-08T10:03:22.056+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:22.063+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-08T10:03:22.064+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:22.100+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 65 JPA repository interfaces.
2025-09-08T10:03:22.101+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:22.102+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-08T10:03:22.106+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T10:03:22.109+07:00  INFO 18604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-08T10:03:22.275+07:00  INFO 18604 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-08T10:03:22.279+07:00  INFO 18604 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-08T10:03:22.550+07:00  WARN 18604 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-08T10:03:22.736+07:00  INFO 18604 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-08T10:03:22.739+07:00  INFO 18604 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-08T10:03:22.752+07:00  INFO 18604 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-08T10:03:22.752+07:00  INFO 18604 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1553 ms
2025-09-08T10:03:22.802+07:00  WARN 18604 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T10:03:22.802+07:00  INFO 18604 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-08T10:03:22.902+07:00  INFO 18604 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@72f4232
2025-09-08T10:03:22.903+07:00  INFO 18604 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-08T10:03:22.907+07:00  WARN 18604 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T10:03:22.907+07:00  INFO 18604 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-08T10:03:22.913+07:00  INFO 18604 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@3ef97efc
2025-09-08T10:03:22.913+07:00  INFO 18604 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-08T10:03:22.913+07:00  WARN 18604 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T10:03:22.913+07:00  INFO 18604 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-08T10:03:22.927+07:00  INFO 18604 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@77ef7b2b
2025-09-08T10:03:22.927+07:00  INFO 18604 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-08T10:03:22.927+07:00  WARN 18604 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T10:03:22.927+07:00  INFO 18604 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-08T10:03:22.939+07:00  INFO 18604 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@5f92ef40
2025-09-08T10:03:22.939+07:00  INFO 18604 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-08T10:03:22.939+07:00  WARN 18604 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T10:03:22.939+07:00  INFO 18604 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-08T10:03:22.955+07:00  INFO 18604 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@7f5a5ef3
2025-09-08T10:03:22.955+07:00  INFO 18604 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-08T10:03:22.955+07:00  INFO 18604 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-08T10:03:22.997+07:00  INFO 18604 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-08T10:03:22.998+07:00  INFO 18604 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@5a7e0d68{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.15700835230848613068/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@10f03ab0{STARTED}}
2025-09-08T10:03:22.999+07:00  INFO 18604 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@5a7e0d68{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.15700835230848613068/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@10f03ab0{STARTED}}
2025-09-08T10:03:23.000+07:00  INFO 18604 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@196f9b75{STARTING}[12.0.15,sto=0] @2412ms
2025-09-08T10:03:23.098+07:00  INFO 18604 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-08T10:03:23.124+07:00  INFO 18604 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-08T10:03:23.138+07:00  INFO 18604 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-08T10:03:23.256+07:00  INFO 18604 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-08T10:03:23.295+07:00  WARN 18604 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-08T10:03:23.904+07:00  INFO 18604 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-08T10:03:23.911+07:00  INFO 18604 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@48bff6ff] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-08T10:03:24.039+07:00  INFO 18604 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T10:03:24.217+07:00  INFO 18604 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.company.tmpl", "cloud.datatp.tms", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-08T10:03:24.219+07:00  INFO 18604 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-08T10:03:24.224+07:00  INFO 18604 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-08T10:03:24.225+07:00  INFO 18604 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-08T10:03:24.250+07:00  INFO 18604 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-08T10:03:24.290+07:00  WARN 18604 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-08T10:03:26.400+07:00  INFO 18604 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-08T10:03:26.401+07:00  INFO 18604 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@5b2acbe5] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-08T10:03:26.613+07:00  WARN 18604 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-08T10:03:26.613+07:00  WARN 18604 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-08T10:03:26.621+07:00  WARN 18604 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-08T10:03:26.621+07:00  WARN 18604 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-08T10:03:26.635+07:00  WARN 18604 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-08T10:03:26.636+07:00  WARN 18604 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-08T10:03:27.079+07:00  INFO 18604 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T10:03:27.084+07:00  INFO 18604 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-08T10:03:27.085+07:00  INFO 18604 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-08T10:03:27.103+07:00  INFO 18604 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-08T10:03:27.114+07:00  WARN 18604 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-08T10:03:27.622+07:00  INFO 18604 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-08T10:03:27.623+07:00  INFO 18604 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@4b7a4197] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-08T10:03:27.713+07:00  WARN 18604 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-08T10:03:27.713+07:00  WARN 18604 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-08T10:03:28.153+07:00  INFO 18604 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T10:03:28.184+07:00  INFO 18604 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-08T10:03:28.188+07:00  INFO 18604 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-08T10:03:28.188+07:00  INFO 18604 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-08T10:03:28.195+07:00  WARN 18604 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-08T10:03:28.320+07:00  INFO 18604 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-08T10:03:28.786+07:00  INFO 18604 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-08T10:03:28.789+07:00  INFO 18604 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-08T10:03:28.826+07:00  INFO 18604 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-08T10:03:28.872+07:00  INFO 18604 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-08T10:03:28.937+07:00  INFO 18604 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-08T10:03:28.966+07:00  INFO 18604 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-08T10:03:28.993+07:00  INFO 18604 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 428982848ms : this is harmless.
2025-09-08T10:03:29.001+07:00  INFO 18604 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-08T10:03:29.004+07:00  INFO 18604 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-08T10:03:29.017+07:00  INFO 18604 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 436106410ms : this is harmless.
2025-09-08T10:03:29.019+07:00  INFO 18604 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-08T10:03:29.043+07:00  INFO 18604 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-08T10:03:29.044+07:00  INFO 18604 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-08T10:03:30.098+07:00  INFO 18604 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-08T10:03:30.098+07:00  INFO 18604 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-08T10:03:30.099+07:00  WARN 18604 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-08T10:03:30.743+07:00  INFO 18604 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 08/09/2025@10:00:00+0700 to 08/09/2025@10:15:00+0700
2025-09-08T10:03:30.744+07:00  INFO 18604 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 08/09/2025@10:00:00+0700 to 08/09/2025@10:15:00+0700
2025-09-08T10:03:31.758+07:00  INFO 18604 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-08T10:03:31.758+07:00  INFO 18604 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-08T10:03:31.758+07:00  WARN 18604 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-08T10:03:32.056+07:00  INFO 18604 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-08T10:03:32.056+07:00  INFO 18604 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-08T10:03:32.056+07:00  INFO 18604 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-08T10:03:32.056+07:00  INFO 18604 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-08T10:03:32.056+07:00  INFO 18604 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-08T10:03:33.623+07:00  WARN 18604 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 9477a4bb-a7b2-441a-a22a-3059c344e145

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-08T10:03:33.626+07:00  INFO 18604 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-08T10:03:33.910+07:00  INFO 18604 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-08T10:03:33.911+07:00  INFO 18604 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-08T10:03:33.911+07:00  INFO 18604 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-08T10:03:33.911+07:00  INFO 18604 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-08T10:03:33.911+07:00  INFO 18604 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-08T10:03:33.911+07:00  INFO 18604 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-08T10:03:33.911+07:00  INFO 18604 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-08T10:03:33.911+07:00  INFO 18604 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-08T10:03:33.911+07:00  INFO 18604 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-08T10:03:33.911+07:00  INFO 18604 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-08T10:03:33.911+07:00  INFO 18604 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-08T10:03:33.911+07:00  INFO 18604 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-08T10:03:33.914+07:00  INFO 18604 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-08T10:03:33.914+07:00  INFO 18604 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-08T10:03:33.914+07:00  INFO 18604 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-08T10:03:33.925+07:00  INFO 18604 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-08T10:03:33.925+07:00  INFO 18604 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-08T10:03:33.926+07:00  INFO 18604 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-08T10:03:33.934+07:00  INFO 18604 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@461f51dd{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-08T10:03:33.935+07:00  INFO 18604 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-08T10:03:33.936+07:00  INFO 18604 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-08T10:03:33.971+07:00  INFO 18604 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-08T10:03:33.971+07:00  INFO 18604 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-08T10:03:33.976+07:00  INFO 18604 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.174 seconds (process running for 13.388)
2025-09-08T10:04:03.170+07:00  INFO 18604 --- [qtp1*********-41] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0eck50x4ybetlzpd65hsul3720
2025-09-08T10:04:03.632+07:00  INFO 18604 --- [qtp1*********-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0eck50x4ybetlzpd65hsul3720, token = 6f174e21c81a760b77a085f45823ebf2
2025-09-08T10:04:03.991+07:00  INFO 18604 --- [qtp1*********-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-08T10:04:04.065+07:00  INFO 18604 --- [qtp1*********-35] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-09-08T10:04:04.065+07:00  INFO 18604 --- [qtp1*********-35] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/SyncBFSUsernameToLoginId.groovy"
}
2025-09-08T10:04:04.297+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 1/67: 10 records
2025-09-08T10:04:04.405+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 2/67: 10 records
2025-09-08T10:04:04.464+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 3/67: 10 records
2025-09-08T10:04:04.529+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 4/67: 10 records
2025-09-08T10:04:04.593+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 5/67: 10 records
2025-09-08T10:04:04.664+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 6/67: 10 records
2025-09-08T10:04:04.730+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 7/67: 10 records
2025-09-08T10:04:04.794+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 8/67: 10 records
2025-09-08T10:04:04.853+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 9/67: 10 records
2025-09-08T10:04:04.913+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 10/67: 10 records
2025-09-08T10:04:04.994+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 11/67: 10 records
2025-09-08T10:04:05.052+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 12/67: 10 records
2025-09-08T10:04:05.109+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 13/67: 10 records
2025-09-08T10:04:05.168+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 14/67: 10 records
2025-09-08T10:04:05.226+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 15/67: 10 records
2025-09-08T10:04:05.282+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 16/67: 10 records
2025-09-08T10:04:05.338+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 17/67: 10 records
2025-09-08T10:04:05.397+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 18/67: 10 records
2025-09-08T10:04:05.453+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 19/67: 10 records
2025-09-08T10:04:05.508+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 20/67: 10 records
2025-09-08T10:04:05.565+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 21/67: 10 records
2025-09-08T10:04:05.621+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 22/67: 10 records
2025-09-08T10:04:05.684+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 23/67: 10 records
2025-09-08T10:04:05.748+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 24/67: 10 records
2025-09-08T10:04:05.804+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 25/67: 10 records
2025-09-08T10:04:05.864+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 26/67: 10 records
2025-09-08T10:04:05.927+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 27/67: 10 records
2025-09-08T10:04:05.984+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 28/67: 10 records
2025-09-08T10:04:06.040+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 29/67: 10 records
2025-09-08T10:04:06.097+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 30/67: 10 records
2025-09-08T10:04:06.151+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 31/67: 10 records
2025-09-08T10:04:06.201+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 32/67: 10 records
2025-09-08T10:04:06.258+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 33/67: 10 records
2025-09-08T10:04:06.313+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 34/67: 10 records
2025-09-08T10:04:06.368+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 35/67: 10 records
2025-09-08T10:04:06.421+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 36/67: 10 records
2025-09-08T10:04:06.475+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 37/67: 10 records
2025-09-08T10:04:06.529+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 38/67: 10 records
2025-09-08T10:04:06.573+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 39/67: 10 records
2025-09-08T10:04:06.627+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 40/67: 10 records
2025-09-08T10:04:06.676+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 41/67: 10 records
2025-09-08T10:04:06.732+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 42/67: 10 records
2025-09-08T10:04:06.786+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 43/67: 10 records
2025-09-08T10:04:06.836+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 44/67: 10 records
2025-09-08T10:04:06.910+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 45/67: 10 records
2025-09-08T10:04:06.963+07:00  INFO 18604 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:04:07.004+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 46/67: 10 records
2025-09-08T10:04:07.056+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 47/67: 10 records
2025-09-08T10:04:07.112+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 48/67: 10 records
2025-09-08T10:04:07.167+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 49/67: 10 records
2025-09-08T10:04:07.221+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 50/67: 10 records
2025-09-08T10:04:07.281+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 51/67: 10 records
2025-09-08T10:04:07.334+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 52/67: 10 records
2025-09-08T10:04:07.385+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 53/67: 10 records
2025-09-08T10:04:07.443+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 54/67: 10 records
2025-09-08T10:04:07.503+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 55/67: 10 records
2025-09-08T10:04:07.542+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 56/67: 10 records
2025-09-08T10:04:07.590+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 57/67: 10 records
2025-09-08T10:04:07.624+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 58/67: 10 records
2025-09-08T10:04:07.676+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 59/67: 10 records
2025-09-08T10:04:07.733+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 60/67: 10 records
2025-09-08T10:04:07.781+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 61/67: 10 records
2025-09-08T10:04:07.842+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 62/67: 10 records
2025-09-08T10:04:07.905+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 63/67: 10 records
2025-09-08T10:04:07.959+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 64/67: 10 records
2025-09-08T10:04:08.015+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 65/67: 10 records
2025-09-08T10:04:08.072+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 66/67: 10 records
2025-09-08T10:04:08.136+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 67/67: 9 records
2025-09-08T10:04:37.077+07:00  INFO 18604 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-08T10:04:37.100+07:00  INFO 18604 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:05:05.154+07:00  INFO 18604 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:05:05.156+07:00  INFO 18604 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-08T10:06:06.249+07:00  INFO 18604 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:06:36.308+07:00  INFO 18604 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:06:36.334+07:00  INFO 18604 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:07:04.382+07:00  INFO 18604 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:08:06.482+07:00  INFO 18604 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:08:40.546+07:00  INFO 18604 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:08:40.563+07:00  INFO 18604 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:09:03.598+07:00  INFO 18604 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:10:06.705+07:00  INFO 18604 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:10:06.708+07:00  INFO 18604 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-08T10:10:40.764+07:00  INFO 18604 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:10:40.774+07:00  INFO 18604 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:11:02.803+07:00  INFO 18604 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:12:05.969+07:00  INFO 18604 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:12:33.268+07:00  INFO 18604 --- [qtp1*********-36] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0ulf5oce93rt91vsghyoqzaqhy1
2025-09-08T10:12:33.391+07:00  INFO 18604 --- [qtp1*********-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0ulf5oce93rt91vsghyoqzaqhy1, token = e0482fa3dcdd94be4cb1f9cf338fbfdd
2025-09-08T10:12:33.404+07:00  INFO 18604 --- [qtp1*********-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-08T10:12:33.415+07:00  INFO 18604 --- [qtp1*********-35] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-09-08T10:12:33.415+07:00  INFO 18604 --- [qtp1*********-35] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/SyncBFSUsernameToLoginId.groovy"
}
2025-09-08T10:12:33.521+07:00 ERROR 18604 --- [qtp1*********-35] n.d.m.data.db.util.DBConnectionUtil      : Query: 
 
      SELECT DISTINCT ON (a.id) a.*, e.bfsone_username 
      FROM account_account a
      JOIN company_hr_employee e ON e.account_id = a.id
      WHERE lower(a.login_id != lower(e.bfsone_username) AND a.email IS NOT NULL AND a.login_id IS NOT NULL AND e.bfsone_username IS NOT NULL;
    
2025-09-08T10:12:33.523+07:00 ERROR 18604 --- [qtp1*********-35] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint rpc/system/[POST] /system/script/run
2025-09-08T10:12:33.523+07:00 ERROR 18604 --- [qtp1*********-35] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: ERROR: syntax error at or near ";"
  Position: 284
	at net.datatp.util.error.RuntimeError.UnknownError(RuntimeError.java:96)
	at net.datatp.module.data.db.util.DBConnectionUtil.createSqlSelectView(DBConnectionUtil.java:333)
	at net.datatp.module.data.db.SqlQueryManager$QueryContext.createSqlSelectView(SqlQueryManager.java:82)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at migration.server.hr.SyncBFSOneUsername$1.run(SyncBFSUsernameToLoginId.groovy:47)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at lib.data.ServiceRunnableSet.run(ServiceRunnableSet.groovy:21)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at migration.server.hr.SyncBFSUsernameToLoginId.run(SyncBFSUsernameToLoginId.groovy:214)
	at groovy.util.GroovyScriptEngine.run(GroovyScriptEngine.java:571)
	at net.datatp.module.groovy.GroovyScriptService.run(GroovyScriptService.java:32)
	at net.datatp.module.core.security.http.SystemRPCController.lambda$run$0(SystemRPCController.java:52)
	at net.datatp.module.http.rest.v1.AbstractController$1.doCall(AbstractController.java:65)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.http.rest.v1.AbstractController.execute(AbstractController.java:70)
	at net.datatp.module.core.security.http.SystemRPCController.run(SystemRPCController.java:54)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-09-08T10:12:40.052+07:00  INFO 18604 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-08T10:12:40.063+07:00  INFO 18604 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:13:02.105+07:00  INFO 18604 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:13:23.071+07:00  INFO 18604 --- [Scheduler-1954953964-1] n.d.m.session.AppHttpSessionListener     : The session node0eck50x4ybetlzpd65hsul3720 is destroyed.
2025-09-08T10:14:05.209+07:00  INFO 18604 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T10:14:25.498+07:00  INFO 18604 --- [qtp1*********-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01q7h5vj263zs01iwb8b6s1aotz2
2025-09-08T10:14:25.623+07:00  INFO 18604 --- [qtp1*********-37] n.d.module.session.ClientSessionManager  : Add a client session id = node01q7h5vj263zs01iwb8b6s1aotz2, token = c05a383bfb77a6a60a9221597499d0c8
2025-09-08T10:14:25.633+07:00  INFO 18604 --- [qtp1*********-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-08T10:14:25.647+07:00  INFO 18604 --- [qtp1*********-35] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-09-08T10:14:25.647+07:00  INFO 18604 --- [qtp1*********-35] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/SyncBFSUsernameToLoginId.groovy"
}
2025-09-08T10:14:25.738+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 1/3: 10 records
2025-09-08T10:14:25.825+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 2/3: 10 records
2025-09-08T10:14:25.826+07:00  INFO 18604 --- [qtp1*********-35] migration.server.hr.SyncBFSOneUsername   : Processing batch 3/3: 9 records
2025-09-08T10:14:32.235+07:00  INFO 18604 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@461f51dd{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-08T10:14:32.235+07:00  INFO 18604 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-08T10:14:32.235+07:00  INFO 18604 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-08T10:14:32.235+07:00  INFO 18604 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-08T10:14:32.236+07:00  INFO 18604 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-08T10:14:32.236+07:00  INFO 18604 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-08T10:14:32.236+07:00  INFO 18604 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-08T10:14:32.236+07:00  INFO 18604 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-08T10:14:32.236+07:00  INFO 18604 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-08T10:14:32.236+07:00  INFO 18604 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-08T10:14:32.236+07:00  INFO 18604 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-08T10:14:32.236+07:00  INFO 18604 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-08T10:14:32.236+07:00  INFO 18604 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-08T10:14:32.236+07:00  INFO 18604 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-08T10:14:32.236+07:00  INFO 18604 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-08T10:14:32.236+07:00  INFO 18604 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-08T10:14:32.250+07:00  INFO 18604 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T10:14:32.300+07:00  INFO 18604 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-08T10:14:32.305+07:00  INFO 18604 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-08T10:14:32.356+07:00  INFO 18604 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T10:14:32.357+07:00  INFO 18604 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T10:14:32.358+07:00  INFO 18604 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T10:14:32.358+07:00  INFO 18604 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-08T10:14:32.359+07:00  INFO 18604 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-08T10:14:32.359+07:00  INFO 18604 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-08T10:14:32.359+07:00  INFO 18604 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-08T10:14:32.359+07:00  INFO 18604 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-08T10:14:32.360+07:00  INFO 18604 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-08T10:14:32.360+07:00  INFO 18604 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-08T10:14:32.360+07:00  INFO 18604 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-08T10:14:32.360+07:00  INFO 18604 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-08T10:14:32.360+07:00  INFO 18604 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-08T10:14:32.361+07:00  INFO 18604 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@196f9b75{STOPPING}[12.0.15,sto=0]
2025-09-08T10:14:32.363+07:00  INFO 18604 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-08T10:14:32.364+07:00  INFO 18604 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@5a7e0d68{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.15700835230848613068/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@10f03ab0{STOPPED}}
