2025-09-08T09:15:28.778+07:00  INFO 12907 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 12907 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-08T09:15:28.779+07:00  INFO 12907 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-08T09:15:29.452+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.516+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 60 ms. Found 22 JPA repository interfaces.
2025-09-08T09:15:29.528+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.529+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-08T09:15:29.529+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.537+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 10 JPA repository interfaces.
2025-09-08T09:15:29.538+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.541+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-08T09:15:29.585+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.590+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-08T09:15:29.598+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.601+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 2 JPA repository interfaces.
2025-09-08T09:15:29.601+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.605+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-08T09:15:29.608+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.612+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-08T09:15:29.616+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.618+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-08T09:15:29.618+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.619+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-08T09:15:29.619+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.626+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-09-08T09:15:29.631+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.633+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-08T09:15:29.637+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.640+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-08T09:15:29.640+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.647+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-08T09:15:29.647+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.649+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-08T09:15:29.650+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.650+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-08T09:15:29.650+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.651+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-08T09:15:29.651+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.655+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-08T09:15:29.655+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.656+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-08T09:15:29.656+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.656+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-08T09:15:29.657+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.667+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-09-08T09:15:29.676+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.682+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-08T09:15:29.682+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.685+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-08T09:15:29.685+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.689+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-08T09:15:29.689+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.694+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-09-08T09:15:29.695+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.699+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-08T09:15:29.699+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.706+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-08T09:15:29.707+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.715+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-09-08T09:15:29.716+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.729+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 24 JPA repository interfaces.
2025-09-08T09:15:29.730+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.731+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-08T09:15:29.735+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.736+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-08T09:15:29.736+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.743+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-08T09:15:29.744+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.778+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 34 ms. Found 65 JPA repository interfaces.
2025-09-08T09:15:29.779+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.780+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-08T09:15:29.783+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-08T09:15:29.786+07:00  INFO 12907 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-08T09:15:29.987+07:00  INFO 12907 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-08T09:15:29.993+07:00  INFO 12907 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-08T09:15:30.254+07:00  WARN 12907 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-08T09:15:30.441+07:00  INFO 12907 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-08T09:15:30.443+07:00  INFO 12907 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-08T09:15:30.454+07:00  INFO 12907 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-08T09:15:30.455+07:00  INFO 12907 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1553 ms
2025-09-08T09:15:30.505+07:00  WARN 12907 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T09:15:30.505+07:00  INFO 12907 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-08T09:15:30.607+07:00  INFO 12907 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@3aee72d8
2025-09-08T09:15:30.607+07:00  INFO 12907 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-08T09:15:30.612+07:00  WARN 12907 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T09:15:30.612+07:00  INFO 12907 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-08T09:15:30.617+07:00  INFO 12907 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@147a8d7c
2025-09-08T09:15:30.617+07:00  INFO 12907 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-08T09:15:30.617+07:00  WARN 12907 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T09:15:30.617+07:00  INFO 12907 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-08T09:15:30.624+07:00  INFO 12907 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@a19fe87
2025-09-08T09:15:30.625+07:00  INFO 12907 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-08T09:15:30.625+07:00  WARN 12907 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T09:15:30.625+07:00  INFO 12907 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-08T09:15:30.632+07:00  INFO 12907 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@11544ddd
2025-09-08T09:15:30.632+07:00  INFO 12907 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-08T09:15:30.633+07:00  WARN 12907 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-08T09:15:30.633+07:00  INFO 12907 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-08T09:15:30.643+07:00  INFO 12907 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@5ac3d009
2025-09-08T09:15:30.644+07:00  INFO 12907 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-08T09:15:30.644+07:00  INFO 12907 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-08T09:15:30.685+07:00  INFO 12907 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-08T09:15:30.687+07:00  INFO 12907 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@4af32714{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.10530016138155417787/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@794fbf0d{STARTED}}
2025-09-08T09:15:30.687+07:00  INFO 12907 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@4af32714{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.10530016138155417787/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@794fbf0d{STARTED}}
2025-09-08T09:15:30.689+07:00  INFO 12907 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@35be9318{STARTING}[12.0.15,sto=0] @2490ms
2025-09-08T09:15:30.786+07:00  INFO 12907 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-08T09:15:30.813+07:00  INFO 12907 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-08T09:15:30.826+07:00  INFO 12907 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-08T09:15:30.942+07:00  INFO 12907 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-08T09:15:30.983+07:00  WARN 12907 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-08T09:15:31.576+07:00  INFO 12907 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-08T09:15:31.584+07:00  INFO 12907 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@1230a38d] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-08T09:15:31.716+07:00  INFO 12907 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T09:15:31.970+07:00  INFO 12907 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.chatbot", "net.datatp.module.core.print", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-08T09:15:31.971+07:00  INFO 12907 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-08T09:15:31.978+07:00  INFO 12907 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-08T09:15:31.979+07:00  INFO 12907 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-08T09:15:32.004+07:00  INFO 12907 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-08T09:15:32.013+07:00  WARN 12907 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-08T09:15:33.968+07:00  INFO 12907 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-08T09:15:33.969+07:00  INFO 12907 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@6a8171a3] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-08T09:15:34.199+07:00  WARN 12907 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-08T09:15:34.199+07:00  WARN 12907 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-08T09:15:34.206+07:00  WARN 12907 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-08T09:15:34.207+07:00  WARN 12907 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-08T09:15:34.219+07:00  WARN 12907 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-08T09:15:34.219+07:00  WARN 12907 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-08T09:15:34.631+07:00  INFO 12907 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T09:15:34.637+07:00  INFO 12907 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-08T09:15:34.638+07:00  INFO 12907 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-08T09:15:34.657+07:00  INFO 12907 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-08T09:15:34.668+07:00  WARN 12907 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-08T09:15:35.160+07:00  INFO 12907 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-08T09:15:35.160+07:00  INFO 12907 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@380cbca7] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-08T09:15:35.254+07:00  WARN 12907 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-08T09:15:35.254+07:00  WARN 12907 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-08T09:15:35.572+07:00  INFO 12907 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T09:15:35.604+07:00  INFO 12907 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-08T09:15:35.609+07:00  INFO 12907 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-08T09:15:35.609+07:00  INFO 12907 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-08T09:15:35.616+07:00  WARN 12907 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-08T09:15:35.750+07:00  INFO 12907 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-08T09:15:36.201+07:00  INFO 12907 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-08T09:15:36.203+07:00  INFO 12907 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-08T09:15:36.238+07:00  INFO 12907 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-08T09:15:36.282+07:00  INFO 12907 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-08T09:15:36.336+07:00  INFO 12907 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-08T09:15:36.362+07:00  INFO 12907 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-08T09:15:36.388+07:00  INFO 12907 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 197111146ms : this is harmless.
2025-09-08T09:15:36.397+07:00  INFO 12907 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-08T09:15:36.400+07:00  INFO 12907 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-08T09:15:36.416+07:00  INFO 12907 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 204234709ms : this is harmless.
2025-09-08T09:15:36.417+07:00  INFO 12907 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-08T09:15:36.430+07:00  INFO 12907 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-08T09:15:36.431+07:00  INFO 12907 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-08T09:15:37.736+07:00  INFO 12907 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-08T09:15:37.736+07:00  INFO 12907 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-08T09:15:37.737+07:00  WARN 12907 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-08T09:15:38.259+07:00  INFO 12907 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 08/09/2025@09:15:00+0700 to 08/09/2025@09:30:00+0700
2025-09-08T09:15:38.260+07:00  INFO 12907 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 08/09/2025@09:15:00+0700 to 08/09/2025@09:30:00+0700
2025-09-08T09:15:39.285+07:00  INFO 12907 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-08T09:15:39.285+07:00  INFO 12907 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-08T09:15:39.285+07:00  WARN 12907 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-08T09:15:39.551+07:00  INFO 12907 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-08T09:15:39.552+07:00  INFO 12907 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-08T09:15:39.552+07:00  INFO 12907 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-08T09:15:39.552+07:00  INFO 12907 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-08T09:15:39.552+07:00  INFO 12907 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-08T09:15:41.137+07:00  WARN 12907 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 27ad5202-71d8-48b8-bc0a-ddadd26bc7cf

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-08T09:15:41.141+07:00  INFO 12907 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-08T09:15:41.428+07:00  INFO 12907 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-08T09:15:41.428+07:00  INFO 12907 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-08T09:15:41.428+07:00  INFO 12907 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-08T09:15:41.428+07:00  INFO 12907 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-08T09:15:41.428+07:00  INFO 12907 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-08T09:15:41.428+07:00  INFO 12907 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-08T09:15:41.428+07:00  INFO 12907 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-08T09:15:41.428+07:00  INFO 12907 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-08T09:15:41.428+07:00  INFO 12907 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-08T09:15:41.428+07:00  INFO 12907 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-08T09:15:41.428+07:00  INFO 12907 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-08T09:15:41.428+07:00  INFO 12907 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-08T09:15:41.429+07:00  INFO 12907 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-08T09:15:41.429+07:00  INFO 12907 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-08T09:15:41.429+07:00  INFO 12907 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-08T09:15:41.483+07:00  INFO 12907 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-08T09:15:41.483+07:00  INFO 12907 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-08T09:15:41.485+07:00  INFO 12907 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-08T09:15:41.492+07:00  INFO 12907 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@7a8804df{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-08T09:15:41.493+07:00  INFO 12907 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-08T09:15:41.494+07:00  INFO 12907 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-08T09:15:41.525+07:00  INFO 12907 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-08T09:15:41.525+07:00  INFO 12907 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-08T09:15:41.530+07:00  INFO 12907 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.086 seconds (process running for 13.33)
2025-09-08T09:15:53.212+07:00  INFO 12907 --- [qtp907681414-41] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0y27scx8ck152jejjln4c5jex0
2025-09-08T09:15:53.556+07:00  INFO 12907 --- [qtp907681414-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0y27scx8ck152jejjln4c5jex0, token = d9358acf44d573b8f3b3631ae7881ee3
2025-09-08T09:15:53.926+07:00  INFO 12907 --- [qtp907681414-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-08T09:15:54.033+07:00  INFO 12907 --- [qtp907681414-35] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-09-08T09:15:54.033+07:00  INFO 12907 --- [qtp907681414-35] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/SyncBFSUsernameToLoginId.groovy"
}
2025-09-08T09:15:54.462+07:00  INFO 12907 --- [botTaskExecutor-1] n.d.m.c.BotSendMessageBotHandler         : Process send message [Quan trọng] Thay đổi thông tin đăng nhập. delivered count = 0, delivered error count = 0, delivered abort count = 0
2025-09-08T09:15:54.462+07:00  INFO 12907 --- [botTaskExecutor-1] n.d.m.c.BotSendMessageBotHandler         : Process send message [Quan trọng] Thay đổi thông tin đăng nhập. delivered count = 0, delivered error count = 0, delivered abort count = 0
2025-09-08T09:15:55.257+07:00  WARN 12907 --- [ForkJoinPool.commonPool-worker-3] c.m.a.m.ConfidentialClientApplication    : [Correlation ID: f6d0ceec-8354-4010-a96f-43b8b7098658] Execution of class com.microsoft.aad.msal4j.AcquireTokenSilentSupplier failed: Token not found in the cache
2025-09-08T09:15:55.439+07:00  INFO 12907 --- [botTaskExecutor-1] c.azure.identity.ClientSecretCredential  : Azure Identity => getToken() result for scopes [https://graph.microsoft.com/.default]: SUCCESS
2025-09-08T09:16:02.477+07:00  INFO 12907 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:16:20.975+07:00  INFO 12907 --- [qtp907681414-36] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0l1xeusn5k6981wgvfdpo3cpz41
2025-09-08T09:16:21.002+07:00 ERROR 12907 --- [qtp907681414-36] net.datatp.module.account.AccountLogic   : User nhat.le try to login into system, but fail
2025-09-08T09:16:21.010+07:00 ERROR 12907 --- [qtp907681414-36] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.entity.AccessToken.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.authenticate(CompanyAuthenticationService.java:82)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.company.auth.CompanyAuthenticationService$$SpringCGLIB$$0.authenticate(<generated>)
	at net.datatp.module.company.http.ACLController.lambda$authenticate$0(ACLController.java:71)
	at net.datatp.module.http.rest.v1.AbstractController$1.doCall(AbstractController.java:65)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.http.rest.v1.AbstractController.execute(AbstractController.java:70)
	at net.datatp.module.company.http.ACLController.authenticate(ACLController.java:73)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.produce(AdaptiveExecutionStrategy.java:195)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-09-08T09:16:21.036+07:00  INFO 12907 --- [qtp907681414-36] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint /company/acl/[POST] authenticate
2025-09-08T09:16:44.556+07:00  INFO 12907 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T09:16:44.572+07:00  INFO 12907 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T09:17:05.604+07:00  INFO 12907 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:17:18.550+07:00  INFO 12907 --- [qtp907681414-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01q7394r6jv7iag0384uvbtmn42
2025-09-08T09:17:18.683+07:00  INFO 12907 --- [qtp907681414-37] n.d.module.session.ClientSessionManager  : Add a client session id = node01q7394r6jv7iag0384uvbtmn42, token = 0287291aad6f4375680070686a6b2d82
2025-09-08T09:17:18.696+07:00  INFO 12907 --- [qtp907681414-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-08T09:17:18.711+07:00  INFO 12907 --- [qtp907681414-35] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-09-08T09:17:18.711+07:00  INFO 12907 --- [qtp907681414-35] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/SyncBFSUsernameToLoginId.groovy"
}
2025-09-08T09:17:18.873+07:00  INFO 12907 --- [botTaskExecutor-3] n.d.m.c.BotSendMessageBotHandler         : Process send message [Quan trọng] Thay đổi thông tin đăng nhập. delivered count = 0, delivered error count = 0, delivered abort count = 0
2025-09-08T09:17:18.874+07:00  INFO 12907 --- [botTaskExecutor-3] n.d.m.c.BotSendMessageBotHandler         : Process send message [Quan trọng] Thay đổi thông tin đăng nhập. delivered count = 0, delivered error count = 0, delivered abort count = 0
2025-09-08T09:17:18.879+07:00  INFO 12907 --- [botTaskExecutor-3] c.azure.identity.ClientSecretCredential  : Azure Identity => getToken() result for scopes [https://graph.microsoft.com/.default]: SUCCESS
2025-09-08T09:18:06.957+07:00  INFO 12907 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:18:44.062+07:00  INFO 12907 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-08T09:18:44.068+07:00  INFO 12907 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T09:19:05.111+07:00  INFO 12907 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:20:06.207+07:00  INFO 12907 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:20:06.218+07:00  INFO 12907 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-08T09:20:48.288+07:00  INFO 12907 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T09:20:48.296+07:00  INFO 12907 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T09:21:04.324+07:00  INFO 12907 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:22:06.428+07:00  INFO 12907 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:22:48.517+07:00  INFO 12907 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T09:22:48.527+07:00  INFO 12907 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T09:23:03.558+07:00  INFO 12907 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:23:29.186+07:00  INFO 12907 --- [qtp907681414-34] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01rip759n50p1b78xfrgtcbon63
2025-09-08T09:23:29.325+07:00  INFO 12907 --- [qtp907681414-34] n.d.module.session.ClientSessionManager  : Add a client session id = node01rip759n50p1b78xfrgtcbon63, token = 4a6f80da0db8532da7939ee21e758b29
2025-09-08T09:23:29.333+07:00  INFO 12907 --- [qtp907681414-34] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-08T09:23:29.345+07:00  INFO 12907 --- [qtp907681414-35] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-09-08T09:23:29.345+07:00  INFO 12907 --- [qtp907681414-35] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/SyncBFSUsernameToLoginId.groovy"
}
2025-09-08T09:23:30.616+07:00  INFO 12907 --- [botTaskExecutor-5] n.d.m.c.BotSendMessageBotHandler         : Process send message [Quan trọng] Thay đổi thông tin đăng nhập. delivered count = 0, delivered error count = 0, delivered abort count = 0
2025-09-08T09:23:30.617+07:00  INFO 12907 --- [botTaskExecutor-5] n.d.m.c.BotSendMessageBotHandler         : Process send message [Quan trọng] Thay đổi thông tin đăng nhập. delivered count = 0, delivered error count = 0, delivered abort count = 0
2025-09-08T09:23:30.621+07:00  INFO 12907 --- [botTaskExecutor-5] c.azure.identity.ClientSecretCredential  : Azure Identity => getToken() result for scopes [https://graph.microsoft.com/.default]: SUCCESS
2025-09-08T09:24:06.669+07:00  INFO 12907 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:24:47.787+07:00  INFO 12907 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-08T09:24:47.805+07:00  INFO 12907 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T09:25:02.836+07:00  INFO 12907 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:25:02.837+07:00  INFO 12907 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-08T09:25:30.716+07:00  INFO 12907 --- [Scheduler-773602191-1] n.d.m.session.AppHttpSessionListener     : The session node0y27scx8ck152jejjln4c5jex0 is destroyed.
2025-09-08T09:25:30.721+07:00  INFO 12907 --- [Scheduler-773602191-1] n.d.m.session.AppHttpSessionListener     : The session node01q7394r6jv7iag0384uvbtmn42 is destroyed.
2025-09-08T09:25:30.722+07:00  INFO 12907 --- [Scheduler-773602191-1] n.d.m.session.AppHttpSessionListener     : The session node0l1xeusn5k6981wgvfdpo3cpz41 is destroyed.
2025-09-08T09:25:43.331+07:00  INFO 12907 --- [qtp907681414-40] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0rte3em8k8m6xtljckdn2rw074
2025-09-08T09:25:43.454+07:00  INFO 12907 --- [qtp907681414-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0rte3em8k8m6xtljckdn2rw074, token = e0bce9de7272b0a63ff624a64ffce89b
2025-09-08T09:25:43.464+07:00  INFO 12907 --- [qtp907681414-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-08T09:25:43.487+07:00  INFO 12907 --- [qtp907681414-41] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-09-08T09:25:43.487+07:00  INFO 12907 --- [qtp907681414-41] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/SyncBFSUsernameToLoginId.groovy"
}
2025-09-08T09:25:43.909+07:00  INFO 12907 --- [botTaskExecutor-2] n.d.m.c.BotSendMessageBotHandler         : Process send message [Quan trọng] Thay đổi thông tin đăng nhập. delivered count = 0, delivered error count = 0, delivered abort count = 0
2025-09-08T09:25:43.910+07:00  INFO 12907 --- [botTaskExecutor-2] n.d.m.c.BotSendMessageBotHandler         : Process send message [Quan trọng] Thay đổi thông tin đăng nhập. delivered count = 0, delivered error count = 0, delivered abort count = 0
2025-09-08T09:25:43.913+07:00  INFO 12907 --- [botTaskExecutor-2] c.azure.identity.ClientSecretCredential  : Azure Identity => getToken() result for scopes [https://graph.microsoft.com/.default]: SUCCESS
2025-09-08T09:26:05.949+07:00  INFO 12907 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:26:12.161+07:00  INFO 12907 --- [qtp907681414-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0qhjnkgciplf8642w7ijsl7lc5
2025-09-08T09:26:12.281+07:00  INFO 12907 --- [qtp907681414-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0qhjnkgciplf8642w7ijsl7lc5, token = 80168d6c1d736432dd6f6285dd9da984
2025-09-08T09:26:12.288+07:00  INFO 12907 --- [qtp907681414-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-08T09:26:12.312+07:00  INFO 12907 --- [qtp907681414-40] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-09-08T09:26:12.312+07:00  INFO 12907 --- [qtp907681414-40] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/SyncBFSUsernameToLoginId.groovy"
}
2025-09-08T09:26:12.430+07:00  INFO 12907 --- [botTaskExecutor-4] n.d.m.c.BotSendMessageBotHandler         : Process send message [Quan trọng] Thay đổi thông tin đăng nhập. delivered count = 0, delivered error count = 0, delivered abort count = 0
2025-09-08T09:26:12.430+07:00  INFO 12907 --- [botTaskExecutor-4] n.d.m.c.BotSendMessageBotHandler         : Process send message [Quan trọng] Thay đổi thông tin đăng nhập. delivered count = 0, delivered error count = 0, delivered abort count = 0
2025-09-08T09:26:12.436+07:00  INFO 12907 --- [botTaskExecutor-4] c.azure.identity.ClientSecretCredential  : Azure Identity => getToken() result for scopes [https://graph.microsoft.com/.default]: SUCCESS
2025-09-08T09:26:48.523+07:00  INFO 12907 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-08T09:26:48.529+07:00  INFO 12907 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T09:26:49.727+07:00  INFO 12907 --- [qtp907681414-36] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01n6b032za0nnc1qfkccqqy0t6g6
2025-09-08T09:26:49.844+07:00  INFO 12907 --- [qtp907681414-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01n6b032za0nnc1qfkccqqy0t6g6, token = 4bd9763f7e0b7aae1f8eb81bb9d7d3f3
2025-09-08T09:26:49.850+07:00  INFO 12907 --- [qtp907681414-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-08T09:26:49.874+07:00  INFO 12907 --- [qtp907681414-39] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-09-08T09:26:49.874+07:00  INFO 12907 --- [qtp907681414-39] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/SyncBFSUsernameToLoginId.groovy"
}
2025-09-08T09:26:54.545+07:00  INFO 12907 --- [botTaskExecutor-5] n.d.m.c.BotSendMessageBotHandler         : Process send message [Quan trọng] Thay đổi thông tin đăng nhập. delivered count = 0, delivered error count = 0, delivered abort count = 0
2025-09-08T09:26:54.546+07:00  INFO 12907 --- [botTaskExecutor-5] n.d.m.c.BotSendMessageBotHandler         : Process send message [Quan trọng] Thay đổi thông tin đăng nhập. delivered count = 0, delivered error count = 0, delivered abort count = 0
2025-09-08T09:26:54.551+07:00  INFO 12907 --- [botTaskExecutor-5] c.azure.identity.ClientSecretCredential  : Azure Identity => getToken() result for scopes [https://graph.microsoft.com/.default]: SUCCESS
2025-09-08T09:27:02.557+07:00  INFO 12907 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:28:05.648+07:00  INFO 12907 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:28:47.737+07:00  INFO 12907 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T09:28:47.744+07:00  INFO 12907 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T09:28:56.551+07:00  INFO 12907 --- [qtp907681414-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01qod3kw1y50v6y9ffcdl8ly677
2025-09-08T09:28:56.645+07:00  INFO 12907 --- [qtp907681414-37] n.d.module.session.ClientSessionManager  : Add a client session id = node01qod3kw1y50v6y9ffcdl8ly677, token = de930dfa1dacaa34b3bc11c0d2cd1c73
2025-09-08T09:28:56.651+07:00  INFO 12907 --- [qtp907681414-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-08T09:28:56.679+07:00  INFO 12907 --- [qtp907681414-36] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-09-08T09:28:56.679+07:00  INFO 12907 --- [qtp907681414-36] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/SyncBFSUsernameToLoginId.groovy"
}
2025-09-08T09:28:56.836+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 1/67: 10 records
2025-09-08T09:28:56.894+07:00  WARN 12907 --- [qtp907681414-36] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-08T09:28:56.894+07:00 ERROR 12907 --- [qtp907681414-36] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(nancy.vnhph) already exists.
2025-09-08T09:28:56.967+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 2/67: 10 records
2025-09-08T09:28:57.041+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 3/67: 10 records
2025-09-08T09:28:57.107+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 4/67: 10 records
2025-09-08T09:28:57.167+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 5/67: 10 records
2025-09-08T09:28:57.224+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 6/67: 10 records
2025-09-08T09:28:57.264+07:00  WARN 12907 --- [qtp907681414-36] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-08T09:28:57.264+07:00 ERROR 12907 --- [qtp907681414-36] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hpnttrang) already exists.
2025-09-08T09:28:57.281+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 7/67: 10 records
2025-09-08T09:28:57.337+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 8/67: 10 records
2025-09-08T09:28:57.394+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 9/67: 10 records
2025-09-08T09:28:57.451+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 10/67: 10 records
2025-09-08T09:28:57.506+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 11/67: 10 records
2025-09-08T09:28:57.562+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 12/67: 10 records
2025-09-08T09:28:57.617+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 13/67: 10 records
2025-09-08T09:28:57.662+07:00  WARN 12907 --- [qtp907681414-36] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-08T09:28:57.662+07:00 ERROR 12907 --- [qtp907681414-36] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(katherine.vnhph) already exists.
2025-09-08T09:28:57.669+07:00  WARN 12907 --- [qtp907681414-36] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-08T09:28:57.669+07:00 ERROR 12907 --- [qtp907681414-36] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(derek.vnhph) already exists.
2025-09-08T09:28:57.676+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 14/67: 10 records
2025-09-08T09:28:57.734+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 15/67: 10 records
2025-09-08T09:28:57.789+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 16/67: 10 records
2025-09-08T09:28:57.845+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 17/67: 10 records
2025-09-08T09:28:57.899+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 18/67: 10 records
2025-09-08T09:28:57.953+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 19/67: 10 records
2025-09-08T09:28:58.007+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 20/67: 10 records
2025-09-08T09:28:58.063+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 21/67: 10 records
2025-09-08T09:28:58.117+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 22/67: 10 records
2025-09-08T09:28:58.172+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 23/67: 10 records
2025-09-08T09:28:58.183+07:00  WARN 12907 --- [qtp907681414-36] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-08T09:28:58.183+07:00 ERROR 12907 --- [qtp907681414-36] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(royal.vnhan) already exists.
2025-09-08T09:28:58.228+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 24/67: 10 records
2025-09-08T09:28:58.286+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 25/67: 10 records
2025-09-08T09:28:58.343+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 26/67: 10 records
2025-09-08T09:28:58.399+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 27/67: 10 records
2025-09-08T09:28:58.451+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 28/67: 10 records
2025-09-08T09:28:58.505+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 29/67: 10 records
2025-09-08T09:28:58.558+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 30/67: 10 records
2025-09-08T09:28:58.612+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 31/67: 10 records
2025-09-08T09:28:58.659+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 32/67: 10 records
2025-09-08T09:28:58.692+07:00  WARN 12907 --- [qtp907681414-36] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-08T09:28:58.692+07:00 ERROR 12907 --- [qtp907681414-36] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hpntthuyen) already exists.
2025-09-08T09:28:58.713+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 33/67: 10 records
2025-09-08T09:28:58.769+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 34/67: 10 records
2025-09-08T09:28:58.823+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 35/67: 10 records
2025-09-08T09:28:58.877+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 36/67: 10 records
2025-09-08T09:28:58.930+07:00  WARN 12907 --- [qtp907681414-36] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-08T09:28:58.930+07:00 ERROR 12907 --- [qtp907681414-36] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(lloyd.vnhph) already exists.
2025-09-08T09:28:58.931+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 37/67: 10 records
2025-09-08T09:28:58.936+07:00  WARN 12907 --- [qtp907681414-36] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-08T09:28:58.936+07:00 ERROR 12907 --- [qtp907681414-36] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hphungpt) already exists.
2025-09-08T09:28:58.983+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 38/67: 10 records
2025-09-08T09:28:59.025+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 39/67: 10 records
2025-09-08T09:28:59.078+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 40/67: 10 records
2025-09-08T09:28:59.127+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 41/67: 10 records
2025-09-08T09:28:59.181+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 42/67: 10 records
2025-09-08T09:28:59.240+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 43/67: 10 records
2025-09-08T09:28:59.301+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 44/67: 10 records
2025-09-08T09:28:59.357+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 45/67: 10 records
2025-09-08T09:28:59.420+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 46/67: 10 records
2025-09-08T09:28:59.785+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 47/67: 10 records
2025-09-08T09:28:59.857+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 48/67: 10 records
2025-09-08T09:28:59.925+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 49/67: 10 records
2025-09-08T09:28:59.984+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 50/67: 10 records
2025-09-08T09:29:00.039+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 51/67: 10 records
2025-09-08T09:29:00.088+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 52/67: 10 records
2025-09-08T09:29:00.138+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 53/67: 10 records
2025-09-08T09:29:00.191+07:00  WARN 12907 --- [qtp907681414-36] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-08T09:29:00.191+07:00 ERROR 12907 --- [qtp907681414-36] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(hcmloanntt) already exists.
2025-09-08T09:29:00.198+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 54/67: 10 records
2025-09-08T09:29:00.257+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 55/67: 10 records
2025-09-08T09:29:00.290+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 56/67: 10 records
2025-09-08T09:29:00.301+07:00  WARN 12907 --- [qtp907681414-36] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-08T09:29:00.301+07:00 ERROR 12907 --- [qtp907681414-36] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(tt3.log.vnsgn) already exists.
2025-09-08T09:29:00.306+07:00  WARN 12907 --- [qtp907681414-36] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-08T09:29:00.306+07:00 ERROR 12907 --- [qtp907681414-36] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "account_account_login_id"
  Detail: Key (login_id)=(tt3.log.vnhan) already exists.
2025-09-08T09:29:00.334+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 57/67: 10 records
2025-09-08T09:29:00.361+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 58/67: 10 records
2025-09-08T09:29:00.402+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 59/67: 10 records
2025-09-08T09:29:00.449+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 60/67: 10 records
2025-09-08T09:29:00.495+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 61/67: 10 records
2025-09-08T09:29:00.547+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 62/67: 10 records
2025-09-08T09:29:00.602+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 63/67: 10 records
2025-09-08T09:29:00.659+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 64/67: 10 records
2025-09-08T09:29:00.720+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 65/67: 10 records
2025-09-08T09:29:00.772+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 66/67: 10 records
2025-09-08T09:29:00.828+07:00  INFO 12907 --- [qtp907681414-36] migration.server.hr.SyncBFSOneUsername   : Processing batch 67/67: 8 records
2025-09-08T09:29:06.765+07:00  INFO 12907 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:30:04.889+07:00  INFO 12907 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:30:04.892+07:00  INFO 12907 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-08T09:30:04.904+07:00  INFO 12907 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-08T09:30:04.913+07:00  INFO 12907 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 08/09/2025@09:30:04+0700
2025-09-08T09:30:04.938+07:00  INFO 12907 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 08/09/2025@09:30:00+0700 to 08/09/2025@09:45:00+0700
2025-09-08T09:30:04.938+07:00  INFO 12907 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 08/09/2025@09:30:00+0700 to 08/09/2025@09:45:00+0700
2025-09-08T09:30:47.024+07:00  INFO 12907 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-08T09:30:47.047+07:00  INFO 12907 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T09:31:04.364+07:00  INFO 12907 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@7a8804df{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-08T09:31:04.364+07:00  INFO 12907 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-08T09:31:04.365+07:00  INFO 12907 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-08T09:31:04.365+07:00  INFO 12907 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-08T09:31:04.365+07:00  INFO 12907 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-08T09:31:04.365+07:00  INFO 12907 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-08T09:31:04.365+07:00  INFO 12907 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-08T09:31:04.365+07:00  INFO 12907 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-08T09:31:04.365+07:00  INFO 12907 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-08T09:31:04.365+07:00  INFO 12907 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-08T09:31:04.365+07:00  INFO 12907 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-08T09:31:04.365+07:00  INFO 12907 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-08T09:31:04.365+07:00  INFO 12907 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-08T09:31:04.365+07:00  INFO 12907 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-08T09:31:04.365+07:00  INFO 12907 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-08T09:31:04.365+07:00  INFO 12907 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-08T09:31:04.365+07:00  INFO 12907 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-08T09:31:04.378+07:00  INFO 12907 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-08T09:31:04.430+07:00  INFO 12907 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-08T09:31:04.435+07:00  INFO 12907 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-08T09:31:04.454+07:00  INFO 12907 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T09:31:04.455+07:00  INFO 12907 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T09:31:04.457+07:00  INFO 12907 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-08T09:31:04.457+07:00  INFO 12907 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-08T09:31:04.458+07:00  INFO 12907 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-08T09:31:04.458+07:00  INFO 12907 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-08T09:31:04.458+07:00  INFO 12907 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-08T09:31:04.458+07:00  INFO 12907 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-08T09:31:04.458+07:00  INFO 12907 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-08T09:31:04.458+07:00  INFO 12907 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-08T09:31:04.458+07:00  INFO 12907 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-08T09:31:04.458+07:00  INFO 12907 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-08T09:31:04.459+07:00  INFO 12907 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-08T09:31:04.462+07:00  INFO 12907 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@35be9318{STOPPING}[12.0.15,sto=0]
2025-09-08T09:31:04.464+07:00  INFO 12907 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-08T09:31:04.465+07:00  INFO 12907 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@4af32714{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.10530016138155417787/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@794fbf0d{STOPPED}}
