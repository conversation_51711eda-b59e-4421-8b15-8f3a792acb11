2025-09-09T15:28:34.323+07:00  INFO 68428 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 68428 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-09T15:28:34.323+07:00  INFO 68428 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-09T15:28:35.021+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.084+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 60 ms. Found 22 JPA repository interfaces.
2025-09-09T15:28:35.093+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.095+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-09T15:28:35.095+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.102+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-09-09T15:28:35.102+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.105+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-09T15:28:35.151+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.156+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 1 JPA repository interface.
2025-09-09T15:28:35.165+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.166+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-09T15:28:35.167+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.170+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-09T15:28:35.173+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.176+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-09T15:28:35.180+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.182+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-09T15:28:35.183+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.183+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-09T15:28:35.183+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.189+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 10 JPA repository interfaces.
2025-09-09T15:28:35.194+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.196+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-09T15:28:35.199+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.202+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-09T15:28:35.203+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.209+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-09T15:28:35.209+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.212+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-09T15:28:35.212+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.213+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-09T15:28:35.213+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.214+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-09T15:28:35.214+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.218+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-09T15:28:35.218+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.219+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-09T15:28:35.219+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.219+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-09T15:28:35.219+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.229+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-09-09T15:28:35.238+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.244+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-09T15:28:35.244+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.247+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-09T15:28:35.247+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.251+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-09T15:28:35.251+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.256+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 9 JPA repository interfaces.
2025-09-09T15:28:35.256+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.259+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-09T15:28:35.260+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.267+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-09T15:28:35.267+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.276+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-09-09T15:28:35.276+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.291+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 24 JPA repository interfaces.
2025-09-09T15:28:35.291+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.292+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-09T15:28:35.298+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.298+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-09T15:28:35.298+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.305+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-09T15:28:35.307+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.342+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 34 ms. Found 65 JPA repository interfaces.
2025-09-09T15:28:35.343+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.344+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-09T15:28:35.349+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T15:28:35.352+07:00  INFO 68428 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-09T15:28:35.557+07:00  INFO 68428 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-09T15:28:35.561+07:00  INFO 68428 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-09T15:28:35.838+07:00  WARN 68428 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-09T15:28:36.029+07:00  INFO 68428 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-09T15:28:36.031+07:00  INFO 68428 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-09T15:28:36.042+07:00  INFO 68428 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-09T15:28:36.042+07:00  INFO 68428 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1595 ms
2025-09-09T15:28:36.107+07:00  WARN 68428 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-09T15:28:36.107+07:00  INFO 68428 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-09T15:28:36.204+07:00  INFO 68428 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@3c5095df
2025-09-09T15:28:36.204+07:00  INFO 68428 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-09T15:28:36.213+07:00  WARN 68428 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-09T15:28:36.213+07:00  INFO 68428 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-09T15:28:36.218+07:00  INFO 68428 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@2746b1cc
2025-09-09T15:28:36.219+07:00  INFO 68428 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-09T15:28:36.219+07:00  WARN 68428 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-09T15:28:36.219+07:00  INFO 68428 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-09T15:28:36.226+07:00  INFO 68428 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@7d8802cf
2025-09-09T15:28:36.227+07:00  INFO 68428 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-09T15:28:36.227+07:00  WARN 68428 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-09T15:28:36.227+07:00  INFO 68428 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-09T15:28:36.302+07:00  INFO 68428 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@6d17cd8b
2025-09-09T15:28:36.302+07:00  INFO 68428 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-09T15:28:36.302+07:00  WARN 68428 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-09T15:28:36.302+07:00  INFO 68428 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-09T15:28:36.341+07:00  INFO 68428 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@373e3dac
2025-09-09T15:28:36.341+07:00  INFO 68428 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-09T15:28:36.341+07:00  INFO 68428 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-09T15:28:36.384+07:00  INFO 68428 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-09T15:28:36.386+07:00  INFO 68428 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@7e4f5062{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.12193808350900233347/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@473ee461{STARTED}}
2025-09-09T15:28:36.387+07:00  INFO 68428 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@7e4f5062{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.12193808350900233347/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@473ee461{STARTED}}
2025-09-09T15:28:36.388+07:00  INFO 68428 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@7afffc81{STARTING}[12.0.15,sto=0] @2509ms
2025-09-09T15:28:36.485+07:00  INFO 68428 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-09T15:28:36.511+07:00  INFO 68428 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-09T15:28:36.525+07:00  INFO 68428 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-09T15:28:36.644+07:00  INFO 68428 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-09T15:28:36.672+07:00  WARN 68428 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-09T15:28:37.293+07:00  INFO 68428 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-09T15:28:37.301+07:00  INFO 68428 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@4a9cd434] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-09T15:28:37.433+07:00  INFO 68428 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-09T15:28:37.628+07:00  INFO 68428 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-09T15:28:37.630+07:00  INFO 68428 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-09T15:28:37.636+07:00  INFO 68428 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-09T15:28:37.638+07:00  INFO 68428 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-09T15:28:37.662+07:00  INFO 68428 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-09T15:28:37.668+07:00  WARN 68428 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-09T15:28:39.769+07:00  INFO 68428 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-09T15:28:39.770+07:00  INFO 68428 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2c9d065d] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-09T15:28:39.943+07:00  WARN 68428 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-09T15:28:39.943+07:00  WARN 68428 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-09T15:28:39.952+07:00  WARN 68428 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-09T15:28:39.953+07:00  WARN 68428 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-09T15:28:39.966+07:00  WARN 68428 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-09T15:28:39.967+07:00  WARN 68428 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-09T15:28:40.398+07:00  INFO 68428 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-09T15:28:40.405+07:00  INFO 68428 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-09T15:28:40.406+07:00  INFO 68428 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-09T15:28:40.424+07:00  INFO 68428 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-09T15:28:40.426+07:00  WARN 68428 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-09T15:28:40.963+07:00  INFO 68428 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-09T15:28:40.963+07:00  INFO 68428 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@79a68d9c] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-09T15:28:41.034+07:00  WARN 68428 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-09T15:28:41.034+07:00  WARN 68428 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-09T15:28:41.336+07:00  INFO 68428 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-09T15:28:41.364+07:00  INFO 68428 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-09T15:28:41.368+07:00  INFO 68428 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-09T15:28:41.368+07:00  INFO 68428 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T15:28:41.375+07:00  WARN 68428 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-09T15:28:41.516+07:00  INFO 68428 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-09T15:28:42.058+07:00  INFO 68428 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-09T15:28:42.061+07:00  INFO 68428 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-09T15:28:42.098+07:00  INFO 68428 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-09T15:28:42.145+07:00  INFO 68428 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-09T15:28:42.205+07:00  INFO 68428 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-09T15:28:42.235+07:00  INFO 68428 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-09T15:28:42.263+07:00  INFO 68428 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 3922072ms : this is harmless.
2025-09-09T15:28:42.271+07:00  INFO 68428 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-09T15:28:42.274+07:00  INFO 68428 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-09T15:28:42.286+07:00  INFO 68428 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 3922062ms : this is harmless.
2025-09-09T15:28:42.287+07:00  INFO 68428 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-09T15:28:42.298+07:00  INFO 68428 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-09T15:28:42.299+07:00  INFO 68428 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-09T15:28:44.112+07:00  INFO 68428 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-09T15:28:44.112+07:00  INFO 68428 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T15:28:44.113+07:00  WARN 68428 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-09T15:28:44.388+07:00  INFO 68428 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 09/09/2025@15:15:00+0700 to 09/09/2025@15:30:00+0700
2025-09-09T15:28:44.388+07:00  INFO 68428 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 09/09/2025@15:15:00+0700 to 09/09/2025@15:30:00+0700
2025-09-09T15:28:44.852+07:00  INFO 68428 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-09T15:28:44.852+07:00  INFO 68428 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T15:28:44.853+07:00  WARN 68428 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-09T15:28:45.120+07:00  INFO 68428 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-09T15:28:45.121+07:00  INFO 68428 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-09T15:28:45.121+07:00  INFO 68428 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-09T15:28:45.121+07:00  INFO 68428 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-09T15:28:45.121+07:00  INFO 68428 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-09T15:28:47.099+07:00  WARN 68428 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 42902b12-e99d-492d-bb6e-fdaafa67a595

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-09T15:28:47.103+07:00  INFO 68428 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-09T15:28:47.447+07:00  INFO 68428 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-09T15:28:47.447+07:00  INFO 68428 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-09T15:28:47.447+07:00  INFO 68428 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-09T15:28:47.447+07:00  INFO 68428 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-09T15:28:47.447+07:00  INFO 68428 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-09T15:28:47.447+07:00  INFO 68428 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-09T15:28:47.447+07:00  INFO 68428 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-09T15:28:47.447+07:00  INFO 68428 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-09T15:28:47.447+07:00  INFO 68428 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-09T15:28:47.447+07:00  INFO 68428 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-09T15:28:47.447+07:00  INFO 68428 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-09T15:28:47.447+07:00  INFO 68428 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-09T15:28:47.450+07:00  INFO 68428 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-09T15:28:47.450+07:00  INFO 68428 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-09T15:28:47.450+07:00  INFO 68428 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-09T15:28:47.506+07:00  INFO 68428 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-09T15:28:47.506+07:00  INFO 68428 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-09T15:28:47.507+07:00  INFO 68428 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-09T15:28:47.515+07:00  INFO 68428 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@44c5fa4{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-09T15:28:47.516+07:00  INFO 68428 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-09T15:28:47.517+07:00  INFO 68428 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-09T15:28:47.552+07:00  INFO 68428 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-09T15:28:47.552+07:00  INFO 68428 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-09T15:28:47.558+07:00  INFO 68428 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.507 seconds (process running for 13.679)
2025-09-09T15:29:06.488+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:29:18.630+07:00  INFO 68428 --- [qtp20********-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-09T15:29:50.605+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-09T15:29:50.624+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T15:30:04.646+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:30:04.648+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-09T15:30:04.650+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T15:30:04.651+07:00  INFO 68428 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 09/09/2025@15:30:04+0700
2025-09-09T15:30:04.671+07:00  INFO 68428 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 09/09/2025@15:30:00+0700 to 09/09/2025@15:45:00+0700
2025-09-09T15:30:04.671+07:00  INFO 68428 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 09/09/2025@15:30:00+0700 to 09/09/2025@15:45:00+0700
2025-09-09T15:30:09.927+07:00  INFO 68428 --- [qtp20********-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0dtzwldgfqqdxnwslil4oy94d1
2025-09-09T15:30:09.927+07:00  INFO 68428 --- [qtp20********-40] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01jgn6ovrpmo0p1d29j5bcjpv060
2025-09-09T15:30:10.084+07:00  INFO 68428 --- [qtp20********-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = d03c8c88f18f1988e37ace07cebb2c46
2025-09-09T15:30:10.088+07:00  INFO 68428 --- [qtp20********-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01jgn6ovrpmo0p1d29j5bcjpv060, token = d03c8c88f18f1988e37ace07cebb2c46
2025-09-09T15:30:10.526+07:00  INFO 68428 --- [qtp20********-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T15:30:10.538+07:00  INFO 68428 --- [qtp20********-35] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T15:30:20.355+07:00  INFO 68428 --- [qtp20********-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = d03c8c88f18f1988e37ace07cebb2c46
2025-09-09T15:30:20.360+07:00  INFO 68428 --- [qtp20********-37] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T15:30:20.375+07:00  INFO 68428 --- [qtp20********-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = d03c8c88f18f1988e37ace07cebb2c46
2025-09-09T15:30:20.378+07:00  INFO 68428 --- [qtp20********-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T15:30:22.781+07:00  INFO 68428 --- [qtp20********-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T15:30:22.781+07:00  INFO 68428 --- [qtp20********-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T15:30:22.833+07:00  INFO 68428 --- [qtp20********-37] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-09T15:30:22.843+07:00  INFO 68428 --- [qtp20********-60] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-09T15:31:06.773+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:31:31.036+07:00  INFO 68428 --- [qtp20********-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T15:31:31.036+07:00  INFO 68428 --- [qtp20********-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T15:31:31.674+07:00  INFO 68428 --- [qtp20********-39] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T15:31:36.296+07:00  INFO 68428 --- [qtp20********-37] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T15:31:49.887+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 16, expire count 0
2025-09-09T15:31:49.906+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-09T15:32:03.961+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:33:06.078+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:33:54.182+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-09T15:33:54.201+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T15:34:03.216+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:34:32.805+07:00  INFO 68428 --- [qtp20********-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = d03c8c88f18f1988e37ace07cebb2c46
2025-09-09T15:34:32.807+07:00  INFO 68428 --- [qtp20********-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = d03c8c88f18f1988e37ace07cebb2c46
2025-09-09T15:34:32.819+07:00  INFO 68428 --- [qtp20********-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T15:34:32.819+07:00  INFO 68428 --- [qtp20********-62] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T15:34:33.342+07:00  INFO 68428 --- [qtp20********-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = d03c8c88f18f1988e37ace07cebb2c46
2025-09-09T15:34:33.348+07:00  INFO 68428 --- [qtp20********-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T15:34:33.362+07:00  INFO 68428 --- [qtp20********-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = d03c8c88f18f1988e37ace07cebb2c46
2025-09-09T15:34:33.366+07:00  INFO 68428 --- [qtp20********-41] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T15:35:06.324+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:35:06.328+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T15:35:54.441+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-09T15:35:54.476+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T15:36:02.492+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:37:05.596+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:37:10.193+07:00  INFO 68428 --- [qtp20********-41] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T15:37:10.193+07:00  INFO 68428 --- [qtp20********-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T15:37:10.413+07:00  INFO 68428 --- [qtp20********-41] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T15:37:10.448+07:00  INFO 68428 --- [qtp20********-40] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T15:37:53.705+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-09T15:37:53.716+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T15:37:56.016+07:00  INFO 68428 --- [qtp20********-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T15:37:56.016+07:00  INFO 68428 --- [qtp20********-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T15:37:56.197+07:00  INFO 68428 --- [qtp20********-39] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T15:38:01.254+07:00  INFO 68428 --- [qtp20********-62] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T15:38:06.731+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:38:36.438+07:00  INFO 68428 --- [Scheduler-1158910889-1] n.d.m.session.AppHttpSessionListener     : The session node01jgn6ovrpmo0p1d29j5bcjpv060 is destroyed.
2025-09-09T15:38:39.250+07:00  INFO 68428 --- [qtp20********-66] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint QuotationService/getSpecificQuotationById
2025-09-09T15:38:40.127+07:00  INFO 68428 --- [qtp20********-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = d03c8c88f18f1988e37ace07cebb2c46
2025-09-09T15:38:40.129+07:00  INFO 68428 --- [qtp20********-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = d03c8c88f18f1988e37ace07cebb2c46
2025-09-09T15:38:40.141+07:00  INFO 68428 --- [qtp20********-62] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T15:38:40.141+07:00  INFO 68428 --- [qtp20********-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T15:39:04.821+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:39:27.522+07:00  INFO 68428 --- [qtp20********-37] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-09T15:39:27.792+07:00  INFO 68428 --- [qtp20********-37] c.d.f.core.integration.BFSOneApi         : ---------------- Create IBooking -----------------------
2025-09-09T15:39:27.860+07:00  INFO 68428 --- [qtp20********-37] c.d.f.core.integration.BFSOneApi         : -----------Error Message From BFSOne Api:: The customer don't exists in system !


2025-09-09T15:39:27.860+07:00 ERROR 68428 --- [qtp20********-37] c.d.f.core.integration.BFSOneApi         : The customer dont exists in system !
2025-09-09T15:39:27.860+07:00  INFO 68428 --- [qtp20********-37] c.d.f.sales.integration.BFSOneCRMLogic   : The customer dont exists in system !
2025-09-09T15:39:27.860+07:00 ERROR 68428 --- [qtp20********-37] c.d.f.sales.integration.BFSOneCRMLogic   : Error when create internal booking: 

2025-09-09T15:39:27.873+07:00 ERROR 68428 --- [qtp20********-37] n.d.m.monitor.call.EndpointCallContext   : Start call with component BookingService, method sendBFSOneIBookingNew, arguments
[ {
  "tenantId" : "default",
  "companyId" : 8,
  "companyParentId" : 4,
  "companyCode" : "beehph",
  "companyLabel" : "Bee HPH",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE",
  "loginId" : "dan",
  "accountId" : 3,
  "token" : "d03c8c88f18f1988e37ace07cebb2c46",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0dtzwldgfqqdxnwslil4oy94d1",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee-tw" : {
      "companyId" : 56,
      "companyParentId" : 4,
      "companyCode" : "bee-tw",
      "companyLabel" : "BEE TW",
      "companyFullName" : null
    },
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    },
    "thudo" : {
      "companyId" : 53,
      "companyParentId" : 0,
      "companyCode" : "thudo",
      "companyLabel" : "THUDO",
      "companyFullName" : null
    },
    "datatp" : {
      "companyId" : 10,
      "companyParentId" : 1,
      "companyCode" : "datatp",
      "companyLabel" : "DataTP Cloud",
      "companyFullName" : null
    },
    "beehan" : {
      "companyId" : 16,
      "companyParentId" : 4,
      "companyCode" : "beehan",
      "companyLabel" : "Bee HAN",
      "companyFullName" : null
    },
    "beescs" : {
      "companyId" : 12,
      "companyParentId" : 1,
      "companyCode" : "beescs",
      "companyLabel" : "BEESCS",
      "companyFullName" : null
    },
    "beedad" : {
      "companyId" : 18,
      "companyParentId" : 4,
      "companyCode" : "beedad",
      "companyLabel" : "Bee DAD",
      "companyFullName" : null
    },
    "hps" : {
      "companyId" : 51,
      "companyParentId" : 0,
      "companyCode" : "hps",
      "companyLabel" : "HPS",
      "companyFullName" : null
    },
    "marine" : {
      "companyId" : 52,
      "companyParentId" : 0,
      "companyCode" : "marine",
      "companyLabel" : "MARINE",
      "companyFullName" : null
    },
    "bonds" : {
      "companyId" : 54,
      "companyParentId" : 4,
      "companyCode" : "bonds",
      "companyLabel" : "BONDS",
      "companyFullName" : null
    },
    "tiendat" : {
      "companyId" : 55,
      "companyParentId" : 4,
      "companyCode" : "tiendat",
      "companyLabel" : "TIENDAT",
      "companyFullName" : null
    },
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    },
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 4516,
    "appId" : 21,
    "appModule" : "tms",
    "appName" : "user-tms-app",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 18276,
    "appId" : 88,
    "appModule" : "user",
    "appName" : "my-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 16821,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 3051,
    "appId" : 19,
    "appModule" : "sample",
    "appName" : "reactjs-lib",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14275,
    "appId" : 80,
    "appModule" : "logistics",
    "appName" : "company-logistics-sales",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 1591,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 6228,
    "appId" : 9,
    "appModule" : "company",
    "appName" : "company-hr",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 6853,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 10113,
    "appId" : 4,
    "appModule" : "communication",
    "appName" : "communication",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 11366,
    "appId" : 68,
    "appModule" : "document",
    "appName" : "company-document",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 535,
    "appId" : 14,
    "appModule" : "logistics",
    "appName" : "logistics-fleets",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 538,
    "appId" : 17,
    "appModule" : "logistics",
    "appName" : "user-logistics-managements",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 536,
    "appId" : 15,
    "appModule" : "logistics",
    "appName" : "user-logistics-prices",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 740,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 705,
    "appId" : 18,
    "appModule" : "system",
    "appName" : "system",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 986,
    "appId" : 5,
    "appModule" : "admin",
    "appName" : "admin-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 5517,
    "appId" : 64,
    "appModule" : "job-tracking",
    "appName" : "job-tracking",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1791,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 3046,
    "appId" : 27,
    "appModule" : "logistics",
    "appName" : "user-partner-logistics-crm",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 534,
    "appId" : 13,
    "appModule" : "logistics",
    "appName" : "company-logistics-settings",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 537,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 13081,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14931,
    "appId" : 70,
    "appModule" : "hr",
    "appName" : "company-kpi",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14940,
    "appId" : 81,
    "appModule" : "logistics",
    "appName" : "company-logistics-prices",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 15467,
    "appId" : 25,
    "appModule" : "tms",
    "appName" : "user-vehicle-fleets",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:dan"
}, {
  "id" : 220901,
  "editState" : "ORIGIN",
  "loadState" : "FromDB",
  "version" : 0,
  "createdBy" : "dan",
  "createdTime" : "09/09/2025@08:38:32+0000",
  "modifiedBy" : "dan",
  "modifiedTime" : "09/09/2025@08:38:32+0000",
  "storageState" : "ACTIVE",
  "companyId" : 8,
  "bookingDate" : "09/09/2025@08:37:40+0000",
  "hawbNo" : "IT TEST",
  "mawbNo" : "IT TEST",
  "shipmentType" : "FREE-HAND",
  "paymentTerm" : "PREPAID",
  "planTimeArrival" : "16/07/2025@04:52:38+0000",
  "planTimeDeparture" : "16/07/2025@04:52:38+0000",
  "note" : "IT TEST",
  "receiverAccountId" : 10836,
  "receiverBFSOneCode" : "CT0707",
  "receiverLabel" : "PHẠM HỒNG NHUNG - OSCAR.VNHPH",
  "senderAccountId" : 3,
  "senderBFSOneCode" : "CT1613",
  "senderLabel" : "LÊ NGỌC ĐÀN - JESSE.VNHPH",
  "handlingAgentPartnerId" : 56203,
  "handlingAgentLabel" : "BLAZQUEZ - SPAIN",
  "carrierPartnerId" : 56248,
  "carrierLabel" : "FEDEX EXPRESS VIETNAM COMPANY LIMITED",
  "transportDate" : "09/09/2025@08:37:40+0000",
  "specificQuotationChargeId" : 2681,
  "chargeType" : "SEA",
  "chargeId" : 89467,
  "inquiryId" : 224834,
  "inquiry" : {
    "id" : 224834,
    "editState" : "ORIGIN",
    "loadState" : "FromDB",
    "version" : 10,
    "createdBy" : "dan",
    "createdTime" : "16/07/2025@04:52:39+0000",
    "modifiedBy" : "dan",
    "modifiedTime" : "16/07/2025@08:01:27+0000",
    "storageState" : "ACTIVE",
    "companyId" : 8,
    "referenceCode" : "IS25071612597",
    "requestDate" : "16/07/2025@04:52:38+0000",
    "mode" : "SEA_FCL",
    "purpose" : "IMPORT",
    "typeOfService" : "SeaImpTransactions_FCL",
    "clientPartnerType" : "CUSTOMERS",
    "clientPartnerId" : 37492,
    "clientLabel" : "CôNG TY TNHH KHUÔN THÉP LONG VIệT updated",
    "attention" : "Mr Minh",
    "handlingAgentPartnerId" : 56203,
    "handlingAgentLabel" : "BLAZQUEZ - SPAIN",
    "salemanAccountId" : 3,
    "salemanLabel" : "LÊ NGỌC ĐÀN",
    "fromLocationCode" : "THLCH",
    "fromLocationLabel" : "LAEM CHABANG, THAILAND",
    "toLocationCode" : "CAHAL",
    "toLocationLabel" : "HALIFAX - NS, CANADA",
    "finalDestination" : "MONTREAL, CANADA (MTR)",
    "termOfService" : "PORT_TO_PORT",
    "incoterms" : "FOB",
    "estimatedTimeDeparture" : "16/07/2025@04:52:38+0000",
    "cargoReadyDate" : "16/07/2025@04:52:38+0000",
    "containerTypes" : "1x20DC",
    "packagingType" : "PK",
    "packageQty" : 0,
    "descOfGoods" : "",
    "commodity" : "GENERAL",
    "grossWeightKg" : 0.0,
    "volumeCbm" : 0.0,
    "chargeableWeight" : 0.0,
    "chargeableVolume" : 0.0,
    "termsAndConditions" : "Rates are applied for general, non-hazardous, non-special cargo.\n          Rates are subject to Peak Season Surcharge (PSS), General Rate Increase (GRI) whenever applicable, unless otherwise indicated herein.\n          Rates exclude import duty, loading & unloading cargo from truck…, unless otherwise indicated herein.\n          Rates exclude container detention, demurrage, container repair, storage charge, customs penalty, truck detention charge, if any.\n          Transit time indicated is based on carrier's publication, which may be subject to change with/without prior notice.",
    "note" : "The price quote is temporarily calculated in USD, will be converted into VND at exchange rate of Vietcombank on date of invoice.\n          - Rates are subjects to all arising charges during shipment handling process.\n          - Booking acceptance is subject to equipment and space availability at origin.\n          - All our business and services are always subject to the Standard Trading Conditions of VLA and a copy of which shall be supplied on demand. ",
    "containers" : [ {
      "id" : 184761,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "companyId" : 8,
      "containerType" : "20DC",
      "quantity" : 1,
      "cargoWeight" : 0.0,
      "totalCargoWeight" : 0.0,
      "cargoWeightUnit" : "KGM"
    } ]
  },
  "sellingRates" : [ {
    "group" : "SEA_FCL",
    "type" : "LOCAL_CHARGE",
    "target" : "ORIGIN",
    "payerPartnerId" : 37492,
    "payerPartnerCode" : null,
    "payerPartnerLabel" : "CôNG TY TNHH KHUÔN THÉP LONG VIệT updated",
    "code" : "S_DO FEE",
    "name" : "D/O FEE ",
    "quantity" : 1.0,
    "unit" : "20´DC",
    "unitPrice" : 100.0,
    "currency" : "USD",
    "exchangeRate" : 25470.0,
    "taxRate" : 0.0,
    "totalAmount" : 100.0,
    "domesticCurrency" : "VND",
    "domesticUnitPrice" : 2547000.0,
    "domesticTotalAmount" : 2547000.0,
    "note" : "Rate: 100.00 x 25,470"
  }, {
    "group" : "SEA_FCL",
    "type" : "LOCAL_CHARGE",
    "target" : "ORIGIN",
    "payerPartnerId" : 37492,
    "payerPartnerCode" : null,
    "payerPartnerLabel" : "CôNG TY TNHH KHUÔN THÉP LONG VIệT updated",
    "code" : "S_CIC",
    "name" : "CIC",
    "quantity" : 1.0,
    "unit" : "20´DC",
    "unitPrice" : 100.0,
    "currency" : "USD",
    "exchangeRate" : 25470.0,
    "taxRate" : 0.0,
    "totalAmount" : 100.0,
    "domesticCurrency" : "VND",
    "domesticUnitPrice" : 2547000.0,
    "domesticTotalAmount" : 2547000.0,
    "note" : "Rate: 100.00 x 25,470"
  }, {
    "group" : "SEA_FCL",
    "type" : "LOCAL_CHARGE",
    "target" : "ORIGIN",
    "payerPartnerId" : 37492,
    "payerPartnerCode" : null,
    "payerPartnerLabel" : "CôNG TY TNHH KHUÔN THÉP LONG VIệT updated",
    "code" : "S_THC",
    "name" : "THC",
    "quantity" : 1.0,
    "unit" : "20´DC",
    "unitPrice" : 100.0,
    "currency" : "USD",
    "exchangeRate" : 25470.0,
    "taxRate" : 0.0,
    "totalAmount" : 100.0,
    "domesticCurrency" : "VND",
    "domesticUnitPrice" : 2547000.0,
    "domesticTotalAmount" : 2547000.0,
    "note" : "Rate: 100.00 x 25,470"
  }, {
    "group" : "SEA_FCL",
    "type" : "LOCAL_CHARGE",
    "target" : "ORIGIN",
    "payerPartnerId" : 37492,
    "payerPartnerCode" : null,
    "payerPartnerLabel" : "CôNG TY TNHH KHUÔN THÉP LONG VIệT updated",
    "code" : "S_CLEAN",
    "name" : "CLEANING FEE",
    "quantity" : 1.0,
    "unit" : "20´DC",
    "unitPrice" : 100.0,
    "currency" : "USD",
    "exchangeRate" : 25470.0,
    "taxRate" : 0.0,
    "totalAmount" : 100.0,
    "domesticCurrency" : "VND",
    "domesticUnitPrice" : 2547000.0,
    "domesticTotalAmount" : 2547000.0,
    "note" : "Rate: 100.00 x 25,470"
  }, {
    "group" : "SEA_FCL",
    "type" : "LOCAL_CHARGE",
    "target" : "ORIGIN",
    "payerPartnerId" : 37492,
    "payerPartnerCode" : null,
    "payerPartnerLabel" : "CôNG TY TNHH KHUÔN THÉP LONG VIệT updated",
    "code" : "S_HAND",
    "name" : "HANDLING FEE",
    "quantity" : 1.0,
    "unit" : "20´DC",
    "unitPrice" : 100.0,
    "currency" : "USD",
    "exchangeRate" : 25470.0,
    "taxRate" : 0.0,
    "totalAmount" : 100.0,
    "domesticCurrency" : "VND",
    "domesticUnitPrice" : 2547000.0,
    "domesticTotalAmount" : 2547000.0,
    "note" : "Rate: 100.00 x 25,470"
  }, {
    "group" : "SEA_FCL",
    "type" : "SEAFREIGHT",
    "target" : "ORIGIN",
    "payerPartnerId" : 37492,
    "payerPartnerCode" : null,
    "payerPartnerLabel" : "CôNG TY TNHH KHUÔN THÉP LONG VIệT updated",
    "code" : "S_OF",
    "name" : "SEAFREIGHT",
    "quantity" : 1.0,
    "unit" : "20´DC",
    "unitPrice" : 111.0,
    "currency" : "USD",
    "exchangeRate" : 0.0,
    "taxRate" : 0.0,
    "totalAmount" : 111.0,
    "domesticCurrency" : "VND",
    "domesticUnitPrice" : 0.0,
    "domesticTotalAmount" : 0.0,
    "note" : "SUBJECT TO LOCAL CHARGES AT BOTH ENDS\nRemarks:\nIncluded LSS\n"
  } ]
} ]
2025-09-09T15:39:27.876+07:00 ERROR 68428 --- [qtp20********-37] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint BookingService/sendBFSOneIBookingNew
2025-09-09T15:39:27.876+07:00 ERROR 68428 --- [qtp20********-37] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: The customer dont exists in system !
	at net.datatp.util.error.RuntimeError.UnknownError(RuntimeError.java:96)
	at cloud.datatp.fforwarder.sales.booking.BookingLogic.sendBFSOneIBookingNew(BookingLogic.java:119)
	at cloud.datatp.fforwarder.sales.booking.BookingService.sendBFSOneIBookingNew(BookingService.java:32)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.sales.booking.BookingService$$SpringCGLIB$$0.sendBFSOneIBookingNew(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-09-09T15:39:53.923+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 1
2025-09-09T15:39:53.934+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T15:40:06.958+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:40:06.961+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T15:41:04.063+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:41:39.982+07:00  INFO 68428 --- [qtp20********-66] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-09T15:41:40.056+07:00  INFO 68428 --- [qtp20********-66] c.d.f.core.integration.BFSOneApi         : ---------------- Create IBooking -----------------------
2025-09-09T15:41:40.102+07:00  INFO 68428 --- [qtp20********-66] c.d.f.core.integration.BFSOneApi         : -----------Error Message From BFSOne Api:: You don't owner this customer !


2025-09-09T15:41:40.102+07:00 ERROR 68428 --- [qtp20********-66] c.d.f.core.integration.BFSOneApi         : You dont owner this customer !
2025-09-09T15:41:40.103+07:00  INFO 68428 --- [qtp20********-66] c.d.f.sales.integration.BFSOneCRMLogic   : You dont owner this customer !
2025-09-09T15:41:40.103+07:00 ERROR 68428 --- [qtp20********-66] c.d.f.sales.integration.BFSOneCRMLogic   : Error when create internal booking: 

2025-09-09T15:41:40.106+07:00 ERROR 68428 --- [qtp20********-66] n.d.m.monitor.call.EndpointCallContext   : Start call with component BookingService, method sendBFSOneIBookingNew, arguments
[ {
  "tenantId" : "default",
  "companyId" : 8,
  "companyParentId" : 4,
  "companyCode" : "beehph",
  "companyLabel" : "Bee HPH",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE",
  "loginId" : "dan",
  "accountId" : 3,
  "token" : "d03c8c88f18f1988e37ace07cebb2c46",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0dtzwldgfqqdxnwslil4oy94d1",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee-tw" : {
      "companyId" : 56,
      "companyParentId" : 4,
      "companyCode" : "bee-tw",
      "companyLabel" : "BEE TW",
      "companyFullName" : null
    },
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    },
    "thudo" : {
      "companyId" : 53,
      "companyParentId" : 0,
      "companyCode" : "thudo",
      "companyLabel" : "THUDO",
      "companyFullName" : null
    },
    "datatp" : {
      "companyId" : 10,
      "companyParentId" : 1,
      "companyCode" : "datatp",
      "companyLabel" : "DataTP Cloud",
      "companyFullName" : null
    },
    "beehan" : {
      "companyId" : 16,
      "companyParentId" : 4,
      "companyCode" : "beehan",
      "companyLabel" : "Bee HAN",
      "companyFullName" : null
    },
    "beescs" : {
      "companyId" : 12,
      "companyParentId" : 1,
      "companyCode" : "beescs",
      "companyLabel" : "BEESCS",
      "companyFullName" : null
    },
    "beedad" : {
      "companyId" : 18,
      "companyParentId" : 4,
      "companyCode" : "beedad",
      "companyLabel" : "Bee DAD",
      "companyFullName" : null
    },
    "hps" : {
      "companyId" : 51,
      "companyParentId" : 0,
      "companyCode" : "hps",
      "companyLabel" : "HPS",
      "companyFullName" : null
    },
    "marine" : {
      "companyId" : 52,
      "companyParentId" : 0,
      "companyCode" : "marine",
      "companyLabel" : "MARINE",
      "companyFullName" : null
    },
    "bonds" : {
      "companyId" : 54,
      "companyParentId" : 4,
      "companyCode" : "bonds",
      "companyLabel" : "BONDS",
      "companyFullName" : null
    },
    "tiendat" : {
      "companyId" : 55,
      "companyParentId" : 4,
      "companyCode" : "tiendat",
      "companyLabel" : "TIENDAT",
      "companyFullName" : null
    },
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    },
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 4516,
    "appId" : 21,
    "appModule" : "tms",
    "appName" : "user-tms-app",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 18276,
    "appId" : 88,
    "appModule" : "user",
    "appName" : "my-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 16821,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 3051,
    "appId" : 19,
    "appModule" : "sample",
    "appName" : "reactjs-lib",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14275,
    "appId" : 80,
    "appModule" : "logistics",
    "appName" : "company-logistics-sales",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 1591,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 6228,
    "appId" : 9,
    "appModule" : "company",
    "appName" : "company-hr",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 6853,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 10113,
    "appId" : 4,
    "appModule" : "communication",
    "appName" : "communication",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 11366,
    "appId" : 68,
    "appModule" : "document",
    "appName" : "company-document",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 535,
    "appId" : 14,
    "appModule" : "logistics",
    "appName" : "logistics-fleets",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 538,
    "appId" : 17,
    "appModule" : "logistics",
    "appName" : "user-logistics-managements",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 536,
    "appId" : 15,
    "appModule" : "logistics",
    "appName" : "user-logistics-prices",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 740,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 705,
    "appId" : 18,
    "appModule" : "system",
    "appName" : "system",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 986,
    "appId" : 5,
    "appModule" : "admin",
    "appName" : "admin-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 5517,
    "appId" : 64,
    "appModule" : "job-tracking",
    "appName" : "job-tracking",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1791,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 3046,
    "appId" : 27,
    "appModule" : "logistics",
    "appName" : "user-partner-logistics-crm",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 534,
    "appId" : 13,
    "appModule" : "logistics",
    "appName" : "company-logistics-settings",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 537,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 13081,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14931,
    "appId" : 70,
    "appModule" : "hr",
    "appName" : "company-kpi",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14940,
    "appId" : 81,
    "appModule" : "logistics",
    "appName" : "company-logistics-prices",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 15467,
    "appId" : 25,
    "appModule" : "tms",
    "appName" : "user-vehicle-fleets",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:dan"
}, {
  "id" : 220901,
  "editState" : "ORIGIN",
  "loadState" : "FromDB",
  "version" : 1,
  "createdBy" : "dan",
  "createdTime" : "09/09/2025@08:38:32+0000",
  "modifiedBy" : "dan",
  "modifiedTime" : "09/09/2025@08:41:35+0000",
  "storageState" : "ACTIVE",
  "companyId" : 8,
  "bookingDate" : "09/09/2025@08:37:40+0000",
  "hawbNo" : "IT TEST",
  "mawbNo" : "IT TEST",
  "shipmentType" : "FREE-HAND",
  "paymentTerm" : "PREPAID",
  "planTimeArrival" : "16/07/2025@04:52:38+0000",
  "planTimeDeparture" : "16/07/2025@04:52:38+0000",
  "note" : "IT TEST",
  "receiverAccountId" : 10836,
  "receiverBFSOneCode" : "CT0707",
  "receiverLabel" : "PHẠM HỒNG NHUNG - OSCAR.VNHPH",
  "senderAccountId" : 3,
  "senderBFSOneCode" : "CT1613",
  "senderLabel" : "LÊ NGỌC ĐÀN - JESSE.VNHPH",
  "handlingAgentPartnerId" : 56203,
  "handlingAgentLabel" : "BLAZQUEZ - SPAIN",
  "carrierPartnerId" : 56248,
  "carrierLabel" : "FEDEX EXPRESS VIETNAM COMPANY LIMITED",
  "transportDate" : "09/09/2025@08:37:40+0000",
  "specificQuotationChargeId" : 2681,
  "chargeType" : "SEA",
  "chargeId" : 89467,
  "inquiryId" : 224834,
  "inquiry" : {
    "id" : 224834,
    "editState" : "ORIGIN",
    "loadState" : "FromDB",
    "version" : 11,
    "createdBy" : "dan",
    "createdTime" : "16/07/2025@04:52:39+0000",
    "modifiedBy" : "dan",
    "modifiedTime" : "16/07/2025@08:01:27+0000",
    "storageState" : "ACTIVE",
    "companyId" : 8,
    "referenceCode" : "IS25071612597",
    "requestDate" : "16/07/2025@04:52:38+0000",
    "mode" : "SEA_FCL",
    "purpose" : "IMPORT",
    "typeOfService" : "SeaImpTransactions_FCL",
    "clientPartnerType" : "CUSTOMERS",
    "clientPartnerId" : 56204,
    "clientLabel" : "PHU HOANG TECHNOLOGY-CONG NGHE PHU HOANG",
    "attention" : "N/A",
    "handlingAgentPartnerId" : 56203,
    "handlingAgentLabel" : "BLAZQUEZ - SPAIN",
    "salemanAccountId" : 3,
    "salemanLabel" : "LÊ NGỌC ĐÀN",
    "fromLocationCode" : "THLCH",
    "fromLocationLabel" : "LAEM CHABANG, THAILAND",
    "toLocationCode" : "CAHAL",
    "toLocationLabel" : "HALIFAX - NS, CANADA",
    "finalDestination" : "MONTREAL, CANADA (MTR)",
    "termOfService" : "PORT_TO_PORT",
    "incoterms" : "FOB",
    "estimatedTimeDeparture" : "16/07/2025@04:52:38+0000",
    "cargoReadyDate" : "16/07/2025@04:52:38+0000",
    "containerTypes" : "1x20DC",
    "packagingType" : "PK",
    "packageQty" : 0,
    "descOfGoods" : "",
    "commodity" : "GENERAL",
    "grossWeightKg" : 0.0,
    "volumeCbm" : 0.0,
    "chargeableWeight" : 0.0,
    "chargeableVolume" : 0.0,
    "termsAndConditions" : "Rates are applied for general, non-hazardous, non-special cargo.\n          Rates are subject to Peak Season Surcharge (PSS), General Rate Increase (GRI) whenever applicable, unless otherwise indicated herein.\n          Rates exclude import duty, loading & unloading cargo from truck…, unless otherwise indicated herein.\n          Rates exclude container detention, demurrage, container repair, storage charge, customs penalty, truck detention charge, if any.\n          Transit time indicated is based on carrier's publication, which may be subject to change with/without prior notice.",
    "note" : "The price quote is temporarily calculated in USD, will be converted into VND at exchange rate of Vietcombank on date of invoice.\n          - Rates are subjects to all arising charges during shipment handling process.\n          - Booking acceptance is subject to equipment and space availability at origin.\n          - All our business and services are always subject to the Standard Trading Conditions of VLA and a copy of which shall be supplied on demand. ",
    "containers" : [ {
      "id" : 184761,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "companyId" : 8,
      "containerType" : "20DC",
      "quantity" : 1,
      "cargoWeight" : 0.0,
      "totalCargoWeight" : 0.0,
      "cargoWeightUnit" : "KGM"
    } ]
  },
  "sellingRates" : [ {
    "group" : "SEA_FCL",
    "type" : "LOCAL_CHARGE",
    "target" : "ORIGIN",
    "payerPartnerId" : 37492,
    "payerPartnerCode" : null,
    "payerPartnerLabel" : "CôNG TY TNHH KHUÔN THÉP LONG VIệT updated",
    "code" : "S_DO FEE",
    "name" : "D/O FEE ",
    "quantity" : 1.0,
    "unit" : "20´DC",
    "unitPrice" : 100.0,
    "currency" : "USD",
    "exchangeRate" : 25470.0,
    "taxRate" : 0.0,
    "totalAmount" : 100.0,
    "domesticCurrency" : "VND",
    "domesticUnitPrice" : 2547000.0,
    "domesticTotalAmount" : 2547000.0,
    "note" : "Rate: 100.00 x 25,470"
  }, {
    "group" : "SEA_FCL",
    "type" : "LOCAL_CHARGE",
    "target" : "ORIGIN",
    "payerPartnerId" : 37492,
    "payerPartnerCode" : null,
    "payerPartnerLabel" : "CôNG TY TNHH KHUÔN THÉP LONG VIệT updated",
    "code" : "S_CIC",
    "name" : "CIC",
    "quantity" : 1.0,
    "unit" : "20´DC",
    "unitPrice" : 100.0,
    "currency" : "USD",
    "exchangeRate" : 25470.0,
    "taxRate" : 0.0,
    "totalAmount" : 100.0,
    "domesticCurrency" : "VND",
    "domesticUnitPrice" : 2547000.0,
    "domesticTotalAmount" : 2547000.0,
    "note" : "Rate: 100.00 x 25,470"
  }, {
    "group" : "SEA_FCL",
    "type" : "LOCAL_CHARGE",
    "target" : "ORIGIN",
    "payerPartnerId" : 37492,
    "payerPartnerCode" : null,
    "payerPartnerLabel" : "CôNG TY TNHH KHUÔN THÉP LONG VIệT updated",
    "code" : "S_THC",
    "name" : "THC",
    "quantity" : 1.0,
    "unit" : "20´DC",
    "unitPrice" : 100.0,
    "currency" : "USD",
    "exchangeRate" : 25470.0,
    "taxRate" : 0.0,
    "totalAmount" : 100.0,
    "domesticCurrency" : "VND",
    "domesticUnitPrice" : 2547000.0,
    "domesticTotalAmount" : 2547000.0,
    "note" : "Rate: 100.00 x 25,470"
  }, {
    "group" : "SEA_FCL",
    "type" : "LOCAL_CHARGE",
    "target" : "ORIGIN",
    "payerPartnerId" : 37492,
    "payerPartnerCode" : null,
    "payerPartnerLabel" : "CôNG TY TNHH KHUÔN THÉP LONG VIệT updated",
    "code" : "S_CLEAN",
    "name" : "CLEANING FEE",
    "quantity" : 1.0,
    "unit" : "20´DC",
    "unitPrice" : 100.0,
    "currency" : "USD",
    "exchangeRate" : 25470.0,
    "taxRate" : 0.0,
    "totalAmount" : 100.0,
    "domesticCurrency" : "VND",
    "domesticUnitPrice" : 2547000.0,
    "domesticTotalAmount" : 2547000.0,
    "note" : "Rate: 100.00 x 25,470"
  }, {
    "group" : "SEA_FCL",
    "type" : "LOCAL_CHARGE",
    "target" : "ORIGIN",
    "payerPartnerId" : 37492,
    "payerPartnerCode" : null,
    "payerPartnerLabel" : "CôNG TY TNHH KHUÔN THÉP LONG VIệT updated",
    "code" : "S_HAND",
    "name" : "HANDLING FEE",
    "quantity" : 1.0,
    "unit" : "20´DC",
    "unitPrice" : 100.0,
    "currency" : "USD",
    "exchangeRate" : 25470.0,
    "taxRate" : 0.0,
    "totalAmount" : 100.0,
    "domesticCurrency" : "VND",
    "domesticUnitPrice" : 2547000.0,
    "domesticTotalAmount" : 2547000.0,
    "note" : "Rate: 100.00 x 25,470"
  }, {
    "group" : "SEA_FCL",
    "type" : "SEAFREIGHT",
    "target" : "ORIGIN",
    "payerPartnerId" : 56204,
    "payerPartnerCode" : null,
    "payerPartnerLabel" : "PHU HOANG TECHNOLOGY-CONG NGHE PHU HOANG",
    "code" : "S_OF",
    "name" : "SEAFREIGHT",
    "quantity" : 1.0,
    "unit" : "20´DC",
    "unitPrice" : 111.0,
    "currency" : "USD",
    "exchangeRate" : 0.0,
    "taxRate" : 0.0,
    "totalAmount" : 111.0,
    "domesticCurrency" : "VND",
    "domesticUnitPrice" : 0.0,
    "domesticTotalAmount" : 0.0,
    "note" : "SUBJECT TO LOCAL CHARGES AT BOTH ENDS\nRemarks:\nIncluded LSS\n"
  } ]
} ]
2025-09-09T15:41:40.110+07:00 ERROR 68428 --- [qtp20********-66] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint BookingService/sendBFSOneIBookingNew
2025-09-09T15:41:40.110+07:00 ERROR 68428 --- [qtp20********-66] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: You dont owner this customer !
	at net.datatp.util.error.RuntimeError.UnknownError(RuntimeError.java:96)
	at cloud.datatp.fforwarder.sales.booking.BookingLogic.sendBFSOneIBookingNew(BookingLogic.java:119)
	at cloud.datatp.fforwarder.sales.booking.BookingService.sendBFSOneIBookingNew(BookingService.java:32)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.sales.booking.BookingService$$SpringCGLIB$$0.sendBFSOneIBookingNew(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-09-09T15:41:53.184+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 8
2025-09-09T15:41:53.192+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T15:42:06.206+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:43:03.309+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:43:52.406+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-09T15:43:52.426+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T15:44:06.453+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:44:17.557+07:00  INFO 68428 --- [qtp20********-37] n.d.m.c.a.CompanyAuthenticationService   : User dan logout successfully 
2025-09-09T15:44:20.751+07:00 ERROR 68428 --- [qtp20********-60] net.datatp.module.account.AccountLogic   : User minhtv try to login into system, but fail
2025-09-09T15:44:20.753+07:00 ERROR 68428 --- [qtp20********-60] n.d.m.monitor.call.EndpointCallContext   : Start call with component CompanyAuthenticationService, method authenticate, arguments
[ {
  "tenantId" : "",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "anon",
  "accountId" : 0,
  "token" : "anon",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0dtzwldgfqqdxnwslil4oy94d1",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : null,
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : ":anon"
}, null, {
  "loginId" : "minhtv",
  "authorization" : null,
  "company" : "",
  "password" : "minhtv",
  "timeToLiveInMin" : 10080,
  "accessType" : "Employee"
} ]
2025-09-09T15:44:20.753+07:00 ERROR 68428 --- [qtp20********-60] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.authenticateCall(RPCController.java:61)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.entity.AccessToken.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.authenticate(CompanyAuthenticationService.java:82)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.company.auth.CompanyAuthenticationService$$SpringCGLIB$$0.authenticate(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-09T15:44:20.755+07:00  INFO 68428 --- [qtp20********-60] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CompanyAuthenticationService/authenticate
2025-09-09T15:44:24.895+07:00 ERROR 68428 --- [qtp20********-39] net.datatp.module.account.AccountLogic   : User minhtv try to login into system, but fail
2025-09-09T15:44:24.896+07:00 ERROR 68428 --- [qtp20********-39] n.d.m.monitor.call.EndpointCallContext   : Start call with component CompanyAuthenticationService, method authenticate, arguments
[ {
  "tenantId" : "",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "anon",
  "accountId" : 0,
  "token" : "anon",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0dtzwldgfqqdxnwslil4oy94d1",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : null,
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : ":anon"
}, null, {
  "loginId" : "minhtv",
  "authorization" : null,
  "company" : "",
  "password" : "minhtv",
  "timeToLiveInMin" : 10080,
  "accessType" : "Employee"
} ]
2025-09-09T15:44:24.896+07:00 ERROR 68428 --- [qtp20********-39] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.authenticateCall(RPCController.java:61)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.entity.AccessToken.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.authenticate(CompanyAuthenticationService.java:82)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.company.auth.CompanyAuthenticationService$$SpringCGLIB$$0.authenticate(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-09T15:44:24.897+07:00  INFO 68428 --- [qtp20********-39] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CompanyAuthenticationService/authenticate
2025-09-09T15:44:27.834+07:00 ERROR 68428 --- [qtp20********-40] net.datatp.module.account.AccountLogic   : User minhtv try to login into system, but fail
2025-09-09T15:44:27.835+07:00 ERROR 68428 --- [qtp20********-40] n.d.m.monitor.call.EndpointCallContext   : Start call with component CompanyAuthenticationService, method authenticate, arguments
[ {
  "tenantId" : "",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "anon",
  "accountId" : 0,
  "token" : "anon",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0dtzwldgfqqdxnwslil4oy94d1",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : null,
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : ":anon"
}, null, {
  "loginId" : "minhtv",
  "authorization" : null,
  "company" : "",
  "password" : "minhtv123",
  "timeToLiveInMin" : 10080,
  "accessType" : "Employee"
} ]
2025-09-09T15:44:27.835+07:00 ERROR 68428 --- [qtp20********-40] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.authenticateCall(RPCController.java:61)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.entity.AccessToken.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.authenticate(CompanyAuthenticationService.java:82)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.company.auth.CompanyAuthenticationService$$SpringCGLIB$$0.authenticate(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-09T15:44:27.853+07:00  INFO 68428 --- [qtp20********-40] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CompanyAuthenticationService/authenticate
2025-09-09T15:44:32.472+07:00 ERROR 68428 --- [qtp20********-41] net.datatp.module.account.AccountLogic   : User minhtv try to login into system, but fail
2025-09-09T15:44:32.473+07:00 ERROR 68428 --- [qtp20********-41] n.d.m.monitor.call.EndpointCallContext   : Start call with component CompanyAuthenticationService, method authenticate, arguments
[ {
  "tenantId" : "",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "anon",
  "accountId" : 0,
  "token" : "anon",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0dtzwldgfqqdxnwslil4oy94d1",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : null,
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : ":anon"
}, null, {
  "loginId" : "minhtv",
  "authorization" : null,
  "company" : "",
  "password" : "minhtv@123",
  "timeToLiveInMin" : 10080,
  "accessType" : "Employee"
} ]
2025-09-09T15:44:32.473+07:00 ERROR 68428 --- [qtp20********-41] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.authenticateCall(RPCController.java:61)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.entity.AccessToken.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.authenticate(CompanyAuthenticationService.java:82)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.company.auth.CompanyAuthenticationService$$SpringCGLIB$$0.authenticate(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-09T15:44:32.474+07:00  INFO 68428 --- [qtp20********-41] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CompanyAuthenticationService/authenticate
2025-09-09T15:44:36.898+07:00  INFO 68428 --- [qtp20********-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 5414c0fb99b704a338179ac6d100b403
2025-09-09T15:44:36.908+07:00  INFO 68428 --- [qtp20********-66] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-09T15:44:41.356+07:00  INFO 68428 --- [qtp20********-37] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-09T15:44:56.135+07:00  INFO 68428 --- [qtp20********-62] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le logout successfully 
2025-09-09T15:44:58.407+07:00  INFO 68428 --- [qtp20********-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 074c2784c89c0bf4b28980d799889bdc
2025-09-09T15:44:58.414+07:00  INFO 68428 --- [qtp20********-34] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-09T15:45:02.551+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:45:02.552+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T15:45:02.553+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-09T15:45:02.554+07:00  INFO 68428 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 09/09/2025@15:45:02+0700
2025-09-09T15:45:02.566+07:00  INFO 68428 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 09/09/2025@15:45:00+0700 to 09/09/2025@16:00:00+0700
2025-09-09T15:45:02.566+07:00  INFO 68428 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 09/09/2025@15:45:00+0700 to 09/09/2025@16:00:00+0700
2025-09-09T15:45:07.386+07:00  INFO 68428 --- [qtp20********-102] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T15:45:07.392+07:00  INFO 68428 --- [qtp20********-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T15:45:07.997+07:00  INFO 68428 --- [qtp20********-37] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-09T15:45:07.997+07:00  INFO 68428 --- [qtp20********-102] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-09T15:45:43.367+07:00  INFO 68428 --- [qtp20********-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 074c2784c89c0bf4b28980d799889bdc
2025-09-09T15:45:43.368+07:00  INFO 68428 --- [qtp20********-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 074c2784c89c0bf4b28980d799889bdc
2025-09-09T15:45:43.389+07:00  INFO 68428 --- [qtp20********-66] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-09T15:45:43.405+07:00  INFO 68428 --- [qtp20********-60] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-09T15:45:51.654+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 15, expire count 0
2025-09-09T15:45:51.674+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T15:45:57.090+07:00  INFO 68428 --- [qtp20********-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T15:45:57.090+07:00  INFO 68428 --- [qtp20********-61] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T15:45:57.382+07:00  INFO 68428 --- [qtp20********-61] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T15:46:02.317+07:00  INFO 68428 --- [qtp20********-34] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T15:46:05.692+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:46:42.156+07:00  INFO 68428 --- [qtp20********-61] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: HPMINHTV
2025-09-09T15:46:42.337+07:00  INFO 68428 --- [qtp20********-61] c.d.f.core.integration.BFSOneApi         : ---------------- Create IBooking -----------------------
2025-09-09T15:47:06.788+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:47:50.919+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 17, expire count 2
2025-09-09T15:47:50.948+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-09T15:48:04.972+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:49:06.067+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:49:50.167+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 5
2025-09-09T15:49:50.172+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T15:50:04.196+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T15:50:04.198+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:51:06.284+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:51:54.360+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 5
2025-09-09T15:51:54.368+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-09T15:52:03.387+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:52:31.113+07:00  INFO 68428 --- [qtp20********-61] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: HPMINHTV
2025-09-09T15:52:31.421+07:00  INFO 68428 --- [qtp20********-61] n.d.m.data.db.util.DBConnectionUtil      : Execute SQL: 
 SELECT id FROM lgc_sales_booking_cc WHERE booking_id IN (:candidateIds)
2025-09-09T15:52:31.423+07:00  INFO 68428 --- [qtp20********-61] n.d.m.data.db.util.DBConnectionUtil      : Execute SQL: 
 SELECT id FROM lgc_sales_booking_truck_charge WHERE booking_id IN (:candidateIds)
2025-09-09T15:52:31.425+07:00  INFO 68428 --- [qtp20********-61] n.d.m.data.db.util.DBConnectionUtil      : Execute SQL: 
 SELECT inquiry_id FROM lgc_sales_booking WHERE id IN (:candidateIds)
2025-09-09T15:52:31.427+07:00  INFO 68428 --- [qtp20********-61] n.d.m.data.db.util.DBConnectionUtil      : Execute SQL: 
 SELECT id FROM lgc_sales_container WHERE specific_service_inquiry_id IN (:candidateIds)
2025-09-09T15:52:31.437+07:00  INFO 68428 --- [qtp20********-61] n.d.m.data.db.util.DBConnectionUtil      : Execute SQL: 
 DELETE FROM lgc_sales_booking WHERE id IN (:ids)
2025-09-09T15:52:31.438+07:00  INFO 68428 --- [qtp20********-61] n.d.m.data.db.util.DBConnectionUtil      : Execute SQL: 
 DELETE FROM lgc_sales_container WHERE id IN (:ids)
2025-09-09T15:52:31.438+07:00  INFO 68428 --- [qtp20********-61] n.d.m.data.db.util.DBConnectionUtil      : Execute SQL: 
 DELETE FROM lgc_sales_specific_service_inquiry WHERE id IN (:ids)
2025-09-09T15:53:06.493+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:53:54.608+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 2
2025-09-09T15:53:54.617+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T15:54:02.634+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:55:05.735+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:55:05.738+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T15:55:53.837+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 26
2025-09-09T15:55:53.841+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T15:56:06.866+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:57:04.956+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:57:54.042+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 7
2025-09-09T15:57:54.048+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T15:58:06.058+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:59:04.222+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T15:59:53.292+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T15:59:53.297+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:00:06.313+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-09T16:00:06.316+07:00  INFO 68428 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 09/09/2025@16:00:06+0700
2025-09-09T16:00:06.348+07:00  INFO 68428 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 09/09/2025@16:00:00+0700 to 09/09/2025@16:15:00+0700
2025-09-09T16:00:06.348+07:00  INFO 68428 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 09/09/2025@16:00:00+0700 to 09/09/2025@16:15:00+0700
2025-09-09T16:00:06.348+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:00:06.348+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T16:00:06.350+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-09T16:00:18.251+07:00  INFO 68428 --- [qtp20********-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 074c2784c89c0bf4b28980d799889bdc
2025-09-09T16:00:18.253+07:00  INFO 68428 --- [qtp20********-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 074c2784c89c0bf4b28980d799889bdc
2025-09-09T16:00:18.347+07:00  INFO 68428 --- [qtp20********-37] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-09T16:00:18.362+07:00  INFO 68428 --- [qtp20********-60] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-09T16:01:03.447+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:01:52.584+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-09-09T16:01:52.620+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:02:06.640+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:02:29.562+07:00  INFO 68428 --- [qtp20********-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T16:02:29.562+07:00  INFO 68428 --- [qtp20********-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T16:02:29.796+07:00  INFO 68428 --- [qtp20********-37] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T16:02:34.797+07:00  INFO 68428 --- [qtp20********-60] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T16:03:02.729+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:03:51.832+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 3
2025-09-09T16:03:51.840+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:04:05.867+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:05:06.946+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:05:06.950+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T16:05:51.037+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-09T16:05:51.045+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:06:05.063+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:07:06.165+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:07:50.240+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:07:50.276+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:08:04.319+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:09:06.430+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:09:54.535+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-09T16:09:54.559+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-09T16:10:03.586+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:10:03.587+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T16:11:06.690+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:11:53.772+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 6
2025-09-09T16:11:53.776+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:12:02.793+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:13:05.899+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:13:54.008+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 2
2025-09-09T16:13:54.017+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-09T16:14:02.031+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:15:05.130+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T16:15:05.136+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-09T16:15:05.146+07:00  INFO 68428 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 09/09/2025@16:15:05+0700
2025-09-09T16:15:05.204+07:00  INFO 68428 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 09/09/2025@16:15:00+0700 to 09/09/2025@16:30:00+0700
2025-09-09T16:15:05.205+07:00  INFO 68428 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 09/09/2025@16:15:00+0700 to 09/09/2025@16:30:00+0700
2025-09-09T16:15:05.205+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:15:54.293+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-09T16:15:54.305+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:16:06.326+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:17:04.432+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:17:53.521+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:17:53.526+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-09T16:18:06.544+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:19:03.640+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:19:23.375+07:00  INFO 68428 --- [qtp20********-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 074c2784c89c0bf4b28980d799889bdc
2025-09-09T16:19:23.399+07:00  INFO 68428 --- [qtp20********-34] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-09T16:19:23.403+07:00  INFO 68428 --- [qtp20********-164] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 074c2784c89c0bf4b28980d799889bdc
2025-09-09T16:19:23.411+07:00  INFO 68428 --- [qtp20********-164] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-09T16:19:25.346+07:00  INFO 68428 --- [qtp20********-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 074c2784c89c0bf4b28980d799889bdc
2025-09-09T16:19:25.350+07:00  INFO 68428 --- [qtp20********-164] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 074c2784c89c0bf4b28980d799889bdc
2025-09-09T16:19:25.361+07:00  INFO 68428 --- [qtp20********-37] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-09T16:19:25.371+07:00  INFO 68428 --- [qtp20********-164] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-09T16:19:25.388+07:00  INFO 68428 --- [qtp20********-171] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 074c2784c89c0bf4b28980d799889bdc
2025-09-09T16:19:25.394+07:00  INFO 68428 --- [qtp20********-100] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 074c2784c89c0bf4b28980d799889bdc
2025-09-09T16:19:25.399+07:00  INFO 68428 --- [qtp20********-171] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-09T16:19:25.407+07:00  INFO 68428 --- [qtp20********-100] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-09T16:19:52.748+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 1
2025-09-09T16:19:52.778+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-09T16:20:06.799+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:20:06.800+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T16:21:02.886+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:21:51.965+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 4
2025-09-09T16:21:51.970+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:22:05.985+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:22:25.372+07:00  INFO 68428 --- [qtp20********-100] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 074c2784c89c0bf4b28980d799889bdc
2025-09-09T16:22:25.374+07:00  INFO 68428 --- [qtp20********-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 074c2784c89c0bf4b28980d799889bdc
2025-09-09T16:22:25.376+07:00  INFO 68428 --- [qtp20********-164] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 074c2784c89c0bf4b28980d799889bdc
2025-09-09T16:22:25.391+07:00  INFO 68428 --- [qtp20********-66] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-09T16:22:25.391+07:00  INFO 68428 --- [qtp20********-100] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-09T16:22:25.426+07:00  INFO 68428 --- [qtp20********-164] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-09T16:22:25.429+07:00  INFO 68428 --- [qtp20********-196] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 074c2784c89c0bf4b28980d799889bdc
2025-09-09T16:22:25.438+07:00  INFO 68428 --- [qtp20********-196] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-09T16:23:02.075+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:23:51.204+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 4
2025-09-09T16:23:51.229+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-09T16:24:05.253+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:25:06.348+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:25:06.351+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T16:25:50.435+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-09T16:25:50.441+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:26:04.463+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:27:06.562+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:27:49.638+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-09T16:27:49.652+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:28:03.672+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:29:06.782+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:29:53.877+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-09T16:29:53.887+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:30:02.908+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T16:30:02.910+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:30:02.910+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-09T16:30:02.911+07:00  INFO 68428 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 09/09/2025@16:30:02+0700
2025-09-09T16:30:02.924+07:00  INFO 68428 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 09/09/2025@16:30:00+0700 to 09/09/2025@16:45:00+0700
2025-09-09T16:30:02.924+07:00  INFO 68428 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 09/09/2025@16:30:00+0700 to 09/09/2025@16:45:00+0700
2025-09-09T16:30:52.424+07:00  INFO 68428 --- [qtp20********-235] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 074c2784c89c0bf4b28980d799889bdc
2025-09-09T16:30:52.425+07:00  INFO 68428 --- [qtp20********-198] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 074c2784c89c0bf4b28980d799889bdc
2025-09-09T16:30:52.425+07:00  INFO 68428 --- [qtp20********-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 074c2784c89c0bf4b28980d799889bdc
2025-09-09T16:30:52.442+07:00  INFO 68428 --- [qtp20********-251] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 074c2784c89c0bf4b28980d799889bdc
2025-09-09T16:30:52.508+07:00  INFO 68428 --- [qtp20********-235] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-09T16:30:52.517+07:00  INFO 68428 --- [qtp20********-251] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-09T16:30:52.536+07:00  INFO 68428 --- [qtp20********-61] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-09T16:30:52.557+07:00  INFO 68428 --- [qtp20********-198] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-09T16:31:04.580+07:00  INFO 68428 --- [qtp20********-100] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 074c2784c89c0bf4b28980d799889bdc
2025-09-09T16:31:04.590+07:00  INFO 68428 --- [qtp20********-100] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-09T16:31:04.613+07:00  INFO 68428 --- [qtp20********-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 074c2784c89c0bf4b28980d799889bdc
2025-09-09T16:31:04.622+07:00  INFO 68428 --- [qtp20********-238] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-09T16:31:06.027+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:31:54.119+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 0
2025-09-09T16:31:54.142+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-09T16:32:02.165+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:33:05.272+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:33:54.373+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-09T16:33:54.384+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:34:06.399+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:35:04.479+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:35:04.482+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T16:35:53.570+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:35:53.579+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:36:06.599+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:37:03.692+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:37:52.784+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-09T16:37:52.794+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:38:06.820+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:39:02.922+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:39:09.458+07:00  INFO 68428 --- [qtp20********-198] n.d.m.c.a.CompanyAuthenticationService   : User minhtv logout successfully 
2025-09-09T16:39:11.195+07:00  INFO 68428 --- [qtp20********-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 530775628c342c3b86b44df69b1f561c
2025-09-09T16:39:11.202+07:00  INFO 68428 --- [qtp20********-238] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-09T16:39:17.267+07:00  INFO 68428 --- [qtp20********-238] n.d.m.c.a.CompanyAuthenticationService   : User minhtv logout successfully 
2025-09-09T16:39:19.566+07:00  INFO 68428 --- [qtp20********-198] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T16:39:19.573+07:00  INFO 68428 --- [qtp20********-198] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T16:39:52.007+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 15, expire count 0
2025-09-09T16:39:52.025+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:40:06.042+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:40:06.044+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T16:41:02.149+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:41:51.263+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 6
2025-09-09T16:41:51.270+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:42:05.289+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:43:06.388+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:43:46.737+07:00  INFO 68428 --- [qtp20********-61] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T16:43:46.737+07:00  INFO 68428 --- [qtp20********-291] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T16:43:47.330+07:00  INFO 68428 --- [qtp20********-291] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T16:43:50.513+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-09-09T16:43:50.573+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-09T16:43:50.924+07:00  INFO 68428 --- [qtp20********-171] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T16:43:50.929+07:00  INFO 68428 --- [qtp20********-238] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T16:43:51.074+07:00  INFO 68428 --- [qtp20********-171] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T16:43:51.085+07:00  INFO 68428 --- [qtp20********-238] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T16:43:51.946+07:00  INFO 68428 --- [qtp20********-61] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T16:44:00.416+07:00  INFO 68428 --- [qtp20********-238] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T16:44:00.431+07:00  INFO 68428 --- [qtp20********-238] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T16:44:00.453+07:00  INFO 68428 --- [qtp20********-291] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T16:44:00.461+07:00  INFO 68428 --- [qtp20********-291] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T16:44:04.597+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:44:07.463+07:00  INFO 68428 --- [qtp20********-235] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T16:44:07.463+07:00  INFO 68428 --- [qtp20********-238] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T16:44:08.316+07:00  INFO 68428 --- [qtp20********-235] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T16:44:12.647+07:00  INFO 68428 --- [qtp20********-238] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T16:44:41.186+07:00 ERROR 68428 --- [qtp20********-171] n.d.m.monitor.call.EndpointCallContext   : Start call with component BookingService, method saveBooking, arguments
[ {
  "tenantId" : "default",
  "companyId" : 8,
  "companyParentId" : 4,
  "companyCode" : "beehph",
  "companyLabel" : "Bee HPH",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE",
  "loginId" : "dan",
  "accountId" : 3,
  "token" : "4672c14613cf4c2f497f824ff3c46925",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0dtzwldgfqqdxnwslil4oy94d1",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee-tw" : {
      "companyId" : 56,
      "companyParentId" : 4,
      "companyCode" : "bee-tw",
      "companyLabel" : "BEE TW",
      "companyFullName" : null
    },
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    },
    "thudo" : {
      "companyId" : 53,
      "companyParentId" : 0,
      "companyCode" : "thudo",
      "companyLabel" : "THUDO",
      "companyFullName" : null
    },
    "datatp" : {
      "companyId" : 10,
      "companyParentId" : 1,
      "companyCode" : "datatp",
      "companyLabel" : "DataTP Cloud",
      "companyFullName" : null
    },
    "beehan" : {
      "companyId" : 16,
      "companyParentId" : 4,
      "companyCode" : "beehan",
      "companyLabel" : "Bee HAN",
      "companyFullName" : null
    },
    "beescs" : {
      "companyId" : 12,
      "companyParentId" : 1,
      "companyCode" : "beescs",
      "companyLabel" : "BEESCS",
      "companyFullName" : null
    },
    "beedad" : {
      "companyId" : 18,
      "companyParentId" : 4,
      "companyCode" : "beedad",
      "companyLabel" : "Bee DAD",
      "companyFullName" : null
    },
    "hps" : {
      "companyId" : 51,
      "companyParentId" : 0,
      "companyCode" : "hps",
      "companyLabel" : "HPS",
      "companyFullName" : null
    },
    "marine" : {
      "companyId" : 52,
      "companyParentId" : 0,
      "companyCode" : "marine",
      "companyLabel" : "MARINE",
      "companyFullName" : null
    },
    "bonds" : {
      "companyId" : 54,
      "companyParentId" : 4,
      "companyCode" : "bonds",
      "companyLabel" : "BONDS",
      "companyFullName" : null
    },
    "tiendat" : {
      "companyId" : 55,
      "companyParentId" : 4,
      "companyCode" : "tiendat",
      "companyLabel" : "TIENDAT",
      "companyFullName" : null
    },
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    },
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 4516,
    "appId" : 21,
    "appModule" : "tms",
    "appName" : "user-tms-app",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 18276,
    "appId" : 88,
    "appModule" : "user",
    "appName" : "my-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 16821,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 3051,
    "appId" : 19,
    "appModule" : "sample",
    "appName" : "reactjs-lib",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14275,
    "appId" : 80,
    "appModule" : "logistics",
    "appName" : "company-logistics-sales",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 1591,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 6228,
    "appId" : 9,
    "appModule" : "company",
    "appName" : "company-hr",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 6853,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 10113,
    "appId" : 4,
    "appModule" : "communication",
    "appName" : "communication",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 11366,
    "appId" : 68,
    "appModule" : "document",
    "appName" : "company-document",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 535,
    "appId" : 14,
    "appModule" : "logistics",
    "appName" : "logistics-fleets",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 538,
    "appId" : 17,
    "appModule" : "logistics",
    "appName" : "user-logistics-managements",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 536,
    "appId" : 15,
    "appModule" : "logistics",
    "appName" : "user-logistics-prices",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 740,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 705,
    "appId" : 18,
    "appModule" : "system",
    "appName" : "system",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 986,
    "appId" : 5,
    "appModule" : "admin",
    "appName" : "admin-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 5517,
    "appId" : 64,
    "appModule" : "job-tracking",
    "appName" : "job-tracking",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1791,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 3046,
    "appId" : 27,
    "appModule" : "logistics",
    "appName" : "user-partner-logistics-crm",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 534,
    "appId" : 13,
    "appModule" : "logistics",
    "appName" : "company-logistics-settings",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 537,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 13081,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14931,
    "appId" : 70,
    "appModule" : "hr",
    "appName" : "company-kpi",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14940,
    "appId" : 81,
    "appModule" : "logistics",
    "appName" : "company-logistics-prices",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 15467,
    "appId" : 25,
    "appModule" : "tms",
    "appName" : "user-vehicle-fleets",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:dan"
}, {
  "parentId" : 0,
  "id" : 8,
  "code" : "beehph",
  "label" : "Bee HPH",
  "fullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE"
}, {
  "editState" : "ORIGIN",
  "loadState" : "FromDB",
  "version" : 0,
  "createdBy" : "dan",
  "createdTime" : "09/09/2025@09:44:41+0000",
  "modifiedBy" : "dan",
  "modifiedTime" : "09/09/2025@09:44:41+0000",
  "storageState" : "ACTIVE",
  "companyId" : 8,
  "bookingDate" : "09/09/2025@09:44:02+0000",
  "hawbNo" : "IT TEST",
  "mawbNo" : "IT TEST",
  "shipmentType" : "NOMINATED",
  "paymentTerm" : "COLLECT",
  "planTimeArrival" : "08/09/2025@03:53:00+0000",
  "planTimeDeparture" : "08/09/2025@03:53:00+0000",
  "note" : "INC DCIC TEST",
  "senderAccountId" : 3,
  "senderBFSOneCode" : "CT1613",
  "senderLabel" : "LÊ NGỌC ĐÀN - JESSE.VNHPH",
  "handlingAgentPartnerId" : 56201,
  "handlingAgentLabel" : "CUSTOM LOGISTIC SERVICE LTD - AUCKLAND, NEW ZEALAND ",
  "carrierPartnerId" : 56248,
  "carrierLabel" : "FEDEX EXPRESS VIETNAM COMPANY LIMITED",
  "transportDate" : "09/09/2025@09:44:02+0000",
  "specificQuotationChargeId" : 3660,
  "chargeType" : "SEA",
  "chargeId" : 89469,
  "inquiry" : {
    "id" : 224836,
    "editState" : "ORIGIN",
    "loadState" : "FromDB",
    "version" : 0,
    "createdBy" : "dan",
    "createdTime" : "08/09/2025@03:53:08+0000",
    "modifiedBy" : "dan",
    "modifiedTime" : "08/09/2025@03:53:08+0000",
    "storageState" : "ACTIVE",
    "companyId" : 8,
    "referenceCode" : "IS25090818961",
    "requestDate" : "08/09/2025@03:53:00+0000",
    "mode" : "SEA_FCL",
    "purpose" : "IMPORT",
    "typeOfService" : "SeaImpTransactions_FCL",
    "clientPartnerType" : "CUSTOMERS",
    "clientPartnerId" : 56202,
    "clientLabel" : "NEWWAY HA NAM., JSC",
    "attention" : "N/A",
    "handlingAgentPartnerId" : 56201,
    "handlingAgentLabel" : "CUSTOM LOGISTIC SERVICE LTD - AUCKLAND, NEW ZEALAND ",
    "salemanAccountId" : 3,
    "salemanLabel" : "LÊ NGỌC ĐÀN",
    "fromLocationCode" : "MYPKG",
    "fromLocationLabel" : "PORT KELANG, MALAYSIA",
    "toLocationCode" : "VNHPH",
    "toLocationLabel" : "HAIPHONG, VIETNAM",
    "finalDestination" : "",
    "termOfService" : "PORT_TO_PORT",
    "incoterms" : "FOB",
    "estimatedTimeDeparture" : "08/09/2025@03:53:00+0000",
    "cargoReadyDate" : "08/09/2025@03:53:00+0000",
    "containerTypes" : "1x20DC",
    "packagingType" : "PK",
    "packageQty" : 0,
    "descOfGoods" : "",
    "commodity" : "GENERAL",
    "grossWeightKg" : 0.0,
    "volumeCbm" : 0.0,
    "chargeableWeight" : 0.0,
    "chargeableVolume" : 0.0,
    "termsAndConditions" : "Rates are applied for general, non-hazardous, non-special cargo.\n          Rates are subject to Peak Season Surcharge (PSS), General Rate Increase (GRI) whenever applicable, unless otherwise indicated herein.\n          Rates exclude import duty, loading & unloading cargo from truck…, unless otherwise indicated herein.\n          Rates exclude container detention, demurrage, container repair, storage charge, customs penalty, truck detention charge, if any.\n          Transit time indicated is based on carrier's publication, which may be subject to change with/without prior notice.",
    "note" : "",
    "signatureNote" : "",
    "containers" : [ {
      "id" : 184763,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "companyId" : 8,
      "containerType" : "20DC",
      "quantity" : 1,
      "cargoWeight" : 0.0,
      "totalCargoWeight" : 0.0,
      "cargoWeightUnit" : "KGM"
    } ]
  },
  "sellingRates" : [ {
    "group" : "SEA_FCL",
    "type" : "SEAFREIGHT",
    "target" : "ORIGIN",
    "payerPartnerId" : 56202,
    "payerPartnerCode" : null,
    "payerPartnerLabel" : "NEWWAY HA NAM., JSC",
    "code" : "S_OF",
    "name" : "SEAFREIGHT",
    "quantity" : 1.0,
    "unit" : "20´DC",
    "unitPrice" : 1.0,
    "currency" : "USD",
    "exchangeRate" : 25470.0,
    "taxRate" : 0.0,
    "totalAmount" : 1.0,
    "domesticCurrency" : "VND",
    "domesticUnitPrice" : 25470.0,
    "domesticTotalAmount" : 25470.0,
    "note" : "Rate: 1.00 x 25,470"
  } ]
} ]
2025-09-09T16:44:41.188+07:00 ERROR 68428 --- [qtp20********-171] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: jakarta.validation.ConstraintViolationException: Validation failed for classes [cloud.datatp.fforwarder.sales.booking.entity.Booking] during persist time for groups [jakarta.validation.groups.Default, ]
List of constraint violations:[
	ConstraintViolationImpl{interpolatedMessage='must not be null', propertyPath=receiverAccountId, rootBeanClass=class cloud.datatp.fforwarder.sales.booking.entity.Booking, messageTemplate='{jakarta.validation.constraints.NotNull.message}'}
	ConstraintViolationImpl{interpolatedMessage='must not be null', propertyPath=receiverBFSOneCode, rootBeanClass=class cloud.datatp.fforwarder.sales.booking.entity.Booking, messageTemplate='{jakarta.validation.constraints.NotNull.message}'}
]
	at org.hibernate.boot.beanvalidation.BeanValidationEventListener.validate(BeanValidationEventListener.java:151)
	at org.hibernate.boot.beanvalidation.BeanValidationEventListener.onPreInsert(BeanValidationEventListener.java:81)
	at org.hibernate.action.internal.EntityIdentityInsertAction.preInsert(EntityIdentityInsertAction.java:186)
	at org.hibernate.action.internal.EntityIdentityInsertAction.execute(EntityIdentityInsertAction.java:75)
	at org.hibernate.engine.spi.ActionQueue.execute(ActionQueue.java:670)
	at org.hibernate.engine.spi.ActionQueue.addResolvedEntityInsertAction(ActionQueue.java:291)
	at org.hibernate.engine.spi.ActionQueue.addInsertAction(ActionQueue.java:272)
	at org.hibernate.engine.spi.ActionQueue.addAction(ActionQueue.java:322)
	at org.hibernate.event.internal.AbstractSaveEventListener.addInsertAction(AbstractSaveEventListener.java:386)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSaveOrReplicate(AbstractSaveEventListener.java:300)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSave(AbstractSaveEventListener.java:219)
	at org.hibernate.event.internal.AbstractSaveEventListener.saveWithGeneratedId(AbstractSaveEventListener.java:134)
	at org.hibernate.event.internal.DefaultPersistEventListener.entityIsTransient(DefaultPersistEventListener.java:175)
	at org.hibernate.event.internal.DefaultPersistEventListener.persist(DefaultPersistEventListener.java:93)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:77)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:54)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.firePersist(SessionImpl.java:754)
	at org.hibernate.internal.SessionImpl.persist(SessionImpl.java:738)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:320)
	at jdk.proxy2/jdk.proxy2.$Proxy181.persist(Unknown Source)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.save(SimpleJpaRepository.java:618)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:516)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:628)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:168)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy440.save(Unknown Source)
	at cloud.datatp.fforwarder.sales.booking.BookingLogic.saveBooking(BookingLogic.java:249)
	at cloud.datatp.fforwarder.sales.booking.BookingService.saveBooking(BookingService.java:42)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.sales.booking.BookingService$$SpringCGLIB$$0.saveBooking(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-09T16:44:41.190+07:00  INFO 68428 --- [qtp20********-171] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint BookingService/saveBooking
2025-09-09T16:45:06.716+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:45:06.717+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T16:45:06.718+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-09T16:45:06.718+07:00  INFO 68428 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 09/09/2025@16:45:06+0700
2025-09-09T16:45:06.747+07:00  INFO 68428 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 09/09/2025@16:45:00+0700 to 09/09/2025@17:00:00+0700
2025-09-09T16:45:06.747+07:00  INFO 68428 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 09/09/2025@16:45:00+0700 to 09/09/2025@17:00:00+0700
2025-09-09T16:45:49.896+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-09-09T16:45:49.906+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:46:02.328+07:00  INFO 68428 --- [qtp20********-291] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T16:46:02.338+07:00  INFO 68428 --- [qtp20********-291] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T16:46:02.357+07:00  INFO 68428 --- [qtp20********-234] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T16:46:02.367+07:00  INFO 68428 --- [qtp20********-234] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T16:46:03.934+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:46:33.014+07:00  INFO 68428 --- [qtp20********-171] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T16:46:33.040+07:00  INFO 68428 --- [qtp20********-171] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T16:46:33.053+07:00  INFO 68428 --- [qtp20********-236] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T16:46:33.069+07:00  INFO 68428 --- [qtp20********-236] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T16:47:06.044+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:47:07.818+07:00  INFO 68428 --- [qtp20********-291] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T16:47:07.838+07:00  INFO 68428 --- [qtp20********-338] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T16:47:08.172+07:00  INFO 68428 --- [qtp20********-291] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T16:47:13.390+07:00  INFO 68428 --- [qtp20********-338] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T16:47:29.752+07:00  INFO 68428 --- [qtp20********-338] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T16:47:29.752+07:00  INFO 68428 --- [qtp20********-171] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T16:47:29.962+07:00  INFO 68428 --- [qtp20********-171] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T16:47:30.187+07:00  INFO 68428 --- [qtp20********-338] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T16:47:54.165+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 0
2025-09-09T16:47:54.177+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 2
2025-09-09T16:48:03.195+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:49:06.298+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:49:54.374+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 14
2025-09-09T16:49:54.390+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-09T16:50:02.410+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:50:02.410+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T16:51:05.497+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:51:13.522+07:00  INFO 68428 --- [qtp20********-100] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T16:51:13.536+07:00  INFO 68428 --- [qtp20********-100] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T16:51:13.554+07:00  INFO 68428 --- [qtp20********-284] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T16:51:13.562+07:00  INFO 68428 --- [qtp20********-284] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T16:51:54.600+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-09T16:51:54.616+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:52:06.630+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:52:06.631+07:00  INFO 68428 --- [qtp20********-284] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T16:52:06.631+07:00  INFO 68428 --- [qtp20********-340] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T16:52:06.839+07:00  INFO 68428 --- [qtp20********-340] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T16:52:06.839+07:00  INFO 68428 --- [qtp20********-284] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T16:52:59.910+07:00  INFO 68428 --- [qtp20********-236] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T16:52:59.912+07:00  INFO 68428 --- [qtp20********-100] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T16:52:59.930+07:00  INFO 68428 --- [qtp20********-236] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T16:52:59.930+07:00  INFO 68428 --- [qtp20********-100] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T16:53:04.734+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:53:10.659+07:00  INFO 68428 --- [qtp20********-235] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T16:53:10.659+07:00  INFO 68428 --- [qtp20********-236] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T16:53:10.869+07:00  INFO 68428 --- [qtp20********-236] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T16:53:15.777+07:00  INFO 68428 --- [qtp20********-235] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T16:53:53.825+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-09-09T16:53:53.844+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:54:06.862+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:54:22.139+07:00  INFO 68428 --- [qtp20********-291] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T16:54:22.139+07:00  INFO 68428 --- [qtp20********-236] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T16:54:22.147+07:00  INFO 68428 --- [qtp20********-291] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T16:54:22.147+07:00  INFO 68428 --- [qtp20********-236] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T16:55:03.967+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:55:03.970+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T16:55:53.075+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 1
2025-09-09T16:55:53.086+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:56:06.104+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:57:03.210+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:57:52.291+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:57:52.296+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T16:58:06.323+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:59:02.401+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T16:59:51.517+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 4
2025-09-09T16:59:51.531+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T17:00:05.553+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:00:05.554+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T17:00:05.555+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-09T17:00:05.555+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-09T17:00:05.556+07:00  INFO 68428 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 09/09/2025@17:00:05+0700
2025-09-09T17:00:05.577+07:00  INFO 68428 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 09/09/2025@17:00:00+0700 to 09/09/2025@17:15:00+0700
2025-09-09T17:00:05.577+07:00  INFO 68428 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 09/09/2025@17:00:00+0700 to 09/09/2025@17:15:00+0700
2025-09-09T17:00:45.976+07:00  INFO 68428 --- [qtp20********-291] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T17:00:45.977+07:00  INFO 68428 --- [qtp20********-100] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T17:00:45.990+07:00  INFO 68428 --- [qtp20********-291] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T17:00:45.990+07:00  INFO 68428 --- [qtp20********-100] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T17:00:55.392+07:00  INFO 68428 --- [qtp20********-284] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T17:00:55.392+07:00  INFO 68428 --- [qtp20********-198] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T17:00:55.651+07:00  INFO 68428 --- [qtp20********-284] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T17:01:00.688+07:00  INFO 68428 --- [qtp20********-198] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T17:01:06.682+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:01:50.775+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 0
2025-09-09T17:01:50.795+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T17:02:04.820+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:03:06.934+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:03:50.020+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-09T17:03:50.050+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T17:04:04.075+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:04:38.752+07:00  INFO 68428 --- [qtp20********-198] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T17:04:38.754+07:00  INFO 68428 --- [qtp20********-284] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T17:04:38.821+07:00  INFO 68428 --- [qtp20********-198] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T17:04:38.842+07:00  INFO 68428 --- [qtp20********-284] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T17:04:47.546+07:00  INFO 68428 --- [qtp20********-198] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T17:04:47.548+07:00  INFO 68428 --- [qtp20********-100] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T17:04:47.555+07:00  INFO 68428 --- [qtp20********-198] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T17:04:47.556+07:00  INFO 68428 --- [qtp20********-100] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T17:05:06.194+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:05:06.204+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T17:05:06.621+07:00  INFO 68428 --- [qtp20********-284] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T17:05:06.621+07:00  INFO 68428 --- [qtp20********-236] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T17:05:06.628+07:00  INFO 68428 --- [qtp20********-284] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T17:05:06.628+07:00  INFO 68428 --- [qtp20********-236] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T17:05:39.179+07:00  INFO 68428 --- [qtp20********-198] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T17:05:39.179+07:00  INFO 68428 --- [qtp20********-236] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T17:05:39.516+07:00  INFO 68428 --- [qtp20********-198] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T17:05:44.308+07:00  INFO 68428 --- [qtp20********-236] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T17:05:54.290+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 0
2025-09-09T17:05:54.311+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T17:06:03.335+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:06:40.937+07:00  INFO 68428 --- [qtp20********-284] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T17:06:40.950+07:00  INFO 68428 --- [qtp20********-284] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T17:06:40.970+07:00  INFO 68428 --- [qtp20********-425] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T17:06:40.980+07:00  INFO 68428 --- [qtp20********-425] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T17:06:45.546+07:00  INFO 68428 --- [qtp20********-100] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T17:06:45.553+07:00  INFO 68428 --- [qtp20********-100] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T17:06:45.574+07:00  INFO 68428 --- [qtp20********-340] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T17:06:45.618+07:00  INFO 68428 --- [qtp20********-340] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T17:07:03.635+07:00  INFO 68428 --- [qtp20********-100] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T17:07:03.644+07:00  INFO 68428 --- [qtp20********-100] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T17:07:03.671+07:00  INFO 68428 --- [qtp20********-385] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T17:07:03.687+07:00  INFO 68428 --- [qtp20********-385] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T17:07:06.424+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:07:08.309+07:00  INFO 68428 --- [qtp20********-100] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T17:07:08.323+07:00  INFO 68428 --- [qtp20********-100] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T17:07:08.343+07:00  INFO 68428 --- [qtp20********-383] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T17:07:08.427+07:00  INFO 68428 --- [qtp20********-383] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T17:07:31.077+07:00  INFO 68428 --- [qtp20********-284] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T17:07:31.081+07:00  INFO 68428 --- [qtp20********-425] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T17:07:31.136+07:00  INFO 68428 --- [qtp20********-284] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T17:07:31.136+07:00  INFO 68428 --- [qtp20********-425] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T17:07:54.533+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-09T17:07:54.544+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T17:08:02.562+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:08:52.047+07:00  INFO 68428 --- [qtp20********-198] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T17:08:52.064+07:00  INFO 68428 --- [qtp20********-198] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T17:08:52.076+07:00  INFO 68428 --- [qtp20********-438] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T17:08:52.083+07:00  INFO 68428 --- [qtp20********-438] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T17:09:05.668+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:09:15.269+07:00  INFO 68428 --- [qtp20********-340] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T17:09:15.269+07:00  INFO 68428 --- [qtp20********-61] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T17:09:15.446+07:00  INFO 68428 --- [qtp20********-340] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T17:09:15.637+07:00  INFO 68428 --- [qtp20********-61] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T17:09:37.876+07:00 ERROR 68428 --- [qtp20********-430] n.d.m.monitor.call.EndpointCallContext   : Start call with component BookingService, method getBooking, arguments
[ {
  "tenantId" : "default",
  "companyId" : 8,
  "companyParentId" : 4,
  "companyCode" : "beehph",
  "companyLabel" : "Bee HPH",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE",
  "loginId" : "dan",
  "accountId" : 3,
  "token" : "4672c14613cf4c2f497f824ff3c46925",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0dtzwldgfqqdxnwslil4oy94d1",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee-tw" : {
      "companyId" : 56,
      "companyParentId" : 4,
      "companyCode" : "bee-tw",
      "companyLabel" : "BEE TW",
      "companyFullName" : null
    },
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    },
    "thudo" : {
      "companyId" : 53,
      "companyParentId" : 0,
      "companyCode" : "thudo",
      "companyLabel" : "THUDO",
      "companyFullName" : null
    },
    "datatp" : {
      "companyId" : 10,
      "companyParentId" : 1,
      "companyCode" : "datatp",
      "companyLabel" : "DataTP Cloud",
      "companyFullName" : null
    },
    "beehan" : {
      "companyId" : 16,
      "companyParentId" : 4,
      "companyCode" : "beehan",
      "companyLabel" : "Bee HAN",
      "companyFullName" : null
    },
    "beescs" : {
      "companyId" : 12,
      "companyParentId" : 1,
      "companyCode" : "beescs",
      "companyLabel" : "BEESCS",
      "companyFullName" : null
    },
    "beedad" : {
      "companyId" : 18,
      "companyParentId" : 4,
      "companyCode" : "beedad",
      "companyLabel" : "Bee DAD",
      "companyFullName" : null
    },
    "hps" : {
      "companyId" : 51,
      "companyParentId" : 0,
      "companyCode" : "hps",
      "companyLabel" : "HPS",
      "companyFullName" : null
    },
    "marine" : {
      "companyId" : 52,
      "companyParentId" : 0,
      "companyCode" : "marine",
      "companyLabel" : "MARINE",
      "companyFullName" : null
    },
    "bonds" : {
      "companyId" : 54,
      "companyParentId" : 4,
      "companyCode" : "bonds",
      "companyLabel" : "BONDS",
      "companyFullName" : null
    },
    "tiendat" : {
      "companyId" : 55,
      "companyParentId" : 4,
      "companyCode" : "tiendat",
      "companyLabel" : "TIENDAT",
      "companyFullName" : null
    },
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    },
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 4516,
    "appId" : 21,
    "appModule" : "tms",
    "appName" : "user-tms-app",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 18276,
    "appId" : 88,
    "appModule" : "user",
    "appName" : "my-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 16821,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 3051,
    "appId" : 19,
    "appModule" : "sample",
    "appName" : "reactjs-lib",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14275,
    "appId" : 80,
    "appModule" : "logistics",
    "appName" : "company-logistics-sales",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 1591,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 6228,
    "appId" : 9,
    "appModule" : "company",
    "appName" : "company-hr",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 6853,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 10113,
    "appId" : 4,
    "appModule" : "communication",
    "appName" : "communication",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 11366,
    "appId" : 68,
    "appModule" : "document",
    "appName" : "company-document",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 535,
    "appId" : 14,
    "appModule" : "logistics",
    "appName" : "logistics-fleets",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 538,
    "appId" : 17,
    "appModule" : "logistics",
    "appName" : "user-logistics-managements",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 536,
    "appId" : 15,
    "appModule" : "logistics",
    "appName" : "user-logistics-prices",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 740,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 705,
    "appId" : 18,
    "appModule" : "system",
    "appName" : "system",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 986,
    "appId" : 5,
    "appModule" : "admin",
    "appName" : "admin-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 5517,
    "appId" : 64,
    "appModule" : "job-tracking",
    "appName" : "job-tracking",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1791,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 3046,
    "appId" : 27,
    "appModule" : "logistics",
    "appName" : "user-partner-logistics-crm",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 534,
    "appId" : 13,
    "appModule" : "logistics",
    "appName" : "company-logistics-settings",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 537,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 13081,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14931,
    "appId" : 70,
    "appModule" : "hr",
    "appName" : "company-kpi",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14940,
    "appId" : 81,
    "appModule" : "logistics",
    "appName" : "company-logistics-prices",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 15467,
    "appId" : 25,
    "appModule" : "tms",
    "appName" : "user-vehicle-fleets",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:dan"
}, 220908 ]
2025-09-09T17:09:37.879+07:00 ERROR 68428 --- [qtp20********-430] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "cloud.datatp.fforwarder.sales.common.quote.CustomerSeaFCLPriceGroup.toMapObject()" because the return value of "cloud.datatp.fforwarder.sales.booking.entity.BookingSeaTransportCharge.getFclPriceGroup()" is null
	at cloud.datatp.fforwarder.sales.booking.dto.SellingRate.sellingRateCreator(SellingRate.java:75)
	at cloud.datatp.fforwarder.sales.booking.BookingLogic.getBooking(BookingLogic.java:195)
	at cloud.datatp.fforwarder.sales.booking.BookingService.getBooking(BookingService.java:27)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.sales.booking.BookingService$$SpringCGLIB$$0.getBooking(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-09T17:09:37.884+07:00  INFO 68428 --- [qtp20********-430] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint BookingService/getBooking
2025-09-09T17:09:45.859+07:00  INFO 68428 --- [qtp20********-236] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T17:09:45.862+07:00  INFO 68428 --- [qtp20********-100] n.d.module.session.ClientSessionManager  : Add a client session id = node0dtzwldgfqqdxnwslil4oy94d1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T17:09:45.868+07:00  INFO 68428 --- [qtp20********-100] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T17:09:45.868+07:00  INFO 68428 --- [qtp20********-236] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T17:09:47.825+07:00 ERROR 68428 --- [qtp20********-198] n.d.m.monitor.call.EndpointCallContext   : Start call with component BookingService, method getBooking, arguments
[ {
  "tenantId" : "default",
  "companyId" : 8,
  "companyParentId" : 4,
  "companyCode" : "beehph",
  "companyLabel" : "Bee HPH",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE",
  "loginId" : "dan",
  "accountId" : 3,
  "token" : "4672c14613cf4c2f497f824ff3c46925",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0dtzwldgfqqdxnwslil4oy94d1",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee-tw" : {
      "companyId" : 56,
      "companyParentId" : 4,
      "companyCode" : "bee-tw",
      "companyLabel" : "BEE TW",
      "companyFullName" : null
    },
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    },
    "thudo" : {
      "companyId" : 53,
      "companyParentId" : 0,
      "companyCode" : "thudo",
      "companyLabel" : "THUDO",
      "companyFullName" : null
    },
    "datatp" : {
      "companyId" : 10,
      "companyParentId" : 1,
      "companyCode" : "datatp",
      "companyLabel" : "DataTP Cloud",
      "companyFullName" : null
    },
    "beehan" : {
      "companyId" : 16,
      "companyParentId" : 4,
      "companyCode" : "beehan",
      "companyLabel" : "Bee HAN",
      "companyFullName" : null
    },
    "beescs" : {
      "companyId" : 12,
      "companyParentId" : 1,
      "companyCode" : "beescs",
      "companyLabel" : "BEESCS",
      "companyFullName" : null
    },
    "beedad" : {
      "companyId" : 18,
      "companyParentId" : 4,
      "companyCode" : "beedad",
      "companyLabel" : "Bee DAD",
      "companyFullName" : null
    },
    "hps" : {
      "companyId" : 51,
      "companyParentId" : 0,
      "companyCode" : "hps",
      "companyLabel" : "HPS",
      "companyFullName" : null
    },
    "marine" : {
      "companyId" : 52,
      "companyParentId" : 0,
      "companyCode" : "marine",
      "companyLabel" : "MARINE",
      "companyFullName" : null
    },
    "bonds" : {
      "companyId" : 54,
      "companyParentId" : 4,
      "companyCode" : "bonds",
      "companyLabel" : "BONDS",
      "companyFullName" : null
    },
    "tiendat" : {
      "companyId" : 55,
      "companyParentId" : 4,
      "companyCode" : "tiendat",
      "companyLabel" : "TIENDAT",
      "companyFullName" : null
    },
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    },
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 4516,
    "appId" : 21,
    "appModule" : "tms",
    "appName" : "user-tms-app",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 18276,
    "appId" : 88,
    "appModule" : "user",
    "appName" : "my-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 16821,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 3051,
    "appId" : 19,
    "appModule" : "sample",
    "appName" : "reactjs-lib",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14275,
    "appId" : 80,
    "appModule" : "logistics",
    "appName" : "company-logistics-sales",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 1591,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 6228,
    "appId" : 9,
    "appModule" : "company",
    "appName" : "company-hr",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 6853,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 10113,
    "appId" : 4,
    "appModule" : "communication",
    "appName" : "communication",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 11366,
    "appId" : 68,
    "appModule" : "document",
    "appName" : "company-document",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 535,
    "appId" : 14,
    "appModule" : "logistics",
    "appName" : "logistics-fleets",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 538,
    "appId" : 17,
    "appModule" : "logistics",
    "appName" : "user-logistics-managements",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 536,
    "appId" : 15,
    "appModule" : "logistics",
    "appName" : "user-logistics-prices",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 740,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 705,
    "appId" : 18,
    "appModule" : "system",
    "appName" : "system",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 986,
    "appId" : 5,
    "appModule" : "admin",
    "appName" : "admin-space",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 5517,
    "appId" : 64,
    "appModule" : "job-tracking",
    "appName" : "job-tracking",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1791,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 3046,
    "appId" : 27,
    "appModule" : "logistics",
    "appName" : "user-partner-logistics-crm",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 534,
    "appId" : 13,
    "appModule" : "logistics",
    "appName" : "company-logistics-settings",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 537,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 13081,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14931,
    "appId" : 70,
    "appModule" : "hr",
    "appName" : "company-kpi",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14940,
    "appId" : 81,
    "appModule" : "logistics",
    "appName" : "company-logistics-prices",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 15467,
    "appId" : 25,
    "appModule" : "tms",
    "appName" : "user-vehicle-fleets",
    "companyId" : 8,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:dan"
}, 220908 ]
2025-09-09T17:09:47.826+07:00 ERROR 68428 --- [qtp20********-198] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "cloud.datatp.fforwarder.sales.common.quote.CustomerSeaFCLPriceGroup.toMapObject()" because the return value of "cloud.datatp.fforwarder.sales.booking.entity.BookingSeaTransportCharge.getFclPriceGroup()" is null
	at cloud.datatp.fforwarder.sales.booking.dto.SellingRate.sellingRateCreator(SellingRate.java:75)
	at cloud.datatp.fforwarder.sales.booking.BookingLogic.getBooking(BookingLogic.java:195)
	at cloud.datatp.fforwarder.sales.booking.BookingService.getBooking(BookingService.java:27)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.sales.booking.BookingService$$SpringCGLIB$$0.getBooking(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-09T17:09:47.830+07:00  INFO 68428 --- [qtp20********-198] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint BookingService/getBooking
2025-09-09T17:09:53.746+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-09-09T17:09:53.755+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T17:10:06.772+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:10:06.774+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T17:11:04.879+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:11:53.999+07:00  INFO 68428 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 1
2025-09-09T17:11:54.015+07:00  INFO 68428 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T17:12:06.028+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:13:04.130+07:00  INFO 68428 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:13:12.422+07:00  INFO 68428 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@44c5fa4{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-09T17:13:12.424+07:00  INFO 68428 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-09T17:13:12.424+07:00  INFO 68428 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-09T17:13:12.424+07:00  INFO 68428 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-09T17:13:12.425+07:00  INFO 68428 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-09T17:13:12.425+07:00  INFO 68428 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-09T17:13:12.426+07:00  INFO 68428 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-09T17:13:12.426+07:00  INFO 68428 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-09T17:13:12.426+07:00  INFO 68428 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-09T17:13:12.426+07:00  INFO 68428 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-09T17:13:12.426+07:00  INFO 68428 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-09T17:13:12.426+07:00  INFO 68428 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-09T17:13:12.426+07:00  INFO 68428 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-09T17:13:12.426+07:00  INFO 68428 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-09T17:13:12.426+07:00  INFO 68428 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-09T17:13:12.426+07:00  INFO 68428 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-09T17:13:12.456+07:00  INFO 68428 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T17:13:12.538+07:00  INFO 68428 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-09T17:13:12.545+07:00  INFO 68428 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-09T17:13:12.578+07:00  INFO 68428 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-09T17:13:12.587+07:00  INFO 68428 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-09T17:13:12.600+07:00  INFO 68428 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-09T17:13:12.603+07:00  INFO 68428 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-09T17:13:12.606+07:00  INFO 68428 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-09T17:13:12.606+07:00  INFO 68428 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-09T17:13:12.606+07:00  INFO 68428 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-09T17:13:12.607+07:00  INFO 68428 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-09T17:13:12.607+07:00  INFO 68428 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-09T17:13:12.607+07:00  INFO 68428 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-09T17:13:12.607+07:00  INFO 68428 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-09T17:13:12.607+07:00  INFO 68428 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-09T17:13:12.607+07:00  INFO 68428 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-09T17:13:12.613+07:00  INFO 68428 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@7afffc81{STOPPING}[12.0.15,sto=0]
2025-09-09T17:13:12.618+07:00  INFO 68428 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-09T17:13:12.620+07:00  INFO 68428 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@7e4f5062{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.12193808350900233347/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@473ee461{STOPPED}}
