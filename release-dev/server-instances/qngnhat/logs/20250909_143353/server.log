2025-09-09T14:33:54.274+07:00  INFO 59843 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 59843 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-09T14:33:54.274+07:00  INFO 59843 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-09T14:33:55.018+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.084+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 64 ms. Found 22 JPA repository interfaces.
2025-09-09T14:33:55.093+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.094+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-09T14:33:55.094+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.102+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 10 JPA repository interfaces.
2025-09-09T14:33:55.103+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.106+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-09T14:33:55.151+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.156+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-09T14:33:55.164+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.166+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-09T14:33:55.166+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.170+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-09T14:33:55.172+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.176+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-09T14:33:55.179+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.182+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-09T14:33:55.183+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.183+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-09T14:33:55.183+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.189+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 10 JPA repository interfaces.
2025-09-09T14:33:55.193+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.196+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-09T14:33:55.199+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.203+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-09T14:33:55.203+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.211+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-09T14:33:55.211+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.214+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-09T14:33:55.214+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.215+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-09T14:33:55.215+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.216+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-09T14:33:55.216+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.220+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-09T14:33:55.221+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.222+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-09T14:33:55.222+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.222+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-09T14:33:55.222+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.232+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-09-09T14:33:55.241+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.247+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-09T14:33:55.247+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.250+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-09T14:33:55.250+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.254+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-09T14:33:55.254+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.259+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 9 JPA repository interfaces.
2025-09-09T14:33:55.259+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.262+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-09T14:33:55.263+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.270+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-09T14:33:55.270+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.278+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-09-09T14:33:55.279+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.292+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 24 JPA repository interfaces.
2025-09-09T14:33:55.293+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.293+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-09T14:33:55.298+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.299+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-09T14:33:55.299+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.306+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-09T14:33:55.308+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.343+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 34 ms. Found 65 JPA repository interfaces.
2025-09-09T14:33:55.343+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.344+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-09T14:33:55.349+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T14:33:55.352+07:00  INFO 59843 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-09T14:33:55.542+07:00  INFO 59843 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-09T14:33:55.546+07:00  INFO 59843 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-09T14:33:55.826+07:00  WARN 59843 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-09T14:33:56.031+07:00  INFO 59843 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-09T14:33:56.033+07:00  INFO 59843 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-09T14:33:56.045+07:00  INFO 59843 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-09T14:33:56.045+07:00  INFO 59843 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1619 ms
2025-09-09T14:33:56.096+07:00  WARN 59843 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-09T14:33:56.096+07:00  INFO 59843 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-09T14:33:56.194+07:00  INFO 59843 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@3cc7cf58
2025-09-09T14:33:56.195+07:00  INFO 59843 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-09T14:33:56.199+07:00  WARN 59843 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-09T14:33:56.200+07:00  INFO 59843 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-09T14:33:56.204+07:00  INFO 59843 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@31ee5a9
2025-09-09T14:33:56.204+07:00  INFO 59843 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-09T14:33:56.204+07:00  WARN 59843 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-09T14:33:56.204+07:00  INFO 59843 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-09T14:33:56.210+07:00  INFO 59843 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@26415b5e
2025-09-09T14:33:56.210+07:00  INFO 59843 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-09T14:33:56.211+07:00  WARN 59843 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-09T14:33:56.211+07:00  INFO 59843 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-09T14:33:56.216+07:00  INFO 59843 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@2746b1cc
2025-09-09T14:33:56.216+07:00  INFO 59843 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-09T14:33:56.216+07:00  WARN 59843 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-09T14:33:56.216+07:00  INFO 59843 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-09T14:33:56.223+07:00  INFO 59843 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@7d8802cf
2025-09-09T14:33:56.223+07:00  INFO 59843 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-09T14:33:56.224+07:00  INFO 59843 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-09T14:33:56.266+07:00  INFO 59843 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-09T14:33:56.268+07:00  INFO 59843 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@489ec791{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.2416372445452137078/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@24a38ef3{STARTED}}
2025-09-09T14:33:56.268+07:00  INFO 59843 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@489ec791{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.2416372445452137078/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@24a38ef3{STARTED}}
2025-09-09T14:33:56.269+07:00  INFO 59843 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@2ca9e25a{STARTING}[12.0.15,sto=0] @2525ms
2025-09-09T14:33:56.366+07:00  INFO 59843 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-09T14:33:56.391+07:00  INFO 59843 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-09T14:33:56.404+07:00  INFO 59843 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-09T14:33:56.525+07:00  INFO 59843 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-09T14:33:56.549+07:00  WARN 59843 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-09T14:33:57.150+07:00  INFO 59843 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-09T14:33:57.159+07:00  INFO 59843 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@3b8a02b6] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-09T14:33:57.286+07:00  INFO 59843 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-09T14:33:57.481+07:00  INFO 59843 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.tms", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-09T14:33:57.483+07:00  INFO 59843 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-09T14:33:57.490+07:00  INFO 59843 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-09T14:33:57.491+07:00  INFO 59843 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-09T14:33:57.515+07:00  INFO 59843 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-09T14:33:57.519+07:00  WARN 59843 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-09T14:33:59.562+07:00  INFO 59843 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-09T14:33:59.563+07:00  INFO 59843 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@84d2b8b] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-09T14:33:59.741+07:00  WARN 59843 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-09T14:33:59.741+07:00  WARN 59843 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-09T14:33:59.748+07:00  WARN 59843 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-09T14:33:59.748+07:00  WARN 59843 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-09T14:33:59.763+07:00  WARN 59843 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-09T14:33:59.763+07:00  WARN 59843 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-09T14:34:00.126+07:00  INFO 59843 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-09T14:34:00.132+07:00  INFO 59843 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-09T14:34:00.133+07:00  INFO 59843 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-09T14:34:00.155+07:00  INFO 59843 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-09T14:34:00.157+07:00  WARN 59843 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-09T14:34:00.720+07:00  INFO 59843 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-09T14:34:00.721+07:00  INFO 59843 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2b6a1b16] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-09T14:34:00.777+07:00  WARN 59843 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-09T14:34:00.777+07:00  WARN 59843 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-09T14:34:01.082+07:00  INFO 59843 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-09T14:34:01.113+07:00  INFO 59843 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-09T14:34:01.118+07:00  INFO 59843 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-09T14:34:01.118+07:00  INFO 59843 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T14:34:01.125+07:00  WARN 59843 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-09T14:34:01.261+07:00  INFO 59843 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-09T14:34:01.731+07:00  INFO 59843 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-09T14:34:01.735+07:00  INFO 59843 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-09T14:34:01.771+07:00  INFO 59843 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-09T14:34:01.821+07:00  INFO 59843 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-09T14:34:01.883+07:00  INFO 59843 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-09T14:34:01.911+07:00  INFO 59843 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-09T14:34:01.938+07:00  INFO 59843 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 1671625ms : this is harmless.
2025-09-09T14:34:01.946+07:00  INFO 59843 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-09T14:34:01.949+07:00  INFO 59843 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-09T14:34:01.963+07:00  INFO 59843 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 1671615ms : this is harmless.
2025-09-09T14:34:01.965+07:00  INFO 59843 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-09T14:34:01.977+07:00  INFO 59843 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-09T14:34:01.977+07:00  INFO 59843 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-09T14:34:04.156+07:00  INFO 59843 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-09T14:34:04.157+07:00  INFO 59843 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T14:34:04.157+07:00  WARN 59843 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-09T14:34:04.472+07:00  INFO 59843 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 09/09/2025@14:30:00+0700 to 09/09/2025@14:45:00+0700
2025-09-09T14:34:04.472+07:00  INFO 59843 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 09/09/2025@14:30:00+0700 to 09/09/2025@14:45:00+0700
2025-09-09T14:34:05.120+07:00  INFO 59843 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-09T14:34:05.120+07:00  INFO 59843 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T14:34:05.120+07:00  WARN 59843 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-09T14:34:05.384+07:00  INFO 59843 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-09T14:34:05.384+07:00  INFO 59843 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-09T14:34:05.384+07:00  INFO 59843 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-09T14:34:05.384+07:00  INFO 59843 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-09T14:34:05.385+07:00  INFO 59843 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-09T14:34:06.987+07:00  WARN 59843 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 35b3eee9-9e7b-43d9-b9f8-b8d73a301894

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-09T14:34:06.990+07:00  INFO 59843 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-09T14:34:07.296+07:00  INFO 59843 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-09T14:34:07.297+07:00  INFO 59843 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-09T14:34:07.297+07:00  INFO 59843 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-09T14:34:07.297+07:00  INFO 59843 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-09T14:34:07.297+07:00  INFO 59843 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-09T14:34:07.297+07:00  INFO 59843 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-09T14:34:07.297+07:00  INFO 59843 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-09T14:34:07.297+07:00  INFO 59843 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-09T14:34:07.297+07:00  INFO 59843 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-09T14:34:07.297+07:00  INFO 59843 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-09T14:34:07.297+07:00  INFO 59843 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-09T14:34:07.297+07:00  INFO 59843 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-09T14:34:07.300+07:00  INFO 59843 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-09T14:34:07.300+07:00  INFO 59843 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-09T14:34:07.300+07:00  INFO 59843 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-09T14:34:07.364+07:00  INFO 59843 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-09T14:34:07.364+07:00  INFO 59843 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-09T14:34:07.365+07:00  INFO 59843 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-09T14:34:07.373+07:00  INFO 59843 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@6785a2c{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-09T14:34:07.374+07:00  INFO 59843 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-09T14:34:07.375+07:00  INFO 59843 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-09T14:34:07.399+07:00  INFO 59843 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-09T14:34:07.399+07:00  INFO 59843 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-09T14:34:07.405+07:00  INFO 59843 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.43 seconds (process running for 13.661)
2025-09-09T14:35:03.424+07:00  INFO 59843 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T14:35:03.429+07:00  INFO 59843 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T14:35:10.444+07:00  INFO 59843 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T14:35:10.447+07:00  INFO 59843 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T14:35:18.299+07:00  INFO 59843 --- [qtp1109150782-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01exoe6nto48lk3fm24qn6u5oa0
2025-09-09T14:35:18.299+07:00  INFO 59843 --- [qtp1109150782-34] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node011styro680zp29plys4mjdkx71
2025-09-09T14:35:18.666+07:00  INFO 59843 --- [qtp1109150782-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01exoe6nto48lk3fm24qn6u5oa0, token = 76228d850b3efc0bd6f0238233a52890
2025-09-09T14:35:18.667+07:00  INFO 59843 --- [qtp1109150782-34] n.d.module.session.ClientSessionManager  : Add a client session id = node011styro680zp29plys4mjdkx71, token = 76228d850b3efc0bd6f0238233a52890
2025-09-09T14:35:19.142+07:00  INFO 59843 --- [qtp1109150782-34] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T14:35:19.146+07:00  INFO 59843 --- [qtp1109150782-39] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T14:35:32.665+07:00  INFO 59843 --- [qtp1109150782-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01exoe6nto48lk3fm24qn6u5oa0, token = 76228d850b3efc0bd6f0238233a52890
2025-09-09T14:35:32.676+07:00  INFO 59843 --- [qtp1109150782-39] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T14:35:32.693+07:00  INFO 59843 --- [qtp1109150782-37] n.d.module.session.ClientSessionManager  : Add a client session id = node01exoe6nto48lk3fm24qn6u5oa0, token = 76228d850b3efc0bd6f0238233a52890
2025-09-09T14:35:32.699+07:00  INFO 59843 --- [qtp1109150782-37] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T14:36:06.546+07:00  INFO 59843 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T14:37:02.633+07:00  INFO 59843 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T14:37:09.690+07:00  INFO 59843 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-09T14:37:09.712+07:00  INFO 59843 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T14:38:05.806+07:00  INFO 59843 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T14:38:55.758+07:00  INFO 59843 --- [qtp1109150782-37] n.d.module.session.ClientSessionManager  : Add a client session id = node01exoe6nto48lk3fm24qn6u5oa0, token = 76228d850b3efc0bd6f0238233a52890
2025-09-09T14:38:55.768+07:00  INFO 59843 --- [qtp1109150782-37] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T14:38:55.783+07:00  INFO 59843 --- [qtp1109150782-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01exoe6nto48lk3fm24qn6u5oa0, token = 76228d850b3efc0bd6f0238233a52890
2025-09-09T14:38:55.797+07:00  INFO 59843 --- [qtp1109150782-35] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T14:39:02.397+07:00  INFO 59843 --- [qtp1109150782-34] n.d.module.session.ClientSessionManager  : Add a client session id = node01exoe6nto48lk3fm24qn6u5oa0, token = 76228d850b3efc0bd6f0238233a52890
2025-09-09T14:39:02.407+07:00  INFO 59843 --- [qtp1109150782-34] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T14:39:02.447+07:00  INFO 59843 --- [qtp1109150782-66] n.d.module.session.ClientSessionManager  : Add a client session id = node01exoe6nto48lk3fm24qn6u5oa0, token = 76228d850b3efc0bd6f0238233a52890
2025-09-09T14:39:02.456+07:00  INFO 59843 --- [qtp1109150782-66] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T14:39:06.911+07:00  INFO 59843 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T14:39:13.959+07:00  INFO 59843 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-09T14:39:13.990+07:00  INFO 59843 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T14:40:05.093+07:00  INFO 59843 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T14:40:05.097+07:00  INFO 59843 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T14:40:18.855+07:00  INFO 59843 --- [qtp1109150782-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01exoe6nto48lk3fm24qn6u5oa0, token = 76228d850b3efc0bd6f0238233a52890
2025-09-09T14:40:18.856+07:00  INFO 59843 --- [qtp1109150782-37] n.d.module.session.ClientSessionManager  : Add a client session id = node01exoe6nto48lk3fm24qn6u5oa0, token = 76228d850b3efc0bd6f0238233a52890
2025-09-09T14:40:18.865+07:00  INFO 59843 --- [qtp1109150782-37] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T14:40:18.865+07:00  INFO 59843 --- [qtp1109150782-35] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T14:40:23.432+07:00  INFO 59843 --- [qtp1109150782-67] n.d.module.session.ClientSessionManager  : Add a client session id = node01exoe6nto48lk3fm24qn6u5oa0, token = 76228d850b3efc0bd6f0238233a52890
2025-09-09T14:40:23.433+07:00  INFO 59843 --- [qtp1109150782-72] n.d.module.session.ClientSessionManager  : Add a client session id = node01exoe6nto48lk3fm24qn6u5oa0, token = 76228d850b3efc0bd6f0238233a52890
2025-09-09T14:40:23.442+07:00  INFO 59843 --- [qtp1109150782-72] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T14:40:23.444+07:00  INFO 59843 --- [qtp1109150782-67] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T14:41:06.199+07:00  INFO 59843 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T14:41:09.991+07:00  INFO 59843 --- [qtp1109150782-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01exoe6nto48lk3fm24qn6u5oa0, token = 76228d850b3efc0bd6f0238233a52890
2025-09-09T14:41:09.991+07:00  INFO 59843 --- [qtp1109150782-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01exoe6nto48lk3fm24qn6u5oa0, token = 76228d850b3efc0bd6f0238233a52890
2025-09-09T14:41:10.004+07:00  INFO 59843 --- [qtp1109150782-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T14:41:10.010+07:00  INFO 59843 --- [qtp1109150782-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T14:41:14.249+07:00  INFO 59843 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-09T14:41:14.259+07:00  INFO 59843 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T14:42:04.351+07:00  INFO 59843 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T14:43:06.455+07:00  INFO 59843 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T14:43:13.472+07:00  INFO 59843 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T14:43:13.478+07:00  INFO 59843 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T14:44:03.562+07:00  INFO 59843 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T14:44:56.288+07:00  INFO 59843 --- [Scheduler-1335528817-1] n.d.m.session.AppHttpSessionListener     : The session node011styro680zp29plys4mjdkx71 is destroyed.
2025-09-09T14:45:06.673+07:00  INFO 59843 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-09T14:45:06.684+07:00  INFO 59843 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 09/09/2025@14:45:06+0700
2025-09-09T14:45:06.716+07:00  INFO 59843 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 09/09/2025@14:45:00+0700 to 09/09/2025@15:00:00+0700
2025-09-09T14:45:06.717+07:00  INFO 59843 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 09/09/2025@14:45:00+0700 to 09/09/2025@15:00:00+0700
2025-09-09T14:45:06.717+07:00  INFO 59843 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T14:45:06.717+07:00  INFO 59843 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T14:45:11.090+07:00  INFO 59843 --- [qtp1109150782-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-09T14:45:13.750+07:00  INFO 59843 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-09T14:45:13.760+07:00  INFO 59843 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T14:46:02.818+07:00  INFO 59843 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T14:46:11.918+07:00  INFO 59843 --- [qtp1109150782-72] n.d.module.session.ClientSessionManager  : Add a client session id = node01exoe6nto48lk3fm24qn6u5oa0, token = 76228d850b3efc0bd6f0238233a52890
2025-09-09T14:46:11.944+07:00  INFO 59843 --- [qtp1109150782-72] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T14:46:11.964+07:00  INFO 59843 --- [qtp1109150782-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01exoe6nto48lk3fm24qn6u5oa0, token = 76228d850b3efc0bd6f0238233a52890
2025-09-09T14:46:11.979+07:00  INFO 59843 --- [qtp1109150782-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T14:47:05.929+07:00  INFO 59843 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T14:47:12.968+07:00  INFO 59843 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-09T14:47:12.980+07:00  INFO 59843 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T14:48:02.080+07:00  INFO 59843 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T14:49:05.189+07:00  INFO 59843 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T14:49:12.243+07:00  INFO 59843 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-09T14:49:12.252+07:00  INFO 59843 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T14:50:06.330+07:00  INFO 59843 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T14:50:06.333+07:00  INFO 59843 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T14:50:07.504+07:00  INFO 59843 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@6785a2c{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-09T14:50:07.505+07:00  INFO 59843 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-09T14:50:07.506+07:00  INFO 59843 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-09T14:50:07.506+07:00  INFO 59843 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-09T14:50:07.506+07:00  INFO 59843 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-09T14:50:07.507+07:00  INFO 59843 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-09T14:50:07.507+07:00  INFO 59843 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-09T14:50:07.507+07:00  INFO 59843 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-09T14:50:07.507+07:00  INFO 59843 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-09T14:50:07.507+07:00  INFO 59843 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-09T14:50:07.507+07:00  INFO 59843 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-09T14:50:07.507+07:00  INFO 59843 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-09T14:50:07.507+07:00  INFO 59843 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-09T14:50:07.507+07:00  INFO 59843 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-09T14:50:07.507+07:00  INFO 59843 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-09T14:50:07.507+07:00  INFO 59843 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-09T14:50:07.524+07:00  INFO 59843 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T14:50:07.604+07:00  INFO 59843 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-09T14:50:07.610+07:00  INFO 59843 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-09T14:50:07.633+07:00  INFO 59843 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-09T14:50:07.635+07:00  INFO 59843 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-09T14:50:07.638+07:00  INFO 59843 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-09T14:50:07.638+07:00  INFO 59843 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-09T14:50:07.640+07:00  INFO 59843 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-09T14:50:07.640+07:00  INFO 59843 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-09T14:50:07.640+07:00  INFO 59843 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-09T14:50:07.640+07:00  INFO 59843 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-09T14:50:07.640+07:00  INFO 59843 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-09T14:50:07.640+07:00  INFO 59843 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-09T14:50:07.642+07:00  INFO 59843 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-09T14:50:07.642+07:00  INFO 59843 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-09T14:50:07.642+07:00  INFO 59843 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-09T14:50:07.648+07:00  INFO 59843 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@2ca9e25a{STOPPING}[12.0.15,sto=0]
2025-09-09T14:50:07.650+07:00  INFO 59843 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-09T14:50:07.655+07:00  INFO 59843 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@489ec791{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.2416372445452137078/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@24a38ef3{STOPPED}}
