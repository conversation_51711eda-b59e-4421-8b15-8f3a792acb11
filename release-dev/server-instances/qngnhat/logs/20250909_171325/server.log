2025-09-09T17:13:26.524+07:00  INFO 79345 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 79345 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-09T17:13:26.525+07:00  INFO 79345 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-09T17:13:27.232+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.297+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 61 ms. Found 22 JPA repository interfaces.
2025-09-09T17:13:27.306+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.308+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-09T17:13:27.308+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.316+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 10 JPA repository interfaces.
2025-09-09T17:13:27.317+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.320+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-09T17:13:27.367+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.372+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-09T17:13:27.381+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.383+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-09T17:13:27.383+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.388+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-09T17:13:27.391+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.395+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-09T17:13:27.399+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.402+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-09T17:13:27.402+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.403+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-09T17:13:27.404+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.416+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 10 JPA repository interfaces.
2025-09-09T17:13:27.422+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.425+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-09T17:13:27.429+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.439+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 6 JPA repository interfaces.
2025-09-09T17:13:27.439+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.452+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 12 JPA repository interfaces.
2025-09-09T17:13:27.452+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.455+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-09T17:13:27.456+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.456+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-09T17:13:27.456+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.457+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-09T17:13:27.458+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.463+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-09T17:13:27.463+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.465+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-09T17:13:27.465+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.465+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-09T17:13:27.465+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.478+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 19 JPA repository interfaces.
2025-09-09T17:13:27.489+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.496+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-09-09T17:13:27.496+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.500+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-09T17:13:27.500+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.504+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-09T17:13:27.505+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.511+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-09-09T17:13:27.512+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.521+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 6 JPA repository interfaces.
2025-09-09T17:13:27.521+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.531+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 12 JPA repository interfaces.
2025-09-09T17:13:27.531+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.542+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 14 JPA repository interfaces.
2025-09-09T17:13:27.542+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.558+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 15 ms. Found 24 JPA repository interfaces.
2025-09-09T17:13:27.558+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.559+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-09T17:13:27.564+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.565+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-09T17:13:27.566+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.573+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-09T17:13:27.575+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.613+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 37 ms. Found 65 JPA repository interfaces.
2025-09-09T17:13:27.613+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.614+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-09T17:13:27.619+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-09T17:13:27.621+07:00  INFO 79345 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-09T17:13:27.819+07:00  INFO 79345 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-09T17:13:27.823+07:00  INFO 79345 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-09T17:13:28.095+07:00  WARN 79345 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-09T17:13:28.316+07:00  INFO 79345 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-09T17:13:28.318+07:00  INFO 79345 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-09T17:13:28.333+07:00  INFO 79345 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-09T17:13:28.333+07:00  INFO 79345 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1670 ms
2025-09-09T17:13:28.391+07:00  WARN 79345 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-09T17:13:28.391+07:00  INFO 79345 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-09T17:13:28.510+07:00  INFO 79345 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@44bbf5e3
2025-09-09T17:13:28.510+07:00  INFO 79345 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-09T17:13:28.515+07:00  WARN 79345 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-09T17:13:28.516+07:00  INFO 79345 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-09T17:13:28.528+07:00  INFO 79345 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@3f04847e
2025-09-09T17:13:28.528+07:00  INFO 79345 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-09T17:13:28.528+07:00  WARN 79345 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-09T17:13:28.528+07:00  INFO 79345 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-09T17:13:28.537+07:00  INFO 79345 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@4a3d4cd2
2025-09-09T17:13:28.537+07:00  INFO 79345 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-09T17:13:28.537+07:00  WARN 79345 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-09T17:13:28.537+07:00  INFO 79345 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-09T17:13:28.547+07:00  INFO 79345 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@3d1b6816
2025-09-09T17:13:28.548+07:00  INFO 79345 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-09T17:13:28.548+07:00  WARN 79345 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-09T17:13:28.548+07:00  INFO 79345 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-09T17:13:28.554+07:00  INFO 79345 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@4f9871a2
2025-09-09T17:13:28.555+07:00  INFO 79345 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-09T17:13:28.555+07:00  INFO 79345 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-09T17:13:28.605+07:00  INFO 79345 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-09T17:13:28.607+07:00  INFO 79345 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@18c2b4c6{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16760089426214328972/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@461e9b31{STARTED}}
2025-09-09T17:13:28.608+07:00  INFO 79345 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@18c2b4c6{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16760089426214328972/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@461e9b31{STARTED}}
2025-09-09T17:13:28.610+07:00  INFO 79345 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@7db53694{STARTING}[12.0.15,sto=0] @2906ms
2025-09-09T17:13:28.719+07:00  INFO 79345 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-09T17:13:28.747+07:00  INFO 79345 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-09T17:13:28.762+07:00  INFO 79345 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-09T17:13:28.895+07:00  INFO 79345 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-09T17:13:29.005+07:00  WARN 79345 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-09T17:13:29.659+07:00  INFO 79345 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-09T17:13:29.667+07:00  INFO 79345 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@79eaf64] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-09T17:13:29.839+07:00  INFO 79345 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-09T17:13:30.089+07:00  INFO 79345 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.tms", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "net.datatp.module.core.print", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-09-09T17:13:30.091+07:00  INFO 79345 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-09T17:13:30.098+07:00  INFO 79345 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-09T17:13:30.100+07:00  INFO 79345 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-09T17:13:30.125+07:00  INFO 79345 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-09T17:13:30.133+07:00  WARN 79345 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-09T17:13:32.324+07:00  INFO 79345 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-09T17:13:32.325+07:00  INFO 79345 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@5b248b97] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-09T17:13:32.593+07:00  WARN 79345 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-09T17:13:32.593+07:00  WARN 79345 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-09T17:13:32.610+07:00  WARN 79345 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-09T17:13:32.610+07:00  WARN 79345 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-09T17:13:32.630+07:00  WARN 79345 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-09T17:13:32.630+07:00  WARN 79345 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-09T17:13:33.206+07:00  INFO 79345 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-09T17:13:33.213+07:00  INFO 79345 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-09T17:13:33.214+07:00  INFO 79345 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-09T17:13:33.234+07:00  INFO 79345 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-09T17:13:33.239+07:00  WARN 79345 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-09T17:13:33.797+07:00  INFO 79345 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-09T17:13:33.797+07:00  INFO 79345 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2cdb59d3] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-09T17:13:34.027+07:00  WARN 79345 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-09T17:13:34.028+07:00  WARN 79345 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-09T17:13:34.790+07:00  INFO 79345 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-09T17:13:34.836+07:00  INFO 79345 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-09T17:13:34.842+07:00  INFO 79345 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-09T17:13:34.843+07:00  INFO 79345 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T17:13:34.851+07:00  WARN 79345 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-09T17:13:35.040+07:00  INFO 79345 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-09T17:13:35.544+07:00  INFO 79345 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-09T17:13:35.547+07:00  INFO 79345 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-09T17:13:35.583+07:00  INFO 79345 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-09T17:13:35.630+07:00  INFO 79345 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-09T17:13:35.689+07:00  INFO 79345 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-09T17:13:35.722+07:00  INFO 79345 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-09T17:13:35.748+07:00  INFO 79345 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 11242546ms : this is harmless.
2025-09-09T17:13:35.757+07:00  INFO 79345 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-09T17:13:35.761+07:00  INFO 79345 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-09T17:13:35.776+07:00  INFO 79345 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 11242537ms : this is harmless.
2025-09-09T17:13:35.777+07:00  INFO 79345 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-09T17:13:35.791+07:00  INFO 79345 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-09T17:13:35.792+07:00  INFO 79345 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-09T17:13:37.692+07:00  INFO 79345 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-09T17:13:37.692+07:00  INFO 79345 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T17:13:37.693+07:00  WARN 79345 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-09T17:13:37.970+07:00  INFO 79345 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 09/09/2025@17:00:00+0700 to 09/09/2025@17:15:00+0700
2025-09-09T17:13:37.970+07:00  INFO 79345 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 09/09/2025@17:00:00+0700 to 09/09/2025@17:15:00+0700
2025-09-09T17:13:38.424+07:00  INFO 79345 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-09T17:13:38.424+07:00  INFO 79345 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T17:13:38.424+07:00  WARN 79345 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-09T17:13:38.723+07:00  INFO 79345 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-09T17:13:38.723+07:00  INFO 79345 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-09T17:13:38.723+07:00  INFO 79345 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-09T17:13:38.723+07:00  INFO 79345 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-09T17:13:38.723+07:00  INFO 79345 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-09T17:13:40.632+07:00  WARN 79345 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: c0cd8720-57c7-426d-a92d-9ec844425066

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-09T17:13:40.636+07:00  INFO 79345 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-09T17:13:40.951+07:00  INFO 79345 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-09T17:13:40.951+07:00  INFO 79345 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-09T17:13:40.951+07:00  INFO 79345 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-09T17:13:40.951+07:00  INFO 79345 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-09T17:13:40.951+07:00  INFO 79345 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-09T17:13:40.951+07:00  INFO 79345 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-09T17:13:40.951+07:00  INFO 79345 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-09T17:13:40.951+07:00  INFO 79345 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-09T17:13:40.951+07:00  INFO 79345 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-09T17:13:40.951+07:00  INFO 79345 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-09T17:13:40.951+07:00  INFO 79345 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-09T17:13:40.951+07:00  INFO 79345 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-09T17:13:40.954+07:00  INFO 79345 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-09T17:13:40.954+07:00  INFO 79345 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-09T17:13:40.954+07:00  INFO 79345 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-09T17:13:41.066+07:00  INFO 79345 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-09T17:13:41.067+07:00  INFO 79345 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-09T17:13:41.068+07:00  INFO 79345 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-09T17:13:41.076+07:00  INFO 79345 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@4f32c0ca{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-09T17:13:41.077+07:00  INFO 79345 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-09T17:13:41.077+07:00  INFO 79345 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-09T17:13:41.110+07:00  INFO 79345 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-09T17:13:41.110+07:00  INFO 79345 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-09T17:13:41.116+07:00  INFO 79345 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 15.038 seconds (process running for 15.411)
2025-09-09T17:13:47.246+07:00  INFO 79345 --- [qtp1346508543-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-09T17:14:07.004+07:00  INFO 79345 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:14:44.147+07:00  INFO 79345 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-09T17:14:44.174+07:00  INFO 79345 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T17:14:48.487+07:00  INFO 79345 --- [qtp1346508543-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01gyykkqyihzej9rqja44ihf2z1
2025-09-09T17:14:48.487+07:00  INFO 79345 --- [qtp1346508543-40] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01qf3owy42n5pa1hn49663r257z0
2025-09-09T17:14:48.580+07:00  INFO 79345 --- [qtp1346508543-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01qf3owy42n5pa1hn49663r257z0, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T17:14:48.580+07:00  INFO 79345 --- [qtp1346508543-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01gyykkqyihzej9rqja44ihf2z1, token = 4672c14613cf4c2f497f824ff3c46925
2025-09-09T17:14:49.088+07:00  INFO 79345 --- [qtp1346508543-35] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T17:14:49.094+07:00  INFO 79345 --- [qtp1346508543-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-09T17:15:05.210+07:00  INFO 79345 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:15:05.214+07:00  INFO 79345 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T17:15:05.215+07:00  INFO 79345 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-09T17:15:05.219+07:00  INFO 79345 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 09/09/2025@17:15:05+0700
2025-09-09T17:15:05.237+07:00  INFO 79345 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 09/09/2025@17:15:00+0700 to 09/09/2025@17:30:00+0700
2025-09-09T17:15:05.237+07:00  INFO 79345 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 09/09/2025@17:15:00+0700 to 09/09/2025@17:30:00+0700
2025-09-09T17:16:06.346+07:00  INFO 79345 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:16:43.434+07:00  INFO 79345 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-09-09T17:16:43.450+07:00  INFO 79345 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-09T17:17:04.499+07:00  INFO 79345 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:17:52.293+07:00  INFO 79345 --- [qtp1346508543-34] n.d.m.c.a.CompanyAuthenticationService   : User dan logout successfully 
2025-09-09T17:17:56.584+07:00  INFO 79345 --- [qtp1346508543-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01qf3owy42n5pa1hn49663r257z0, token = 37898d400cb40608afb7ae11c8408759
2025-09-09T17:17:56.601+07:00  INFO 79345 --- [qtp1346508543-41] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-09T17:18:06.621+07:00  INFO 79345 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:18:12.450+07:00  INFO 79345 --- [qtp1346508543-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T17:18:12.450+07:00  INFO 79345 --- [qtp1346508543-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T17:18:12.925+07:00  INFO 79345 --- [qtp1346508543-64] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T17:18:12.941+07:00  INFO 79345 --- [qtp1346508543-34] c.d.f.sales.integration.BFSOneCRMLogic   : Retrieved 0 records
2025-09-09T17:18:33.970+07:00  INFO 79345 --- [qtp1346508543-40] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: HPMINHTV
2025-09-09T17:18:34.212+07:00  INFO 79345 --- [qtp1346508543-40] c.d.f.core.integration.BFSOneApi         : ---------------- Create IBooking -----------------------
2025-09-09T17:18:40.520+07:00  INFO 79345 --- [qtp1346508543-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T17:18:40.523+07:00  INFO 79345 --- [qtp1346508543-61] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-09T17:18:41.074+07:00  INFO 79345 --- [qtp1346508543-64] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-09T17:18:41.074+07:00  INFO 79345 --- [qtp1346508543-61] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-09T17:18:47.727+07:00  INFO 79345 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 16, expire count 0
2025-09-09T17:18:47.751+07:00  INFO 79345 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T17:19:03.787+07:00  INFO 79345 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:20:06.901+07:00  INFO 79345 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:20:06.904+07:00  INFO 79345 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T17:20:48.005+07:00  INFO 79345 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-09T17:20:48.027+07:00  INFO 79345 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-09T17:21:03.053+07:00  INFO 79345 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:22:06.158+07:00  INFO 79345 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:22:47.262+07:00  INFO 79345 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-09T17:22:47.267+07:00  INFO 79345 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T17:23:02.300+07:00  INFO 79345 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:23:28.637+07:00  INFO 79345 --- [Scheduler-881590489-1] n.d.m.session.AppHttpSessionListener     : The session node01gyykkqyihzej9rqja44ihf2z1 is destroyed.
2025-09-09T17:24:05.410+07:00  INFO 79345 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:24:47.512+07:00  INFO 79345 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-09T17:24:47.530+07:00  INFO 79345 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-09T17:25:06.560+07:00  INFO 79345 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:25:06.562+07:00  INFO 79345 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T17:26:04.646+07:00  INFO 79345 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:26:46.721+07:00  INFO 79345 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 8
2025-09-09T17:26:46.735+07:00  INFO 79345 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T17:27:06.762+07:00  INFO 79345 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:28:03.853+07:00  INFO 79345 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:28:45.954+07:00  INFO 79345 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 15
2025-09-09T17:28:45.980+07:00  INFO 79345 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T17:29:06.018+07:00  INFO 79345 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:30:03.124+07:00  INFO 79345 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:30:03.128+07:00  INFO 79345 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-09T17:30:03.128+07:00  INFO 79345 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-09T17:30:03.132+07:00  INFO 79345 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 09/09/2025@17:30:03+0700
2025-09-09T17:30:03.176+07:00  INFO 79345 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 09/09/2025@17:30:00+0700 to 09/09/2025@17:45:00+0700
2025-09-09T17:30:03.176+07:00  INFO 79345 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 09/09/2025@17:30:00+0700 to 09/09/2025@17:45:00+0700
2025-09-09T17:30:45.259+07:00  INFO 79345 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 2
2025-09-09T17:30:45.267+07:00  INFO 79345 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T17:31:06.308+07:00  INFO 79345 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-09T17:31:37.199+07:00  INFO 79345 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@4f32c0ca{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-09T17:31:37.200+07:00  INFO 79345 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-09T17:31:37.200+07:00  INFO 79345 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-09T17:31:37.200+07:00  INFO 79345 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-09T17:31:37.200+07:00  INFO 79345 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-09T17:31:37.200+07:00  INFO 79345 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-09T17:31:37.200+07:00  INFO 79345 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-09T17:31:37.200+07:00  INFO 79345 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-09T17:31:37.200+07:00  INFO 79345 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-09T17:31:37.201+07:00  INFO 79345 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-09T17:31:37.201+07:00  INFO 79345 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-09T17:31:37.201+07:00  INFO 79345 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-09T17:31:37.201+07:00  INFO 79345 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-09T17:31:37.201+07:00  INFO 79345 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-09T17:31:37.201+07:00  INFO 79345 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-09T17:31:37.201+07:00  INFO 79345 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-09T17:31:37.216+07:00  INFO 79345 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-09T17:31:37.284+07:00  INFO 79345 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-09T17:31:37.289+07:00  INFO 79345 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-09T17:31:37.309+07:00  INFO 79345 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-09T17:31:37.310+07:00  INFO 79345 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-09T17:31:37.311+07:00  INFO 79345 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-09T17:31:37.312+07:00  INFO 79345 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-09T17:31:37.313+07:00  INFO 79345 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-09T17:31:37.313+07:00  INFO 79345 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-09T17:31:37.314+07:00  INFO 79345 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-09T17:31:37.314+07:00  INFO 79345 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-09T17:31:37.314+07:00  INFO 79345 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-09T17:31:37.315+07:00  INFO 79345 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-09T17:31:37.315+07:00  INFO 79345 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-09T17:31:37.315+07:00  INFO 79345 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-09T17:31:37.315+07:00  INFO 79345 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-09T17:31:37.318+07:00  INFO 79345 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@7db53694{STOPPING}[12.0.15,sto=0]
2025-09-09T17:31:37.321+07:00  INFO 79345 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-09T17:31:37.322+07:00  INFO 79345 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@18c2b4c6{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16760089426214328972/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@461e9b31{STOPPED}}
