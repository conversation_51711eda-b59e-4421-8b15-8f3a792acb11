"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[3145],{494:(n,e,i)=>{i.d(e,{R:()=>h,x:()=>c});var t=i(6372);const l={},d=t.createContext(l);function h(n){const e=t.useContext(d);return t.useMemo(function(){return"function"==typeof n?n(e):{...e,...n}},[e,n])}function c(n){let e;return e=n.disableParentContext?"function"==typeof n.components?n.components(l):n.components||l:h(n.components),t.createElement(d.Provider,{value:e},n.children)}},4166:(n,e,i)=>{i.r(e),i.d(e,{assets:()=>s,contentTitle:()=>h,default:()=>a,frontMatter:()=>d,metadata:()=>c,toc:()=>r});var t=i(216),l=i(494);const d={sidebar_position:2,hide_table_of_contents:!0,displayed_sidebar:"developerSidebar"},h="Changelog",c={id:"shared/developer/CHANGELOG",title:"Changelog",description:"All notable changes to this project will be documented in this file.",source:"@site/i18n/vi/docusaurus-plugin-content-docs/current/shared/developer/CHANGELOG.md",sourceDirName:"shared/developer",slug:"/shared/developer/CHANGELOG",permalink:"/docs/shared/developer/CHANGELOG",draft:!1,unlisted:!1,tags:[],version:"current",sidebarPosition:2,frontMatter:{sidebar_position:2,hide_table_of_contents:!0,displayed_sidebar:"developerSidebar"},sidebar:"developerSidebar"},s={},r=[{value:"[R20250621]",id:"r20250621",level:3},{value:"[R20250621]",id:"r20250621-1",level:3},{value:"[R20250620]",id:"r20250620",level:3},{value:"[*********]",id:"r20250618",level:3},{value:"[*********]",id:"r20250618-1",level:3},{value:"[R20250617]",id:"r20250617",level:3},{value:"[R20250616]",id:"r20250616",level:3},{value:"[R20250612]",id:"r20250612",level:3},{value:"[R20250612]",id:"r20250612-1",level:3},{value:"[R20250612]",id:"r20250612-2",level:3},{value:"[R20250611]",id:"r20250611",level:3},{value:"[R20250610]",id:"r20250610",level:3}];function o(n){const e={a:"a",code:"code",h1:"h1",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",ul:"ul",...(0,l.R)(),...n.components};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(e.header,{children:(0,t.jsx)(e.h1,{id:"changelog",children:"Changelog"})}),"\n",(0,t.jsx)(e.p,{children:"All notable changes to this project will be documented in this file."}),"\n",(0,t.jsx)(e.h3,{id:"r20250621",children:"[R20250621]"}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt nh\xe1nh develop"}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt d\u1ef1 \xe1n datatp-crm:"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsxs)(e.li,{children:["Checkout code (nh\xe1nh develop) v\xe0 setup:  git@gitlab",":datatp",".net",":tuan","/datatp-crm.git"]}),"\n",(0,t.jsx)(e.li,{children:"Remove node_modules pnpm-lock.yaml dist v\xe0 pnpm install && pnpm run build \u1edf c\xe1c d\u1ef1 \xe1n lib, erp, document-ie, logistics, crm, phoenix."}),"\n",(0,t.jsxs)(e.li,{children:["Build java code b\u1eb1ng command: ./datatp.sh lgc",":build"," -clean -build"]}),"\n",(0,t.jsx)(e.li,{children:"Run code nh\u01b0 b\xecnh th\u01b0\u1eddng."}),"\n"]}),"\n",(0,t.jsxs)(e.p,{children:["C\xf3 th\u1ec3 download db m\u1edbi t\u1ea1i \u0111\u01b0\u1eddng d\u1eabn ",(0,t.jsx)(e.a,{href:"https://beelogistics.cloud/download/datatpdb-latest.dump",children:"https://beelogistics.cloud/download/datatpdb-latest.dump"})]}),"\n",(0,t.jsx)(e.h3,{id:"r20250621-1",children:"[R20250621]"}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt nh\xe1nh document-ie:"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"Em clear b\u1ea3ng n\u1eb1m tr\xean database c\u0169 v\xe0 fix bug b\xean doument set \u1ea1"}),"\n"]}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt nh\xe1nh tms:\n[Qu\xe2n] :"}),"\n",(0,t.jsxs)(e.ol,{children:["\n",(0,t.jsx)(e.li,{children:"Cho ph\xe9p cus t\u1ef1 l\xean th\xf4ng tin xe thay \u0111i\u1ec1u v\u1eadn:"}),"\n"]}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"Giao di\u1ec7n TMSBillList hi\u1ec3n th\u1ecb popup c\xe1c tab VehicleTrip Form cho ph\xe9p nh\u1eadp th\xf4ng tin l\xf4 h\xe0ng"}),"\n"]}),"\n",(0,t.jsxs)(e.ol,{start:"2",children:["\n",(0,t.jsx)(e.li,{children:"T\u1ef1 \u0111\u1ed9ng gh\xe9p chuy\u1ebfn cho c\xe1c l\xf4 h\xe0ng theo request c\u1ee7a cus:"}),"\n"]}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"Giao di\u1ec7n: Th\xeam 1 checkbox Combine Trip t\u1ea1i popup Processing Goods Request"}),"\n",(0,t.jsx)(e.li,{children:"Backend: T\u1ea1o VehicleTrip chung cho c\xe1c VehicleTripGoodsTracking \u0111\u01b0\u1ee3c t\u1ea1o t\u1eeb c\xe1c TMSBill request."}),"\n"]}),"\n",(0,t.jsxs)(e.ol,{start:"3",children:["\n",(0,t.jsx)(e.li,{children:"Export template xu\u1ea5t th\xf4ng tin xe cho BFSOne theo feedback c\u1ee7a ng\u01b0\u1eddi d\xf9ng.\nTh\xeam note c\u1ea3nh b\xe1o c\xe1c l\xf4 ch\u01b0a \u0111\u1ee7 \u0111i\u1ec1u ki\u1ec7n thanh to\xe1n(Sai hbl, ch\u01b0a nh\u1eadp gi\xe1, sai \u0111\u01a1n v\u1ecb)"}),"\n"]}),"\n",(0,t.jsx)(e.p,{children:"Chi\u1ebfn:"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"D\u1ef1ng Entity TMSHouseBill: S\u1ed1 Hbl, Lo\u1ea1i H\xecnh(Nh\u1eadp/Xu\u1ea5t), Customer, Kho b\xe3i l\u1ea5y/tr\u1ea3 h\xe0ng, Carrier, COT/ETA,..."}),"\n",(0,t.jsx)(e.li,{children:"Th\xeam c\xe1c h\xe0m logic s\u1eed l\xfd CRUD. T\u1ed1i \u01b0u h\xe0m search khi join b\u1ea3ng tms-bill"}),"\n",(0,t.jsx)(e.li,{children:"Migration TMS HouseBill data, t\u1ea1o li\xean k\u1ebft \u0111\u1ebfn TMSBill t\u01b0\u01a1ng \u1ee9ng"}),"\n",(0,t.jsx)(e.li,{children:"D\u1ef1ng m\xe0n h\xecnh cho TMSHouseBillList v\xe0 TMSHouseBillEditor"}),"\n"]}),"\n",(0,t.jsxs)(e.p,{children:["C\xf3 th\u1ec3 download db m\u1edbi t\u1ea1i \u0111\u01b0\u1eddng d\u1eabn ",(0,t.jsx)(e.a,{href:"https://beelogistics.cloud/download/datatpdb-latest.dump",children:"https://beelogistics.cloud/download/datatpdb-latest.dump"}),"\nho\u1eb7c ch\u1ea1y run update v\xe0 c\xe1c migrate script"]}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsxs)(e.li,{children:["migrate",":run"," --script document/CleanDocument.groovy"]}),"\n",(0,t.jsxs)(e.li,{children:["server:migrate",":run"," --script tms/MigrationTMSHouseBill.groovy"]}),"\n"]}),"\n",(0,t.jsx)(e.h3,{id:"r20250620",children:"[R20250620]"}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt nh\xe1nh document-ie:\nEm fix bug search document set \u1ea1"}),"\n",(0,t.jsxs)(e.p,{children:["C\xf3 th\u1ec3 download db m\u1edbi t\u1ea1i \u0111\u01b0\u1eddng d\u1eabn ",(0,t.jsx)(e.a,{href:"https://beelogistics.cloud/download/datatpdb-latest.dump",children:"https://beelogistics.cloud/download/datatpdb-latest.dump"})]}),"\n",(0,t.jsx)(e.h3,{id:"r20250618",children:"[*********]"}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt nh\xe1nh crm:\n[Dan] - Implement UI Dashboard Salesman Activity Tracker."}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt d\u1ef1 \xe1n document-ie:"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsxs)(e.li,{children:["Checkout code (nh\xe1nh develop):  git@gitlab",":datatp",".cloud",":tuan","/datatp-document-ie.git"]}),"\n",(0,t.jsx)(e.li,{children:"T\u1ea1o db, user v\xe0 restore theo link db t\u01b0\u01a1ng \u1ee9ng \u1edf d\u01b0\u1edbi. (S\u1eeda th\xf4ng tin \u1edf file env.sh, ch\u1ea1y l\u1ea1i c\xe1c l\u1ec7nh \u1edf file postgres-admin.sh)\ndb_name: document_ie_db\nusername: document_ie\npassword: document_ie"}),"\n"]}),"\n",(0,t.jsxs)(e.p,{children:["Xem l\u1ea1i c\u1ea5u h\xecnh c\xe1c file config application.properties, b\u1ed5 sung th\xeam datasource cho db m\u1edbi.\ndocument-ie:\ntype: com.zaxxer.hikari.HikariDataSource\nconnectionTimeout: 30000\nidleTimeout: 600000\nmaxLifetime: 600000\nminimumIdle: 5\nmaximumPoolSize: 15\nauto-commit: false\ndriverClassName: ",(0,t.jsx)(e.code,{children:"***********************************************:{spring.datasource.server.port}/document_ie_db"}),"\nusername: document_ie\npassword: document_ie"]}),"\n",(0,t.jsxs)(e.p,{children:["C\xf3 th\u1ec3 download db m\u1edbi t\u1ea1i \u0111\u01b0\u1eddng d\u1eabn ",(0,t.jsx)(e.a,{href:"https://beelogistics.cloud/download/datatpdb-latest.dump",children:"https://beelogistics.cloud/download/datatpdb-latest.dump"}),"\nC\xf3 th\u1ec3 download db document_ie_db m\u1edbi t\u1ea1i \u0111\u01b0\u1eddng d\u1eabn ",(0,t.jsx)(e.a,{href:"https://beelogistics.cloud/download/document_ie_db-latest.dump",children:"https://beelogistics.cloud/download/document_ie_db-latest.dump"})]}),"\n",(0,t.jsx)(e.h3,{id:"r20250618-1",children:"[*********]"}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt nh\xe1nh crm:"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"[Dan] - Implement UI Dashboard cho Pricing Company."}),"\n"]}),"\n",(0,t.jsx)(e.p,{children:"[Nhat]"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"C\u1eadp nh\u1eadt ph\xe2n quy\u1ec1n m\xe0n h\xecnh UI Customer Lead, Customer Lead Detail"}),"\n",(0,t.jsx)(e.li,{children:"Check Customer theo Tax Code: B\u1ed5 sung Button Check, Check th\xeam Customer Lead"}),"\n",(0,t.jsx)(e.li,{children:"Th\xeam button t\u1ea1o Shipping Instruction t\u1eeb Partner Obligation, search Partner Obligation theo Cus/ Docs"}),"\n"]}),"\n",(0,t.jsxs)(e.p,{children:["C\xf3 th\u1ec3 download db m\u1edbi t\u1ea1i \u0111\u01b0\u1eddng d\u1eabn ",(0,t.jsx)(e.a,{href:"https://beelogistics.cloud/download/datatpdb-latest.dump",children:"https://beelogistics.cloud/download/datatpdb-latest.dump"})]}),"\n",(0,t.jsx)(e.h3,{id:"r20250617",children:"[R20250617]"}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt nh\xe1nh quan-tms:"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"Th\xeam n\xfat clear tr\u1ea1ng th\xe1i thanh to\xe1n v\u1edbi quy\u1ec1n moderator t\u1ea1i m\xe0n h\xecnh TMSBillList"}),"\n",(0,t.jsx)(e.li,{children:"\u0110i\u1ec1u ch\u1ec9nh hi\u1ec3n th\u1ecb c\xe1c \u0111i\u1ec3m d\u1eebng t\u1ea1i m\xe0n TMSBillList, VehicleTripGoodTrackingList theo y\xeau c\u1ea7u"}),"\n"]}),"\n",(0,t.jsxs)(e.p,{children:["C\xf3 th\u1ec3 download db m\u1edbi t\u1ea1i \u0111\u01b0\u1eddng d\u1eabn ",(0,t.jsx)(e.a,{href:"https://beelogistics.cloud/download/datatpdb-latest.dump",children:"https://beelogistics.cloud/download/datatpdb-latest.dump"})]}),"\n",(0,t.jsx)(e.h3,{id:"r20250616",children:"[R20250616]"}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt nh\xe1nh crm:"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsxs)(e.li,{children:["\n",(0,t.jsx)(e.p,{children:"[An] - Ti\u1ebfp t\u1ee5c nh\u1eadn feedback ch\u1ec9nh s\u1eeda, bugs cho inquiry h\xe0ng r\u1eddi (Req from Team BD)"}),"\n"]}),"\n",(0,t.jsxs)(e.li,{children:["\n",(0,t.jsx)(e.p,{children:"[An] -Refactor l\u1ea1i th\xf4ng tin BFSOne Partner, Vi\u1ebft cron sync t\u1ef1 \u0111\u1ed9ng theo ng\xe0y"}),"\n"]}),"\n",(0,t.jsxs)(e.li,{children:["\n",(0,t.jsx)(e.p,{children:"[An] - \"C\u1eadp nh\u1eadt b\u1ea3ng gi\xe1 cont (Trucking): Chia l\u1ea1i m\u1ee9c gi\xe1 cho cont 20' theo y/c c\u1ee7a HPH."}),"\n"]}),"\n",(0,t.jsxs)(e.li,{children:["\n",(0,t.jsx)(e.p,{children:"[Nhat] - Clean code Uncompleted Sale Daily Task, thay th\u1ebf logic sendMessage c\u0169 b\u1eb1ng CRMMessageSystem"}),"\n"]}),"\n",(0,t.jsxs)(e.li,{children:["\n",(0,t.jsx)(e.p,{children:"[Nhat] - C\u1eadp nh\u1eadt ph\xe2n quy\u1ec1n m\xe0n h\xecnh UI Customer Lead, Customer Lead Detail"}),"\n"]}),"\n",(0,t.jsxs)(e.li,{children:["\n",(0,t.jsx)(e.p,{children:"[Dan] - Implement UI Dashboard cho CRM Company."}),"\n"]}),"\n",(0,t.jsxs)(e.li,{children:["\n",(0,t.jsxs)(e.p,{children:["[Dan] - Vi\u1ebft api gen token, api c\u1eadp nh\u1eadt BFSOne Partner Code cho a Qu\xfd.\nQuy tr\xecnh l\xfac t\u1ea1o m\u1edbi partner -> KT approve -> BFSOne gen code partner -> G\u1ecdi api \u0111\u1ec3 sync l\u1ea1i v\u1edbi DataTP\n\u0110\xe3 g\u1eedi l\u1ea1i api cho a Qu\xfd l\xfac  13/06\n",(0,t.jsx)(e.a,{href:"https://docs.google.com/document/d/1hI71aD9YjN2zbHxVUAVEc_Sp0lgYsOJwgHvZv1x1zsA/edit?usp=sharing",children:"https://docs.google.com/document/d/1hI71aD9YjN2zbHxVUAVEc_Sp0lgYsOJwgHvZv1x1zsA/edit?usp=sharing"}),'"']}),"\n"]}),"\n",(0,t.jsxs)(e.li,{children:["\n",(0,t.jsx)(e.p,{children:"[Dan] - T\u1ea1o b\u1ea3ng d\u1eef li\u1ec7u, l\u01b0u th\xf4ng tin kh\xe1ch h\xe0ng\nT\u1ea1o c\xe1c api service cho ph\xe9p c\u1eadp nh\u1eadt, ch\u1ec9nh s\u1eeda v\xe0 xo\xe1 record."}),"\n"]}),"\n",(0,t.jsxs)(e.li,{children:["\n",(0,t.jsx)(e.p,{children:'[Dan] - "C\u1eadp nh\u1eadt api li\xean quan \u0111\u1ebfn authorization, cho ph\xe9p c\xe1c h\u1ec7 th\u1ed1ng b\xean ngo\xe0i g\u1ecdi v\xe0o c\xe1c service \u0111\u1ec3 c\u1eadp nh\u1eadt d\u1eef li\u1ec7u.\nHard code token, g\u1eedi cho client sau \u0111\xf3 ki\u1ec3m tra request \u0111\u1ec3 x\xe1c th\u1ef1c."'}),"\n"]}),"\n",(0,t.jsxs)(e.li,{children:["\n",(0,t.jsx)(e.p,{children:"[Dan] - T\u1ea1o b\u1ea3ng d\u1eef li\u1ec7u, l\u01b0u th\xf4ng tin unit\nT\u1ea1o c\xe1c api service cho ph\xe9p sync, c\u1eadp nh\u1eadt, ch\u1ec9nh s\u1eeda v\xe0 xo\xe1 record."}),"\n"]}),"\n"]}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt nh\xe1nh quan-tms:"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"Giao di\u1ec7n TMSBillList, VehicleTripGoodTrackingList cho ph\xe9p nh\u1eadp th\xeam c\xe1c \u0111i\u1ec3m d\u1eebng"}),"\n",(0,t.jsx)(e.li,{children:"H\xe0m search TMSBillList, VehicleTripGoodTrackingList load th\xeam c\xe1c \u0111i\u1ec3m d\u1eebng"}),"\n"]}),"\n",(0,t.jsxs)(e.p,{children:["C\xf3 th\u1ec3 download db m\u1edbi t\u1ea1i \u0111\u01b0\u1eddng d\u1eabn ",(0,t.jsx)(e.a,{href:"https://beelogistics.cloud/download/datatpdb-latest.dump",children:"https://beelogistics.cloud/download/datatpdb-latest.dump"}),"\nho\u1eb7c ch\u1ea1y run update v\xe0 c\xe1c migrate script"]}),"\n",(0,t.jsx)(e.h3,{id:"r20250612",children:"[R20250612]"}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt nh\xe1nh crm:"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"Refactor l\u1ea1i th\xf4ng tin BFSOne Partner"}),"\n",(0,t.jsx)(e.li,{children:"Ti\u1ebfp t\u1ee5c nh\u1eadn feedback ch\u1ec9nh s\u1eeda, bugs cho inquiry h\xe0ng r\u1eddi (Req from Team BD)"}),"\n"]}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt nh\xe1nh tms:"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"[Qu\xe2n] Drop b\u1ea3ng document_document_set_category, drop column document_category_id t\u1ea1i b\u1ea3ng document_document_set,  drop c\xe1c index \u0111\u1eb7t t\xean sai t\u1ea1i b\u1ea3ng document_document"}),"\n",(0,t.jsx)(e.li,{children:"[Chi\u1ebfn] Fix bugs TMSBillFee save l\u1ed7i, Clear th\xf4ng tin xe v\xe0 gi\xe1 cost khi copy l\xf4 h\xe0ng"}),"\n"]}),"\n",(0,t.jsxs)(e.p,{children:["C\xf3 th\u1ec3 download db m\u1edbi t\u1ea1i \u0111\u01b0\u1eddng d\u1eabn ",(0,t.jsx)(e.a,{href:"https://beelogistics.cloud/download/datatpdb-latest.dump",children:"https://beelogistics.cloud/download/datatpdb-latest.dump"}),"\nho\u1eb7c ch\u1ea1y run update v\xe0 c\xe1c migrate script"]}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsxs)(e.li,{children:["migrate",":run"," --script document/CleanDocument.groovy"]}),"\n"]}),"\n",(0,t.jsx)(e.h3,{id:"r20250612-1",children:"[R20250612]"}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt nh\xe1nh crm:"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"Refactor l\u1ea1i th\xf4ng tin BFSOne Partner"}),"\n",(0,t.jsx)(e.li,{children:"Ti\u1ebfp t\u1ee5c nh\u1eadn feedback ch\u1ec9nh s\u1eeda, bugs cho inquiry h\xe0ng r\u1eddi (Req from Team BD)"}),"\n"]}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt nh\xe1nh tms:"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"\u0110\u1ecba ch\u1ec9 config cho kh\xe1ch h\xe0ng th\xeam StreetName \u0111\u1ec3 l\u01b0u s\u1ed1 nh\xe0, \u0111\u01b0\u1eddng,...(K\u1ebf to\xe1n y\xeau c\u1ea7u t\xe1ch)"}),"\n",(0,t.jsx)(e.li,{children:"Th\xeam senderStreetName, receiverStreetName tr\xean TMSBill v\xe0 \u0111\u1ed3ng b\u1ed9 v\u1edbi \u0111\u1ecba ch\u1ec9 kh\xe1ch h\xe0ng \u0111\xe3 config"}),"\n",(0,t.jsx)(e.li,{children:"Fix BFSOne template export, chu\u1ea9n h\xf3a \u0111\u1ecba ch\u1ec9  x\xe3/ph\u01b0\u1eddng, qu\u1eadn/huy\xean theo y\xeau c\u1ea7u"}),"\n",(0,t.jsx)(e.li,{children:"Fix l\u1ed7i query li\xean quan \u0111\u1ebfn migration TMSVendor sang Vehicle Fleet (Qu\xe2n ch\u01b0a thay h\u1ebft)"}),"\n"]}),"\n",(0,t.jsxs)(e.p,{children:["C\xf3 th\u1ec3 download db m\u1edbi t\u1ea1i \u0111\u01b0\u1eddng d\u1eabn ",(0,t.jsx)(e.a,{href:"https://beelogistics.cloud/download/datatpdb-latest.dump",children:"https://beelogistics.cloud/download/datatpdb-latest.dump"})]}),"\n",(0,t.jsx)(e.h3,{id:"r20250612-2",children:"[R20250612]"}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt nh\xe1nh crm:"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"Clean code groovy sql kh\xf4ng s\u1eed d\u1ee5ng t\u1eeb datatp-build/app/cli"}),"\n",(0,t.jsx)(e.li,{children:"Drop c\xe1c colume th\u1eeba, t\u1ea1o nh\u1ea7m, kh\xf4ng c\xf2n s\u1eed d\u1ee5ng \u1edf c\xe1c b\u1ea3ng bfsone_partner, lgc_price_bulk_cargo_inquiry_request, lgc_price_truck_container_charge"}),"\n"]}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt nh\xe1nh nhat:"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"Ch\u1ec9nh s\u1eeda giao di\u1ec7n Asset Calendar+ fix bug m\xe0n h\xecnh t\u1ea1o Task"}),"\n",(0,t.jsx)(e.li,{children:"B\u1ed5 sung m\xe0n h\xecnh Admin KPI"}),"\n"]}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt nh\xe1nh tms:\nTask:\nChi\u1ebfn:"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"Push c\u01b0\u1edbc t\u1eeb Vehicle Goods Tracking v\u1ec1 ph\u1ea7n Chi ph\xed TMSBill"}),"\n",(0,t.jsx)(e.li,{children:"Cho ph\xe9p cus \u0111\u1ed3ng b\u1ed9 c\u01b0\u1edbc khi Vehicle Goods Tracking \u0111\xe3 \u0111\u01b0\u1ee3c nh\u1eadp c\u01b0\u1edbc"}),"\n",(0,t.jsx)(e.li,{children:"Th\xeam tr\u1ea1ng th\xe1i th\xf4ng b\xe1o thanh to\xe1n chi ph\xed TMSBill v\xe0 c\xe1c l\u1ed7i d\u1eabn \u0111\u1ebfn ch\u01b0a \u0111c thanh to\xe1n"}),"\n",(0,t.jsx)(e.li,{children:"UITMSBillList l\u1ecdc c\xe1c bill ch\u01b0a thanh to\xe1n"}),"\n",(0,t.jsx)(e.li,{children:"Verify HouseBill tmsBill v\u1edbi BFSOne, c\u1ea3nh b\xe1o c\xe1c l\xf4 h\xe0ng HouseBill ch\u01b0a Verify"}),"\n"]}),"\n",(0,t.jsx)(e.p,{children:"Qu\xe2n:"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"[Vehicle Fleet] Th\xeam field emails v\xe0 c\xe1c field webhook config"}),"\n",(0,t.jsx)(e.li,{children:"Vi\u1ebft groovy script merge tms vendor v\xe0o vehicle fleet v\xe0 migration d\u1eef li\u1ec7u c\xe1c entity d\xf9ng TMSVendor sang Vehicle Fleets"}),"\n",(0,t.jsx)(e.li,{children:"Thay th\u1ebf tr\xean c\xe1c giao di\u1ec7n m\xe0n h\xecnh d\xf9ng BBRefTMSVendor sang BBRefVehicleFleet."}),"\n",(0,t.jsx)(e.li,{children:"Chuy\u1ec3n v\xe0 ki\u1ec3m tra c\xe1c ch\u1ee9c n\u0103ng call webhook \u0111\u01b0\u1ee3c c\u1ea5u h\xecnh t\u1eeb TMS Partner sang Vehicle Fleet"}),"\n"]}),"\n",(0,t.jsxs)(e.p,{children:["C\xf3 th\u1ec3 download db m\u1edbi t\u1ea1i \u0111\u01b0\u1eddng d\u1eabn ",(0,t.jsx)(e.a,{href:"https://beelogistics.cloud/download/datatpdb-latest.dump",children:"https://beelogistics.cloud/download/datatpdb-latest.dump"}),"\nho\u1eb7c ch\u1ea1y run update v\xe0 c\xe1c migrate script"]}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsxs)(e.li,{children:["migrate",":run"," --script crm/AlterTables.groovy"]}),"\n",(0,t.jsxs)(e.li,{children:["migrate",":run"," --script tms/MigrationTmsBillFee.groovy"]}),"\n",(0,t.jsxs)(e.li,{children:["server:migrate",":run"," --script tms/MigrationVehicleFleet.groovy"]}),"\n"]}),"\n",(0,t.jsx)(e.h3,{id:"r20250611",children:"[R20250611]"}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt nh\xe1nh crm:"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"S\u1eeda logo b\xe1o c\xe1o"}),"\n"]}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt nh\xe1nh asset:\nTask:"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"[Asset] Th\xeam giao di\u1ec7n Calendar ri\xeang cho book xe + ph\xf2ng h\u1ecdp (trong module Asset). Default xem \u1edf d\u1ea1ng week"}),"\n",(0,t.jsx)(e.li,{children:"[Spreadsheet] T\u1ea1o b\u1ea3ng config m\xe0n h\xecnh Report team IST (BD HCM)"}),"\n"]}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt nh\xe1nh ocr:\nEm fix bug tr\xedch r\xfat b\xean k\u1ebf to\xe1n \u1ea1"}),"\n",(0,t.jsxs)(e.p,{children:["C\xf3 th\u1ec3 download db m\u1edbi t\u1ea1i \u0111\u01b0\u1eddng d\u1eabn ",(0,t.jsx)(e.a,{href:"https://beelogistics.cloud/download/datatpdb-latest.dump",children:"https://beelogistics.cloud/download/datatpdb-latest.dump"}),"\nho\u1eb7c ch\u1ea1y run update v\xe0 c\xe1c migrate script"]}),"\n",(0,t.jsx)(e.h3,{id:"r20250610",children:"[R20250610]"}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt nh\xe1nh crm:"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"C\u1eadp nh\u1eadt form, feedback ch\u1ec9nh s\u1eeda, bugs cho inquiry h\xe0ng r\u1eddi (Team BD)"}),"\n",(0,t.jsx)(e.li,{children:"Enhance c\xe1c b\xe1o c\xe1o \u1edf m\xe0n h\xecnh CRM. (L\u1ecdc, filter, xu\u1ea5t excel, ..)\nB\xe1o c\xe1o ho\u1ea1t \u0111\u1ed9ng kh\xe1ch h\xe0ng/ lead theo d\xf5i g\u1ea7n \u0111\xe2y"}),"\n",(0,t.jsx)(e.li,{children:"Fix bugs l\u1ed7i spam mail nh\u1eafc c\u1eadp nh\u1eadt request"}),"\n",(0,t.jsx)(e.li,{children:"C\u1eadp nh\u1eadt response MSA: String => MapObject"}),"\n"]}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt nh\xe1nh tms:"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"Fix bugs li\xean quan \u0111\u1ebfn GPS"}),"\n",(0,t.jsx)(e.li,{children:"Fix bugs TMS li\xean quan \u0111\u1ebfn TMSPartner v\xe0 TMSBillFee"}),"\n"]}),"\n",(0,t.jsxs)(e.p,{children:["C\xf3 th\u1ec3 download db m\u1edbi t\u1ea1i \u0111\u01b0\u1eddng d\u1eabn ",(0,t.jsx)(e.a,{href:"https://beelogistics.cloud/download/datatpdb-latest.dump",children:"https://beelogistics.cloud/download/datatpdb-latest.dump"}),"\nho\u1eb7c ch\u1ea1y run update v\xe0 c\xe1c migrate script"]}),"\n",(0,t.jsx)(e.h1,{id:"c\u1eadp-nh\u1eadt-server--code-nh\xe1nh-develop-v\xe0-download-db",children:"C\u1eadp nh\u1eadt server , code nh\xe1nh develop v\xe0 download db"}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt nh\xe1nh ocr:"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"B\u1ed5 sung tr\xedch r\xfat t\xean ng\u01b0\u1eddi b\xe1n ng\u01b0\u1eddi mua b\xean python v\xe0 th\xeam v\xe0o b\xean giao di\u1ec7n"}),"\n",(0,t.jsx)(e.li,{children:"c\u1eadp nh\u1eadt tr\xedch r\xfat b\xean dat-ocr, code datatp-python"}),"\n"]}),"\n",(0,t.jsx)(e.p,{children:"C\u1eadp nh\u1eadt nh\xe1nh maintenance"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"Remove react-beautiful-dnd th\u01b0 vi\u1ec7n"}),"\n",(0,t.jsx)(e.li,{children:"C\u1eadp nh\u1eadt kanban s\u1eed d\u1ee5ng dndkit lib"}),"\n"]}),"\n",(0,t.jsxs)(e.p,{children:["C\xf3 th\u1ec3 download db m\u1edbi t\u1ea1i \u0111\u01b0\u1eddng d\u1eabn ",(0,t.jsx)(e.a,{href:"https://beelogistics.cloud/download/datatpdb-latest.dump",children:"https://beelogistics.cloud/download/datatpdb-latest.dump"}),"\nho\u1eb7c ch\u1ea1y run update v\xe0 c\xe1c migrate script"]}),"\n",(0,t.jsx)(e.p,{children:"N\u1ebfu kh\xf4ng c\u1eadp nh\u1eadt db, ch\u1ea1y script:"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsxs)(e.li,{children:["migrate",":run"," --script tms/MigrationTmsPartnerAddess.groovy"]}),"\n"]})]})}function a(n={}){const{wrapper:e}={...(0,l.R)(),...n.components};return e?(0,t.jsx)(e,{...n,children:(0,t.jsx)(o,{...n})}):o(n)}}}]);