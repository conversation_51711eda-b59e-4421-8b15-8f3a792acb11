"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[9048],{290:(e,t,n)=>{n.r(t),n.d(t,{default:()=>pe});var a=n(6372),o=n(3394),i=n(3135),s=n(8659),c=n(6473),r=n(458),l=n(8419),d=n(1020),u=n(8754);const m={backToTopButton:"backToTopButton_yO2w",backToTopButtonShow:"backToTopButtonShow_TCt_"};var b=n(216);function h(){const{shown:e,scrollToTop:t}=function({threshold:e}){const[t,n]=(0,a.useState)(!1),o=(0,a.useRef)(!1),{startScroll:i,cancelScroll:s}=(0,d.gk)();return(0,d.Mq)(({scrollY:t},a)=>{const i=a?.scrollY;i&&(o.current?o.current=!1:t>=i?(s(),n(!1)):t<e?n(!1):t+window.innerHeight<document.documentElement.scrollHeight&&n(!0))}),(0,u.$)(e=>{e.location.hash&&(o.current=!0,n(!1))}),{shown:t,scrollToTop:()=>i(0)}}({threshold:300});return(0,b.jsx)("button",{"aria-label":(0,l.T)({id:"theme.BackToTopButton.buttonAriaLabel",message:"Scroll back to top",description:"The ARIA label for the back to top button"}),className:(0,o.A)("clean-btn",s.G.common.backToTopButton,m.backToTopButton,e&&m.backToTopButtonShow),type:"button",onClick:t})}var p=n(6137),x=n(9551),f=n(1801),j=n(2410),_=n(4996);function g(e){return(0,b.jsx)("svg",{width:"20",height:"20","aria-hidden":"true",...e,children:(0,b.jsxs)("g",{fill:"#7a7a7a",children:[(0,b.jsx)("path",{d:"M9.992 10.023c0 .2-.062.399-.172.547l-4.996 7.492a.982.982 0 01-.828.454H1c-.55 0-1-.453-1-1 0-.2.059-.403.168-.551l4.629-6.942L.168 3.078A.939.939 0 010 2.528c0-.548.45-.997 1-.997h2.996c.352 0 .649.18.828.45L9.82 9.472c.11.148.172.347.172.55zm0 0"}),(0,b.jsx)("path",{d:"M19.98 10.023c0 .2-.058.399-.168.547l-4.996 7.492a.987.987 0 01-.828.454h-3c-.547 0-.996-.453-.996-1 0-.2.059-.403.168-.551l4.625-6.942-4.625-6.945a.939.939 0 01-.168-.55 1 1 0 01.996-.997h3c.348 0 .649.18.828.45l4.996 7.492c.11.148.168.347.168.55zm0 0"})]})})}const v="collapseSidebarButton_Vuw3",C="collapseSidebarButtonIcon_Vp19";function A({onClick:e}){return(0,b.jsx)("button",{type:"button",title:(0,l.T)({id:"theme.docs.sidebar.collapseButtonTitle",message:"Collapse sidebar",description:"The title attribute for collapse button of doc sidebar"}),"aria-label":(0,l.T)({id:"theme.docs.sidebar.collapseButtonAriaLabel",message:"Collapse sidebar",description:"The title attribute for collapse button of doc sidebar"}),className:(0,o.A)("button button--secondary button--outline",v),onClick:e,children:(0,b.jsx)(g,{className:C})})}var k=n(4389),S=n(7080);const T=Symbol("EmptyContext"),N=a.createContext(T);function I({children:e}){const[t,n]=(0,a.useState)(null),o=(0,a.useMemo)(()=>({expandedItem:t,setExpandedItem:n}),[t]);return(0,b.jsx)(N.Provider,{value:o,children:e})}var y=n(2026),w=n(2805),B=n(1830),L=n(4255);function E({collapsed:e,categoryLabel:t,onClick:n}){return(0,b.jsx)("button",{"aria-label":e?(0,l.T)({id:"theme.DocSidebarItem.expandCategoryAriaLabel",message:"Expand sidebar category '{label}'",description:"The ARIA label to expand the sidebar category"},{label:t}):(0,l.T)({id:"theme.DocSidebarItem.collapseCategoryAriaLabel",message:"Collapse sidebar category '{label}'",description:"The ARIA label to collapse the sidebar category"},{label:t}),"aria-expanded":!e,type:"button",className:"clean-btn menu__caret",onClick:n})}function M({item:e,onItemClick:t,activePath:n,level:i,index:r,...l}){const{items:d,label:u,collapsible:m,className:h,href:p}=e,{docs:{sidebar:{autoCollapseCategories:x}}}=(0,j.p)(),f=function(e){const t=(0,L.A)();return(0,a.useMemo)(()=>e.href&&!e.linkUnlisted?e.href:!t&&e.collapsible?(0,c.Nr)(e):void 0,[e,t])}(e),_=(0,c.w8)(e,n),g=(0,w.ys)(p,n),{collapsed:v,setCollapsed:C}=(0,y.u)({initialState:()=>!!m&&(!_&&e.collapsed)}),{expandedItem:A,setExpandedItem:k}=function(){const e=(0,a.useContext)(N);if(e===T)throw new S.dV("DocSidebarItemsExpandedStateProvider");return e}(),I=(e=!v)=>{k(e?null:r),C(e)};return function({isActive:e,collapsed:t,updateCollapsed:n}){const o=(0,S.ZC)(e);(0,a.useEffect)(()=>{e&&!o&&t&&n(!1)},[e,o,t,n])}({isActive:_,collapsed:v,updateCollapsed:I}),(0,a.useEffect)(()=>{m&&null!=A&&A!==r&&x&&C(!0)},[m,A,r,C,x]),(0,b.jsxs)("li",{className:(0,o.A)(s.G.docs.docSidebarItemCategory,s.G.docs.docSidebarItemCategoryLevel(i),"menu__list-item",{"menu__list-item--collapsed":v},h),children:[(0,b.jsxs)("div",{className:(0,o.A)("menu__list-item-collapsible",{"menu__list-item-collapsible--active":g}),children:[(0,b.jsx)(B.A,{className:(0,o.A)("menu__link",{"menu__link--sublist":m,"menu__link--sublist-caret":!p&&m,"menu__link--active":_}),onClick:m?n=>{t?.(e),p?I(!1):(n.preventDefault(),I())}:()=>{t?.(e)},"aria-current":g?"page":void 0,role:m&&!p?"button":void 0,"aria-expanded":m&&!p?!v:void 0,href:m?f??"#":f,...l,children:u}),p&&m&&(0,b.jsx)(E,{collapsed:v,categoryLabel:u,onClick:e=>{e.preventDefault(),I()}})]}),(0,b.jsx)(y.N,{lazy:!0,as:"ul",className:"menu__list",collapsed:v,children:(0,b.jsx)(Y,{items:d,tabIndex:v?-1:0,onItemClick:t,activePath:n,level:i+1})})]})}var H=n(6942),G=n(9968);const P="menuExternalLink_y0Iq";function R({item:e,onItemClick:t,activePath:n,level:a,index:i,...r}){const{href:l,label:d,className:u,autoAddBaseUrl:m}=e,h=(0,c.w8)(e,n),p=(0,H.A)(l);return(0,b.jsx)("li",{className:(0,o.A)(s.G.docs.docSidebarItemLink,s.G.docs.docSidebarItemLinkLevel(a),"menu__list-item",u),children:(0,b.jsxs)(B.A,{className:(0,o.A)("menu__link",!p&&P,{"menu__link--active":h}),autoAddBaseUrl:m,"aria-current":h?"page":void 0,to:l,...p&&{onClick:t?()=>t(e):void 0},...r,children:[d,!p&&(0,b.jsx)(G.A,{})]})},d)}const V="menuHtmlItem_m9pP";function W({item:e,level:t,index:n}){const{value:a,defaultStyle:i,className:c}=e;return(0,b.jsx)("li",{className:(0,o.A)(s.G.docs.docSidebarItemLink,s.G.docs.docSidebarItemLinkLevel(t),i&&[V,"menu__list-item"],c),dangerouslySetInnerHTML:{__html:a}},n)}function D({item:e,...t}){switch(e.type){case"category":return(0,b.jsx)(M,{item:e,...t});case"html":return(0,b.jsx)(W,{item:e,...t});default:return(0,b.jsx)(R,{item:e,...t})}}function F({items:e,...t}){const n=(0,c.Y)(e,t.activePath);return(0,b.jsx)(I,{children:n.map((e,n)=>(0,b.jsx)(D,{item:e,index:n,...t},n))})}const Y=(0,a.memo)(F),q="menu_kjpJ",z="menuWithAnnouncementBar_VBhE";function O({path:e,sidebar:t,className:n}){const i=function(){const{isActive:e}=(0,k.M)(),[t,n]=(0,a.useState)(e);return(0,d.Mq)(({scrollY:t})=>{e&&n(0===t)},[e]),e&&t}();return(0,b.jsx)("nav",{"aria-label":(0,l.T)({id:"theme.docs.sidebar.navAriaLabel",message:"Docs sidebar",description:"The ARIA label for the sidebar navigation"}),className:(0,o.A)("menu thin-scrollbar",q,i&&z,n),children:(0,b.jsx)("ul",{className:(0,o.A)(s.G.docs.docSidebarMenu,"menu__list"),children:(0,b.jsx)(Y,{items:t,activePath:e,level:1})})})}const U="sidebar_Jdpv",X="sidebarWithHideableNavbar_jVRF",J="sidebarHidden_gcqw",K="sidebarLogo_clwO";function Z({path:e,sidebar:t,onCollapse:n,isHidden:a}){const{navbar:{hideOnScroll:i},docs:{sidebar:{hideable:s}}}=(0,j.p)();return(0,b.jsxs)("div",{className:(0,o.A)(U,i&&X,a&&J),children:[i&&(0,b.jsx)(_.A,{tabIndex:-1,className:K}),(0,b.jsx)(O,{path:e,sidebar:t}),s&&(0,b.jsx)(A,{onClick:n})]})}const Q=a.memo(Z);var $=n(2684),ee=n(5861);const te=({sidebar:e,path:t})=>{const n=(0,ee.M)();return(0,b.jsx)("ul",{className:(0,o.A)(s.G.docs.docSidebarMenu,"menu__list"),children:(0,b.jsx)(Y,{items:e,activePath:t,onItemClick:e=>{"category"===e.type&&e.href&&n.toggle(),"link"===e.type&&n.toggle()},level:1})})};function ne(e){return(0,b.jsx)($.GX,{component:te,props:e})}const ae=a.memo(ne);function oe(e){const t=(0,f.l)(),n="desktop"===t||"ssr"===t,a="mobile"===t;return(0,b.jsxs)(b.Fragment,{children:[n&&(0,b.jsx)(Q,{...e}),a&&(0,b.jsx)(ae,{...e})]})}const ie={expandButton:"expandButton_ynyX",expandButtonIcon:"expandButtonIcon_wts6"};function se({toggleSidebar:e}){return(0,b.jsx)("div",{className:ie.expandButton,title:(0,l.T)({id:"theme.docs.sidebar.expandButtonTitle",message:"Expand sidebar",description:"The ARIA label and title attribute for expand button of doc sidebar"}),"aria-label":(0,l.T)({id:"theme.docs.sidebar.expandButtonAriaLabel",message:"Expand sidebar",description:"The ARIA label and title attribute for expand button of doc sidebar"}),tabIndex:0,role:"button",onKeyDown:e,onClick:e,children:(0,b.jsx)(g,{className:ie.expandButtonIcon})})}const ce={docSidebarContainer:"docSidebarContainer_YfKm",docSidebarContainerHidden:"docSidebarContainerHidden_biZe",sidebarViewport:"sidebarViewport_kETH"};function re({children:e}){const t=(0,r.t)();return(0,b.jsx)(a.Fragment,{children:e},t?.name??"noSidebar")}function le({sidebar:e,hiddenSidebarContainer:t,setHiddenSidebarContainer:n}){const{pathname:i}=(0,x.zy)(),[c,r]=(0,a.useState)(!1),l=(0,a.useCallback)(()=>{c&&r(!1),!c&&(0,p.O)()&&r(!0),n(e=>!e)},[n,c]);return(0,b.jsx)("aside",{className:(0,o.A)(s.G.docs.docSidebarContainer,ce.docSidebarContainer,t&&ce.docSidebarContainerHidden),onTransitionEnd:e=>{e.currentTarget.classList.contains(ce.docSidebarContainer)&&t&&r(!0)},children:(0,b.jsx)(re,{children:(0,b.jsxs)("div",{className:(0,o.A)(ce.sidebarViewport,c&&ce.sidebarViewportHidden),children:[(0,b.jsx)(oe,{sidebar:e,path:i,onCollapse:l,isHidden:c}),c&&(0,b.jsx)(se,{toggleSidebar:l})]})})})}const de={docMainContainer:"docMainContainer_hcuh",docMainContainerEnhanced:"docMainContainerEnhanced_J67b",docItemWrapperEnhanced:"docItemWrapperEnhanced_vdlk"};function ue({hiddenSidebarContainer:e,children:t}){const n=(0,r.t)();return(0,b.jsx)("main",{className:(0,o.A)(de.docMainContainer,(e||!n)&&de.docMainContainerEnhanced),children:(0,b.jsx)("div",{className:(0,o.A)("container padding-top--md padding-bottom--lg",de.docItemWrapper,e&&de.docItemWrapperEnhanced),children:t})})}const me={docRoot:"docRoot_QXfX",docsWrapper:"docsWrapper_LEG8"};function be({children:e}){const t=(0,r.t)(),[n,o]=(0,a.useState)(!1);return(0,b.jsxs)("div",{className:me.docsWrapper,children:[(0,b.jsx)(h,{}),(0,b.jsxs)("div",{className:me.docRoot,children:[t&&(0,b.jsx)(le,{sidebar:t.items,hiddenSidebarContainer:n,setHiddenSidebarContainer:o}),(0,b.jsx)(ue,{hiddenSidebarContainer:n,children:e})]})]})}var he=n(2662);function pe(e){const t=(0,c.B5)(e);if(!t)return(0,b.jsx)(he.A,{});const{docElement:n,sidebarName:a,sidebarItems:l}=t;return(0,b.jsx)(i.e3,{className:(0,o.A)(s.G.page.docsDocPage),children:(0,b.jsx)(r.V,{name:a,items:l,children:(0,b.jsx)(be,{children:n})})})}},2662:(e,t,n)=>{n.d(t,{A:()=>c});n(6372);var a=n(3394),o=n(8419),i=n(615),s=n(216);function c({className:e}){return(0,s.jsx)("main",{className:(0,a.A)("container margin-vert--xl",e),children:(0,s.jsx)("div",{className:"row",children:(0,s.jsxs)("div",{className:"col col--6 col--offset-3",children:[(0,s.jsx)(i.A,{as:"h1",className:"hero__title",children:(0,s.jsx)(o.A,{id:"theme.NotFound.title",description:"The title of the 404 page",children:"Page Not Found"})}),(0,s.jsx)("p",{children:(0,s.jsx)(o.A,{id:"theme.NotFound.p1",description:"The first paragraph of the 404 page",children:"We could not find what you were looking for."})}),(0,s.jsx)("p",{children:(0,s.jsx)(o.A,{id:"theme.NotFound.p2",description:"The 2nd paragraph of the 404 page",children:"Please contact the owner of the site that linked you to the original URL and let them know their link is broken."})})]})})})}}}]);