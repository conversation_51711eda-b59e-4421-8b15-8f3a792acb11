"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[6315],{494:(n,e,l)=>{l.d(e,{R:()=>r,x:()=>h});var i=l(6372);const c={},s=i.createContext(c);function r(n){const e=i.useContext(s);return i.useMemo(function(){return"function"==typeof n?n(e):{...e,...n}},[e,n])}function h(n){let e;return e=n.disableParentContext?"function"==typeof n.components?n.components(c):n.components||c:r(n.components),i.createElement(s.Provider,{value:e},n.children)}},7312:(n,e,l)=>{l.r(e),l.d(e,{assets:()=>d,contentTitle:()=>r,default:()=>a,frontMatter:()=>s,metadata:()=>h,toc:()=>o});var i=l(216),c=l(494);const s={},r="H\u01b0\u1edbng d\u1eabn c\xe0i \u0111\u1eb7t v\xe0 s\u1eed d\u1ee5ng Keycloak (Windows & macOS)",h={id:"datatp-keycloak/developer/SETUP",title:"H\u01b0\u1edbng d\u1eabn c\xe0i \u0111\u1eb7t v\xe0 s\u1eed d\u1ee5ng Keycloak (Windows & macOS)",description:"Y\xeau c\u1ea7u h\u1ec7 th\u1ed1ng",source:"@site/docs/datatp-keycloak/developer/SETUP.md",sourceDirName:"datatp-keycloak/developer",slug:"/datatp-keycloak/developer/SETUP",permalink:"/docs/datatp-keycloak/developer/SETUP",draft:!1,unlisted:!1,tags:[],version:"current",frontMatter:{}},d={},o=[{value:"Y\xeau c\u1ea7u h\u1ec7 th\u1ed1ng",id:"y\xeau-c\u1ea7u-h\u1ec7-th\u1ed1ng",level:2},{value:"B\u01b0\u1edbc 1: T\u1ea3i Keycloak",id:"b\u01b0\u1edbc-1-t\u1ea3i-keycloak",level:2},{value:"C\xe0i \u0111\u1eb7t tr\xean Windows",id:"c\xe0i-\u0111\u1eb7t-tr\xean-windows",level:2},{value:"C\xe0i \u0111\u1eb7t tr\xean MacOs",id:"c\xe0i-\u0111\u1eb7t-tr\xean-macos",level:2},{value:"C\xe0i \u0111\u1eb7t Database",id:"c\xe0i-\u0111\u1eb7t-database",level:2},{value:"C\xe0i \u0111\u1eb7t m\xf4i tr\u01b0\u1eddng ch\u1ea1y",id:"c\xe0i-\u0111\u1eb7t-m\xf4i-tr\u01b0\u1eddng-ch\u1ea1y",level:2},{value:"1. C\u1eadp nh\u1eadt file c\u1ea5u h\xecnh <code>keycloak.conf</code>",id:"1-c\u1eadp-nh\u1eadt-file-c\u1ea5u-h\xecnh-keycloakconf",level:3},{value:"2. C\u1ea5u h\xecnh Spring \u0111\u1ec3 k\u1ebft n\u1ed1i v\u1edbi Keycloak",id:"2-c\u1ea5u-h\xecnh-spring-\u0111\u1ec3-k\u1ebft-n\u1ed1i-v\u1edbi-keycloak",level:2},{value:"3. C\u1ea5u h\xecnh file <code>hosts</code>",id:"3-c\u1ea5u-h\xecnh-file-hosts",level:2},{value:"T\u1ea1o Admin User",id:"t\u1ea1o-admin-user",level:2},{value:"T\u1ea1o Realm m\u1edbi",id:"t\u1ea1o-realm-m\u1edbi",level:2},{value:"T\u1ea1o Role",id:"t\u1ea1o-role",level:2},{value:"T\u1ea1o User v\xe0 g\xe1n Role",id:"t\u1ea1o-user-v\xe0-g\xe1n-role",level:2},{value:"T\u1ea1o Client \u0111\u1ec3 c\u1ea5p token",id:"t\u1ea1o-client-\u0111\u1ec3-c\u1ea5p-token",level:2},{value:"Ghi ch\xfa",id:"ghi-ch\xfa",level:2},{value:"\ud83d\udcda T\xe0i li\u1ec7u tham kh\u1ea3o",id:"-t\xe0i-li\u1ec7u-tham-kh\u1ea3o",level:2}];function t(n){const e={a:"a",blockquote:"blockquote",code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",hr:"hr",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",ul:"ul",...(0,c.R)(),...n.components};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(e.header,{children:(0,i.jsx)(e.h1,{id:"h\u01b0\u1edbng-d\u1eabn-c\xe0i-\u0111\u1eb7t-v\xe0-s\u1eed-d\u1ee5ng-keycloak-windows--macos",children:"H\u01b0\u1edbng d\u1eabn c\xe0i \u0111\u1eb7t v\xe0 s\u1eed d\u1ee5ng Keycloak (Windows & macOS)"})}),"\n",(0,i.jsx)(e.h2,{id:"y\xeau-c\u1ea7u-h\u1ec7-th\u1ed1ng",children:"Y\xeau c\u1ea7u h\u1ec7 th\u1ed1ng"}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsx)(e.li,{children:"Java 17 (JDK)"}),"\n",(0,i.jsx)(e.li,{children:"PostgresSql (CSDL)"}),"\n",(0,i.jsx)(e.li,{children:"DBeaver"}),"\n",(0,i.jsx)(e.li,{children:"M\xe1y c\xf3 internet"}),"\n",(0,i.jsx)(e.li,{children:"Postman \u0111\u1ec3 test API"}),"\n"]}),"\n",(0,i.jsx)(e.hr,{}),"\n",(0,i.jsx)(e.h2,{id:"b\u01b0\u1edbc-1-t\u1ea3i-keycloak",children:"B\u01b0\u1edbc 1: T\u1ea3i Keycloak"}),"\n",(0,i.jsxs)(e.p,{children:["Truy c\u1eadp: ",(0,i.jsx)(e.a,{href:"https://www.keycloak.org/downloads",children:"https://www.keycloak.org/downloads"})]}),"\n",(0,i.jsxs)(e.p,{children:["T\u1ea3i b\u1ea3n ",(0,i.jsx)(e.code,{children:"keycloak-24.x.x.zip"})," (Keycloak.X)"]}),"\n",(0,i.jsx)(e.hr,{}),"\n",(0,i.jsx)(e.h2,{id:"c\xe0i-\u0111\u1eb7t-tr\xean-windows",children:"C\xe0i \u0111\u1eb7t tr\xean Windows"}),"\n",(0,i.jsxs)(e.ol,{children:["\n",(0,i.jsx)(e.li,{children:(0,i.jsx)(e.strong,{children:"Gi\u1ea3i n\xe9n:"})}),"\n"]}),"\n",(0,i.jsx)(e.pre,{children:(0,i.jsx)(e.code,{children:"C:\\ahaysoft\\code\\keycloak\n"})}),"\n",(0,i.jsxs)(e.ol,{children:["\n",(0,i.jsx)(e.li,{children:(0,i.jsx)(e.strong,{children:"Ch\u1ea1y keycloak:"})}),"\n"]}),"\n",(0,i.jsx)(e.pre,{children:(0,i.jsx)(e.code,{children:"cd C:\\ahaysoft\\code\\keycloak\\bin\r\n./kc.sh start-dev\n"})}),"\n",(0,i.jsx)(e.h2,{id:"c\xe0i-\u0111\u1eb7t-tr\xean-macos",children:"C\xe0i \u0111\u1eb7t tr\xean MacOs"}),"\n",(0,i.jsxs)(e.ol,{children:["\n",(0,i.jsx)(e.li,{children:(0,i.jsx)(e.strong,{children:"Gi\u1ea3i n\xe9n:"})}),"\n"]}),"\n",(0,i.jsx)(e.pre,{children:(0,i.jsx)(e.code,{children:"unzip keycloak-24.x.x.zip\n"})}),"\n",(0,i.jsxs)(e.ol,{children:["\n",(0,i.jsx)(e.li,{children:(0,i.jsx)(e.strong,{children:"Ch\u1ea1y keycloak:"})}),"\n"]}),"\n",(0,i.jsx)(e.pre,{children:(0,i.jsx)(e.code,{children:"cd keycloak-24.x.x/bin\r\n./kc.sh start-dev\n"})}),"\n",(0,i.jsx)(e.h2,{id:"c\xe0i-\u0111\u1eb7t-database",children:"C\xe0i \u0111\u1eb7t Database"}),"\n",(0,i.jsx)(e.pre,{children:(0,i.jsx)(e.code,{children:"./db.sh keycloak_db create-admin\r\n./db.sh keycloak_db create-user\r\n./db.sh keycloak_db new\n"})}),"\n",(0,i.jsx)(e.h2,{id:"c\xe0i-\u0111\u1eb7t-m\xf4i-tr\u01b0\u1eddng-ch\u1ea1y",children:"C\xe0i \u0111\u1eb7t m\xf4i tr\u01b0\u1eddng ch\u1ea1y"}),"\n",(0,i.jsxs)(e.h3,{id:"1-c\u1eadp-nh\u1eadt-file-c\u1ea5u-h\xecnh-keycloakconf",children:["1. C\u1eadp nh\u1eadt file c\u1ea5u h\xecnh ",(0,i.jsx)(e.code,{children:"keycloak.conf"})]}),"\n",(0,i.jsx)(e.p,{children:"Di chuy\u1ec3n v\xe0o th\u01b0 m\u1ee5c ch\u1ee9a file c\u1ea5u h\xecnh:"}),"\n",(0,i.jsx)(e.pre,{children:(0,i.jsx)(e.code,{children:"cd keycloak-24.x.x/conf\n"})}),"\n",(0,i.jsxs)(e.p,{children:["M\u1edf file ",(0,i.jsx)(e.code,{children:"keycloak.conf"})," v\xe0 c\u1eadp nh\u1eadt n\u1ed9i dung nh\u01b0 sau:"]}),"\n",(0,i.jsx)(e.pre,{children:(0,i.jsx)(e.code,{className:"language-properties",children:"# Basic settings for running in production. Change accordingly before deploying the server.\r\n\r\n# Database\r\ndb=postgres\r\ndb-username=keycloak\r\ndb-password=keycloak\r\ndb-url=***********************************************# Observability\r\nhealth-enabled=true\r\nmetrics-enabled=true\r\n\r\n# HTTP\r\nhttp-enabled=true\r\n\r\n# Proxy v\xe0 hostname\r\nproxy=edge\r\nhostname-strict=false\r\nhostname-strict-https=false\r\nhostname=keycloak\r\n\r\n# N\u1ebfu c\u1ea7n HTTPS:\r\n# https-certificate-file=${kc.home.dir}/conf/server.crt.pem\r\n# https-certificate-key-file=${kc.home.dir}/conf/server.key.pem\n"})}),"\n",(0,i.jsxs)(e.blockquote,{children:["\n",(0,i.jsx)(e.p,{children:(0,i.jsx)(e.strong,{children:"\ud83d\udca1 Ghi ch\xfa:"})}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.code,{children:"db-url"})," c\u1ea7n tr\u1ecf \u0111\xfang \u0111\u1ebfn database PostgresSQL \u0111\xe3 kh\u1edfi t\u1ea1o s\u1eb5n."]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.code,{children:"proxy=edge"})," \u0111\u01b0\u1ee3c d\xf9ng khi Keycloak ch\u1ea1y sau reverse proxy nh\u01b0 Nginx, Traefik,..."]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.code,{children:"hostname-strict=false"})," gi\xfap Keycloak ho\u1ea1t \u0111\u1ed9ng linh ho\u1ea1t h\u01a1n trong m\xf4i tr\u01b0\u1eddng dev ho\u1eb7c m\u1ea1ng n\u1ed9i b\u1ed9."]}),"\n",(0,i.jsxs)(e.li,{children:["N\u1ebfu ch\u01b0a c\u1ea5u h\xecnh HTTPS, c\xf3 th\u1ec3 \u0111\u1ec3 nguy\xean ph\u1ea7n ",(0,i.jsx)(e.code,{children:"https-certificate-*"})," nh\u01b0 \u0111ang comment."]}),"\n"]}),"\n"]}),"\n",(0,i.jsx)(e.h2,{id:"2-c\u1ea5u-h\xecnh-spring-\u0111\u1ec3-k\u1ebft-n\u1ed1i-v\u1edbi-keycloak",children:"2. C\u1ea5u h\xecnh Spring \u0111\u1ec3 k\u1ebft n\u1ed1i v\u1edbi Keycloak"}),"\n",(0,i.jsxs)(e.p,{children:["C\u1eadp nh\u1eadp b\u1ed5 sung file ",(0,i.jsx)(e.code,{children:"application-{profile}.yml"})," th\xeam c\u1ea5u h\xecnh sau:"]}),"\n",(0,i.jsx)(e.pre,{children:(0,i.jsx)(e.code,{className:"language-yaml",children:"spring:\r\n  security:\r\n    oauth2:\r\n      resourceserver:\r\n        jwt:\r\n          issuer-uri: http://keycloak:8080/realms/{realm_name}\r\n\r\nkeycloak:\r\n  server-url: http://keycloak:8080\r\n  realm: {realm_name}\r\n  scope: openid profile email\r\n  client-id: spring-client\r\n  client-secret: {client-secret}\r\n  token-uri: http://keycloak:8080/realms/{realm_name}/protocol/openid-connect/token\r\n  user-info-uri: http://keycloak:8080/realms/{realm_name}/protocol/openid-connect/userinfo\n"})}),"\n",(0,i.jsxs)(e.blockquote,{children:["\n",(0,i.jsx)(e.p,{children:(0,i.jsx)(e.strong,{children:"\ud83d\udca1 L\u01b0u \xfd:"})}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.code,{children:"issuer-uri"})," l\xe0 \u0111\u01b0\u1eddng d\u1eabn t\u1edbi realm trong Keycloak, d\xf9ng \u0111\u1ec3 x\xe1c minh JWT."]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.code,{children:"client-id"})," v\xe0 ",(0,i.jsx)(e.code,{children:"client-secret"})," ph\u1ea3i tr\xf9ng v\u1edbi client c\u1ea5u h\xecnh trong realm c\u1ee7a Keycloak."]}),"\n",(0,i.jsxs)(e.li,{children:["C\xe1c endpoint nh\u01b0 ",(0,i.jsx)(e.code,{children:"token-uri"})," v\xe0 ",(0,i.jsx)(e.code,{children:"user-info-uri"})," d\xf9ng \u0111\u1ec3 l\u1ea5y token v\xe0 th\xf4ng tin ng\u01b0\u1eddi d\xf9ng."]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.code,{children:"keycloak"})," block l\xe0 custom config, c\xf3 th\u1ec3 d\xf9ng ",(0,i.jsx)(e.code,{children:'@ConfigurationProperties("keycloak")'})," \u0111\u1ec3 \xe1nh x\u1ea1 trong code."]}),"\n"]}),"\n"]}),"\n",(0,i.jsxs)(e.h2,{id:"3-c\u1ea5u-h\xecnh-file-hosts",children:["3. C\u1ea5u h\xecnh file ",(0,i.jsx)(e.code,{children:"hosts"})]}),"\n",(0,i.jsxs)(e.p,{children:["\u0110\u1ec3 Spring Boot c\xf3 th\u1ec3 g\u1ecdi \u0111\xfang \u0111\u1ecba ch\u1ec9 ",(0,i.jsx)(e.code,{children:"http://keycloak:8080"}),", c\u1ea7n th\xeam c\u1ea5u h\xecnh DNS t\u1ea1m th\u1eddi v\xe0o file ",(0,i.jsx)(e.code,{children:"/etc/hosts"})," (tr\xean Linux/macOS) ho\u1eb7c ",(0,i.jsx)(e.code,{children:"C:\\Windows\\System32\\drivers\\etc\\hosts"})," (tr\xean Windows)."]}),"\n",(0,i.jsxs)(e.p,{children:["Th\xeam d\xf2ng sau v\xe0o cu\u1ed1i file ",(0,i.jsx)(e.code,{children:"hosts"}),":"]}),"\n",(0,i.jsx)(e.pre,{children:(0,i.jsx)(e.code,{children:"127.0.0.1   keycloak\n"})}),"\n",(0,i.jsxs)(e.blockquote,{children:["\n",(0,i.jsx)(e.p,{children:(0,i.jsx)(e.strong,{children:"\ud83d\udca1 L\u01b0u \xfd:"})}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:["D\xf2ng tr\xean gi\xfap \xe1nh x\u1ea1 t\xean mi\u1ec1n ",(0,i.jsx)(e.code,{children:"keycloak"})," v\u1ec1 ",(0,i.jsx)(e.code,{children:"localhost"}),"."]}),"\n",(0,i.jsxs)(e.li,{children:["N\u1ebfu ch\u1ea1y Keycloak trong Docker Compose, c\xf3 th\u1ec3 \u0111\u1ec3 Spring Boot d\xf9ng ",(0,i.jsx)(e.code,{children:"keycloak"})," l\xe0m hostname, v\xec Docker Compose t\u1ef1 t\u1ea1o DNS n\u1ed9i b\u1ed9."]}),"\n",(0,i.jsxs)(e.li,{children:["N\u1ebfu ch\u1ea1y tr\xean m\xf4i tr\u01b0\u1eddng th\u1eadt (VD: staging, production), c\u1ea7n thay ",(0,i.jsx)(e.code,{children:"keycloak"})," b\u1eb1ng domain th\u1ef1c t\u1ebf ho\u1eb7c IP c\u1ee7a Keycloak server."]}),"\n"]}),"\n"]}),"\n",(0,i.jsx)(e.hr,{}),"\n",(0,i.jsx)(e.h2,{id:"t\u1ea1o-admin-user",children:"T\u1ea1o Admin User"}),"\n",(0,i.jsx)(e.p,{children:"L\u1ea7n \u0111\u1ea7u ch\u1ea1y, Keycloak y\xeau c\u1ea7u t\u1ea1o admin user:"}),"\n",(0,i.jsx)(e.pre,{children:(0,i.jsx)(e.code,{children:"Username: admin\r\nPassword: admin123\n"})}),"\n",(0,i.jsxs)(e.p,{children:["Truy c\u1eadp: ",(0,i.jsx)(e.a,{href:"http://localhost:8080",children:"http://localhost:8080"})," \u2192 b\u1ea5m ",(0,i.jsx)(e.strong,{children:'"Administration Console"'})]}),"\n",(0,i.jsx)(e.hr,{}),"\n",(0,i.jsx)(e.h2,{id:"t\u1ea1o-realm-m\u1edbi",children:"T\u1ea1o Realm m\u1edbi"}),"\n",(0,i.jsxs)(e.ol,{children:["\n",(0,i.jsxs)(e.li,{children:["V\xe0o admin console \u2192 menu tr\xe1i \u2192 ch\u1ecdn ",(0,i.jsx)(e.strong,{children:"Realm selector"})," \u2192 ",(0,i.jsx)(e.strong,{children:"Create Realm"})]}),"\n",(0,i.jsxs)(e.li,{children:["Nh\u1eadp:","\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:["Realm Name: ",(0,i.jsx)(e.code,{children:"t\xean-reaml"})]}),"\n",(0,i.jsx)(e.li,{children:"Save"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,i.jsx)(e.hr,{}),"\n",(0,i.jsx)(e.h2,{id:"t\u1ea1o-role",children:"T\u1ea1o Role"}),"\n",(0,i.jsxs)(e.ol,{children:["\n",(0,i.jsxs)(e.li,{children:["V\xe0o ",(0,i.jsx)(e.strong,{children:"Realm Settings > Roles"})]}),"\n",(0,i.jsxs)(e.li,{children:["Nh\u1ea5n ",(0,i.jsx)(e.strong,{children:"Create Role"}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:["Role name: ",(0,i.jsx)(e.code,{children:"t\xean-role"})]}),"\n",(0,i.jsx)(e.li,{children:"Save"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,i.jsxs)(e.p,{children:["L\u1eb7p l\u1ea1i n\u1ebfu mu\u1ed1n th\xeam role ",(0,i.jsx)(e.code,{children:"admin"}),", ",(0,i.jsx)(e.code,{children:"manager"}),", v.v."]}),"\n",(0,i.jsx)(e.hr,{}),"\n",(0,i.jsx)(e.h2,{id:"t\u1ea1o-user-v\xe0-g\xe1n-role",children:"T\u1ea1o User v\xe0 g\xe1n Role"}),"\n",(0,i.jsxs)(e.ol,{children:["\n",(0,i.jsxs)(e.li,{children:["\n",(0,i.jsxs)(e.p,{children:["Menu b\xean tr\xe1i \u2192 ",(0,i.jsx)(e.strong,{children:"Users \u2192 Add user"})]}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:["Username: ",(0,i.jsx)(e.code,{children:"testuser"})]}),"\n",(0,i.jsx)(e.li,{children:"Email, t\xean: nh\u1eadp th\xf4ng tin n\u1ebfu c\xf3"}),"\n",(0,i.jsx)(e.li,{children:"Save"}),"\n"]}),"\n"]}),"\n",(0,i.jsxs)(e.li,{children:["\n",(0,i.jsxs)(e.p,{children:["Tab ",(0,i.jsx)(e.strong,{children:"Credentials"})]}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:["Set password: ",(0,i.jsx)(e.code,{children:"123456"})]}),"\n",(0,i.jsx)(e.li,{children:'Turn off "Temporary", r\u1ed3i Save'}),"\n"]}),"\n"]}),"\n",(0,i.jsxs)(e.li,{children:["\n",(0,i.jsxs)(e.p,{children:["Tab ",(0,i.jsx)(e.strong,{children:"Role Mappings"})]}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:["Ch\u1ecdn ",(0,i.jsx)(e.code,{children:"Available Roles"}),": ch\u1ecdn ",(0,i.jsx)(e.code,{children:"user"})," \u2192 nh\u1ea5n ",(0,i.jsx)(e.strong,{children:"Add selected"})]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,i.jsx)(e.hr,{}),"\n",(0,i.jsx)(e.h2,{id:"t\u1ea1o-client-\u0111\u1ec3-c\u1ea5p-token",children:"T\u1ea1o Client \u0111\u1ec3 c\u1ea5p token"}),"\n",(0,i.jsxs)(e.ol,{children:["\n",(0,i.jsxs)(e.li,{children:["\n",(0,i.jsxs)(e.p,{children:["Menu tr\xe1i \u2192 ",(0,i.jsx)(e.strong,{children:"Clients \u2192 Create client"})]}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:["Client ID: ",(0,i.jsx)(e.code,{children:"my-client"})]}),"\n",(0,i.jsxs)(e.li,{children:["Client type: ",(0,i.jsx)(e.code,{children:"public"})]}),"\n",(0,i.jsxs)(e.li,{children:["Root URL: ",(0,i.jsx)(e.code,{children:"http://localhost:3000"})," (ho\u1eb7c b\u1ecf tr\u1ed1ng n\u1ebfu kh\xf4ng c\u1ea7n UI)"]}),"\n"]}),"\n"]}),"\n",(0,i.jsxs)(e.li,{children:["\n",(0,i.jsxs)(e.p,{children:["Save \u2192 Tab ",(0,i.jsx)(e.strong,{children:"Settings"}),":"]}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsx)(e.li,{children:"Standard Flow Enabled: \u2705"}),"\n",(0,i.jsx)(e.li,{children:"Direct Access Grants Enabled: \u2705 (quan tr\u1ecdng \u0111\u1ec3 l\u1ea5y token qua Postman)"}),"\n",(0,i.jsx)(e.li,{children:"Save"}),"\n"]}),"\n"]}),"\n",(0,i.jsxs)(e.li,{children:["\n",(0,i.jsxs)(e.p,{children:["V\xe0o ",(0,i.jsx)(e.strong,{children:"Clients \u2192 Create client"})]}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Client ID"}),": ",(0,i.jsx)(e.code,{children:"my-client"})]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Client type"}),": ",(0,i.jsx)(e.code,{children:"confidential"})]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Root URL"}),": \u0111\u1ec3 tr\u1ed1ng n\u1ebfu kh\xf4ng c\u1ea7n giao di\u1ec7n frontend"]}),"\n"]}),"\n"]}),"\n",(0,i.jsxs)(e.li,{children:["\n",(0,i.jsxs)(e.p,{children:["Nh\u1ea5n ",(0,i.jsx)(e.strong,{children:"Save"})]}),"\n"]}),"\n",(0,i.jsxs)(e.li,{children:["\n",(0,i.jsxs)(e.p,{children:["Sau khi l\u01b0u \u2192 v\xe0o tab ",(0,i.jsx)(e.strong,{children:"Credentials"})]}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:["M\u1ee5c ",(0,i.jsx)(e.strong,{children:"Client Secret"})," s\u1ebd xu\u1ea5t hi\u1ec7n"]}),"\n",(0,i.jsx)(e.li,{children:"Nh\u1ea5n icon con m\u1eaft \u0111\u1ec3 hi\u1ec7n secret"}),"\n",(0,i.jsxs)(e.li,{children:["Copy gi\xe1 tr\u1ecb ",(0,i.jsx)(e.code,{children:"client_secret"})," \u0111\u1ec3 s\u1eed d\u1ee5ng trong c\xe1c request"]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,i.jsx)(e.hr,{}),"\n",(0,i.jsx)(e.h2,{id:"ghi-ch\xfa",children:"Ghi ch\xfa"}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsx)(e.li,{children:'Realm gi\u1ed1ng nh\u01b0 1 "namespace", d\xf9ng \u0111\u1ec3 t\xe1ch bi\u1ec7t h\u1ec7 th\u1ed1ng \u0111\u0103ng nh\u1eadp'}),"\n",(0,i.jsx)(e.li,{children:"Client d\xf9ng \u0111\u1ec3 c\u1ea5p token cho \u1ee9ng d\u1ee5ng c\u1ee5 th\u1ec3"}),"\n",(0,i.jsx)(e.li,{children:"Role l\xe0 ph\xe2n quy\u1ec1n logic, c\u1ea7n validate \u1edf backend"}),"\n",(0,i.jsx)(e.li,{children:"Backend d\xf9ng Spring Boot ho\u1eb7c Express c\xf3 th\u1ec3 \u0111\u1ecdc token, gi\u1ea3i m\xe3 role"}),"\n",(0,i.jsx)(e.li,{children:"N\u1ebfu d\xf9ng Windows m\xe0 export/import l\u1ed7i, n\xean d\xf9ng WSL ho\u1eb7c Docker"}),"\n"]}),"\n",(0,i.jsx)(e.hr,{}),"\n",(0,i.jsx)(e.h2,{id:"-t\xe0i-li\u1ec7u-tham-kh\u1ea3o",children:"\ud83d\udcda T\xe0i li\u1ec7u tham kh\u1ea3o"}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsx)(e.li,{children:(0,i.jsx)(e.a,{href:"https://www.keycloak.org/documentation",children:"https://www.keycloak.org/documentation"})}),"\n",(0,i.jsx)(e.li,{children:(0,i.jsx)(e.a,{href:"https://www.keycloak.org/getting-started",children:"https://www.keycloak.org/getting-started"})}),"\n",(0,i.jsx)(e.li,{children:(0,i.jsx)(e.a,{href:"https://www.keycloak.org/docs/latest/securing_apps/",children:"https://www.keycloak.org/docs/latest/securing_apps/"})}),"\n"]})]})}function a(n={}){const{wrapper:e}={...(0,c.R)(),...n.components};return e?(0,i.jsx)(e,{...n,children:(0,i.jsx)(t,{...n})}):t(n)}}}]);