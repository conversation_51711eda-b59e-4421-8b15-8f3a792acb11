"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[5963],{494:(n,e,t)=>{t.d(e,{R:()=>h,x:()=>s});var i=t(6372);const r={},c=i.createContext(r);function h(n){const e=i.useContext(c);return i.useMemo(function(){return"function"==typeof n?n(e):{...e,...n}},[e,n])}function s(n){let e;return e=n.disableParentContext?"function"==typeof n.components?n.components(r):n.components||r:h(n.components),i.createElement(c.Provider,{value:e},n.children)}},9034:(n,e,t)=>{t.r(e),t.d(e,{assets:()=>l,contentTitle:()=>h,default:()=>u,frontMatter:()=>c,metadata:()=>s,toc:()=>o});var i=t(216),r=t(494);const c={},h="HRM, Workflow, Project Features.",s={id:"datatp-crm/developer/feature/HRM",title:"HRM, Workflow, Project Features.",description:"1. Task Request.",source:"@site/docs/datatp-crm/developer/feature/HRM.md",sourceDirName:"datatp-crm/developer/feature",slug:"/datatp-crm/developer/feature/HRM",permalink:"/docs/datatp-crm/developer/feature/HRM",draft:!1,unlisted:!1,tags:[],version:"current",frontMatter:{},sidebar:"developerSidebar",previous:{title:"CRM Features",permalink:"/docs/datatp-crm/developer/feature/crm"},next:{title:"MAIL-TICKET",permalink:"/docs/datatp-crm/developer/feature/MAIL-TICKET"}},l={},o=[{value:"1. Task Request.",id:"1-task-request",level:3}];function a(n){const e={code:"code",h1:"h1",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",ul:"ul",...(0,r.R)(),...n.components};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(e.header,{children:(0,i.jsx)(e.h1,{id:"hrm-workflow-project-features",children:"HRM, Workflow, Project Features."})}),"\n",(0,i.jsx)(e.h3,{id:"1-task-request",children:"1. Task Request."}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsx)(e.li,{children:"Ng\u01b0\u1eddi tri\u1ec3n khai: \u0110\xe0n, An, Nh\u1eadt."}),"\n",(0,i.jsx)(e.li,{children:"Ng\u01b0\u1eddi request: ch\u1ecb Quy\xean, T\u1ea5m."}),"\n"]}),"\n",(0,i.jsxs)(e.ol,{children:["\n",(0,i.jsxs)(e.li,{children:["\n",(0,i.jsx)(e.p,{children:"\u0110\u1eb7t v\u1ea5n \u0111\u1ec1:"}),"\n",(0,i.jsx)(e.pre,{children:(0,i.jsx)(e.code,{children:"T\u1ea1i ch\u1ed7 team k\u1ebf to\xe1n mu\u1ed1n x\xe2y d\u1ef1ng c\xe1i request l\u1ed7i nh\u01b0 ki\u1ec3u m\xecnh \u0111ki ph\xe9p hay duy\u1ec7t ng\xe0y c\xf4ng \u1ea5y, \u0111\u1ec3 c\xe1c b\u1ed9 ph\u1eadn n\u1ebfu ph\xe1t sinh l\u1ed7i th\xec s\u1ebd request tr\xean ph\u1ea7n m\u1ec1m, k\u1ebf to\xe1n s\u1ebd c\u0103n c\u1ee9 v\xe0o \u0111\xf3 ngo\xe0i vi\u1ec7c x\u1eed l\xfd ph\xe1t sinh, c\u0169ng s\u1ebd l\xe0 t\u1ed5ng h\u1ee3p \u0111\u1ec3 cu\u1ed1i th\xe1ng report v\xe0 t\xednh v\xe0o l\u01b0\u01a1ng hi\u1ec7u qu\u1ea3 sau n\xe0y.\n\nNh\u1eefng tr\u01b0\u1eddng th\xf4ng tin c\u1ea7n \u0111\u1ec3 nh\u1eadn request c\u01a1 b\u1ea3n tr\u01b0\u1edbc.\n  1. T\xean ng\u01b0\u1edfi request.\n  2. B\u1ed9 ph\u1eadn/ VP V\u1ec7 Tinh.\n  3. S\u1ed1 file\n  4. lo\u1ea1i request (m\u1edf file, s\u1eeda ho\xe1 \u0111\u01a1n ...);\n  5. l\xfd do request (text) ...\n  6. T\xean d\u1ecbch v\u1ee5/ kh\xe1ch h\xe0ng.\n"})}),"\n",(0,i.jsx)(e.p,{children:"Hi\u1ec7n t\u1ea1i, c\xe1c y\xeau c\u1ea7u nh\u01b0 s\u1eeda h\xf3a \u0111\u01a1n, ho\u1eb7c m\u1edf file \u0111\u01b0\u1ee3c trao \u0111\u1ed5i qua email ho\u1eb7c nh\xf3m chat (Zalo, Teams), d\u1eabn \u0111\u1ebfn:"}),"\n",(0,i.jsx)(e.p,{children:'H\u1ea1n ch\u1ebf:\nQu\xe1 t\u1ea3i th\xf4ng tin trong nh\xf3m chat, kh\xf3 theo d\xf5i l\u1ecbch s\u1eed y\xeau c\u1ea7u, kh\xf3 t\xednh KPI.\nThi\u1ebfu giao di\u1ec7n t\u1ed5ng quan \u0111\u1ec3 xem tr\u1ea1ng th\xe1i, lo\u1ea1i y\xeau c\u1ea7u, ho\u1eb7c quy tr\xecnh ph\xea duy\u1ec7t.\nKh\xf4ng c\xf3 th\xf4ng b\xe1o t\u1ef1 \u0111\u1ed9ng khi tr\u1ea1ng th\xe1i y\xeau c\u1ea7u thay \u0111\u1ed5i (v\xed d\u1ee5: t\u1eeb "New" sang "Approved").\nKh\xf3 t\xedch h\u1ee3p v\u1edbi c\xe1c ph\xe2n h\u1ec7 kh\xe1c nh\u01b0 HRM, K\u1ebf to\xe1n.'}),"\n"]}),"\n",(0,i.jsxs)(e.li,{children:["\n",(0,i.jsx)(e.p,{children:"Gi\u1ea3i ph\xe1p:"}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsx)(e.li,{children:"Qu\u1ea3n l\xfd Quy tr\xecnh (Workflow Management)"}),"\n",(0,i.jsxs)(e.li,{children:["Nghi\u1ec7p v\u1ee5 li\xean quan:","\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsx)(e.li,{children:"Request m\u1edf file c\u1eadp nh\u1eadt th\xf4ng tin."}),"\n",(0,i.jsx)(e.li,{children:"Request ch\u1ec9nh s\u1eeda ho\xe1 \u0111\u01a1n, settle, ..."}),"\n",(0,i.jsx)(e.li,{children:"Request mua trang thi\u1ebft b\u1ecb."}),"\n",(0,i.jsx)(e.li,{children:"Request t\u0103ng ca, ngh\u1ec9 ph\xe9p, ..."}),"\n"]}),"\n"]}),"\n",(0,i.jsxs)(e.li,{children:["Chi ti\u1ebft:\n\u0111\u1ecbnh ngh\u0129a c\xe1c b\u01b0\u1edbc ph\xea duy\u1ec7t ho\u1eb7c x\u1eed l\xfd cho t\u1eebng lo\u1ea1i y\xeau c\u1ea7u.","\n",(0,i.jsx)(e.pre,{children:(0,i.jsx)(e.code,{children:"- T\u1ea1o m\xe0n h\xecnh ri\xeang cho Request Management.\n- \u0110\u1ecbnh ngh\u0129a c\xe1c lo\u1ea1i request (m\u1edf file, s\u1eeda ho\xe1 \u0111\u01a1n, ...).\n- \u0110\u1ecbnh ngh\u0129a c\xe1c tr\u1ea1ng th\xe1i c\u1ee7a request (new, completed, rejected, ...).\n- \u0110\u1ecbnh ngh\u0129a c\xe1c vai tr\xf2 tham gia (requestor, assignee, approver, ...)\n"})}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsx)(e.li,{children:"G\u1eedi request -> G\u1eedi mail ng\u01b0\u1eddi li\xean quan -> Completed/ Rejected."}),"\n",(0,i.jsxs)(e.li,{children:["Ph\xe2n quy\u1ec1n/ Scope:","\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsx)(e.li,{children:"Requestor: User t\u1ea1o request."}),"\n",(0,i.jsx)(e.li,{children:"Approver: User \u0111\u01b0\u1ee3c giao vi\u1ec7c x\u1eed l\xfd request."}),"\n",(0,i.jsx)(e.li,{children:"Observer: User \u0111\u01b0\u1ee3c ph\xe9p xem request."}),"\n",(0,i.jsx)(e.li,{children:"Data Scope: M\xe0n h\xecnh Requests c\xf3 th\u1ec3 nh\xf3m theo t\u1eebng b\u1ed9 ph\u1eadn (tree) ho\u1eb7c kh\xf4ng (d\u1ea1ng b\u1ea3ng)."}),"\n",(0,i.jsx)(e.li,{children:"C\xf9ng b\u1ed9 ph\u1eadn c\xf3 th\u1ec3 xem requests c\u1ee7a nhau, ng\u01b0\u1eddi tham gia \u0111\u1ec1u xem \u0111\u01b0\u1ee3c request."}),"\n"]}),"\n"]}),"\n"]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,i.jsxs)(e.li,{children:["\n",(0,i.jsx)(e.p,{children:"Tasks:"}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsx)(e.li,{children:"[Dan] - Design code, chu\u1ea9n b\u1ecb code, QA."}),"\n",(0,i.jsx)(e.li,{children:"[Nhat] - Function g\u1eedi mail:\nKhi t\u1ea1o request -> G\u1eedi mail cho ng\u01b0\u1eddi li\xean quan -> Approved/ Rejected qua mail ->\nhi\u1ec3n th\u1ecb m\xe0n h\xecnh th\xf4ng b\xe1o cho ng\u01b0\u1eddi d\xf9ng. -> G\u1eedi k\u1ebft qu\u1ea3 Approved/Rejected qua mail cho ng\u01b0\u1eddi li\xean quan."}),"\n",(0,i.jsx)(e.li,{children:"[An] - Implement code, Backend, Frontend."}),"\n"]}),"\n"]}),"\n"]})]})}function u(n={}){const{wrapper:e}={...(0,r.R)(),...n.components};return e?(0,i.jsx)(e,{...n,children:(0,i.jsx)(a,{...n})}):a(n)}}}]);