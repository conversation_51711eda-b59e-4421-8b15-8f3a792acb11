"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[7112],{494:(e,t,n)=>{n.d(t,{R:()=>o,x:()=>c});var r=n(6372);const i={},s=r.createContext(i);function o(e){const t=r.useContext(s);return r.useMemo(function(){return"function"==typeof e?e(t):{...t,...e}},[t,e])}function c(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(i):e.components||i:o(e.components),r.createElement(s.Provider,{value:t},e.children)}},3337:(e,t,n)=>{n.r(t),n.d(t,{assets:()=>p,contentTitle:()=>o,default:()=>u,frontMatter:()=>s,metadata:()=>c,toc:()=>d});var r=n(216),i=n(494);const s={sidebar_position:8,sidebar:!1,hide_table_of_contents:!0,displayed_sidebar:"userSidebar"},o=void 0,c={id:"datatp-hr/user/kpi/kpi_for_employee",title:"kpi_for_employee",description:"I. Truy c\u1eadp KPI",source:"@site/i18n/vi/docusaurus-plugin-content-docs/current/datatp-hr/user/kpi/kpi_for_employee.md",sourceDirName:"datatp-hr/user/kpi",slug:"/datatp-hr/user/kpi/kpi_for_employee",permalink:"/docs/datatp-hr/user/kpi/kpi_for_employee",draft:!1,unlisted:!1,tags:[],version:"current",sidebarPosition:8,frontMatter:{sidebar_position:8,sidebar:!1,hide_table_of_contents:!0,displayed_sidebar:"userSidebar"},sidebar:"userSidebar"},p={},d=[{value:"I. Truy c\u1eadp KPI",id:"i-truy-c\u1eadp-kpi",level:2}];function a(e){const t={h2:"h2",li:"li",ol:"ol",p:"p",strong:"strong",...(0,i.R)(),...e.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(t.h2,{id:"i-truy-c\u1eadp-kpi",children:"I. Truy c\u1eadp KPI"}),"\n",(0,r.jsx)(t.p,{children:"Nghi\u1ec7p v\u1ee5 li\xean quan:"}),"\n",(0,r.jsxs)(t.ol,{children:["\n",(0,r.jsxs)(t.li,{children:["Ch\u1ecdn ",(0,r.jsx)(t.strong,{children:"Menu"})]}),"\n",(0,r.jsxs)(t.li,{children:["Ch\u1ecdn App ",(0,r.jsx)(t.strong,{children:"KPI"})]}),"\n"]})]})}function u(e={}){const{wrapper:t}={...(0,i.R)(),...e.components};return t?(0,r.jsx)(t,{...e,children:(0,r.jsx)(a,{...e})}):a(e)}}}]);