"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[7049],{238:(e,n,l)=>{l.r(n),l.d(n,{assets:()=>t,contentTitle:()=>r,default:()=>a,frontMatter:()=>c,metadata:()=>o,toc:()=>d});var s=l(216),i=l(494);const c={sidebar_position:2},r="Changelog",o={id:"datatp-tms/developer/CHANGELOG",title:"Changelog",description:"All notable changes to this project will be documented in this file.",source:"@site/i18n/vi/docusaurus-plugin-content-docs/current/datatp-tms/developer/CHANGELOG.md",sourceDirName:"datatp-tms/developer",slug:"/datatp-tms/developer/CHANGELOG",permalink:"/docs/datatp-tms/developer/CHANGELOG",draft:!1,unlisted:!1,tags:[],version:"current",sidebarPosition:2,frontMatter:{sidebar_position:2}},t={},d=[{value:"Planning",id:"planning",level:2},{value:"Changed",id:"changed",level:3},{value:"TODO [Unreleased]",id:"todo-unreleased",level:3},{value:"[R20241113]",id:"r20241113",level:3},{value:"[R20241111]",id:"r20241111",level:3},{value:"[R20241107]",id:"r20241107",level:3},{value:"[R20241106]",id:"r20241106",level:3}];function h(e){const n={em:"em",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,i.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.header,{children:(0,s.jsx)(n.h1,{id:"changelog",children:"Changelog"})}),"\n",(0,s.jsx)(n.p,{children:"All notable changes to this project will be documented in this file."}),"\n",(0,s.jsx)(n.h2,{id:"planning",children:"Planning"}),"\n",(0,s.jsxs)(n.table,{children:[(0,s.jsx)(n.thead,{children:(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.th,{style:{textAlign:"left"},children:"Timeline"}),(0,s.jsx)(n.th,{style:{textAlign:"left"},children:"Status"}),(0,s.jsx)(n.th,{style:{textAlign:"left"},children:"Task"}),(0,s.jsx)(n.th,{style:{textAlign:"left"},children:"Description"})]})}),(0,s.jsx)(n.tbody,{children:(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{style:{textAlign:"left"},children:"2024-11-07"}),(0,s.jsx)(n.td,{style:{textAlign:"left"},children:"Doing"}),(0,s.jsx)(n.td,{style:{textAlign:"left"},children:"Support team Sales HP, HAN"}),(0,s.jsx)(n.td,{style:{textAlign:"left"},children:"Support team Sales HP, HAN fix bugs, c\u1eadp nh\u1eadt feedback"})]})})]}),"\n",(0,s.jsx)(n.h3,{id:"changed",children:"Changed"}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.em,{children:"Format: [Task Desc - Link merge request,...]"})}),"\n",(0,s.jsx)(n.h3,{id:"todo-unreleased",children:"TODO [Unreleased]"}),"\n",(0,s.jsx)(n.p,{children:"Please read and note the below task carefully.\nMake sure you fulfill all of them before filing an issue."}),"\n",(0,s.jsxs)(n.ol,{children:["\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)("input",{type:"checkbox",disabled:!0})," ",(0,s.jsx)("span",{style:{color:"red"},children:"[High]"})," - ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"[Feature]"})," ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"#sales"})," Ho\xe0n thi\u1ec7n c\xe1c m\xe0n h\xecnh, ch\u1ee9c n\u0103ng module sales."]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)("input",{type:"checkbox",disabled:!0})," ",(0,s.jsx)("span",{style:{color:"red"},children:"[High]"})," - ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"[Enhance]"})," ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"#system"})," Enhance th\xf4ng b\xe1o l\u1ed7i g\u1eedi t\u1eeb server hi\u1ec3n th\u1ecb cho users."]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)("input",{type:"checkbox",disabled:!0})," ",(0,s.jsx)("span",{style:{color:"orange"},children:"[Medium]"})," - ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"[Feature]"})," ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"#price"})," Sync th\xf4ng tin (BfSOne username, bfsone contact ID, email, name) cho employee c\xf4ng ty BEEHCM, BEEDAD."]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)("input",{type:"checkbox",disabled:!0})," ",(0,s.jsx)("span",{style:{color:"orange"},children:"[Medium]"})," - ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"[Enhance]"})," ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"#price"})," Sync l\u1ea1i th\xf4ng tin Location (Airport) t\u1eeb bfsone."]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)("input",{type:"checkbox",disabled:!0})," ",(0,s.jsx)("span",{style:{color:"orange"},children:"[Medium]"})," - ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"#price"})," C\u1ea5u h\xecnh Minimum profit/ profit margin cho customer charge (Gi\xe1 b\xe1n cho kh\xe1ch)"]}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Minimum profit (M\u1ee9c gi\xe1 t\u1ed1i thi\u1ec3u \u0111\u01b0\u1ee3c b\xe1n cho kh\xe1ch - li\xean quan \u0111\u1ebfn internal nhi\u1ec1u h\u01a1n)"}),"\n",(0,s.jsx)(n.li,{children:"Profit Margin: External - Margin apply l\xean gi\xe1 \u0111\u1ec3 ch\xe0o cho kh\xe1ch khi kh\xe1ch match gi\xe1."}),"\n"]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)("input",{type:"checkbox",disabled:!0})," ",(0,s.jsx)("span",{style:{color:"green"},children:"[Low]"})," - ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"#price"})," Generate template email client, pricing - m\xe0n h\xecnh quotation (\u0111\u1ee3i user th\u1ed1ng nh\u1ea5t form mail)"]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)("input",{type:"checkbox",disabled:!0})," ",(0,s.jsx)("span",{style:{color:"red"},children:"[High]"})," - ",(0,s.jsx)("span",{style:{backgroundColor:"red"},children:"#sales"})," C\u1eadp nh\u1eadt h\u01b0\u1edbng d\u1eabn module sales"]}),"\n"]}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"r20241113",children:"[R20241113]"}),"\n",(0,s.jsxs)(n.ol,{children:["\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)("input",{type:"checkbox",disabled:!0,checked:!0})," ",(0,s.jsx)("span",{style:{color:"orange"},children:"[Medium]"})," - ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"[Bugs]"})," ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"#sales"})," Fix l\u1ed7i thi\u1ebfu note khi generate email."]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)("input",{type:"checkbox",disabled:!0,checked:!0})," ",(0,s.jsx)("span",{style:{color:"red"},children:"[High]"})," - ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"[Security]"})," Fix l\u1ed7i ph\xe2n quy\u1ec1n li\xean quan \u0111\u1ebfn App Permission v\xe0 login ID."]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)("input",{type:"checkbox",disabled:!0,checked:!0})," ",(0,s.jsx)("span",{style:{color:"red"},children:"[High]"})," - ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"[Security]"})," Ph\xe2n t\xedch v\xe0 clean t\xe0i kho\u1ea3n b\u1ecb tr\xf9ng login ID do normalize login ID theo lowercase."]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)("input",{type:"checkbox",disabled:!0,checked:!0})," ",(0,s.jsx)("span",{style:{color:"orange"},children:"[Medium]"})," - ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"[Feature]"})," ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"#sales"})," Th\xeam t\xednh n\u0103ng qu\u1ea3n l\xfd mail request, cho ph\xe9p user copy v\xe0 g\u1eedi l\u1ea1i y\xeau c\u1ea7u."]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)("input",{type:"checkbox",disabled:!0,checked:!0})," ",(0,s.jsx)("span",{style:{color:"orange"},children:"[Medium]"})," - ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"[Enhance]"})," ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"#sales"})," C\u1ea3i thi\u1ec7n giao di\u1ec7n v\xe0 tr\u1ea3i nghi\u1ec7m ng\u01b0\u1eddi d\xf9ng cho m\xe0n h\xecnh match price."]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)("input",{type:"checkbox",disabled:!0,checked:!0})," ",(0,s.jsx)("span",{style:{color:"orange"},children:"[Medium]"})," - ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"[Feature]"})," ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"#price"})," Ho\xe0n thi\u1ec7n m\xe0n h\xecnh My Pricing List, Upload data cho truck prices."]}),"\n"]}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"r20241111",children:"[R20241111]"}),"\n",(0,s.jsxs)(n.ol,{children:["\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)("input",{type:"checkbox",disabled:!0,checked:!0})," ",(0,s.jsx)("span",{style:{color:"red"},children:"[High]"})," - ",(0,s.jsx)("span",{style:{backgroundColor:"red"},children:"#price"})," Setup mail request pricing cho BEEHAN."]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)("input",{type:"checkbox",disabled:!0,checked:!0})," ",(0,s.jsx)("span",{style:{color:"red"},children:"[High]"})," - ",(0,s.jsx)("span",{style:{backgroundColor:"red"},children:"#price"})," Template upload gi\xe1 LCL Export."]}),"\n"]}),"\n"]}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"LCC \u1edf dest: bi\u1ebfn \u0111\u1ed9ng, chia theo COLOADER CNEE, DIRECT CNEE"}),"\n",(0,s.jsx)(n.li,{children:"T\u1ea1o th\xeam c\u1ed9t cho template/ render tr\xean UI."}),"\n"]}),"\n",(0,s.jsxs)(n.ol,{start:"3",children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)("input",{type:"checkbox",disabled:!0,checked:!0})," ",(0,s.jsx)("span",{style:{color:"orange"},children:"[Medium]"})," - ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"[Feature]"})," ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"#price"})," Email l\xfac t\u1ea1o, t\u1ef1 \u0111\u1ed9ng fetch th\xf4ng tin t\u1eeb account"]}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"r20241107",children:"[R20241107]"}),"\n",(0,s.jsxs)(n.ol,{children:["\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)("input",{type:"checkbox",disabled:!0,checked:!0})," ",(0,s.jsx)("span",{style:{color:"orange"},children:" [Medium]"})," - ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"[Enhance]"})," ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"#price"})," T\u1ed5 ch\u1ee9c l\u1ea1i menu, m\xe0n h\xecnh My Pricing List cho Air, Truck, Container, CBT, ..."]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)("input",{type:"checkbox",disabled:!0,checked:!0})," ",(0,s.jsx)("span",{style:{color:"orange"},children:"[Medium]"})," - ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"[Enhance]"})," ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"#price"})," Communication, \u0111\u1ed5i searchMessageAccount th\xe0nh groovy m\u1edbi. (support cho c\xe1c t\xe1c v\u1ee5 li\xean quan \u0111\u1ebfn email)"]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)("input",{type:"checkbox",disabled:!0,checked:!0})," ",(0,s.jsx)("span",{style:{color:"orange"},children:" [Medium]"})," - ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"[Enhance]"})," ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"#price"})," Enhance seach/ ch\u1ecdn th\xf4ng tin c\u1ea3ng, s\xe2n bay"]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)("input",{type:"checkbox",disabled:!0,checked:!0})," ",(0,s.jsx)("span",{style:{color:"orange"},children:"[Medium]"})," - ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"[Feature]"})," ",(0,s.jsx)("span",{style:{backgroundColor:"yellow"},children:"#price"})," Sync th\xf4ng tin email cho employee trong h\u1ec7 th\u1ed1ng."]}),"\n"]}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"r20241106",children:"[R20241106]"}),"\n",(0,s.jsxs)(n.ol,{children:["\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)("input",{type:"checkbox",disabled:!0,checked:!0})," ",(0,s.jsx)("span",{style:{color:"red"},children:"[High]"})," - [Bugs] - Fix l\u1ed7i ch\u1eef k\xfd mail(Phone) note b\u1ecb cut n\u1ed9i dung l\xfac g\u1eedi email check gi\xe1."]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)("input",{type:"checkbox",disabled:!0,checked:!0})," ",(0,s.jsx)("span",{style:{color:"orange"},children:"[Medium]"})," - [Enhance] T\u1ed5 ch\u1ee9c l\u1ea1i menu, m\xe0n h\xecnh My Pricing List cho Sea FCL, Sea LCL"]}),"\n"]}),"\n"]})]})}function a(e={}){const{wrapper:n}={...(0,i.R)(),...e.components};return n?(0,s.jsx)(n,{...e,children:(0,s.jsx)(h,{...e})}):h(e)}},494:(e,n,l)=>{l.d(n,{R:()=>r,x:()=>o});var s=l(6372);const i={},c=s.createContext(i);function r(e){const n=s.useContext(c);return s.useMemo(function(){return"function"==typeof e?e(n):{...n,...e}},[n,e])}function o(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(i):e.components||i:r(e.components),s.createElement(c.Provider,{value:n},e.children)}}}]);