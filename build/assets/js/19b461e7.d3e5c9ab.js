"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[196],{494:(e,n,r)=>{r.d(n,{R:()=>t,x:()=>i});var s=r(6372);const c={},d=s.createContext(c);function t(e){const n=s.useContext(d);return s.useMemo(function(){return"function"==typeof e?e(n):{...n,...e}},[n,e])}function i(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(c):e.components||c:t(e.components),s.createElement(d.Provider,{value:n},e.children)}},6986:(e,n,r)=>{r.r(n),r.d(n,{assets:()=>l,contentTitle:()=>t,default:()=>a,frontMatter:()=>d,metadata:()=>i,toc:()=>o});var s=r(216),c=r(494);const d={},t=void 0,i={id:"datatp-keycloak/user/README",title:"README",description:"\ud83d\ude80 Test v\u1edbi Postman",source:"@site/docs/datatp-keycloak/user/README.md",sourceDirName:"datatp-keycloak/user",slug:"/datatp-keycloak/user/",permalink:"/docs/datatp-keycloak/user/",draft:!1,unlisted:!1,tags:[],version:"current",frontMatter:{}},l={},o=[{value:"\ud83d\ude80 Test v\u1edbi Postman",id:"-test-v\u1edbi-postman",level:2},{value:"1. L\u1ea5y Access Token b\u1eb1ng Username/Password (Ng\u01b0\u1eddi d\xf9ng \u0111\u0103ng nh\u1eadp)",id:"1-l\u1ea5y-access-token-b\u1eb1ng-usernamepassword-ng\u01b0\u1eddi-d\xf9ng-\u0111\u0103ng-nh\u1eadp",level:3},{value:"URL:",id:"url",level:4},{value:"Headers:",id:"headers",level:4},{value:"Body:",id:"body",level:4},{value:"K\u1ebft qu\u1ea3 tr\u1ea3 v\u1ec1:",id:"k\u1ebft-qu\u1ea3-tr\u1ea3-v\u1ec1",level:4},{value:"2. L\u1ea5y Access Token b\u1eb1ng Client Secret (Service-to-Service)",id:"2-l\u1ea5y-access-token-b\u1eb1ng-client-secret-service-to-service",level:3},{value:"URL:",id:"url-1",level:4},{value:"Headers:",id:"headers-1",level:4},{value:"Body:",id:"body-1",level:4},{value:"K\u1ebft qu\u1ea3 tr\u1ea3 v\u1ec1:",id:"k\u1ebft-qu\u1ea3-tr\u1ea3-v\u1ec1-1",level:4},{value:"3. G\u1eedi request k\xe8m token \u0111\u1ebfn API",id:"3-g\u1eedi-request-k\xe8m-token-\u0111\u1ebfn-api",level:3},{value:"4. \ud83e\uddea Ki\u1ec3m tra token c\xf3 g\xec (decode)",id:"4--ki\u1ec3m-tra-token-c\xf3-g\xec-decode",level:3},{value:"\ud83d\uddc3\ufe0f Backup &amp; Restore Realm",id:"\ufe0f-backup--restore-realm",level:2},{value:"Export:",id:"export",level:3},{value:"Import:",id:"import",level:3},{value:"\ud83d\udccc M\u1ed9t s\u1ed1 l\u1ec7nh h\u1eefu \xedch",id:"-m\u1ed9t-s\u1ed1-l\u1ec7nh-h\u1eefu-\xedch",level:2}];function h(e){const n={a:"a",blockquote:"blockquote",code:"code",h2:"h2",h3:"h3",h4:"h4",hr:"hr",p:"p",pre:"pre",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",...(0,c.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.h2,{id:"-test-v\u1edbi-postman",children:"\ud83d\ude80 Test v\u1edbi Postman"}),"\n",(0,s.jsx)(n.hr,{}),"\n",(0,s.jsx)(n.h3,{id:"1-l\u1ea5y-access-token-b\u1eb1ng-usernamepassword-ng\u01b0\u1eddi-d\xf9ng-\u0111\u0103ng-nh\u1eadp",children:"1. L\u1ea5y Access Token b\u1eb1ng Username/Password (Ng\u01b0\u1eddi d\xf9ng \u0111\u0103ng nh\u1eadp)"}),"\n",(0,s.jsxs)(n.blockquote,{children:["\n",(0,s.jsx)(n.p,{children:"\xc1p d\u1ee5ng cho \u1ee9ng d\u1ee5ng frontend ho\u1eb7c user \u0111\u0103ng nh\u1eadp b\u1eb1ng giao di\u1ec7n."}),"\n"]}),"\n",(0,s.jsx)(n.h4,{id:"url",children:"URL:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"POST http://localhost:8080/realms/myrealm/protocol/openid-connect/token\n"})}),"\n",(0,s.jsx)(n.h4,{id:"headers",children:"Headers:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"Content-Type: application/x-www-form-urlencoded\n"})}),"\n",(0,s.jsx)(n.h4,{id:"body",children:"Body:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"grant_type=password\r\nclient_id=my-client\r\nusername=testuser\r\npassword=123456\n"})}),"\n",(0,s.jsx)(n.h4,{id:"k\u1ebft-qu\u1ea3-tr\u1ea3-v\u1ec1",children:"K\u1ebft qu\u1ea3 tr\u1ea3 v\u1ec1:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\r\n  "access_token": "eyJhbGciOiJSUzI1NiIsInR5...",\r\n  "expires_in": 300,\r\n  ...\r\n}\n'})}),"\n",(0,s.jsx)(n.hr,{}),"\n",(0,s.jsx)(n.h3,{id:"2-l\u1ea5y-access-token-b\u1eb1ng-client-secret-service-to-service",children:"2. L\u1ea5y Access Token b\u1eb1ng Client Secret (Service-to-Service)"}),"\n",(0,s.jsxs)(n.blockquote,{children:["\n",(0,s.jsx)(n.p,{children:"\xc1p d\u1ee5ng cho \u1ee9ng d\u1ee5ng backend ho\u1eb7c microservice kh\xf4ng c\xf3 ng\u01b0\u1eddi d\xf9ng \u0111\u0103ng nh\u1eadp."}),"\n"]}),"\n",(0,s.jsx)(n.h4,{id:"url-1",children:"URL:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"POST http://localhost:8080/realms/myrealm/protocol/openid-connect/token\n"})}),"\n",(0,s.jsx)(n.h4,{id:"headers-1",children:"Headers:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"Content-Type: application/x-www-form-urlencoded\n"})}),"\n",(0,s.jsx)(n.h4,{id:"body-1",children:"Body:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"grant_type=client_credentials\r\nclient_id=my-service\r\nclient_secret=<gi\xe1-tr\u1ecb-secret-\u0111\xe3-copy>\n"})}),"\n",(0,s.jsx)(n.h4,{id:"k\u1ebft-qu\u1ea3-tr\u1ea3-v\u1ec1-1",children:"K\u1ebft qu\u1ea3 tr\u1ea3 v\u1ec1:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\r\n  "access_token": "eyJhbGciOiJSUzI1NiIsInR5...",\r\n  "expires_in": 300,\r\n  ...\r\n}\n'})}),"\n",(0,s.jsx)(n.hr,{}),"\n",(0,s.jsx)(n.h3,{id:"3-g\u1eedi-request-k\xe8m-token-\u0111\u1ebfn-api",children:"3. G\u1eedi request k\xe8m token \u0111\u1ebfn API"}),"\n",(0,s.jsx)(n.p,{children:"Gi\u1ea3 s\u1eed c\xf3 m\u1ed9t API \u1edf:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"GET http://localhost:8081/api/secure\n"})}),"\n",(0,s.jsx)(n.p,{children:"Th\xeam header:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"Authorization: Bearer <access_token>\n"})}),"\n",(0,s.jsx)(n.hr,{}),"\n",(0,s.jsx)(n.h3,{id:"4--ki\u1ec3m-tra-token-c\xf3-g\xec-decode",children:"4. \ud83e\uddea Ki\u1ec3m tra token c\xf3 g\xec (decode)"}),"\n",(0,s.jsxs)(n.p,{children:["D\xe1n access token v\xe0o: ",(0,s.jsx)(n.a,{href:"https://jwt.io",children:"https://jwt.io"})]}),"\n",(0,s.jsx)(n.p,{children:"V\xed d\u1ee5:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\r\n  "preferred_username": "testuser",\r\n  "client_id": "my-service",\r\n  "realm_access": {\r\n    "roles": ["user", "admin"]\r\n  },\r\n  ...\r\n}\n'})}),"\n",(0,s.jsx)(n.hr,{}),"\n",(0,s.jsx)(n.h2,{id:"\ufe0f-backup--restore-realm",children:"\ud83d\uddc3\ufe0f Backup & Restore Realm"}),"\n",(0,s.jsx)(n.h3,{id:"export",children:"Export:"}),"\n",(0,s.jsxs)(n.blockquote,{children:["\n",(0,s.jsx)(n.p,{children:"S\u1eed d\u1ee5ng l\u1ec7nh c\u1ee7a keycloak"}),"\n"]}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"./kc.sh export --dir=data-export --realm=BeeCorp --users different_files --users-per-file 100\n"})}),"\n",(0,s.jsxs)(n.blockquote,{children:["\n",(0,s.jsx)(n.p,{children:"\u0110\u1ed1i v\u1edbi keycloak ch\u1ea1y v\u1edbi postgres SQL"}),"\n"]}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"./db.sh keycloak_db dump --file={$BACKUP_DIR}/dbbackup/keycloak_db_backup.tar\n"})}),"\n",(0,s.jsx)(n.h3,{id:"import",children:"Import:"}),"\n",(0,s.jsxs)(n.blockquote,{children:["\n",(0,s.jsx)(n.p,{children:"S\u1eed d\u1ee5ng l\u1ec7nh c\u1ee7a keycloak"}),"\n"]}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"./kc.sh import --dir backup-dir\n"})}),"\n",(0,s.jsxs)(n.blockquote,{children:["\n",(0,s.jsx)(n.p,{children:"\u0110\u1ed1i v\u1edbi keycloak ch\u1ea1y v\u1edbi postgres SQL"}),"\n"]}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"./db.sh keycloak_db restore --db-name=keycloak_db --file=keycloak_db_backup.tar\n"})}),"\n",(0,s.jsx)(n.hr,{}),"\n",(0,s.jsx)(n.h2,{id:"-m\u1ed9t-s\u1ed1-l\u1ec7nh-h\u1eefu-\xedch",children:"\ud83d\udccc M\u1ed9t s\u1ed1 l\u1ec7nh h\u1eefu \xedch"}),"\n",(0,s.jsxs)(n.table,{children:[(0,s.jsx)(n.thead,{children:(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.th,{children:"L\u1ec7nh"}),(0,s.jsx)(n.th,{children:"M\xf4 t\u1ea3"})]})}),(0,s.jsxs)(n.tbody,{children:[(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:(0,s.jsx)(n.code,{children:"kc.sh start-dev"})}),(0,s.jsx)(n.td,{children:"Kh\u1edfi \u0111\u1ed9ng Keycloak dev mode"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:(0,s.jsx)(n.code,{children:"kc.sh export"})}),(0,s.jsx)(n.td,{children:"Xu\u1ea5t d\u1eef li\u1ec7u realm"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:(0,s.jsx)(n.code,{children:"kc.sh import"})}),(0,s.jsx)(n.td,{children:"Nh\u1eadp d\u1eef li\u1ec7u realm"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:(0,s.jsx)(n.code,{children:"kc.sh show-config"})}),(0,s.jsx)(n.td,{children:"Xem c\u1ea5u h\xecnh \u0111ang ch\u1ea1y hi\u1ec7n t\u1ea1i"})]})]})]})]})}function a(e={}){const{wrapper:n}={...(0,c.R)(),...e.components};return n?(0,s.jsx)(n,{...e,children:(0,s.jsx)(h,{...e})}):h(e)}}}]);