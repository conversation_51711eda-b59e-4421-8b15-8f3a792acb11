"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[6779],{4097:(e,d,i)=>{i.r(d),i.d(d,{default:()=>l});var t=i(6372),r=i(8126),n=i(9551),s=i(216);const o=["dev_token_123","manager_token_456"];function c({children:e}){const[d,i]=(0,t.useState)(!1),[r,c]=(0,t.useState)(""),[l,p]=(0,t.useState)("");(0,n.W6)();(0,t.useEffect)(()=>{const e=localStorage.getItem("dev_auth_token");e&&o.includes(e)&&i(!0)},[]);const h=e=>{e.preventDefault(),o.includes(r)?(localStorage.setItem("dev_auth_token",r),i(!0),p("")):p("M\xe3 x\xe1c th\u1ef1c kh\xf4ng h\u1ee3p l\u1ec7")};return d?(0,s.jsx)(s.Fragment,{children:e}):(0,s.jsxs)("div",{style:{maxWidth:"400px",margin:"100px auto",padding:"20px",border:"1px solid #ccc",borderRadius:"5px"},children:[(0,s.jsx)("h2",{children:"Khu v\u1ef1c d\xe0nh cho Developer"}),(0,s.jsx)("p",{children:"Vui l\xf2ng nh\u1eadp m\xe3 x\xe1c th\u1ef1c \u0111\u1ec3 truy c\u1eadp n\u1ed9i dung n\xe0y"}),(0,s.jsxs)("form",{onSubmit:h,children:[(0,s.jsx)("div",{style:{marginBottom:"15px"},children:(0,s.jsx)("input",{type:"password",value:r,onChange:e=>c(e.target.value),placeholder:"Nh\u1eadp m\xe3 x\xe1c th\u1ef1c",style:{width:"100%",padding:"8px"}})}),l&&(0,s.jsx)("div",{style:{color:"red",marginBottom:"10px"},children:l}),(0,s.jsx)("button",{type:"submit",style:{background:"#2e8555",color:"white",border:"none",padding:"10px 15px",borderRadius:"4px",cursor:"pointer"},children:"X\xe1c th\u1ef1c"})]})]})}function l(){const e=(0,n.W6)(),d=d=>{e.push(`/docs/${d}/developer/features`)};return(0,s.jsx)(r.A,{title:"Docs For Developer",children:(0,s.jsx)(c,{children:(0,s.jsxs)("div",{style:{padding:"20px"},children:[(0,s.jsx)("h1",{children:"Developer Wiki"}),(0,s.jsx)("p",{children:"Ch\u1ecdn d\u1ef1 \xe1n \u0111\u1ec3 xem t\xe0i li\u1ec7u ph\xe1t tri\u1ec3n"}),(0,s.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fill, minmax(250px, 1fr))",gap:"20px",marginTop:"20px"},children:[(0,s.jsxs)("div",{style:{padding:"15px",border:"1px solid #ddd",borderRadius:"5px",cursor:"pointer"},onClick:()=>d("datatp-crm"),children:[(0,s.jsx)("h3",{children:"DataTP CRM"}),(0,s.jsx)("p",{children:"T\xe0i li\u1ec7u ph\xe1t tri\u1ec3n cho CRM"})]}),(0,s.jsxs)("div",{style:{padding:"15px",border:"1px solid #ddd",borderRadius:"5px",cursor:"pointer"},onClick:()=>d("datatp-tms"),children:[(0,s.jsx)("h3",{children:"DataTP TMS"}),(0,s.jsx)("p",{children:"T\xe0i li\u1ec7u ph\xe1t tri\u1ec3n cho TMS"})]}),(0,s.jsxs)("div",{style:{padding:"15px",border:"1px solid #ddd",borderRadius:"5px",cursor:"pointer"},onClick:()=>d("document-ie"),children:[(0,s.jsx)("h3",{children:"Document IE"}),(0,s.jsx)("p",{children:"T\xe0i li\u1ec7u ph\xe1t tri\u1ec3n cho Document IE"})]})]})]})})})}}}]);