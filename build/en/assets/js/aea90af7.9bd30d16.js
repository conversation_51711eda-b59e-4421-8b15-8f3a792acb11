"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[7883],{494:(e,n,r)=>{r.d(n,{R:()=>o,x:()=>a});var t=r(6372);const s={},i=t.createContext(s);function o(e){const n=t.useContext(i);return t.useMemo(function(){return"function"==typeof e?e(n):{...n,...e}},[n,e])}function a(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:o(e.components),t.createElement(i.Provider,{value:n},e.children)}},4184:(e,n,r)=>{r.r(n),r.d(n,{assets:()=>c,contentTitle:()=>o,default:()=>u,frontMatter:()=>i,metadata:()=>a,toc:()=>d});var t=r(216),s=r(494);const i={sidebar_position:8,sidebar:!1,hide_table_of_contents:!0,displayed_sidebar:"userSidebar"},o=void 0,a={id:"datatp-hr/user/kpi/kpi_for_manager",title:"kpi_for_manager",description:"I. Truy c\u1eadp KPI",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/datatp-hr/user/kpi/kpi_for_manager.md",sourceDirName:"datatp-hr/user/kpi",slug:"/datatp-hr/user/kpi/kpi_for_manager",permalink:"/en/docs/datatp-hr/user/kpi/kpi_for_manager",draft:!1,unlisted:!1,tags:[],version:"current",sidebarPosition:8,frontMatter:{sidebar_position:8,sidebar:!1,hide_table_of_contents:!0,displayed_sidebar:"userSidebar"},sidebar:"userSidebar"},c={},d=[{value:"I. Truy c\u1eadp KPI",id:"i-truy-c\u1eadp-kpi",level:2}];function p(e){const n={h2:"h2",li:"li",ol:"ol",p:"p",strong:"strong",...(0,s.R)(),...e.components};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(n.h2,{id:"i-truy-c\u1eadp-kpi",children:"I. Truy c\u1eadp KPI"}),"\n",(0,t.jsx)(n.p,{children:"Nghi\u1ec7p v\u1ee5 li\xean quan:"}),"\n",(0,t.jsxs)(n.ol,{children:["\n",(0,t.jsxs)(n.li,{children:["Ch\u1ecdn ",(0,t.jsx)(n.strong,{children:"Menu"})]}),"\n",(0,t.jsxs)(n.li,{children:["Ch\u1ecdn App ",(0,t.jsx)(n.strong,{children:"KPI"})]}),"\n"]})]})}function u(e={}){const{wrapper:n}={...(0,s.R)(),...e.components};return n?(0,t.jsx)(n,{...e,children:(0,t.jsx)(p,{...e})}):p(e)}}}]);