"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[8665],{494:(e,t,n)=>{n.d(t,{R:()=>a,x:()=>s});var o=n(6372);const i={},r=o.createContext(i);function a(e){const t=o.useContext(r);return o.useMemo(function(){return"function"==typeof e?e(t):{...t,...e}},[t,e])}function s(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(i):e.components||i:a(e.components),o.createElement(r.Provider,{value:t},e.children)}},1457:(e,t,n)=>{n.d(t,{A:()=>o});const o=n.p+"assets/images/resend_request-4d1bb2d0c577e21e2158c152a1d508c1.gif"},2742:(e,t,n)=>{n.d(t,{A:()=>o});const o=n.p+"assets/images/copy_quotation-e4e51314711122ed6d5e02d150fdb5a2.gif"},2960:(e,t,n)=>{n.d(t,{A:()=>o});const o=n.p+"assets/images/promote_price-13b12f0d6d7ea942f320b57c0de24ec1.gif"},3777:(e,t,n)=>{n.r(t),n.d(t,{assets:()=>c,contentTitle:()=>a,default:()=>u,frontMatter:()=>r,metadata:()=>s,toc:()=>d});var o=n(216),i=n(494);const r={sidebar_position:4,hide_table_of_contents:!1,displayed_sidebar:"userSidebar"},a="Quotation",s={id:"datatp-crm/user/crm/references/quotation",title:"Quotation",description:"Flow diagram of quotation screens & functions.",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/datatp-crm/user/crm/references/quotation.md",sourceDirName:"datatp-crm/user/crm/references",slug:"/datatp-crm/user/crm/references/quotation",permalink:"/en/docs/datatp-crm/user/crm/references/quotation",draft:!1,unlisted:!1,tags:[],version:"current",sidebarPosition:4,frontMatter:{sidebar_position:4,hide_table_of_contents:!1,displayed_sidebar:"userSidebar"},sidebar:"userSidebar"},c={},d=[{value:"Flow diagram of quotation screens &amp; functions.",id:"flow-diagram-of-quotation-screens--functions",level:4},{value:"Quotation Workboard Screen.",id:"quotation-workboard-screen",level:3},{value:"1. Update customer feedback, quotation status (Win, Mismatch, etc.).",id:"1-update-customer-feedback-quotation-status-win-mismatch-etc",level:4},{value:"2. Edit, update quotation - <mark>Quotation Form</mark>",id:"2-edit-update-quotation---quotation-form",level:4},{value:"3. Send new price check request to Pricing department.",id:"3-send-new-price-check-request-to-pricing-department",level:4},{value:"4. Copy an old quotation to create a new one:",id:"4-copy-an-old-quotation-to-create-a-new-one",level:4},{value:"5. Create, update Internal Booking information, send to BFSOne.",id:"5-create-update-internal-booking-information-send-to-bfsone",level:4},{value:"Quotation Form Screen.",id:"quotation-form-screen",level:3},{value:"Internal Booking Screen.",id:"internal-booking-screen",level:3}];function l(e){const t={a:"a",code:"code",em:"em",h1:"h1",h3:"h3",h4:"h4",header:"header",img:"img",li:"li",p:"p",pre:"pre",strong:"strong",ul:"ul",...(0,i.R)(),...e.components},{Details:r}=t;return r||function(e,t){throw new Error("Expected "+(t?"component":"object")+" `"+e+"` to be defined: you likely forgot to import, pass, or provide it.")}("Details",!0),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(t.header,{children:(0,o.jsx)(t.h1,{id:"quotation",children:"Quotation"})}),"\n",(0,o.jsx)(t.h4,{id:"flow-diagram-of-quotation-screens--functions",children:"Flow diagram of quotation screens & functions."}),"\n",(0,o.jsxs)(r,{children:[(0,o.jsx)("summary",{children:" Flow Diagram & Functions "}),(0,o.jsx)(t.pre,{children:(0,o.jsx)(t.code,{className:"language-text",children:'\u250c\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510 \u250c\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510\n\u2502  Click "Request a Quote"   \u2502 \u2502  Click "Request Pricing"   \u2502\n\u2502                            \u2502 \u2502                            \u2502\n\u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u252c\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2518 \u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u252c\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2518\n            \u2502                              \u2502\n            \u2502                              \u25bc\n            \u2502                  \u250c\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510\n            \u2502                  \u2502  Pricing Department        \u2502\n            \u2502                  \u2502  updates price information \u2502\n            \u2502                  \u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u252c\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2518\n            \u2502                              \u2502\n            \u2502                              \u25bc\n            \u2502                  \u250c\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510\n            \u2502                  \u2502  Go to Quotation Workboard \u2502\n            \u2502                  \u2502  Click Create Quotation    \u2502\n            \u2502                  \u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u252c\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2518\n            \u2502                              \u2502\n            \u25bc                              \u25bc\n\u250c\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510\n\u2502           Quotation Form Screen                            \u2502\n\u2502           - Add other costs                                \u2502\n\u2502           - Add margin                                     \u2502\n\u2502           - Edit quotation                                 \u2502\n\u2502           - Export quotation to Excel                      \u2502\n\u2502           - Send email to customer                         \u2502\n\u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2518\n'})})]}),"\n",(0,o.jsx)(t.h3,{id:"quotation-workboard-screen",children:"Quotation Workboard Screen."}),"\n",(0,o.jsx)(t.p,{children:"Manage the list of created quotations, including the following functions:"}),"\n",(0,o.jsx)(t.h4,{id:"1-update-customer-feedback-quotation-status-win-mismatch-etc",children:"1. Update customer feedback, quotation status (Win, Mismatch, etc.)."}),"\n",(0,o.jsx)(t.p,{children:"Track and update feedback and status for each quotation."}),"\n",(0,o.jsx)(t.p,{children:(0,o.jsx)(t.img,{alt:"../img/update_status_quote.gif",src:n(8427).A+"",width:"1913",height:"451"})}),"\n",(0,o.jsxs)(t.h4,{id:"2-edit-update-quotation---quotation-form",children:["2. Edit, update quotation - ",(0,o.jsx)("mark",{children:"Quotation Form"})]}),"\n",(0,o.jsxs)(t.ul,{children:["\n",(0,o.jsx)(t.li,{children:"Click on the quotation you want to edit."}),"\n",(0,o.jsxs)(t.li,{children:["The system will switch to the ",(0,o.jsx)(t.a,{href:"#quotation-form-screen",children:(0,o.jsx)("mark",{children:"Quotation Form screen"})})," for you to perform actions such as adding costs, margin, editing content, exporting Excel file, sending email to customer, etc.\n",(0,o.jsx)(t.img,{alt:"./img/quotation/view_quotation.gif",src:n(5242).A+"",width:"1915",height:"1069"})]}),"\n"]}),"\n",(0,o.jsx)(t.h4,{id:"3-send-new-price-check-request-to-pricing-department",children:"3. Send new price check request to Pricing department."}),"\n",(0,o.jsxs)(t.ul,{children:["\n",(0,o.jsxs)(t.li,{children:["\n",(0,o.jsx)(t.p,{children:"When you need a new quotation, you can send a request directly from the old quotation."}),"\n"]}),"\n",(0,o.jsxs)(t.li,{children:["\n",(0,o.jsx)(t.p,{children:'The Pricing department will update the price, then you return to the Quotation Workboard, click "Create Quotation" to switch to the Quotation Form and complete the quotation.'}),"\n",(0,o.jsx)(t.p,{children:(0,o.jsx)(t.img,{alt:"./img/quotation/resend_request.gif",src:n(1457).A+"",width:"1913",height:"1075"})}),"\n"]}),"\n"]}),"\n",(0,o.jsx)(t.h4,{id:"4-copy-an-old-quotation-to-create-a-new-one",children:"4. Copy an old quotation to create a new one:"}),"\n",(0,o.jsxs)(t.ul,{children:["\n",(0,o.jsxs)(t.li,{children:["\n",(0,o.jsx)(t.p,{children:"Select the quotation you want to copy, use the copy function to create a new quotation based on the old information, saving input time."}),"\n",(0,o.jsx)("div",{style:{background:"#fff3cd",color:"#856404",padding:"12px 16px",margin:"16px 16px",borderRadius:"6px",border:"1px solid #ffeeba"},children:(0,o.jsxs)(t.p,{children:[(0,o.jsx)("strong",{children:"Note:"})," You can only copy a quotation if it was previously created (copy icon is green)."]})}),"\n",(0,o.jsx)(t.p,{children:(0,o.jsx)(t.img,{alt:"./img/quotation/copy_quotation.gif",src:n(2742).A+"",width:"1906",height:"1067"})}),"\n"]}),"\n"]}),"\n",(0,o.jsx)(t.h4,{id:"5-create-update-internal-booking-information-send-to-bfsone",children:"5. Create, update Internal Booking information, send to BFSOne."}),"\n",(0,o.jsxs)(t.ul,{children:["\n",(0,o.jsxs)(t.li,{children:["\n",(0,o.jsxs)(t.p,{children:["After the quotation is confirmed, you can create an Internal Booking from this quotation and send the information to the BFSOne system.\n",(0,o.jsx)(t.img,{alt:"./img/quotation/create_ib.gif",src:n(9624).A+"",width:"1912",height:"1076"})]}),"\n"]}),"\n",(0,o.jsxs)(t.li,{children:["\n",(0,o.jsxs)(t.p,{children:["You can resend a new Internal Booking based on the information of the old Internal Booking.\n",(0,o.jsx)(t.img,{alt:"./img/quotation/save_as_ib.gif",src:n(6702).A+"",width:"1913",height:"1069"})]}),"\n",(0,o.jsxs)("div",{style:{background:"#fff3cd",color:"#856404",padding:"12px 16px",margin:"16px 16px",borderRadius:"6px",border:"1px solid #ffeeba"},children:[(0,o.jsx)("strong",{children:"Note:"}),(0,o.jsx)("br",{}),(0,o.jsxs)(t.ul,{children:["\n",(0,o.jsxs)(t.li,{children:["Green icon indicates Internal Booking was previously created. ",(0,o.jsx)("br",{})]}),"\n",(0,o.jsx)(t.li,{children:"Black icon indicates Internal Booking is being created for the first time."}),"\n"]})]}),"\n"]}),"\n",(0,o.jsxs)(t.li,{children:["\n",(0,o.jsxs)(t.p,{children:["When using standard pricing, creating a ",(0,o.jsx)(t.code,{children:"quotation"})," and successfully sending an ",(0,o.jsx)(t.code,{children:"IB"}),", the pricing information will be promoted to the price list, allowing other salespeople to easily reference and use these rates. Additionally, the pricing team can evaluate price effectiveness and frequency of use.\n",(0,o.jsx)(t.img,{alt:"./img/quotation/promote_price.gif",src:n(2960).A+"",width:"1905",height:"1067"})]}),"\n"]}),"\n"]}),"\n",(0,o.jsx)("hr",{}),"\n",(0,o.jsx)(t.h3,{id:"quotation-form-screen",children:"Quotation Form Screen."}),"\n",(0,o.jsx)(t.p,{children:(0,o.jsxs)(t.em,{children:["Case Study: ",(0,o.jsx)(t.code,{children:"Quotation FCL 2x40DC, NANSHA, CHINA -> HOCHIMINH, VIETNAM, term FOB"})]})}),"\n",(0,o.jsxs)(t.p,{children:[(0,o.jsx)(t.em,{children:"Demo video"}),": ",(0,o.jsx)(t.a,{href:"https://youtu.be/6cwWaDSQWGg",children:"https://youtu.be/6cwWaDSQWGg"})]}),"\n",(0,o.jsx)("div",{style:{position:"relative",paddingBottom:"56.25%",height:0,overflow:"hidden",maxWidth:"100%",height:"auto",marginLeft:"20px"},children:(0,o.jsx)("iframe",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%"},src:"https://www.youtube.com/embed/6cwWaDSQWGg",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})}),"\n",(0,o.jsx)("hr",{}),"\n",(0,o.jsx)(t.h3,{id:"internal-booking-screen",children:"Internal Booking Screen."}),"\n",(0,o.jsxs)(t.p,{children:[(0,o.jsx)(t.em,{children:"Demo video"}),": ",(0,o.jsx)(t.a,{href:"https://youtu.be/L0-s335pG_Q",children:"https://youtu.be/L0-s335pG_Q"})]}),"\n",(0,o.jsx)("div",{style:{position:"relative",paddingBottom:"56.25%",height:0,overflow:"hidden",maxWidth:"100%",height:"auto",marginLeft:"20px"},children:(0,o.jsx)("iframe",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%"},src:"https://www.youtube.com/embed/L0-s335pG_Q",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})}),"\n",(0,o.jsx)("hr",{}),"\n",(0,o.jsx)(t.p,{children:(0,o.jsx)(t.strong,{children:"If the system does not have a price available"})}),"\n",(0,o.jsxs)(t.ul,{children:["\n",(0,o.jsxs)(t.li,{children:["Click ",(0,o.jsx)(t.code,{children:"Request a Quote"})," to create a custom quotation. Other operations are similar to the demo video above."]}),"\n"]}),"\n",(0,o.jsx)(t.p,{children:(0,o.jsx)(t.img,{alt:"./img/quotation_fcl_1.png",src:n(8050).A+"",width:"1925",height:"538"})})]})}function u(e={}){const{wrapper:t}={...(0,i.R)(),...e.components};return t?(0,o.jsx)(t,{...e,children:(0,o.jsx)(l,{...e})}):l(e)}},5242:(e,t,n)=>{n.d(t,{A:()=>o});const o=n.p+"assets/images/view_quotation-a57445038cfbec1b90f5971782ddeff7.gif"},6702:(e,t,n)=>{n.d(t,{A:()=>o});const o=n.p+"assets/images/save_as_ib-82ace2ad9904b199fc0d56bad77873e7.gif"},8050:(e,t,n)=>{n.d(t,{A:()=>o});const o=n.p+"assets/images/quotation_fcl_1-18a771072ddbbba527ed37b9dcc519ca.png"},8427:(e,t,n)=>{n.d(t,{A:()=>o});const o=n.p+"assets/images/update_status_quote-b662df93e0b31e0e5739f06f0f94e2ed.gif"},9624:(e,t,n)=>{n.d(t,{A:()=>o});const o=n.p+"assets/images/create_ib-31cb91f734976cc95f1a1d24cfae974b.gif"}}]);