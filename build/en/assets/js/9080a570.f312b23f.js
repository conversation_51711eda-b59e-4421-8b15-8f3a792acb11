"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[8835],{494:(e,s,n)=>{n.d(s,{R:()=>c,x:()=>a});var r=n(6372);const t={},i=r.createContext(t);function c(e){const s=r.useContext(i);return r.useMemo(function(){return"function"==typeof e?e(s):{...s,...e}},[s,e])}function a(e){let s;return s=e.disableParentContext?"function"==typeof e.components?e.components(t):e.components||t:c(e.components),r.createElement(i.Provider,{value:s},e.children)}},740:(e,s,n)=>{n.r(s),n.d(s,{assets:()=>d,contentTitle:()=>c,default:()=>h,frontMatter:()=>i,metadata:()=>a,toc:()=>o});var r=n(216),t=n(494);const i={sidebar_position:4,hide_table_of_contents:!0,displayed_sidebar:"userSidebar"},c="Partners",a={id:"datatp-crm/user/crm/references/partners",title:"Partners",description:"Functions and business screens related to tracking each salesman's Partners.",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/datatp-crm/user/crm/references/partners.md",sourceDirName:"datatp-crm/user/crm/references",slug:"/datatp-crm/user/crm/references/partners",permalink:"/en/docs/datatp-crm/user/crm/references/partners",draft:!1,unlisted:!1,tags:[],version:"current",sidebarPosition:4,frontMatter:{sidebar_position:4,hide_table_of_contents:!0,displayed_sidebar:"userSidebar"},sidebar:"userSidebar"},d={},o=[{value:"1. <strong>Overview</strong>",id:"1-overview",level:4},{value:"2. <strong>Customer Leads</strong>",id:"2-customer-leads",level:4},{value:"3. <strong>Customers</strong>",id:"3-customers",level:4},{value:"4. <strong>Shipping Instruction</strong>",id:"4-shipping-instruction",level:4},{value:"5. <strong>Customer Map</strong>",id:"5-customer-map",level:4}];function l(e){const s={a:"a",code:"code",h1:"h1",h4:"h4",header:"header",img:"img",li:"li",p:"p",strong:"strong",ul:"ul",...(0,t.R)(),...e.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(s.header,{children:(0,r.jsx)(s.h1,{id:"partners",children:"Partners"})}),"\n",(0,r.jsx)(s.p,{children:"Functions and business screens related to tracking each salesman's Partners."}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:["Access ",(0,r.jsx)(s.code,{children:"CRM"}),", select ",(0,r.jsx)(s.code,{children:"Partners"})," from the toolbar."]}),"\n"]}),"\n",(0,r.jsx)(s.p,{children:(0,r.jsx)(s.img,{alt:"enter_partners.gif",src:n(1238).A+"",width:"1824",height:"957"})}),"\n",(0,r.jsx)(s.p,{children:"Partners screen:"}),"\n",(0,r.jsx)(s.p,{children:(0,r.jsx)(s.img,{alt:"menu.png",src:n(5248).A+"",width:"2932",height:"1538"})}),"\n",(0,r.jsxs)(s.h4,{id:"1-overview",children:["1. ",(0,r.jsx)(s.strong,{children:"Overview"})]}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:["The screen displays performance metrics for each salesman, including:","\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsx)(s.li,{children:"Jobs, Volume, Revenue, Profit of the salesman, categorized by type."}),"\n",(0,r.jsx)(s.li,{children:"Number of leads entered and tracked by the salesman."}),"\n",(0,r.jsx)(s.li,{children:"Number of new customers and their history, tracking progress."}),"\n",(0,r.jsx)(s.li,{children:"Market evaluation, arising issues, suggestions, forecasts, ..."}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(s.p,{children:(0,r.jsx)(s.img,{alt:"overview.png",src:n(7308).A+"",width:"2932",height:"1538"})}),"\n",(0,r.jsx)(s.p,{children:(0,r.jsx)(s.a,{href:"/docs/datatp-crm/user/crm/references/partner_overview",children:"See detailed instructions here"})}),"\n",(0,r.jsxs)(s.h4,{id:"2-customer-leads",children:["2. ",(0,r.jsx)(s.strong,{children:"Customer Leads"})]}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:["The Lead management screen includes the following functions:","\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsx)(s.li,{children:"Create, update, delete Lead."}),"\n",(0,r.jsx)(s.li,{children:"Track overdue Leads, transfer to another salesman."}),"\n",(0,r.jsx)(s.li,{children:"Convert Lead to Customer."}),"\n",(0,r.jsx)(s.li,{children:"Track interaction history, categorize Leads."}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(s.p,{children:(0,r.jsx)(s.img,{alt:"customer_leads.png",src:n(8962).A+"",width:"3344",height:"804"})}),"\n",(0,r.jsx)(s.p,{children:(0,r.jsx)(s.a,{href:"/docs/datatp-crm/user/crm/references/lead_management",children:"See detailed instructions here"})}),"\n",(0,r.jsxs)(s.h4,{id:"3-customers",children:["3. ",(0,r.jsx)(s.strong,{children:"Customers"})]}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:["The Customer management screen includes the following functions:","\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsx)(s.li,{children:"Create, update customer information."}),"\n",(0,r.jsx)(s.li,{children:"Request accounting to create new customers, synchronize data with BFSOne."}),"\n",(0,r.jsx)(s.li,{children:"Track interaction history, categorize customers."}),"\n",(0,r.jsx)(s.li,{children:"Track transaction information, metrics such as latest shipment, volume, revenue over time."}),"\n",(0,r.jsx)(s.li,{children:"Create, upload, manage contracts, related documents."}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(s.p,{children:(0,r.jsx)(s.img,{alt:"customers.png",src:n(9494).A+"",width:"3354",height:"838"})}),"\n",(0,r.jsx)(s.p,{children:(0,r.jsx)(s.a,{href:"/docs/datatp-crm/user/crm/references/customer_management",children:"See detailed instructions here"})}),"\n",(0,r.jsxs)(s.h4,{id:"4-shipping-instruction",children:["4. ",(0,r.jsx)(s.strong,{children:"Shipping Instruction"})]}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:["\n",(0,r.jsx)(s.p,{children:"Track and update shipping notes, take notes for customers."}),"\n",(0,r.jsx)(s.p,{children:(0,r.jsx)(s.a,{href:"/docs/datatp-crm/user/crm/references/search_prices",children:"See detailed instructions here"})}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(s.h4,{id:"5-customer-map",children:["5. ",(0,r.jsx)(s.strong,{children:"Customer Map"})]}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsx)(s.li,{children:"Customer map, categorize customers by each geographic area (Province, Industrial Park)."}),"\n",(0,r.jsx)(s.li,{children:"Also, detailed monthly/quarterly volume reports for each customer in those areas."}),"\n",(0,r.jsx)(s.li,{children:"Analyze service type ratio, number of files, business sectors, and latest transactions."}),"\n"]}),"\n",(0,r.jsx)(s.p,{children:(0,r.jsx)(s.img,{alt:"customer_map.png",src:n(5322).A+"",width:"2920",height:"1326"})}),"\n",(0,r.jsx)(s.p,{children:(0,r.jsx)(s.a,{href:"/docs/datatp-crm/user/crm/references/customer_management",children:"See detailed instructions here"})})]})}function h(e={}){const{wrapper:s}={...(0,t.R)(),...e.components};return s?(0,r.jsx)(s,{...e,children:(0,r.jsx)(l,{...e})}):l(e)}},1238:(e,s,n)=>{n.d(s,{A:()=>r});const r=n.p+"assets/images/enter_partners-f06c6508ebe5dc6fa9c8fcdf5192e89d.gif"},5248:(e,s,n)=>{n.d(s,{A:()=>r});const r=n.p+"assets/images/menu-c9206ba4d47576bdf8f618b50d73cace.png"},5322:(e,s,n)=>{n.d(s,{A:()=>r});const r=n.p+"assets/images/customer_map-fa44b28b254c20334f93df714d6dbb4e.png"},7308:(e,s,n)=>{n.d(s,{A:()=>r});const r=n.p+"assets/images/overview-45e042545cdee628b182796ff4bb3036.png"},8962:(e,s,n)=>{n.d(s,{A:()=>r});const r=n.p+"assets/images/customer_leads-c74e0be630ab3fb4dd26875d95d0d417.png"},9494:(e,s,n)=>{n.d(s,{A:()=>r});const r=n.p+"assets/images/customers-049eebf5fcc7ce65dad175a06dcf8041.png"}}]);