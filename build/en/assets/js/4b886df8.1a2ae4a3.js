"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[2653],{494:(e,n,t)=>{t.d(n,{R:()=>c,x:()=>a});var r=t(6372);const s={},o=r.createContext(s);function c(e){const n=r.useContext(o);return r.useMemo(function(){return"function"==typeof e?e(n):{...n,...e}},[n,e])}function a(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:c(e.components),r.createElement(o.Provider,{value:n},e.children)}},8315:(e,n,t)=>{t.r(n),t.d(n,{assets:()=>i,contentTitle:()=>c,default:()=>l,frontMatter:()=>o,metadata:()=>a,toc:()=>u});var r=t(216),s=t(494);const o={},c="CRM Features",a={id:"shared/developer/features",title:"CRM Features",description:"- Lead Management: Qu\u1ea3n l\xfd kh\xe1ch h\xe0ng ti\u1ec1m n\u0103ng.",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/shared/developer/features.md",sourceDirName:"shared/developer",slug:"/shared/developer/features",permalink:"/en/docs/shared/developer/features",draft:!1,unlisted:!1,tags:[],version:"current",frontMatter:{}},i={},u=[];function d(e){const n={h1:"h1",header:"header",li:"li",p:"p",ul:"ul",...(0,s.R)(),...e.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.header,{children:(0,r.jsx)(n.h1,{id:"crm-features",children:"CRM Features"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"Lead Management: Qu\u1ea3n l\xfd kh\xe1ch h\xe0ng ti\u1ec1m n\u0103ng."}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"Quotation Tracking: Theo d\xf5i b\xe1o gi\xe1 (t\xedch h\u1ee3p t\u1eeb CRM, tham kh\u1ea3o y\xeau c\u1ea7u ng\xe0y 15/05/2025)."}),"\n"]}),"\n"]})]})}function l(e={}){const{wrapper:n}={...(0,s.R)(),...e.components};return n?(0,r.jsx)(n,{...e,children:(0,r.jsx)(d,{...e})}):d(e)}}}]);