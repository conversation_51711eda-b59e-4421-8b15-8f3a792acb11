"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[4808],{494:(e,s,n)=>{n.d(s,{R:()=>r,x:()=>l});var t=n(6372);const i={},a=t.createContext(i);function r(e){const s=t.useContext(a);return t.useMemo(function(){return"function"==typeof e?e(s):{...s,...e}},[s,e])}function l(e){let s;return s=e.disableParentContext?"function"==typeof e.components?e.components(i):e.components||i:r(e.components),t.createElement(a.Provider,{value:s},e.children)}},3260:(e,s,n)=>{n.d(s,{A:()=>t});const t=n.p+"assets/images/daily_tasks_1-25922a7d3b552e6cb6cc156de2273ff0.png"},4618:(e,s,n)=>{n.d(s,{A:()=>t});const t=n.p+"assets/images/daily_tasks_3_1-4330d40327ce06c12287c233d02a8c76.png"},5201:(e,s,n)=>{n.d(s,{A:()=>t});const t=n.p+"assets/images/daily_tasks_3_2-f683ce9a28a91e94821715237e3dee2c.png"},6867:(e,s,n)=>{n.d(s,{A:()=>t});const t=n.p+"assets/images/daily_tasks_6-a4a9afe4bf18ee0b587705ef2908e557.png"},8267:(e,s,n)=>{n.d(s,{A:()=>t});const t=n.p+"assets/images/daily_tasks_2_1-99473a57969247c465bda300d24081b7.png"},9004:(e,s,n)=>{n.d(s,{A:()=>t});const t=n.p+"assets/images/zalo_follow-cba5ff90b6d946b077bbad161933682d.jpg"},9046:(e,s,n)=>{n.r(s),n.d(s,{assets:()=>c,contentTitle:()=>r,default:()=>h,frontMatter:()=>a,metadata:()=>l,toc:()=>d});var t=n(216),i=n(494);const a={sidebar_position:3,hide_table_of_contents:!0,displayed_sidebar:"userSidebar"},r="Tasks Calendar",l={id:"datatp-crm/user/crm/references/tasks_calendar",title:"Tasks Calendar",description:"Task management screen by day for sales staff.",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/datatp-crm/user/crm/references/tasks_calendar.md",sourceDirName:"datatp-crm/user/crm/references",slug:"/datatp-crm/user/crm/references/tasks_calendar",permalink:"/en/docs/datatp-crm/user/crm/references/tasks_calendar",draft:!1,unlisted:!1,tags:[],version:"current",sidebarPosition:3,frontMatter:{sidebar_position:3,hide_table_of_contents:!0,displayed_sidebar:"userSidebar"},sidebar:"userSidebar"},c={},d=[{value:"Feature requirements",id:"feature-requirements",level:4},{value:"Main interface",id:"main-interface",level:4},{value:"Create a task.",id:"create-a-task",level:3},{value:"Update task",id:"update-task",level:3},{value:"Automation",id:"automation",level:4}];function o(e){const s={a:"a",blockquote:"blockquote",code:"code",h1:"h1",h3:"h3",h4:"h4",header:"header",img:"img",li:"li",ol:"ol",p:"p",strong:"strong",ul:"ul",...(0,i.R)(),...e.components};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(s.header,{children:(0,t.jsx)(s.h1,{id:"tasks-calendar",children:"Tasks Calendar"})}),"\n",(0,t.jsx)(s.p,{children:"Task management screen by day for sales staff."}),"\n",(0,t.jsx)(s.h4,{id:"feature-requirements",children:"Feature requirements"}),"\n",(0,t.jsxs)(s.ol,{children:["\n",(0,t.jsxs)(s.li,{children:[(0,t.jsx)(s.strong,{children:"Update personal information"}),"\n",(0,t.jsxs)(s.ul,{children:["\n",(0,t.jsxs)(s.li,{children:["Access ",(0,t.jsx)(s.code,{children:"My Space"})," from the modules list -> Select the ",(0,t.jsx)(s.code,{children:"Account"})," Tab."]}),"\n",(0,t.jsx)(s.li,{children:"Update the phone number registered with Zalo"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,t.jsx)(s.p,{children:(0,t.jsx)(s.a,{href:"https://youtu.be/G_2-CHi5eek",children:"https://youtu.be/G_2-CHi5eek"})}),"\n",(0,t.jsx)("div",{style:{position:"relative",paddingBottom:"56.25%",height:0,overflow:"hidden",maxWidth:"100%",height:"auto"},children:(0,t.jsx)("iframe",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%"},src:"https://www.youtube.com/embed/G_2-CHi5eek",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})}),"\n",(0,t.jsx)("hr",{}),"\n",(0,t.jsxs)(s.ol,{start:"2",children:["\n",(0,t.jsxs)(s.li,{children:[(0,t.jsx)(s.strong,{children:"Connect with OA Zalo"})," (activate Zalo notification feature)","\n",(0,t.jsxs)(s.ul,{children:["\n",(0,t.jsx)(s.li,{children:'Follow and click "Quan t\xe2m" (Follow) OA Zalo as shown below'}),"\n",(0,t.jsx)(s.li,{children:"Interact each message (Like/Heart/...) to maintain the notification feature"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,t.jsx)("div",{style:{background:"#fff3cd",color:"#856404",padding:"12px 16px",borderRadius:"6px",border:"1px solid #ffeeba",margin:"16px 0"},children:(0,t.jsxs)(s.p,{children:[(0,t.jsx)("strong",{children:"Note:"})," If you do not interact with any message within 7 days, Zalo will temporarily stop sending notifications.",(0,t.jsx)("br",{}),"\nTo ensure continuous connection, please keep interacting at least one message per week."]})}),"\n",(0,t.jsx)(s.p,{children:(0,t.jsx)(s.img,{alt:"zalo_follow.jpg",src:n(9004).A+"",width:"1260",height:"1920"})}),"\n",(0,t.jsx)(s.h4,{id:"main-interface",children:"Main interface"}),"\n",(0,t.jsxs)(s.ol,{children:["\n",(0,t.jsx)(s.li,{children:"Access CRM"}),"\n",(0,t.jsxs)(s.li,{children:["Click the ",(0,t.jsx)(s.code,{children:"Tasks Calendar"})," tab on the toolbar."]}),"\n"]}),"\n",(0,t.jsx)(s.p,{children:(0,t.jsx)(s.img,{alt:"Daily Tasks",src:n(3260).A+"",width:"3344",height:"1870"})}),"\n",(0,t.jsx)("hr",{}),"\n",(0,t.jsxs)(s.ul,{children:["\n",(0,t.jsxs)(s.li,{children:[(0,t.jsx)(s.strong,{children:"Today"}),": Go to the current day"]}),"\n",(0,t.jsxs)(s.li,{children:[(0,t.jsx)(s.strong,{children:"Month navigation"}),":  ",(0,t.jsx)(s.code,{children:"<"})," : Previous month | ",(0,t.jsx)(s.code,{children:">"})," : Next month\n",(0,t.jsx)(s.strong,{children:"View mode"}),": ",(0,t.jsx)(s.code,{children:"Day"}),",  ",(0,t.jsx)(s.code,{children:"Week"}),", ",(0,t.jsx)(s.code,{children:"Month"})]}),"\n"]}),"\n",(0,t.jsxs)(s.blockquote,{children:["\n",(0,t.jsx)(s.p,{children:(0,t.jsx)(s.strong,{children:"Filter by type"})}),"\n",(0,t.jsxs)(s.ul,{children:["\n",(0,t.jsxs)(s.li,{children:[(0,t.jsx)(s.code,{children:"All Tasks"}),": All tasks."]}),"\n",(0,t.jsxs)(s.li,{children:[(0,t.jsx)(s.code,{children:"Meeting Note"}),": Task created to note information from meetings."]}),"\n",(0,t.jsxs)(s.li,{children:[(0,t.jsx)(s.code,{children:"Leads"}),": Task related to potential customers."]}),"\n",(0,t.jsxs)(s.li,{children:[(0,t.jsx)(s.code,{children:"Customers"}),": Task related to customers."]}),"\n"]}),"\n"]}),"\n",(0,t.jsx)(s.h3,{id:"create-a-task",children:"Create a task."}),"\n",(0,t.jsxs)(s.ol,{children:["\n",(0,t.jsxs)(s.li,{children:["\n",(0,t.jsx)(s.p,{children:"Click on the date cell you want to create a task on the calendar"}),"\n"]}),"\n",(0,t.jsxs)(s.li,{children:["\n",(0,t.jsx)(s.p,{children:"Enter task information: Task type, status, title, description, ..."}),"\n",(0,t.jsxs)(s.ul,{children:["\n",(0,t.jsx)(s.li,{children:"Created Date (default is the date you select on the calendar)"}),"\n",(0,t.jsx)(s.li,{children:"Due date (default is the end of the selected day)"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,t.jsxs)(s.p,{children:["In addition, for tasks related to tracking ",(0,t.jsx)(s.strong,{children:"Lead/ Customer"}),"."]}),"\n",(0,t.jsx)(s.p,{children:"Enter Lead or Customer, the system will automatically display additional inputs to enter related information."}),"\n",(0,t.jsx)(s.p,{children:(0,t.jsx)(s.img,{alt:"Daily Tasks",src:n(8267).A+"",width:"863",height:"743"})}),"\n",(0,t.jsxs)(s.ol,{start:"4",children:["\n",(0,t.jsxs)(s.li,{children:["\n",(0,t.jsx)(s.p,{children:"Set up notifications:"}),"\n",(0,t.jsxs)(s.ul,{children:["\n",(0,t.jsx)(s.li,{children:'Tick "Send zalo" to receive notifications'}),"\n",(0,t.jsx)(s.li,{children:"Select the date/time to receive notifications"}),"\n"]}),"\n"]}),"\n",(0,t.jsxs)(s.li,{children:["\n",(0,t.jsxs)(s.p,{children:["Click ",(0,t.jsx)(s.strong,{children:"Create"})," to save"]}),"\n"]}),"\n"]}),"\n",(0,t.jsx)(s.p,{children:(0,t.jsx)(s.a,{href:"https://youtu.be/jgOP64wb4u0",children:"https://youtu.be/jgOP64wb4u0"})}),"\n",(0,t.jsx)("div",{style:{position:"relative",paddingBottom:"56.25%",height:0,overflow:"hidden",maxWidth:"100%",height:"auto"},children:(0,t.jsx)("iframe",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%"},src:"https://www.youtube.com/embed/jgOP64wb4u0",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})}),"\n",(0,t.jsx)(s.h3,{id:"update-task",children:"Update task"}),"\n",(0,t.jsx)(s.p,{children:(0,t.jsx)(s.strong,{children:"Method 1: Update each task"})}),"\n",(0,t.jsxs)(s.ol,{children:["\n",(0,t.jsx)(s.li,{children:"Click on the task label on the calendar"}),"\n",(0,t.jsx)(s.li,{children:"Edit the necessary information"}),"\n",(0,t.jsxs)(s.li,{children:["Click ",(0,t.jsx)(s.strong,{children:"Save"})," to save changes"]}),"\n"]}),"\n",(0,t.jsx)(s.p,{children:(0,t.jsx)(s.img,{alt:"Daily Tasks",src:n(4618).A+"",width:"1921",height:"1082"})}),"\n",(0,t.jsx)(s.p,{children:(0,t.jsx)(s.strong,{children:"Method 2: Update multiple tasks"})}),"\n",(0,t.jsxs)(s.ol,{children:["\n",(0,t.jsx)(s.li,{children:'Click "view more" or "+x more" at the last item on the calendar cell.'}),"\n",(0,t.jsx)(s.li,{children:"Select a new status for each task"}),"\n",(0,t.jsx)(s.li,{children:"The system will automatically save when the status changes"}),"\n"]}),"\n",(0,t.jsx)(s.p,{children:(0,t.jsx)(s.img,{alt:"Daily Tasks",src:n(5201).A+"",width:"1916",height:"1085"})}),"\n",(0,t.jsx)(s.h4,{id:"automation",children:"Automation"}),"\n",(0,t.jsxs)(s.ul,{children:["\n",(0,t.jsxs)(s.li,{children:["\n",(0,t.jsxs)(s.p,{children:["Tasks that are not completed (status other than Completed/ Block) will have their ",(0,t.jsx)(s.code,{children:"Due Date"})," automatically updated to the next day by the system."]}),"\n"]}),"\n",(0,t.jsxs)(s.li,{children:["\n",(0,t.jsx)(s.p,{children:"At 8am every day, the system checks for unfinished tasks from the previous day and sends a reminder notification with similar content as below."}),"\n"]}),"\n"]}),"\n",(0,t.jsx)(s.p,{children:(0,t.jsx)(s.img,{alt:"Daily Tasks",src:n(6867).A+"",width:"1561",height:"1034"})})]})}function h(e={}){const{wrapper:s}={...(0,i.R)(),...e.components};return s?(0,t.jsx)(s,{...e,children:(0,t.jsx)(o,{...e})}):o(e)}}}]);