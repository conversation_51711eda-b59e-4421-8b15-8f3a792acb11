"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[1777],{494:(e,t,n)=>{n.d(t,{R:()=>r,x:()=>u});var o=n(6372);const s={},c=o.createContext(s);function r(e){const t=o.useContext(c);return o.useMemo(function(){return"function"==typeof e?e(t):{...t,...e}},[t,e])}function u(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:r(e.components),o.createElement(c.Provider,{value:t},e.children)}},8735:(e,t,n)=>{n.r(t),n.d(t,{assets:()=>i,contentTitle:()=>r,default:()=>d,frontMatter:()=>c,metadata:()=>u,toc:()=>l});var o=n(216),s=n(494);const c={},r=void 0,u={id:"shared/user/welcome",title:"welcome",description:"Welcome",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/shared/user/welcome.md",sourceDirName:"shared/user",slug:"/shared/user/welcome",permalink:"/en/docs/shared/user/welcome",draft:!1,unlisted:!1,tags:[],version:"current",frontMatter:{}},i={},l=[{value:"Welcome",id:"welcome",level:2}];function a(e){const t={h2:"h2",...(0,s.R)(),...e.components};return(0,o.jsx)(t.h2,{id:"welcome",children:"Welcome"})}function d(e={}){const{wrapper:t}={...(0,s.R)(),...e.components};return t?(0,o.jsx)(t,{...e,children:(0,o.jsx)(a,{...e})}):a(e)}}}]);