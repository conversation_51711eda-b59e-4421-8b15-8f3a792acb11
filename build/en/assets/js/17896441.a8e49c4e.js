(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[8401],{494:(e,t,n)=>{"use strict";n.d(t,{R:()=>i,x:()=>c});var s=n(6372);const a={},o=s.createContext(a);function i(e){const t=s.useContext(o);return s.useMemo(function(){return"function"==typeof e?e(t):{...t,...e}},[t,e])}function c(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(a):e.components||a:i(e.components),s.createElement(o.Provider,{value:t},e.children)}},2232:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>an});var s=n(6372),a=n(3135),o=n(7080),i=n(216);const c=s.createContext(null);function r({children:e,content:t}){const n=function(e){return(0,s.useMemo)(()=>({metadata:e.metadata,frontMatter:e.frontMatter,assets:e.assets,contentTitle:e.contentTitle,toc:e.toc}),[e])}(t);return(0,i.jsx)(c.Provider,{value:n,children:e})}function l(){const e=(0,s.useContext)(c);if(null===e)throw new o.dV("DocProvider");return e}function d(){const{metadata:e,frontMatter:t,assets:n}=l();return(0,i.jsx)(a.be,{title:e.title,description:e.description,keywords:t.keywords,image:n.image??t.image})}var u=n(3394),m=n(1801),h=n(8419),p=n(1830);function f(e){const{permalink:t,title:n,subLabel:s,isNext:a}=e;return(0,i.jsxs)(p.A,{className:(0,u.A)("pagination-nav__link",a?"pagination-nav__link--next":"pagination-nav__link--prev"),to:t,children:[s&&(0,i.jsx)("div",{className:"pagination-nav__sublabel",children:s}),(0,i.jsx)("div",{className:"pagination-nav__label",children:n})]})}function x(e){const{previous:t,next:n}=e;return(0,i.jsxs)("nav",{className:"pagination-nav docusaurus-mt-lg","aria-label":(0,h.T)({id:"theme.docs.paginator.navAriaLabel",message:"Docs pages",description:"The ARIA label for the docs pagination"}),children:[t&&(0,i.jsx)(f,{...t,subLabel:(0,i.jsx)(h.A,{id:"theme.docs.paginator.previous",description:"The label used to navigate to the previous doc",children:"Previous"})}),n&&(0,i.jsx)(f,{...n,subLabel:(0,i.jsx)(h.A,{id:"theme.docs.paginator.next",description:"The label used to navigate to the next doc",children:"Next"}),isNext:!0})]})}function b(){const{metadata:e}=l();return(0,i.jsx)(x,{previous:e.previous,next:e.next})}var g=n(9530),j=n(4532),v=n(8659),N=n(8551),A=n(3722);const C={unreleased:function({siteTitle:e,versionMetadata:t}){return(0,i.jsx)(h.A,{id:"theme.docs.versions.unreleasedVersionLabel",description:"The label used to tell the user that he's browsing an unreleased doc version",values:{siteTitle:e,versionLabel:(0,i.jsx)("b",{children:t.label})},children:"This is unreleased documentation for {siteTitle} {versionLabel} version."})},unmaintained:function({siteTitle:e,versionMetadata:t}){return(0,i.jsx)(h.A,{id:"theme.docs.versions.unmaintainedVersionLabel",description:"The label used to tell the user that he's browsing an unmaintained doc version",values:{siteTitle:e,versionLabel:(0,i.jsx)("b",{children:t.label})},children:"This is documentation for {siteTitle} {versionLabel}, which is no longer actively maintained."})}};function y(e){const t=C[e.versionMetadata.banner];return(0,i.jsx)(t,{...e})}function k({versionLabel:e,to:t,onClick:n}){return(0,i.jsx)(h.A,{id:"theme.docs.versions.latestVersionSuggestionLabel",description:"The label used to tell the user to check the latest version",values:{versionLabel:e,latestVersionLink:(0,i.jsx)("b",{children:(0,i.jsx)(p.A,{to:t,onClick:n,children:(0,i.jsx)(h.A,{id:"theme.docs.versions.latestVersionLinkLabel",description:"The label used for the latest version suggestion link label",children:"latest version"})})})},children:"For up-to-date documentation, see the {latestVersionLink} ({versionLabel})."})}function L({className:e,versionMetadata:t}){const{siteConfig:{title:n}}=(0,g.A)(),{pluginId:s}=(0,j.vT)({failfast:!0}),{savePreferredVersionName:a}=(0,N.g1)(s),{latestDocSuggestion:o,latestVersionSuggestion:c}=(0,j.HW)(s),r=o??(l=c).docs.find(e=>e.id===l.mainDocId);var l;return(0,i.jsxs)("div",{className:(0,u.A)(e,v.G.docs.docVersionBanner,"alert alert--warning margin-bottom--md"),role:"alert",children:[(0,i.jsx)("div",{children:(0,i.jsx)(y,{siteTitle:n,versionMetadata:t})}),(0,i.jsx)("div",{className:"margin-top--md",children:(0,i.jsx)(k,{versionLabel:c.label,to:r.path,onClick:()=>a(c.name)})})]})}function _({className:e}){const t=(0,A.r)();return t.banner?(0,i.jsx)(L,{className:e,versionMetadata:t}):null}function B({className:e}){const t=(0,A.r)();return t.badge?(0,i.jsx)("span",{className:(0,u.A)(e,v.G.docs.docVersionBadge,"badge badge--secondary"),children:(0,i.jsx)(h.A,{id:"theme.docs.versionBadge.label",values:{versionLabel:t.label},children:"Version: {versionLabel}"})}):null}const w={tag:"tag_lf5G",tagRegular:"tagRegular_gpwX",tagWithCount:"tagWithCount_Mfdp"};function T({permalink:e,label:t,count:n,description:s}){return(0,i.jsxs)(p.A,{href:e,title:s,className:(0,u.A)(w.tag,n?w.tagWithCount:w.tagRegular),children:[t,n&&(0,i.jsx)("span",{children:n})]})}const E={tags:"tags_jb3E",tag:"tag_hkBL"};function H({tags:e}){return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("b",{children:(0,i.jsx)(h.A,{id:"theme.tags.tagsListLabel",description:"The label alongside a tag list",children:"Tags:"})}),(0,i.jsx)("ul",{className:(0,u.A)(E.tags,"padding--none","margin-left--sm"),children:e.map(e=>(0,i.jsx)("li",{className:E.tag,children:(0,i.jsx)(T,{...e})},e.permalink))})]})}const M={iconEdit:"iconEdit_zlvE"};function I({className:e,...t}){return(0,i.jsx)("svg",{fill:"currentColor",height:"20",width:"20",viewBox:"0 0 40 40",className:(0,u.A)(M.iconEdit,e),"aria-hidden":"true",...t,children:(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"m34.5 11.7l-3 3.1-6.3-6.3 3.1-3q0.5-0.5 1.2-0.5t1.1 0.5l3.9 3.9q0.5 0.4 0.5 1.1t-0.5 1.2z m-29.5 17.1l18.4-18.5 6.3 6.3-18.4 18.4h-6.3v-6.2z"})})})}function S({editUrl:e}){return(0,i.jsxs)(p.A,{to:e,className:v.G.common.editThisPage,children:[(0,i.jsx)(I,{}),(0,i.jsx)(h.A,{id:"theme.common.editThisPage",description:"The link label to edit the current page",children:"Edit this page"})]})}function U(e={}){const{i18n:{currentLocale:t}}=(0,g.A)(),n=function(){const{i18n:{currentLocale:e,localeConfigs:t}}=(0,g.A)();return t[e].calendar}();return new Intl.DateTimeFormat(t,{calendar:n,...e})}function R({lastUpdatedAt:e}){const t=new Date(e),n=U({day:"numeric",month:"short",year:"numeric",timeZone:"UTC"}).format(t);return(0,i.jsx)(h.A,{id:"theme.lastUpdated.atDate",description:"The words used to describe on which date a page has been last updated",values:{date:(0,i.jsx)("b",{children:(0,i.jsx)("time",{dateTime:t.toISOString(),itemProp:"dateModified",children:n})})},children:" on {date}"})}function z({lastUpdatedBy:e}){return(0,i.jsx)(h.A,{id:"theme.lastUpdated.byUser",description:"The words used to describe by who the page has been last updated",values:{user:(0,i.jsx)("b",{children:e})},children:" by {user}"})}function V({lastUpdatedAt:e,lastUpdatedBy:t}){return(0,i.jsxs)("span",{className:v.G.common.lastUpdated,children:[(0,i.jsx)(h.A,{id:"theme.lastUpdated.lastUpdatedAtBy",description:"The sentence used to display when a page has been last updated, and by who",values:{atDate:e?(0,i.jsx)(R,{lastUpdatedAt:e}):"",byUser:t?(0,i.jsx)(z,{lastUpdatedBy:t}):""},children:"Last updated{atDate}{byUser}"}),!1]})}const G={lastUpdated:"lastUpdated_QuRw"};function O({className:e,editUrl:t,lastUpdatedAt:n,lastUpdatedBy:s}){return(0,i.jsxs)("div",{className:(0,u.A)("row",e),children:[(0,i.jsx)("div",{className:"col",children:t&&(0,i.jsx)(S,{editUrl:t})}),(0,i.jsx)("div",{className:(0,u.A)("col",G.lastUpdated),children:(n||s)&&(0,i.jsx)(V,{lastUpdatedAt:n,lastUpdatedBy:s})})]})}function P(){const{metadata:e}=l(),{editUrl:t,lastUpdatedAt:n,lastUpdatedBy:s,tags:a}=e,o=a.length>0,c=!!(t||n||s);return o||c?(0,i.jsxs)("footer",{className:(0,u.A)(v.G.docs.docFooter,"docusaurus-mt-lg"),children:[o&&(0,i.jsx)("div",{className:(0,u.A)("row margin-top--sm",v.G.docs.docFooterTagsRow),children:(0,i.jsx)("div",{className:"col",children:(0,i.jsx)(H,{tags:a})})}),c&&(0,i.jsx)(O,{className:(0,u.A)("margin-top--sm",v.G.docs.docFooterEditMetaRow),editUrl:t,lastUpdatedAt:n,lastUpdatedBy:s})]}):null}var D=n(2026),$=n(2410);function W(e){const t=e.map(e=>({...e,parentIndex:-1,children:[]})),n=Array(7).fill(-1);t.forEach((e,t)=>{const s=n.slice(2,e.level);e.parentIndex=Math.max(...s),n[e.level]=t});const s=[];return t.forEach(e=>{const{parentIndex:n,...a}=e;n>=0?t[n].children.push(a):s.push(a)}),s}function F({toc:e,minHeadingLevel:t,maxHeadingLevel:n}){return e.flatMap(e=>{const s=F({toc:e.children,minHeadingLevel:t,maxHeadingLevel:n});return function(e){return e.level>=t&&e.level<=n}(e)?[{...e,children:s}]:s})}function q(e){const t=e.getBoundingClientRect();return t.top===t.bottom?q(e.parentNode):t}function Z(e,{anchorTopOffset:t}){const n=e.find(e=>q(e).top>=t);if(n){return function(e){return e.top>0&&e.bottom<window.innerHeight/2}(q(n))?n:e[e.indexOf(n)-1]??null}return e[e.length-1]??null}function Q(){const e=(0,s.useRef)(0),{navbar:{hideOnScroll:t}}=(0,$.p)();return(0,s.useEffect)(()=>{e.current=t?0:document.querySelector(".navbar").clientHeight},[t]),e}function Y(e){const t=(0,s.useRef)(void 0),n=Q();(0,s.useEffect)(()=>{if(!e)return()=>{};const{linkClassName:s,linkActiveClassName:a,minHeadingLevel:o,maxHeadingLevel:i}=e;function c(){const e=function(e){return Array.from(document.getElementsByClassName(e))}(s),c=function({minHeadingLevel:e,maxHeadingLevel:t}){const n=[];for(let s=e;s<=t;s+=1)n.push(`h${s}.anchor`);return Array.from(document.querySelectorAll(n.join()))}({minHeadingLevel:o,maxHeadingLevel:i}),r=Z(c,{anchorTopOffset:n.current}),l=e.find(e=>r&&r.id===function(e){return decodeURIComponent(e.href.substring(e.href.indexOf("#")+1))}(e));e.forEach(e=>{!function(e,n){n?(t.current&&t.current!==e&&t.current.classList.remove(a),e.classList.add(a),t.current=e):e.classList.remove(a)}(e,e===l)})}return document.addEventListener("scroll",c),document.addEventListener("resize",c),c(),()=>{document.removeEventListener("scroll",c),document.removeEventListener("resize",c)}},[e,n])}function X({toc:e,className:t,linkClassName:n,isChild:s}){return e.length?(0,i.jsx)("ul",{className:s?void 0:t,children:e.map(e=>(0,i.jsxs)("li",{children:[(0,i.jsx)(p.A,{to:`#${e.id}`,className:n??void 0,dangerouslySetInnerHTML:{__html:e.value}}),(0,i.jsx)(X,{isChild:!0,toc:e.children,className:t,linkClassName:n})]},e.id))}):null}const J=s.memo(X);function K({toc:e,className:t="table-of-contents table-of-contents__left-border",linkClassName:n="table-of-contents__link",linkActiveClassName:a,minHeadingLevel:o,maxHeadingLevel:c,...r}){const l=(0,$.p)(),d=o??l.tableOfContents.minHeadingLevel,u=c??l.tableOfContents.maxHeadingLevel,m=function({toc:e,minHeadingLevel:t,maxHeadingLevel:n}){return(0,s.useMemo)(()=>F({toc:W(e),minHeadingLevel:t,maxHeadingLevel:n}),[e,t,n])}({toc:e,minHeadingLevel:d,maxHeadingLevel:u});return Y((0,s.useMemo)(()=>{if(n&&a)return{linkClassName:n,linkActiveClassName:a,minHeadingLevel:d,maxHeadingLevel:u}},[n,a,d,u])),(0,i.jsx)(J,{toc:m,className:t,linkClassName:n,...r})}const ee={tocCollapsibleButton:"tocCollapsibleButton_Pxai",tocCollapsibleButtonExpanded:"tocCollapsibleButtonExpanded_uk7e"};function te({collapsed:e,...t}){return(0,i.jsx)("button",{type:"button",...t,className:(0,u.A)("clean-btn",ee.tocCollapsibleButton,!e&&ee.tocCollapsibleButtonExpanded,t.className),children:(0,i.jsx)(h.A,{id:"theme.TOCCollapsible.toggleButtonLabel",description:"The label used by the button on the collapsible TOC component",children:"On this page"})})}const ne={tocCollapsible:"tocCollapsible_zskR",tocCollapsibleContent:"tocCollapsibleContent_qXkz",tocCollapsibleExpanded:"tocCollapsibleExpanded_wTmy"};function se({toc:e,className:t,minHeadingLevel:n,maxHeadingLevel:s}){const{collapsed:a,toggleCollapsed:o}=(0,D.u)({initialState:!0});return(0,i.jsxs)("div",{className:(0,u.A)(ne.tocCollapsible,!a&&ne.tocCollapsibleExpanded,t),children:[(0,i.jsx)(te,{collapsed:a,onClick:o}),(0,i.jsx)(D.N,{lazy:!0,className:ne.tocCollapsibleContent,collapsed:a,children:(0,i.jsx)(K,{toc:e,minHeadingLevel:n,maxHeadingLevel:s})})]})}const ae={tocMobile:"tocMobile_v3PZ"};function oe(){const{toc:e,frontMatter:t}=l();return(0,i.jsx)(se,{toc:e,minHeadingLevel:t.toc_min_heading_level,maxHeadingLevel:t.toc_max_heading_level,className:(0,u.A)(v.G.docs.docTocMobile,ae.tocMobile)})}const ie={tableOfContents:"tableOfContents_mBHc",docItemContainer:"docItemContainer_IH8N"},ce="table-of-contents__link toc-highlight",re="table-of-contents__link--active";function le({className:e,...t}){return(0,i.jsx)("div",{className:(0,u.A)(ie.tableOfContents,"thin-scrollbar",e),children:(0,i.jsx)(K,{...t,linkClassName:ce,linkActiveClassName:re})})}function de(){const{toc:e,frontMatter:t}=l();return(0,i.jsx)(le,{toc:e,minHeadingLevel:t.toc_min_heading_level,maxHeadingLevel:t.toc_max_heading_level,className:v.G.docs.docTocDesktop})}var ue=n(615),me=n(494),he=n(7676),pe=n(4255),fe=n(4985);function xe(){const{prism:e}=(0,$.p)(),{colorMode:t}=(0,fe.G)(),n=e.theme,s=e.darkTheme||n;return"dark"===t?s:n}var be=n(4809),ge=n.n(be);const je=/title=(?<quote>["'])(?<title>.*?)\1/,ve=/\{(?<range>[\d,-]+)\}/,Ne={js:{start:"\\/\\/",end:""},jsBlock:{start:"\\/\\*",end:"\\*\\/"},jsx:{start:"\\{\\s*\\/\\*",end:"\\*\\/\\s*\\}"},bash:{start:"#",end:""},html:{start:"\x3c!--",end:"--\x3e"}},Ae={...Ne,lua:{start:"--",end:""},wasm:{start:"\\;\\;",end:""},tex:{start:"%",end:""},vb:{start:"['\u2018\u2019]",end:""},vbnet:{start:"(?:_\\s*)?['\u2018\u2019]",end:""},rem:{start:"[Rr][Ee][Mm]\\b",end:""},f90:{start:"!",end:""},ml:{start:"\\(\\*",end:"\\*\\)"},cobol:{start:"\\*>",end:""}},Ce=Object.keys(Ne);function ye(e,t){const n=e.map(e=>{const{start:n,end:s}=Ae[e];return`(?:${n}\\s*(${t.flatMap(e=>[e.line,e.block?.start,e.block?.end].filter(Boolean)).join("|")})\\s*${s})`}).join("|");return new RegExp(`^\\s*(?:${n})\\s*$`)}function ke(e,t){let n=e.replace(/\n$/,"");const{language:s,magicComments:a,metastring:o}=t;if(o&&ve.test(o)){const e=o.match(ve).groups.range;if(0===a.length)throw new Error(`A highlight range has been given in code block's metastring (\`\`\` ${o}), but no magic comment config is available. Docusaurus applies the first magic comment entry's className for metastring ranges.`);const t=a[0].className,s=ge()(e).filter(e=>e>0).map(e=>[e-1,[t]]);return{lineClassNames:Object.fromEntries(s),code:n}}if(void 0===s)return{lineClassNames:{},code:n};const i=function(e,t){switch(e){case"js":case"javascript":case"ts":case"typescript":return ye(["js","jsBlock"],t);case"jsx":case"tsx":return ye(["js","jsBlock","jsx"],t);case"html":return ye(["js","jsBlock","html"],t);case"python":case"py":case"bash":return ye(["bash"],t);case"markdown":case"md":return ye(["html","jsx","bash"],t);case"tex":case"latex":case"matlab":return ye(["tex"],t);case"lua":case"haskell":case"sql":return ye(["lua"],t);case"wasm":return ye(["wasm"],t);case"vb":case"vba":case"visual-basic":return ye(["vb","rem"],t);case"vbnet":return ye(["vbnet","rem"],t);case"batch":return ye(["rem"],t);case"basic":return ye(["rem","f90"],t);case"fsharp":return ye(["js","ml"],t);case"ocaml":case"sml":return ye(["ml"],t);case"fortran":return ye(["f90"],t);case"cobol":return ye(["cobol"],t);default:return ye(Ce,t)}}(s,a),c=n.split("\n"),r=Object.fromEntries(a.map(e=>[e.className,{start:0,range:""}])),l=Object.fromEntries(a.filter(e=>e.line).map(({className:e,line:t})=>[t,e])),d=Object.fromEntries(a.filter(e=>e.block).map(({className:e,block:t})=>[t.start,e])),u=Object.fromEntries(a.filter(e=>e.block).map(({className:e,block:t})=>[t.end,e]));for(let h=0;h<c.length;){const e=c[h].match(i);if(!e){h+=1;continue}const t=e.slice(1).find(e=>void 0!==e);l[t]?r[l[t]].range+=`${h},`:d[t]?r[d[t]].start=h:u[t]&&(r[u[t]].range+=`${r[u[t]].start}-${h-1},`),c.splice(h,1)}n=c.join("\n");const m={};return Object.entries(r).forEach(([e,{range:t}])=>{ge()(t).forEach(t=>{m[t]??=[],m[t].push(e)})}),{lineClassNames:m,code:n}}const Le="codeBlockContainer_lHyG";function _e({as:e,...t}){const n=function(e){const t={color:"--prism-color",backgroundColor:"--prism-background-color"},n={};return Object.entries(e.plain).forEach(([e,s])=>{const a=t[e];a&&"string"==typeof s&&(n[a]=s)}),n}(xe());return(0,i.jsx)(e,{...t,style:n,className:(0,u.A)(t.className,Le,v.G.common.codeBlock)})}const Be={codeBlockContent:"codeBlockContent_o3WU",codeBlockTitle:"codeBlockTitle_AY2f",codeBlock:"codeBlock_SsTB",codeBlockStandalone:"codeBlockStandalone_wZhM",codeBlockLines:"codeBlockLines_GnfZ",codeBlockLinesWithNumbering:"codeBlockLinesWithNumbering_uMuN",buttonGroup:"buttonGroup_bRv8"};function we({children:e,className:t}){return(0,i.jsx)(_e,{as:"pre",tabIndex:0,className:(0,u.A)(Be.codeBlockStandalone,"thin-scrollbar",t),children:(0,i.jsx)("code",{className:Be.codeBlockLines,children:e})})}const Te={attributes:!0,characterData:!0,childList:!0,subtree:!0};function Ee(e,t){const[n,a]=(0,s.useState)(),i=(0,s.useCallback)(()=>{a(e.current?.closest("[role=tabpanel][hidden]"))},[e,a]);(0,s.useEffect)(()=>{i()},[i]),function(e,t,n=Te){const a=(0,o._q)(t),i=(0,o.Be)(n);(0,s.useEffect)(()=>{const t=new MutationObserver(a);return e&&t.observe(e,i),()=>t.disconnect()},[e,a,i])}(n,e=>{e.forEach(e=>{"attributes"===e.type&&"hidden"===e.attributeName&&(t(),i())})},{attributes:!0,characterData:!1,childList:!1,subtree:!1})}var He=n(6914);const Me="codeLine_jg_W",Ie="codeLineNumber_Efpf",Se="codeLineContent_oY1W";function Ue({line:e,classNames:t,showLineNumbers:n,getLineProps:s,getTokenProps:a}){1===e.length&&"\n"===e[0].content&&(e[0].content="");const o=s({line:e,className:(0,u.A)(t,n&&Me)}),c=e.map((e,t)=>(0,i.jsx)("span",{...a({token:e})},t));return(0,i.jsxs)("span",{...o,children:[n?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("span",{className:Ie}),(0,i.jsx)("span",{className:Se,children:c})]}):c,(0,i.jsx)("br",{})]})}function Re(e){return(0,i.jsx)("svg",{viewBox:"0 0 24 24",...e,children:(0,i.jsx)("path",{fill:"currentColor",d:"M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"})})}function ze(e){return(0,i.jsx)("svg",{viewBox:"0 0 24 24",...e,children:(0,i.jsx)("path",{fill:"currentColor",d:"M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"})})}const Ve={copyButtonCopied:"copyButtonCopied_Fulc",copyButtonIcons:"copyButtonIcons_U7Rx",copyButtonIcon:"copyButtonIcon_RLFU",copyButtonSuccessIcon:"copyButtonSuccessIcon_aQBr"};function Ge({code:e,className:t}){const[n,a]=(0,s.useState)(!1),o=(0,s.useRef)(void 0),c=(0,s.useCallback)(()=>{!function(e,{target:t=document.body}={}){if("string"!=typeof e)throw new TypeError(`Expected parameter \`text\` to be a \`string\`, got \`${typeof e}\`.`);const n=document.createElement("textarea"),s=document.activeElement;n.value=e,n.setAttribute("readonly",""),n.style.contain="strict",n.style.position="absolute",n.style.left="-9999px",n.style.fontSize="12pt";const a=document.getSelection(),o=a.rangeCount>0&&a.getRangeAt(0);t.append(n),n.select(),n.selectionStart=0,n.selectionEnd=e.length;let i=!1;try{i=document.execCommand("copy")}catch{}n.remove(),o&&(a.removeAllRanges(),a.addRange(o)),s&&s.focus()}(e),a(!0),o.current=window.setTimeout(()=>{a(!1)},1e3)},[e]);return(0,s.useEffect)(()=>()=>window.clearTimeout(o.current),[]),(0,i.jsx)("button",{type:"button","aria-label":n?(0,h.T)({id:"theme.CodeBlock.copied",message:"Copied",description:"The copied button label on code blocks"}):(0,h.T)({id:"theme.CodeBlock.copyButtonAriaLabel",message:"Copy code to clipboard",description:"The ARIA label for copy code blocks button"}),title:(0,h.T)({id:"theme.CodeBlock.copy",message:"Copy",description:"The copy button label on code blocks"}),className:(0,u.A)("clean-btn",t,Ve.copyButton,n&&Ve.copyButtonCopied),onClick:c,children:(0,i.jsxs)("span",{className:Ve.copyButtonIcons,"aria-hidden":"true",children:[(0,i.jsx)(Re,{className:Ve.copyButtonIcon}),(0,i.jsx)(ze,{className:Ve.copyButtonSuccessIcon})]})})}function Oe(e){return(0,i.jsx)("svg",{viewBox:"0 0 24 24",...e,children:(0,i.jsx)("path",{fill:"currentColor",d:"M4 19h6v-2H4v2zM20 5H4v2h16V5zm-3 6H4v2h13.25c1.1 0 2 .9 2 2s-.9 2-2 2H15v-2l-3 3l3 3v-2h2c2.21 0 4-1.79 4-4s-1.79-4-4-4z"})})}const Pe="wordWrapButtonIcon_jiQU",De="wordWrapButtonEnabled_eSkG";function $e({className:e,onClick:t,isEnabled:n}){const s=(0,h.T)({id:"theme.CodeBlock.wordWrapToggle",message:"Toggle word wrap",description:"The title attribute for toggle word wrapping button of code block lines"});return(0,i.jsx)("button",{type:"button",onClick:t,className:(0,u.A)("clean-btn",e,n&&De),"aria-label":s,title:s,children:(0,i.jsx)(Oe,{className:Pe,"aria-hidden":"true"})})}function We({children:e,className:t="",metastring:n,title:a,showLineNumbers:o,language:c}){const{prism:{defaultLanguage:r,magicComments:l}}=(0,$.p)(),d=function(e){return e?.toLowerCase()}(c??function(e){const t=e.split(" ").find(e=>e.startsWith("language-"));return t?.replace(/language-/,"")}(t)??r),m=xe(),h=function(){const[e,t]=(0,s.useState)(!1),[n,a]=(0,s.useState)(!1),o=(0,s.useRef)(null),i=(0,s.useCallback)(()=>{const n=o.current.querySelector("code");e?n.removeAttribute("style"):(n.style.whiteSpace="pre-wrap",n.style.overflowWrap="anywhere"),t(e=>!e)},[o,e]),c=(0,s.useCallback)(()=>{const{scrollWidth:e,clientWidth:t}=o.current,n=e>t||o.current.querySelector("code").hasAttribute("style");a(n)},[o]);return Ee(o,c),(0,s.useEffect)(()=>{c()},[e,c]),(0,s.useEffect)(()=>(window.addEventListener("resize",c,{passive:!0}),()=>{window.removeEventListener("resize",c)}),[c]),{codeBlockRef:o,isEnabled:e,isCodeScrollable:n,toggle:i}}(),p=function(e){return e?.match(je)?.groups.title??""}(n)||a,{lineClassNames:f,code:x}=ke(e,{metastring:n,language:d,magicComments:l}),b=o??function(e){return Boolean(e?.includes("showLineNumbers"))}(n);return(0,i.jsxs)(_e,{as:"div",className:(0,u.A)(t,d&&!t.includes(`language-${d}`)&&`language-${d}`),children:[p&&(0,i.jsx)("div",{className:Be.codeBlockTitle,children:p}),(0,i.jsxs)("div",{className:Be.codeBlockContent,children:[(0,i.jsx)(He.f4,{theme:m,code:x,language:d??"text",children:({className:e,style:t,tokens:n,getLineProps:s,getTokenProps:a})=>(0,i.jsx)("pre",{tabIndex:0,ref:h.codeBlockRef,className:(0,u.A)(e,Be.codeBlock,"thin-scrollbar"),style:t,children:(0,i.jsx)("code",{className:(0,u.A)(Be.codeBlockLines,b&&Be.codeBlockLinesWithNumbering),children:n.map((e,t)=>(0,i.jsx)(Ue,{line:e,getLineProps:s,getTokenProps:a,classNames:f[t],showLineNumbers:b},t))})})}),(0,i.jsxs)("div",{className:Be.buttonGroup,children:[(h.isEnabled||h.isCodeScrollable)&&(0,i.jsx)($e,{className:Be.codeButton,onClick:()=>h.toggle(),isEnabled:h.isEnabled}),(0,i.jsx)(Ge,{className:Be.codeButton,code:x})]})]})]})}function Fe({children:e,...t}){const n=(0,pe.A)(),a=function(e){return s.Children.toArray(e).some(e=>(0,s.isValidElement)(e))?e:Array.isArray(e)?e.join(""):e}(e),o="string"==typeof a?We:we;return(0,i.jsx)(o,{...t,children:a},String(n))}function qe(e){return(0,i.jsx)("code",{...e})}var Ze=n(1163);const Qe="details_iGMJ",Ye="isBrowser_bg9b",Xe="collapsibleContent_IS70";function Je(e){return!!e&&("SUMMARY"===e.tagName||Je(e.parentElement))}function Ke(e,t){return!!e&&(e===t||Ke(e.parentElement,t))}function et({summary:e,children:t,...n}){(0,Ze.A)().collectAnchor(n.id);const a=(0,pe.A)(),o=(0,s.useRef)(null),{collapsed:c,setCollapsed:r}=(0,D.u)({initialState:!n.open}),[l,d]=(0,s.useState)(n.open),m=s.isValidElement(e)?e:(0,i.jsx)("summary",{children:e??"Details"});return(0,i.jsxs)("details",{...n,ref:o,open:l,"data-collapsed":c,className:(0,u.A)(Qe,a&&Ye,n.className),onMouseDown:e=>{Je(e.target)&&e.detail>1&&e.preventDefault()},onClick:e=>{e.stopPropagation();const t=e.target;Je(t)&&Ke(t,o.current)&&(e.preventDefault(),c?(r(!1),d(!0)):r(!0))},children:[m,(0,i.jsx)(D.N,{lazy:!1,collapsed:c,disableSSRStyle:!0,onCollapseTransitionEnd:e=>{r(e),d(!e)},children:(0,i.jsx)("div",{className:Xe,children:t})})]})}const tt="details_mI4_";function nt({...e}){return(0,i.jsx)(et,{...e,className:(0,u.A)("alert alert--info",tt,e.className)})}function st(e){const t=s.Children.toArray(e.children),n=t.find(e=>s.isValidElement(e)&&"summary"===e.type),a=(0,i.jsx)(i.Fragment,{children:t.filter(e=>e!==n)});return(0,i.jsx)(nt,{...e,summary:n,children:a})}function at(e){return(0,i.jsx)(ue.A,{...e})}const ot="containsTaskList_cs9p";function it(e){if(void 0!==e)return(0,u.A)(e,e?.includes("contains-task-list")&&ot)}const ct="img_PfYa";function rt(e){const{mdxAdmonitionTitle:t,rest:n}=function(e){const t=s.Children.toArray(e),n=t.find(e=>s.isValidElement(e)&&"mdxAdmonitionTitle"===e.type),a=t.filter(e=>e!==n),o=n?.props.children;return{mdxAdmonitionTitle:o,rest:a.length>0?(0,i.jsx)(i.Fragment,{children:a}):null}}(e.children),a=e.title??t;return{...e,...a&&{title:a},children:n}}const lt="admonition_wK2w",dt="admonitionHeading_QZCZ",ut="admonitionIcon_f5Ht",mt="admonitionContent_Gj1o";function ht({type:e,className:t,children:n}){return(0,i.jsx)("div",{className:(0,u.A)(v.G.common.admonition,v.G.common.admonitionType(e),lt,t),children:n})}function pt({icon:e,title:t}){return(0,i.jsxs)("div",{className:dt,children:[(0,i.jsx)("span",{className:ut,children:e}),t]})}function ft({children:e}){return e?(0,i.jsx)("div",{className:mt,children:e}):null}function xt(e){const{type:t,icon:n,title:s,children:a,className:o}=e;return(0,i.jsxs)(ht,{type:t,className:o,children:[s||n?(0,i.jsx)(pt,{title:s,icon:n}):null,(0,i.jsx)(ft,{children:a})]})}function bt(e){return(0,i.jsx)("svg",{viewBox:"0 0 14 16",...e,children:(0,i.jsx)("path",{fillRule:"evenodd",d:"M6.3 5.69a.942.942 0 0 1-.28-.7c0-.28.09-.52.28-.7.19-.18.42-.28.7-.28.28 0 .52.09.7.28.18.***********.7 0 .28-.09.52-.28.7a1 1 0 0 1-.7.3c-.28 0-.52-.11-.7-.3zM8 7.99c-.02-.25-.11-.48-.31-.69-.2-.19-.42-.3-.69-.31H6c-.27.02-.48.13-.69.31-.2.2-.3.44-.31.69h1v3c.**********.*********.42.31.69.31h1c.27 0 .48-.11.69-.31.2-.19.3-.42.31-.69H8V7.98v.01zM7 2.3c-3.14 0-5.7 2.54-5.7 5.68 0 3.14 2.56 5.7 5.7 5.7s5.7-2.55 5.7-5.7c0-3.15-2.56-5.69-5.7-5.69v.01zM7 .98c3.86 0 7 3.14 7 7s-3.14 7-7 7-7-3.12-7-7 3.14-7 7-7z"})})}const gt={icon:(0,i.jsx)(bt,{}),title:(0,i.jsx)(h.A,{id:"theme.admonition.note",description:"The default label used for the Note admonition (:::note)",children:"note"})};function jt(e){return(0,i.jsx)(xt,{...gt,...e,className:(0,u.A)("alert alert--secondary",e.className),children:e.children})}function vt(e){return(0,i.jsx)("svg",{viewBox:"0 0 12 16",...e,children:(0,i.jsx)("path",{fillRule:"evenodd",d:"M6.5 0C3.48 0 1 2.19 1 5c0 .92.55 2.25 1 3 1.34 2.25 1.78 2.78 2 4v1h5v-1c.22-1.22.66-1.75 2-4 .45-.75 1-2.08 1-3 0-2.81-2.48-5-5.5-5zm3.64 7.48c-.25.44-.47.8-.67 1.11-.86 1.41-1.25 2.06-1.45 3.23-.02.05-.02.11-.02.17H5c0-.06 0-.13-.02-.17-.2-1.17-.59-1.83-1.45-3.23-.2-.31-.42-.67-.67-1.11C2.44 6.78 2 5.65 2 5c0-2.2 2.02-4 4.5-4 1.22 0 2.36.42 3.22 1.19C10.55 2.94 11 3.94 11 5c0 .66-.44 1.78-.86 2.48zM4 14h5c-.23 1.14-1.3 2-2.5 2s-2.27-.86-2.5-2z"})})}const Nt={icon:(0,i.jsx)(vt,{}),title:(0,i.jsx)(h.A,{id:"theme.admonition.tip",description:"The default label used for the Tip admonition (:::tip)",children:"tip"})};function At(e){return(0,i.jsx)(xt,{...Nt,...e,className:(0,u.A)("alert alert--success",e.className),children:e.children})}function Ct(e){return(0,i.jsx)("svg",{viewBox:"0 0 14 16",...e,children:(0,i.jsx)("path",{fillRule:"evenodd",d:"M7 2.3c3.14 0 5.7 2.56 5.7 5.7s-2.56 5.7-5.7 5.7A5.71 5.71 0 0 1 1.3 8c0-3.14 2.56-5.7 5.7-5.7zM7 1C3.14 1 0 4.14 0 8s3.14 7 7 7 7-3.14 7-7-3.14-7-7-7zm1 3H6v5h2V4zm0 6H6v2h2v-2z"})})}const yt={icon:(0,i.jsx)(Ct,{}),title:(0,i.jsx)(h.A,{id:"theme.admonition.info",description:"The default label used for the Info admonition (:::info)",children:"info"})};function kt(e){return(0,i.jsx)(xt,{...yt,...e,className:(0,u.A)("alert alert--info",e.className),children:e.children})}function Lt(e){return(0,i.jsx)("svg",{viewBox:"0 0 16 16",...e,children:(0,i.jsx)("path",{fillRule:"evenodd",d:"M8.893 1.5c-.183-.31-.52-.5-.887-.5s-.703.19-.886.5L.138 13.499a.98.98 0 0 0 0 1.001c.193.31.53.501.886.501h13.964c.367 0 .704-.19.877-.5a1.03 1.03 0 0 0 .01-1.002L8.893 1.5zm.133 11.497H6.987v-2.003h2.039v2.003zm0-3.004H6.987V5.987h2.039v4.006z"})})}const _t={icon:(0,i.jsx)(Lt,{}),title:(0,i.jsx)(h.A,{id:"theme.admonition.warning",description:"The default label used for the Warning admonition (:::warning)",children:"warning"})};function Bt(e){return(0,i.jsx)("svg",{viewBox:"0 0 12 16",...e,children:(0,i.jsx)("path",{fillRule:"evenodd",d:"M5.05.31c.81 2.17.41 3.38-.52 4.31C3.55 5.67 1.98 6.45.9 7.98c-1.45 2.05-1.7 6.53 3.53 7.7-2.2-1.16-2.67-4.52-.3-6.61-.61 2.03.53 3.33 1.94 2.86 1.39-.47 2.3.53 2.27 1.67-.02.78-.31 1.44-1.13 1.81 3.42-.59 4.78-3.42 4.78-5.56 0-2.84-2.53-3.22-1.25-5.61-1.52.13-2.03 1.13-1.89 2.75.09 1.08-1.02 1.8-1.86 1.33-.67-.41-.66-1.19-.06-1.78C8.18 5.31 8.68 2.45 5.05.32L5.03.3l.02.01z"})})}const wt={icon:(0,i.jsx)(Bt,{}),title:(0,i.jsx)(h.A,{id:"theme.admonition.danger",description:"The default label used for the Danger admonition (:::danger)",children:"danger"})};const Tt={icon:(0,i.jsx)(Lt,{}),title:(0,i.jsx)(h.A,{id:"theme.admonition.caution",description:"The default label used for the Caution admonition (:::caution)",children:"caution"})};const Et={...{note:jt,tip:At,info:kt,warning:function(e){return(0,i.jsx)(xt,{..._t,...e,className:(0,u.A)("alert alert--warning",e.className),children:e.children})},danger:function(e){return(0,i.jsx)(xt,{...wt,...e,className:(0,u.A)("alert alert--danger",e.className),children:e.children})}},...{secondary:e=>(0,i.jsx)(jt,{title:"secondary",...e}),important:e=>(0,i.jsx)(kt,{title:"important",...e}),success:e=>(0,i.jsx)(At,{title:"success",...e}),caution:function(e){return(0,i.jsx)(xt,{...Tt,...e,className:(0,u.A)("alert alert--warning",e.className),children:e.children})}}};function Ht(e){const t=rt(e),n=(s=t.type,Et[s]||(console.warn(`No admonition component found for admonition type "${s}". Using Info as fallback.`),Et.info));var s;return(0,i.jsx)(n,{...t})}var Mt=n(2738);const It={Head:he.A,details:st,Details:st,code:function(e){return function(e){return void 0!==e.children&&s.Children.toArray(e.children).every(e=>"string"==typeof e&&!e.includes("\n"))}(e)?(0,i.jsx)(qe,{...e}):(0,i.jsx)(Fe,{...e})},a:function(e){return(0,i.jsx)(p.A,{...e})},pre:function(e){return(0,i.jsx)(i.Fragment,{children:e.children})},ul:function(e){return(0,i.jsx)("ul",{...e,className:it(e.className)})},li:function(e){return(0,Ze.A)().collectAnchor(e.id),(0,i.jsx)("li",{...e})},img:function(e){return(0,i.jsx)("img",{decoding:"async",loading:"lazy",...e,className:(t=e.className,(0,u.A)(t,ct))});var t},h1:e=>(0,i.jsx)(at,{as:"h1",...e}),h2:e=>(0,i.jsx)(at,{as:"h2",...e}),h3:e=>(0,i.jsx)(at,{as:"h3",...e}),h4:e=>(0,i.jsx)(at,{as:"h4",...e}),h5:e=>(0,i.jsx)(at,{as:"h5",...e}),h6:e=>(0,i.jsx)(at,{as:"h6",...e}),admonition:Ht,mermaid:Mt.A};function St({children:e}){return(0,i.jsx)(me.x,{components:It,children:e})}function Ut({children:e}){const t=function(){const{metadata:e,frontMatter:t,contentTitle:n}=l();return t.hide_title||void 0!==n?null:e.title}();return(0,i.jsxs)("div",{className:(0,u.A)(v.G.docs.docMarkdown,"markdown"),children:[t&&(0,i.jsx)("header",{children:(0,i.jsx)(ue.A,{as:"h1",children:t})}),(0,i.jsx)(St,{children:e})]})}var Rt=n(6473),zt=n(2805),Vt=n(4361);function Gt(e){return(0,i.jsx)("svg",{viewBox:"0 0 24 24",...e,children:(0,i.jsx)("path",{d:"M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z",fill:"currentColor"})})}const Ot={breadcrumbHomeIcon:"breadcrumbHomeIcon_H14Z"};function Pt(){const e=(0,Vt.Ay)("/");return(0,i.jsx)("li",{className:"breadcrumbs__item",children:(0,i.jsx)(p.A,{"aria-label":(0,h.T)({id:"theme.docs.breadcrumbs.home",message:"Home page",description:"The ARIA label for the home page in the breadcrumbs"}),className:"breadcrumbs__link",href:e,children:(0,i.jsx)(Gt,{className:Ot.breadcrumbHomeIcon})})})}const Dt={breadcrumbsContainer:"breadcrumbsContainer_ON7I"};function $t({children:e,href:t,isLast:n}){const s="breadcrumbs__link";return n?(0,i.jsx)("span",{className:s,itemProp:"name",children:e}):t?(0,i.jsx)(p.A,{className:s,href:t,itemProp:"item",children:(0,i.jsx)("span",{itemProp:"name",children:e})}):(0,i.jsx)("span",{className:s,children:e})}function Wt({children:e,active:t,index:n,addMicrodata:s}){return(0,i.jsxs)("li",{...s&&{itemScope:!0,itemProp:"itemListElement",itemType:"https://schema.org/ListItem"},className:(0,u.A)("breadcrumbs__item",{"breadcrumbs__item--active":t}),children:[e,(0,i.jsx)("meta",{itemProp:"position",content:String(n+1)})]})}function Ft(){const e=(0,Rt.OF)(),t=(0,zt.Dt)();return e?(0,i.jsx)("nav",{className:(0,u.A)(v.G.docs.docBreadcrumbs,Dt.breadcrumbsContainer),"aria-label":(0,h.T)({id:"theme.docs.breadcrumbs.navAriaLabel",message:"Breadcrumbs",description:"The ARIA label for the breadcrumbs"}),children:(0,i.jsxs)("ul",{className:"breadcrumbs",itemScope:!0,itemType:"https://schema.org/BreadcrumbList",children:[t&&(0,i.jsx)(Pt,{}),e.map((t,n)=>{const s=n===e.length-1,a="category"===t.type&&t.linkUnlisted?void 0:t.href;return(0,i.jsx)(Wt,{active:s,index:n,addMicrodata:!!a,children:(0,i.jsx)($t,{href:a,isLast:s,children:t.label})},n)})]})}):null}function qt(){return(0,i.jsx)(h.A,{id:"theme.contentVisibility.unlistedBanner.title",description:"The unlisted content banner title",children:"Unlisted page"})}function Zt(){return(0,i.jsx)(h.A,{id:"theme.contentVisibility.unlistedBanner.message",description:"The unlisted content banner message",children:"This page is unlisted. Search engines will not index it, and only users having a direct link can access it."})}function Qt(){return(0,i.jsx)(he.A,{children:(0,i.jsx)("meta",{name:"robots",content:"noindex, nofollow"})})}function Yt(){return(0,i.jsx)(h.A,{id:"theme.contentVisibility.draftBanner.title",description:"The draft content banner title",children:"Draft page"})}function Xt(){return(0,i.jsx)(h.A,{id:"theme.contentVisibility.draftBanner.message",description:"The draft content banner message",children:"This page is a draft. It will only be visible in dev and be excluded from the production build."})}function Jt({className:e}){return(0,i.jsx)(Ht,{type:"caution",title:(0,i.jsx)(Yt,{}),className:(0,u.A)(e,v.G.common.draftBanner),children:(0,i.jsx)(Xt,{})})}function Kt({className:e}){return(0,i.jsx)(Ht,{type:"caution",title:(0,i.jsx)(qt,{}),className:(0,u.A)(e,v.G.common.unlistedBanner),children:(0,i.jsx)(Zt,{})})}function en(e){return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(Qt,{}),(0,i.jsx)(Kt,{...e})]})}function tn({metadata:e}){const{unlisted:t,frontMatter:n}=e;return(0,i.jsxs)(i.Fragment,{children:[(t||n.unlisted)&&(0,i.jsx)(en,{}),n.draft&&(0,i.jsx)(Jt,{})]})}const nn={docItemContainer:"docItemContainer_jkBE",docItemCol:"docItemCol_F4eA"};function sn({children:e}){const t=function(){const{frontMatter:e,toc:t}=l(),n=(0,m.l)(),s=e.hide_table_of_contents,a=!s&&t.length>0;return{hidden:s,mobile:a?(0,i.jsx)(oe,{}):void 0,desktop:!a||"desktop"!==n&&"ssr"!==n?void 0:(0,i.jsx)(de,{})}}(),{metadata:n}=l();return(0,i.jsxs)("div",{className:"row",children:[(0,i.jsxs)("div",{className:(0,u.A)("col",!t.hidden&&nn.docItemCol),children:[(0,i.jsx)(tn,{metadata:n}),(0,i.jsx)(_,{}),(0,i.jsxs)("div",{className:nn.docItemContainer,children:[(0,i.jsxs)("article",{children:[(0,i.jsx)(Ft,{}),(0,i.jsx)(B,{}),t.mobile,(0,i.jsx)(Ut,{children:e}),(0,i.jsx)(P,{})]}),(0,i.jsx)(b,{})]})]}),t.desktop&&(0,i.jsx)("div",{className:"col col--3",children:t.desktop})]})}function an(e){const t=`docs-doc-id-${e.content.metadata.id}`,n=e.content;return(0,i.jsx)(r,{content:e.content,children:(0,i.jsxs)(a.e3,{className:t,children:[(0,i.jsx)(d,{}),(0,i.jsx)(sn,{children:(0,i.jsx)(n,{})})]})})}},4809:(e,t)=>{function n(e){let t,n=[];for(let s of e.split(",").map(e=>e.trim()))if(/^-?\d+$/.test(s))n.push(parseInt(s,10));else if(t=s.match(/^(-?\d+)(-|\.\.\.?|\u2025|\u2026|\u22EF)(-?\d+)$/)){let[e,s,a,o]=t;if(s&&o){s=parseInt(s),o=parseInt(o);const e=s<o?1:-1;"-"!==a&&".."!==a&&"\u2025"!==a||(o+=e);for(let t=s;t!==o;t+=e)n.push(t)}}return n}t.default=n,e.exports=n}}]);