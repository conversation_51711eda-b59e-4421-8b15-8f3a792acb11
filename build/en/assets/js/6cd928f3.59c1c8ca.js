"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[6624],{494:(e,n,r)=>{r.d(n,{R:()=>c,x:()=>o});var i=r(6372);const s={},t=i.createContext(s);function c(e){const n=i.useContext(t);return i.useMemo(function(){return"function"==typeof e?e(n):{...n,...e}},[n,e])}function o(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:c(e.components),i.createElement(t.Provider,{value:n},e.children)}},9985:(e,n,r)=>{r.r(n),r.d(n,{assets:()=>l,contentTitle:()=>c,default:()=>d,frontMatter:()=>t,metadata:()=>o,toc:()=>a});var i=r(216),s=r(494);const t={sidebar_position:2,hide_table_of_contents:!0,displayed_sidebar:"developerSidebar"},c="DataTP HR Backlog",o={id:"datatp-hr/developer/BACKLOG",title:"DataTP HR Backlog",description:"Tasks",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/datatp-hr/developer/BACKLOG.md",sourceDirName:"datatp-hr/developer",slug:"/datatp-hr/developer/BACKLOG",permalink:"/en/docs/datatp-hr/developer/BACKLOG",draft:!1,unlisted:!1,tags:[],version:"current",sidebarPosition:2,frontMatter:{sidebar_position:2,hide_table_of_contents:!0,displayed_sidebar:"developerSidebar"},sidebar:"developerSidebar",previous:{title:"Changelog",permalink:"/en/docs/datatp-hr/developer/CHANGELOG"}},l={},a=[{value:"Tasks",id:"tasks",level:2},{value:"Current Sprint",id:"current-sprint",level:2},{value:"Backlog",id:"backlog",level:2}];function h(e){const n={h1:"h1",h2:"h2",header:"header",li:"li",ol:"ol",p:"p",strong:"strong",ul:"ul",...(0,s.R)(),...e.components};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(n.header,{children:(0,i.jsx)(n.h1,{id:"datatp-hr-backlog",children:"DataTP HR Backlog"})}),"\n",(0,i.jsx)(n.h2,{id:"tasks",children:"Tasks"}),"\n",(0,i.jsx)(n.p,{children:(0,i.jsx)(n.strong,{children:"Sprint Rules:"})}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsx)(n.li,{children:"Sprint duration: 1 week (Monday-Sunday)"}),"\n",(0,i.jsx)(n.li,{children:"Daily status updates required"}),"\n",(0,i.jsx)(n.li,{children:"Incomplete tasks return to backlog for next sprint prioritization"}),"\n",(0,i.jsx)(n.li,{children:"C\xe1c tasks l\xe0m \u0111i l\xe0m l\u1ea1i, l\xe0m h\u1ecfng kh\xf4ng note v\xe0o backlog m\xe0 t\u1ef1 gi\xe1c fix, s\u1eeda."}),"\n"]}),"\n",(0,i.jsx)(n.h2,{id:"current-sprint",children:"Current Sprint"}),"\n",(0,i.jsxs)(n.ol,{children:["\n",(0,i.jsx)(n.li,{children:"[Nhat]"}),"\n"]}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["C\u1eadp nh\u1eadt d\u1eef li\u1ec7u Account Th\xeam account m\u1edbi/Xo\xe1 account nh\xe2n s\u1ef1 \u0111\xe3 ngh\u1ec9\nScript: + server:migrate",":run"," --script hr/CheckAccount.groovy --company bee"]}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsxs)(n.li,{children:["server:migrate",":run"," --script hr/CheckAccount.groovy --company beehph"]}),"\n",(0,i.jsxs)(n.li,{children:["server:migrate",":run"," --script hr/CheckAccount.groovy --company beehan"]}),"\n",(0,i.jsxs)(n.li,{children:["server:migrate",":run"," --script hr/CheckAccount.groovy --company beedad"]}),"\n",(0,i.jsxs)(n.li,{children:["server:migrate",":run"," --script hr/CheckAccount.groovy --company beehcm"]}),"\n"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["Init d\u1eef li\u1ec7u KPI Q2 cho c\xe1c v\u0103n ph\xf2ng\nScript: + server:migrate",":run"," --script hr/InitKpiTemplate.groovy --company bee"]}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["server:migrate",":run"," --script hr/InitKpiTemplate.groovy --company beehph"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["server:migrate",":run"," --script hr/InitKpiTemplate.groovy --company beehan"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["server:migrate",":run"," --script hr/InitKpiTemplate.groovy --company beedad"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["server:migrate",":run"," --script hr/InitKpiTemplate.groovy --company beehcm"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["server:migrate",":run"," --script hr/InitKpi.groovy --company bee"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["server:migrate",":run"," --script hr/InitKpi.groovy --company beehph"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["server:migrate",":run"," --script hr/InitKpi.groovy --company beehan"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["server:migrate",":run"," --script hr/InitKpi.groovy --company beedad"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["server:migrate",":run"," --script hr/InitKpi.groovy --company beehcm"]}),"\n"]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,i.jsx)(n.h2,{id:"backlog",children:"Backlog"}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsx)(n.li,{children:"B\u1ed5 sung auto save khi edit trong m\xe0n h\xecnh KPI/KPI Template"}),"\n",(0,i.jsx)(n.li,{children:"Th\xeam field employeeState hi\u1ec3n th\u1ecb tr\u1ea1ng th\xe1i: \u0110\u1ee7 \u0110i\u1ec1u ki\u1ec7n \u0111\xe1nh gi\xe1/Kh\xf4ng \u0111\u1ee7 \u0111i\u1ec1u ki\u1ec7n \u0111\xe1nh/Ngh\u1ec9 thai s\u1ea3n"}),"\n"]})]})}function d(e={}){const{wrapper:n}={...(0,s.R)(),...e.components};return n?(0,i.jsx)(n,{...e,children:(0,i.jsx)(h,{...e})}):h(e)}}}]);