"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[617],{173:(t,e,n)=>{n.r(e),n.d(e,{assets:()=>c,contentTitle:()=>r,default:()=>a,frontMatter:()=>i,metadata:()=>d,toc:()=>o});var l=n(216),s=n(494);const i={sidebar_position:1,hide_table_of_contents:!0,displayed_sidebar:"developerSidebar"},r="DataTP TMS Backlog",d={id:"datatp-tms/developer/BACKLOG",title:"DataTP TMS Backlog",description:"Tasks",source:"@site/docs/datatp-tms/developer/BACKLOG.md",sourceDirName:"datatp-tms/developer",slug:"/datatp-tms/developer/BACKLOG",permalink:"/en/docs/datatp-tms/developer/BACKLOG",draft:!1,unlisted:!1,tags:[],version:"current",sidebarPosition:1,frontMatter:{sidebar_position:1,hide_table_of_contents:!0,displayed_sidebar:"developerSidebar"},sidebar:"developerSidebar"},c={},o=[{value:"Tasks",id:"tasks",level:2},{value:"Current Sprint",id:"current-sprint",level:2},{value:"Backlog",id:"backlog",level:2}];function h(t){const e={em:"em",h1:"h1",h2:"h2",header:"header",li:"li",ol:"ol",p:"p",strong:"strong",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,s.R)(),...t.components};return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(e.header,{children:(0,l.jsx)(e.h1,{id:"datatp-tms-backlog",children:"DataTP TMS Backlog"})}),"\n",(0,l.jsx)(e.h2,{id:"tasks",children:"Tasks"}),"\n",(0,l.jsxs)(e.table,{children:[(0,l.jsx)(e.thead,{children:(0,l.jsxs)(e.tr,{children:[(0,l.jsx)(e.th,{style:{textAlign:"left"},children:"Category"}),(0,l.jsx)(e.th,{style:{textAlign:"left"},children:"Title"}),(0,l.jsx)(e.th,{style:{textAlign:"left"},children:"Status"}),(0,l.jsx)(e.th,{style:{textAlign:"left"},children:"Timeline"}),(0,l.jsx)(e.th,{style:{textAlign:"left"},children:"PIC"})]})}),(0,l.jsxs)(e.tbody,{children:[(0,l.jsxs)(e.tr,{children:[(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"Enhancement"}),(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"Task 1"}),(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"In Progress"}),(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"2025-07-16"}),(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"Quan"})]}),(0,l.jsxs)(e.tr,{children:[(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"Enhancement"}),(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"Task 2"}),(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"In Progress"}),(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"2025-07-4"}),(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"Chi\u1ebfn"})]})]})]}),"\n",(0,l.jsx)(e.p,{children:(0,l.jsx)(e.strong,{children:"Sprint Rules:"})}),"\n",(0,l.jsxs)(e.ul,{children:["\n",(0,l.jsx)(e.li,{children:"Sprint duration: 1 week (Monday-Sunday)"}),"\n",(0,l.jsx)(e.li,{children:"Daily status updates required"}),"\n",(0,l.jsx)(e.li,{children:"Incomplete tasks return to backlog for next sprint prioritization"}),"\n"]}),"\n",(0,l.jsx)(e.h2,{id:"current-sprint",children:"Current Sprint"}),"\n",(0,l.jsxs)(e.ol,{children:["\n",(0,l.jsxs)(e.li,{children:["\n",(0,l.jsx)(e.p,{children:"[Chien]\nL\xean m\xf4 h\xecnh config tuy\u1ebfn \u0111\u01b0\u1eddng d\u1ef1a tr\xean \u0111i\u1ec3m \u0111i \u0111i\u1ec3m \u0111\u1ebfn c\xf3 tr\xean bill\nT\xednh to\xe1n tuy\u1ebfn \u0111\u01b0\u1eddng cho tms-bill v\xe0 vi\u1ebft report tuy\u1ebfn \u0111\u01b0\u1eddng theo th\u1ea7u ph\u1ee5"}),"\n",(0,l.jsx)(e.p,{children:"Config target cho th\u1ea7u ph\u1ee5(S\u1ea3n l\u01b0\u1ee3ng cont theo ng\xe0y, tu\u1ea7n, th\xe1ng, qu\xfd)\nVi\u1ebft report s\u1ea3n l\u01b0\u1ee3ng theo th\u1ea7u ph\u1ee5, c\u1ea3nh b\xe1o c\xe1c th\u1ea7u kh\xf4ng \u0111\xe1p \u1ee9ng s\u1ea3n l\u01b0\u1ee3ng"}),"\n"]}),"\n"]}),"\n",(0,l.jsx)(e.h2,{id:"backlog",children:"Backlog"}),"\n",(0,l.jsxs)(e.ol,{children:["\n",(0,l.jsxs)(e.li,{children:["\n",(0,l.jsx)(e.p,{children:"S\u1eed d\u1ee5ng API c\u1ee7a goong h\u1ed7 tr\u1ee3 \u0111\u1ecdc c\xe1c \u0111\u1ecba ch\u1ec9 m\u1edbi v\xe0 l\u01b0u l\u1ea1i tr\xean h\u1ec7 th\u1ed1ng, s\u1eed d\u1ee5ng l\u1ea1i cho l\u1ea7n k\u1ebf ti\u1ebfp"}),"\n"]}),"\n",(0,l.jsxs)(e.li,{children:["\n",(0,l.jsx)(e.p,{children:"\u0110\u1ea5u n\u1ed1i API push c\u01b0\u1edbc v\xe0 th\xf4ng tin xe t\u1eeb TMS -> BFSOne"}),"\n"]}),"\n",(0,l.jsxs)(e.li,{children:["\n",(0,l.jsx)(e.p,{children:"Config d\u1eef li\u1ec7u ngu\u1ed3n cho office, c\u1eaft k\xed t\u1ef1 trong s\u1ed1 file"}),"\n"]}),"\n"]}),"\n",(0,l.jsx)(e.p,{children:(0,l.jsx)(e.em,{children:"Future tasks"})})]})}function a(t={}){const{wrapper:e}={...(0,s.R)(),...t.components};return e?(0,l.jsx)(e,{...t,children:(0,l.jsx)(h,{...t})}):h(t)}},494:(t,e,n)=>{n.d(e,{R:()=>r,x:()=>d});var l=n(6372);const s={},i=l.createContext(s);function r(t){const e=l.useContext(i);return l.useMemo(function(){return"function"==typeof t?t(e):{...e,...t}},[e,t])}function d(t){let e;return e=t.disableParentContext?"function"==typeof t.components?t.components(s):t.components||s:r(t.components),l.createElement(i.Provider,{value:e},t.children)}}}]);