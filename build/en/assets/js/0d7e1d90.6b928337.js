"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[4459],{494:(e,n,t)=>{t.d(n,{R:()=>o,x:()=>c});var r=t(6372);const s={},i=r.createContext(s);function o(e){const n=r.useContext(i);return r.useMemo(function(){return"function"==typeof e?e(n):{...n,...e}},[n,e])}function c(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:o(e.components),r.createElement(i.Provider,{value:n},e.children)}},1407:(e,n,t)=>{t.r(n),t.d(n,{assets:()=>p,contentTitle:()=>o,default:()=>u,frontMatter:()=>i,metadata:()=>c,toc:()=>d});var r=t(216),s=t(494);const i={sidebar_position:8,sidebar:!1,hide_table_of_contents:!0,displayed_sidebar:"userSidebar"},o=void 0,c={id:"datatp-hr/user/kpi/kpi_for_employee",title:"kpi_for_employee",description:"I. Truy c\u1eadp KPI",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/datatp-hr/user/kpi/kpi_for_employee.md",sourceDirName:"datatp-hr/user/kpi",slug:"/datatp-hr/user/kpi/kpi_for_employee",permalink:"/en/docs/datatp-hr/user/kpi/kpi_for_employee",draft:!1,unlisted:!1,tags:[],version:"current",sidebarPosition:8,frontMatter:{sidebar_position:8,sidebar:!1,hide_table_of_contents:!0,displayed_sidebar:"userSidebar"},sidebar:"userSidebar"},p={},d=[{value:"I. Truy c\u1eadp KPI",id:"i-truy-c\u1eadp-kpi",level:2}];function a(e){const n={h2:"h2",li:"li",ol:"ol",p:"p",strong:"strong",...(0,s.R)(),...e.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.h2,{id:"i-truy-c\u1eadp-kpi",children:"I. Truy c\u1eadp KPI"}),"\n",(0,r.jsx)(n.p,{children:"Nghi\u1ec7p v\u1ee5 li\xean quan:"}),"\n",(0,r.jsxs)(n.ol,{children:["\n",(0,r.jsxs)(n.li,{children:["Ch\u1ecdn ",(0,r.jsx)(n.strong,{children:"Menu"})]}),"\n",(0,r.jsxs)(n.li,{children:["Ch\u1ecdn App ",(0,r.jsx)(n.strong,{children:"KPI"})]}),"\n"]})]})}function u(e={}){const{wrapper:n}={...(0,s.R)(),...e.components};return n?(0,r.jsx)(n,{...e,children:(0,r.jsx)(a,{...e})}):a(e)}}}]);