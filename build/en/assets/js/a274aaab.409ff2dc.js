"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[6380],{494:(e,n,t)=>{t.d(n,{R:()=>s,x:()=>l});var d=t(6372);const i={},a=d.createContext(i);function s(e){const n=d.useContext(a);return d.useMemo(function(){return"function"==typeof e?e(n):{...n,...e}},[n,e])}function l(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(i):e.components||i:s(e.components),d.createElement(a.Provider,{value:n},e.children)}},4695:(e,n,t)=>{t.r(n),t.d(n,{assets:()=>c,contentTitle:()=>s,default:()=>o,frontMatter:()=>a,metadata:()=>l,toc:()=>r});var d=t(216),i=t(494);const a={sidebar_position:1,hide_table_of_contents:!0,displayed_sidebar:"developerSidebar"},s="Setup DataTP Project",l={id:"shared/developer/SETUP",title:"Setup DataTP Project",description:"\ud83d\udccb Y\xeau c\u1ea7u c\xf4ng c\u1ee5 v\xe0 c\u1ea5u h\xecnh",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/shared/developer/SETUP.md",sourceDirName:"shared/developer",slug:"/shared/developer/SETUP",permalink:"/en/docs/shared/developer/SETUP",draft:!1,unlisted:!1,tags:[],version:"current",sidebarPosition:1,frontMatter:{sidebar_position:1,hide_table_of_contents:!0,displayed_sidebar:"developerSidebar"},sidebar:"developerSidebar"},c={},r=[{value:"\ud83d\udccb Y\xeau c\u1ea7u c\xf4ng c\u1ee5 v\xe0 c\u1ea5u h\xecnh",id:"-y\xeau-c\u1ea7u-c\xf4ng-c\u1ee5-v\xe0-c\u1ea5u-h\xecnh",level:2},{value:"\ud83d\udee0\ufe0f C\xf4ng c\u1ee5 ch\xednh",id:"\ufe0f-c\xf4ng-c\u1ee5-ch\xednh",level:3},{value:"\ud83e\uddf0 C\xf4ng c\u1ee5 b\u1ed5 sung",id:"-c\xf4ng-c\u1ee5-b\u1ed5-sung",level:3},{value:"\u2699\ufe0f C\u1ea5u h\xecnh",id:"\ufe0f-c\u1ea5u-h\xecnh",level:2},{value:"1. Git",id:"1-git",level:3},{value:"2. SSH Key",id:"2-ssh-key",level:3},{value:"3. Th\xeam SSH key v\xe0o t\xe0i kho\u1ea3n GitLab",id:"3-th\xeam-ssh-key-v\xe0o-t\xe0i-kho\u1ea3n-gitlab",level:3},{value:"\ud83d\ude80 C\xe0i \u0111\u1eb7t d\u1ef1 \xe1n",id:"-c\xe0i-\u0111\u1eb7t-d\u1ef1-\xe1n",level:2},{value:"1. T\u1ea1o th\u01b0 m\u1ee5c root v\xe0 clone d\u1ef1 \xe1n",id:"1-t\u1ea1o-th\u01b0-m\u1ee5c-root-v\xe0-clone-d\u1ef1-\xe1n",level:3},{value:"\ud83d\udcc2 C\u1ea5u tr\xfac d\u1ef1 \xe1n",id:"-c\u1ea5u-tr\xfac-d\u1ef1-\xe1n",level:4},{value:"\ud83d\udd04 Quan h\u1ec7 gi\u1eefa c\xe1c d\u1ef1 \xe1n",id:"-quan-h\u1ec7-gi\u1eefa-c\xe1c-d\u1ef1-\xe1n",level:4},{value:"2. Backend",id:"2-backend",level:3},{value:"3. Frontend",id:"3-frontend",level:3},{value:"4. Database v\xe0 m\xf4i tr\u01b0\u1eddng",id:"4-database-v\xe0-m\xf4i-tr\u01b0\u1eddng",level:3},{value:"4.1. DataTP Core",id:"41-datatp-core",level:4},{value:"4.2. DataTP Document IE",id:"42-datatp-document-ie",level:4},{value:"5. Kh\u1edfi \u0111\u1ed9ng server ph\xe1t tri\u1ec3n.",id:"5-kh\u1edfi-\u0111\u1ed9ng-server-ph\xe1t-tri\u1ec3n",level:3},{value:"5.1. Backend.",id:"51-backend",level:4},{value:"5.2. Frontend.",id:"52-frontend",level:4},{value:"\ud83d\udd0d Ki\u1ec3m tra c\xe0i \u0111\u1eb7t",id:"-ki\u1ec3m-tra-c\xe0i-\u0111\u1eb7t",level:2}];function h(e){const n={a:"a",blockquote:"blockquote",code:"code",h1:"h1",h2:"h2",h3:"h3",h4:"h4",header:"header",input:"input",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,i.R)(),...e.components};return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(n.header,{children:(0,d.jsx)(n.h1,{id:"setup-datatp-project",children:"Setup DataTP Project"})}),"\n",(0,d.jsx)(n.h2,{id:"-y\xeau-c\u1ea7u-c\xf4ng-c\u1ee5-v\xe0-c\u1ea5u-h\xecnh",children:"\ud83d\udccb Y\xeau c\u1ea7u c\xf4ng c\u1ee5 v\xe0 c\u1ea5u h\xecnh"}),"\n",(0,d.jsx)(n.h3,{id:"\ufe0f-c\xf4ng-c\u1ee5-ch\xednh",children:"\ud83d\udee0\ufe0f C\xf4ng c\u1ee5 ch\xednh"}),"\n",(0,d.jsxs)(n.table,{children:[(0,d.jsx)(n.thead,{children:(0,d.jsxs)(n.tr,{children:[(0,d.jsx)(n.th,{children:"C\xf4ng c\u1ee5"}),(0,d.jsx)(n.th,{children:"Phi\xean b\u1ea3n"}),(0,d.jsx)(n.th,{children:"M\xf4 t\u1ea3"})]})}),(0,d.jsxs)(n.tbody,{children:[(0,d.jsxs)(n.tr,{children:[(0,d.jsx)(n.td,{children:"Node.js"}),(0,d.jsx)(n.td,{children:"> 23"}),(0,d.jsx)(n.td,{children:"JavaScript runtime"})]}),(0,d.jsxs)(n.tr,{children:[(0,d.jsx)(n.td,{children:"pnpm"}),(0,d.jsx)(n.td,{children:"M\u1edbi nh\u1ea5t"}),(0,d.jsx)(n.td,{children:"Package manager"})]}),(0,d.jsxs)(n.tr,{children:[(0,d.jsx)(n.td,{children:"Git"}),(0,d.jsx)(n.td,{children:"M\u1edbi nh\u1ea5t"}),(0,d.jsx)(n.td,{children:"Version control"})]}),(0,d.jsxs)(n.tr,{children:[(0,d.jsx)(n.td,{children:"Java"}),(0,d.jsx)(n.td,{children:"21"}),(0,d.jsx)(n.td,{children:"Java runtime"})]}),(0,d.jsxs)(n.tr,{children:[(0,d.jsx)(n.td,{children:"Gradle"}),(0,d.jsx)(n.td,{children:"> 8.7"}),(0,d.jsx)(n.td,{children:"Build tool"})]}),(0,d.jsxs)(n.tr,{children:[(0,d.jsx)(n.td,{children:"VS Code/Eclipse"}),(0,d.jsx)(n.td,{children:"M\u1edbi nh\u1ea5t"}),(0,d.jsx)(n.td,{children:"IDE"})]}),(0,d.jsxs)(n.tr,{children:[(0,d.jsx)(n.td,{children:"Postgres"}),(0,d.jsx)(n.td,{children:"> 16"}),(0,d.jsx)(n.td,{children:"Database server"})]}),(0,d.jsxs)(n.tr,{children:[(0,d.jsx)(n.td,{children:"DBeaver"}),(0,d.jsx)(n.td,{children:"M\u1edbi nh\u1ea5t"}),(0,d.jsx)(n.td,{children:"Database explorer"})]})]})]}),"\n",(0,d.jsx)(n.h3,{id:"-c\xf4ng-c\u1ee5-b\u1ed5-sung",children:"\ud83e\uddf0 C\xf4ng c\u1ee5 b\u1ed5 sung"}),"\n",(0,d.jsxs)(n.ul,{children:["\n",(0,d.jsx)(n.li,{children:"Python (phi\xean b\u1ea3n 3.10 tr\u1edf l\xean)"}),"\n",(0,d.jsx)(n.li,{children:"Docker"}),"\n",(0,d.jsx)(n.li,{children:"K3s"}),"\n"]}),"\n",(0,d.jsx)(n.h2,{id:"\ufe0f-c\u1ea5u-h\xecnh",children:"\u2699\ufe0f C\u1ea5u h\xecnh"}),"\n",(0,d.jsx)(n.h3,{id:"1-git",children:"1. Git"}),"\n",(0,d.jsx)(n.pre,{children:(0,d.jsx)(n.code,{className:"language-bash",children:'# C\u1ea5u h\xecnh th\xf4ng tin ng\u01b0\u1eddi d\xf9ng\ngit config --global user.email "<EMAIL>"\ngit config --global user.name "Your Name"\n\n# C\u1ea5u h\xecnh file mode\ngit config --global core.filemode false\n\n# C\u1ea5u h\xecnh line ending v\u1edbi unix style\ngit config --global core.autocrlf false\n'})}),"\n",(0,d.jsx)(n.h3,{id:"2-ssh-key",children:"2. SSH Key"}),"\n",(0,d.jsx)(n.pre,{children:(0,d.jsx)(n.code,{className:"language-bash",children:'# T\u1ea1o SSH key m\u1edbi\nssh-keygen -t ed25519 -b 4096 -C "<EMAIL>"\n\n# Kh\u1edfi \u0111\u1ed9ng SSH agent\neval "$(ssh-agent -s)"\n\n# Th\xeam SSH key v\xe0o SSH agent\nssh-add ~/.ssh/id_ed25519\n'})}),"\n",(0,d.jsx)(n.h3,{id:"3-th\xeam-ssh-key-v\xe0o-t\xe0i-kho\u1ea3n-gitlab",children:"3. Th\xeam SSH key v\xe0o t\xe0i kho\u1ea3n GitLab"}),"\n",(0,d.jsxs)(n.ol,{children:["\n",(0,d.jsxs)(n.li,{children:["Sao ch\xe9p n\u1ed9i dung SSH key","\n",(0,d.jsx)(n.pre,{children:(0,d.jsx)(n.code,{className:"language-bash",children:"cat ~/.ssh/id_ed25519.pub | pbcopy\n"})}),"\n"]}),"\n",(0,d.jsx)(n.li,{children:"\u0110\u0103ng nh\u1eadp v\xe0o GitLab"}),"\n",(0,d.jsxs)(n.li,{children:["V\xe0o ",(0,d.jsx)(n.strong,{children:"Settings > SSH Keys"})]}),"\n",(0,d.jsx)(n.li,{children:"D\xe1n SSH key v\xe0 \u0111\u1eb7t t\xean cho key"}),"\n"]}),"\n",(0,d.jsx)(n.h2,{id:"-c\xe0i-\u0111\u1eb7t-d\u1ef1-\xe1n",children:"\ud83d\ude80 C\xe0i \u0111\u1eb7t d\u1ef1 \xe1n"}),"\n",(0,d.jsx)(n.h3,{id:"1-t\u1ea1o-th\u01b0-m\u1ee5c-root-v\xe0-clone-d\u1ef1-\xe1n",children:"1. T\u1ea1o th\u01b0 m\u1ee5c root v\xe0 clone d\u1ef1 \xe1n"}),"\n",(0,d.jsx)(n.pre,{children:(0,d.jsx)(n.code,{className:"language-bash",children:"# T\u1ea1o th\u01b0 m\u1ee5c root\nmkdir datatp\ncd datatp\n\n# Clone c\xe1c d\u1ef1 \xe1n\ngit clone git@gitlab:datatp.net:tuan/datatp-core.git\ngit clone git@gitlab:datatp.net:tuan/datatp-erp.git\ngit clone git@gitlab:datatp.net:tuan/datatp-document-ie.git\ngit clone git@gitlab:datatp.net:tuan/datatp-logistics.git\ngit clone git@gitlab:datatp.net:tuan/datatp-crm.git\ngit clone git@gitlab:datatp.net:tuan/datatp-build.git\n"})}),"\n",(0,d.jsx)(n.h4,{id:"-c\u1ea5u-tr\xfac-d\u1ef1-\xe1n",children:"\ud83d\udcc2 C\u1ea5u tr\xfac d\u1ef1 \xe1n"}),"\n",(0,d.jsx)(n.p,{children:"Sau khi clone, c\u1ea5u tr\xfac th\u01b0 m\u1ee5c c\u1ee7a d\u1ef1 \xe1n s\u1ebd nh\u01b0 sau:"}),"\n",(0,d.jsx)(n.pre,{children:(0,d.jsx)(n.code,{children:"datatp/\n\u251c\u2500\u2500 datatp-core/               # Core services v\xe0 utilities\n\u2502   \u251c\u2500\u2500 app/                   # Core application\n\u2502   \u251c\u2500\u2500 service/               # Core services\n\u2502   \u2514\u2500\u2500 webui/                 # Core UI components\n\u2502\n\u251c\u2500\u2500 datatp-erp/                # Enterprise Resource Planning\n\u2502   \u251c\u2500\u2500 app/                   # ERP application\n\u2502   \u251c\u2500\u2500 module/                # ERP modules\n\u2502   \u2514\u2500\u2500 webui/                 # ERP UI components\n\u2502       \u251c\u2500\u2500 lib/               # Shared UI library\n\u2502       \u2514\u2500\u2500 erp/               # ERP specific UI\n\u2502\n\u251c\u2500\u2500 datatp-document-ie/        # Document Information Extractor\n\u2502   \u251c\u2500\u2500 app/                   # Document IE application\n\u2502   \u2514\u2500\u2500 webui/                 # Document IE UI\n\u2502\n\u251c\u2500\u2500 datatp-logistics/          # Logistics management\n\u2502   \u251c\u2500\u2500 app/                   # Logistics application\n\u2502   \u251c\u2500\u2500 module/                # Logistics modules\n\u2502   \u2514\u2500\u2500 webui/                 # Logistics UI\n\u2502\n\u251c\u2500\u2500 datatp-crm/                # Customer Relationship Management\n\u2502   \u251c\u2500\u2500 app/                   # CRM application\n\u2502   \u2514\u2500\u2500 webui/                 # CRM UI\n\u2502\n\u2514\u2500\u2500 datatp-build/              # Build tools and configurations\n    \u251c\u2500\u2500 scripts/               # Build scripts\n    \u2514\u2500\u2500 config/                # Build configurations\n"})}),"\n",(0,d.jsx)(n.h4,{id:"-quan-h\u1ec7-gi\u1eefa-c\xe1c-d\u1ef1-\xe1n",children:"\ud83d\udd04 Quan h\u1ec7 gi\u1eefa c\xe1c d\u1ef1 \xe1n"}),"\n",(0,d.jsx)(n.pre,{children:(0,d.jsx)(n.code,{children:"                                \u250c\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510\n                                \u2502   datatp-build  \u2502\n                                \u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u252c\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2518\n                                          \u25bc\n          \u250c\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510     \u250c\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510     \u250c\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510\n          \u2502document-ie      \u2502     \u2502  datatp-crm     \u2502     \u2502datatp-logistics \u2502\n          \u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u252c\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2518     \u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u252c\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2518     \u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u252c\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2518\n                    \u2502                       \u2502                       \u2502\n                    \u2502                       \u2502                       \u2502\n                    \u25bc                       \u25bc                       \u25bc\n                                  \u250c\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510\n                                  \u2502   datatp-erp    \u2502\n                                  \u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u252c\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2518\n                                            \u2502\n                                            \u2502\n                                            \u25bc\n                                  \u250c\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510\n                                  \u2502   datatp-core   \u2502\n                                  \u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2518\n"})}),"\n",(0,d.jsx)(n.h3,{id:"2-backend",children:"2. Backend"}),"\n",(0,d.jsx)(n.p,{children:"C\xe0i \u0111\u1eb7t dependencies v\xe0 build cho t\u1eebng projects theo th\u1ee9 t\u1ef1:"}),"\n",(0,d.jsxs)(n.blockquote,{children:["\n",(0,d.jsxs)(n.p,{children:["\u26a0\ufe0f ",(0,d.jsx)(n.strong,{children:"Quan tr\u1ecdng"}),": Tu\xe2n th\u1ee7 \u0111\xfang th\u1ee9 t\u1ef1 build"]}),"\n"]}),"\n",(0,d.jsxs)(n.ol,{children:["\n",(0,d.jsx)(n.li,{children:"datatp-core"}),"\n",(0,d.jsx)(n.li,{children:"datatp-erp"}),"\n",(0,d.jsx)(n.li,{children:"datatp-document-ie"}),"\n",(0,d.jsx)(n.li,{children:"datatp-logistics"}),"\n",(0,d.jsx)(n.li,{children:"datatp-crm"}),"\n",(0,d.jsx)(n.li,{children:"datatp-build"}),"\n"]}),"\n",(0,d.jsx)(n.pre,{children:(0,d.jsx)(n.code,{className:"language-bash",children:"cd datatp-core\ngradle clean build -x test\ngradle publishToMaven\n"})}),"\n",(0,d.jsx)(n.h3,{id:"3-frontend",children:"3. Frontend"}),"\n",(0,d.jsx)(n.p,{children:"Install v\xe0 build webui cho c\xe1c d\u1ef1 \xe1n:"}),"\n",(0,d.jsx)(n.pre,{children:(0,d.jsx)(n.code,{className:"language-bash",children:"cd datatp-erp/webui/lib\npnpm install\npnpm run build\n"})}),"\n",(0,d.jsx)(n.h3,{id:"4-database-v\xe0-m\xf4i-tr\u01b0\u1eddng",children:"4. Database v\xe0 m\xf4i tr\u01b0\u1eddng"}),"\n",(0,d.jsxs)(n.blockquote,{children:["\n",(0,d.jsxs)(n.p,{children:["\ud83d\udcc1 Th\u1ef1c hi\u1ec7n trong th\u01b0 m\u1ee5c: ",(0,d.jsx)(n.code,{children:"working/release-dev/server-env/"})]}),"\n"]}),"\n",(0,d.jsxs)(n.ul,{children:["\n",(0,d.jsxs)(n.li,{children:["C\u1ea5u h\xecnh th\xf4ng tin server posgres trong file ",(0,d.jsx)(n.code,{children:"db-env.sh"})]}),"\n"]}),"\n",(0,d.jsx)(n.pre,{children:(0,d.jsx)(n.code,{children:"  DB_HOST='localhost'\n  DB_PORT='5432'\n  PG_ADMIN_USER='postgres' # User admin database\n  PG_ADMIN_PASSWORD='pgadmin' # Password admin database\n"})}),"\n",(0,d.jsxs)(n.ul,{children:["\n",(0,d.jsx)(n.li,{children:"C\u1ea5u h\xecnh th\xf4ng tin files application config theo m\xf4i tr\u01b0\u1eddng."}),"\n"]}),"\n",(0,d.jsx)(n.pre,{children:(0,d.jsx)(n.code,{children:"  # Copy file application-dev-sample.yaml th\xe0nh application-dev.yaml\n  cp application-dev-sample.yaml application-dev.yaml\n\n  # Copy file application-prod-sample.yaml th\xe0nh application-prod.yaml\n  cp application-prod-sample.yaml application-prod.yaml\n"})}),"\n",(0,d.jsxs)(n.ul,{children:["\n",(0,d.jsx)(n.li,{children:"T\u1ea1o database, user, restore database m\u1eabu."}),"\n"]}),"\n",(0,d.jsx)(n.pre,{children:(0,d.jsx)(n.code,{children:"./db.sh [db_name] [create-admin][create-user][create-ro-user][vacuum][new][drop]\n\n# Example\n./db.sh datatpdb create-admin\n./db.sh datatpdb create-user\n./db.sh datatpdb create-ro-user\n./db.sh datatpdb new\n./db.sh datatpdb restore --file=datatpdb-latest.dump\n./db.sh datatpdb vacuum\n./db.sh datatpdb dump\n./db.sh datatpdb drop\n"})}),"\n",(0,d.jsx)(n.h4,{id:"41-datatp-core",children:"4.1. DataTP Core"}),"\n",(0,d.jsxs)(n.ol,{children:["\n",(0,d.jsxs)(n.li,{children:["Download database t\u1ea1i: ",(0,d.jsx)(n.a,{href:"https://beelogistics.cloud/download/datatpdb-latest.dump",children:"https://beelogistics.cloud/download/datatpdb-latest.dump"})]}),"\n",(0,d.jsxs)(n.li,{children:["T\u1ea1o user, kh\u1edfi t\u1ea1o DB, v\xe0 restore database m\u1eabu:","\n",(0,d.jsx)(n.pre,{children:(0,d.jsx)(n.code,{className:"language-bash",children:" ./db.sh datatpdb new\n ./db.sh datatpdb create-user\n ./db.sh datatpdb new\n ./db.sh datatpdb restore --file=datatpdb-latest.dump\n"})}),"\n"]}),"\n"]}),"\n",(0,d.jsx)(n.h4,{id:"42-datatp-document-ie",children:"4.2. DataTP Document IE"}),"\n",(0,d.jsxs)(n.ol,{children:["\n",(0,d.jsxs)(n.li,{children:["Download database t\u1ea1i: ",(0,d.jsx)(n.a,{href:"https://beelogistics.cloud/download/document_ie_db-latest.dump",children:"https://beelogistics.cloud/download/document_ie_db-latest.dump"})]}),"\n",(0,d.jsxs)(n.li,{children:["T\u1ea1o user, kh\u1edfi t\u1ea1o DB, v\xe0 restore database m\u1eabu:","\n",(0,d.jsx)(n.pre,{children:(0,d.jsx)(n.code,{className:"language-bash",children:" ./db.sh document_ie_db new\n ./db.sh document_ie_db create-user\n ./db.sh document_ie_db new\n ./db.sh document_ie_db restore --file=document_ie_db-latest.dump\n"})}),"\n"]}),"\n"]}),"\n",(0,d.jsx)(n.h3,{id:"5-kh\u1edfi-\u0111\u1ed9ng-server-ph\xe1t-tri\u1ec3n",children:"5. Kh\u1edfi \u0111\u1ed9ng server ph\xe1t tri\u1ec3n."}),"\n",(0,d.jsx)(n.h4,{id:"51-backend",children:"5.1. Backend."}),"\n",(0,d.jsx)(n.pre,{children:(0,d.jsx)(n.code,{className:"language-bash",children:"cd datatp-build\n./datatp.sh build:all -build-ui\n./datatp.sh instances run:update\n"})}),"\n",(0,d.jsx)(n.h4,{id:"52-frontend",children:"5.2. Frontend."}),"\n",(0,d.jsx)(n.pre,{children:(0,d.jsx)(n.code,{className:"language-bash",children:'cd datatp-build/web/phoenix\nADD_APPS="logistics" pnpm run dev-server\n'})}),"\n",(0,d.jsxs)(n.p,{children:["M\u1edf ",(0,d.jsx)(n.a,{href:"http://localhost:3000",children:"http://localhost:3000"})," \u0111\u1ec3 xem trang web."]}),"\n",(0,d.jsx)(n.h2,{id:"-ki\u1ec3m-tra-c\xe0i-\u0111\u1eb7t",children:"\ud83d\udd0d Ki\u1ec3m tra c\xe0i \u0111\u1eb7t"}),"\n",(0,d.jsxs)(n.ul,{className:"contains-task-list",children:["\n",(0,d.jsxs)(n.li,{className:"task-list-item",children:[(0,d.jsx)(n.input,{type:"checkbox",disabled:!0})," ","T\u1ea5t c\u1ea3 c\xe1c d\u1ef1 \xe1n \u0111\xe3 \u0111\u01b0\u1ee3c clone th\xe0nh c\xf4ng"]}),"\n",(0,d.jsxs)(n.li,{className:"task-list-item",children:[(0,d.jsx)(n.input,{type:"checkbox",disabled:!0})," ","Backend \u0111\xe3 \u0111\u01b0\u1ee3c build theo \u0111\xfang th\u1ee9 t\u1ef1"]}),"\n",(0,d.jsxs)(n.li,{className:"task-list-item",children:[(0,d.jsx)(n.input,{type:"checkbox",disabled:!0})," ","Frontend \u0111\xe3 \u0111\u01b0\u1ee3c c\xe0i \u0111\u1eb7t v\xe0 build"]}),"\n",(0,d.jsxs)(n.li,{className:"task-list-item",children:[(0,d.jsx)(n.input,{type:"checkbox",disabled:!0})," ","Database \u0111\xe3 \u0111\u01b0\u1ee3c kh\u1edfi t\u1ea1o v\xe0 restore"]}),"\n",(0,d.jsxs)(n.li,{className:"task-list-item",children:[(0,d.jsx)(n.input,{type:"checkbox",disabled:!0})," ","Server ph\xe1t tri\u1ec3n \u0111\xe3 kh\u1edfi \u0111\u1ed9ng th\xe0nh c\xf4ng"]}),"\n"]})]})}function o(e={}){const{wrapper:n}={...(0,i.R)(),...e.components};return n?(0,d.jsx)(n,{...e,children:(0,d.jsx)(h,{...e})}):h(e)}}}]);