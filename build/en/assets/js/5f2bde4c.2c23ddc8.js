"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[8548],{494:(e,s,n)=>{n.d(s,{R:()=>r,x:()=>l});var i=n(6372);const t={},o=i.createContext(t);function r(e){const s=i.useContext(o);return i.useMemo(function(){return"function"==typeof e?e(s):{...s,...e}},[s,e])}function l(e){let s;return s=e.disableParentContext?"function"==typeof e.components?e.components(t):e.components||t:r(e.components),i.createElement(o.Provider,{value:s},e.children)}},1726:(e,s,n)=>{n.r(s),n.d(s,{assets:()=>c,contentTitle:()=>r,default:()=>h,frontMatter:()=>o,metadata:()=>l,toc:()=>a});var i=n(216),t=n(494);const o={displayed_sidebar:"userSidebar",sidebar_position:1,hide_table_of_contents:!0},r="System",l={id:"shared/user/system",title:"System",description:"Welcome to the Bee Logistics system user guide. In this guide, we will help you get started with the system quickly and efficiently.",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/shared/user/system.md",sourceDirName:"shared/user",slug:"/shared/user/system",permalink:"/en/docs/shared/user/system",draft:!1,unlisted:!1,tags:[],version:"current",sidebarPosition:1,frontMatter:{displayed_sidebar:"userSidebar",sidebar_position:1,hide_table_of_contents:!0},sidebar:"userSidebar"},c={},a=[{value:"Accessing the System",id:"accessing-the-system",level:2},{value:"Logging into the System",id:"logging-into-the-system",level:2},{value:"Forgot Password:",id:"forgot-password",level:3},{value:"Getting Started",id:"getting-started",level:2},{value:"Customize Personal Information and Account Settings",id:"customize-personal-information-and-account-settings",level:3},{value:"List of Functional Modules",id:"list-of-functional-modules",level:3}];function d(e){const s={a:"a",code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",img:"img",li:"li",ol:"ol",p:"p",strong:"strong",ul:"ul",...(0,t.R)(),...e.components};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(s.header,{children:(0,i.jsx)(s.h1,{id:"system",children:"System"})}),"\n",(0,i.jsx)(s.p,{children:"Welcome to the Bee Logistics system user guide. In this guide, we will help you get started with the system quickly and efficiently."}),"\n",(0,i.jsx)(s.h2,{id:"accessing-the-system",children:"Accessing the System"}),"\n",(0,i.jsx)(s.p,{children:"To start using Bee Logistics, please follow these steps:"}),"\n",(0,i.jsxs)(s.ol,{children:["\n",(0,i.jsx)(s.li,{children:"Open your web browser (Google Chrome, Firefox, Safari, etc.)."}),"\n",(0,i.jsxs)(s.li,{children:["Enter the address ",(0,i.jsx)(s.a,{href:"https://beelogistics.cloud",children:"https://beelogistics.cloud"})," in the address bar."]}),"\n",(0,i.jsx)(s.li,{children:"Press Enter to access the official Bee Logistics website."}),"\n"]}),"\n",(0,i.jsx)(s.h2,{id:"logging-into-the-system",children:"Logging into the System"}),"\n",(0,i.jsx)(s.p,{children:"After accessing the website, you need to log in to use the system's features:"}),"\n",(0,i.jsxs)(s.ol,{children:["\n",(0,i.jsxs)(s.li,{children:["On the homepage, enter your login information:","\n",(0,i.jsxs)(s.ul,{children:["\n",(0,i.jsx)(s.li,{children:"Username: [Your provided username]"}),"\n",(0,i.jsx)(s.li,{children:"Password: [Your password]"}),"\n"]}),"\n"]}),"\n",(0,i.jsx)(s.li,{children:'Click the "Login" button to access the system.'}),"\n"]}),"\n",(0,i.jsx)(s.p,{children:(0,i.jsx)(s.img,{alt:"login.gif",src:n(7175).A+"",width:"1920",height:"1080"})}),"\n",(0,i.jsx)(s.h3,{id:"forgot-password",children:"Forgot Password:"}),"\n",(0,i.jsx)(s.p,{children:"If you forget your login password, you can follow these steps:"}),"\n",(0,i.jsxs)(s.ol,{children:["\n",(0,i.jsxs)(s.li,{children:["Click the ",(0,i.jsx)(s.strong,{children:(0,i.jsx)(s.code,{children:"Forgot Password"})})," feature on the login page"]}),"\n",(0,i.jsx)(s.li,{children:"Enter the email registered with your account"}),"\n",(0,i.jsx)(s.li,{children:"Follow the instructions sent to your email to reset your password"}),"\n"]}),"\n",(0,i.jsx)(s.p,{children:"Important notes:"}),"\n",(0,i.jsxs)(s.ul,{children:["\n",(0,i.jsx)(s.li,{children:"Securing your login information is your responsibility"}),"\n",(0,i.jsx)(s.li,{children:"Do not share your account and password with anyone"}),"\n",(0,i.jsx)(s.li,{children:"Use a strong password and change it regularly"}),"\n"]}),"\n",(0,i.jsx)(s.p,{children:"Illustration of the forgot password interface:"}),"\n",(0,i.jsx)(s.p,{children:(0,i.jsx)(s.img,{alt:"forgot_password.png",src:n(3840).A+"",width:"1921",height:"1085"})}),"\n",(0,i.jsx)(s.p,{children:"Detailed video guide on how to reset your password:"}),"\n",(0,i.jsx)(s.p,{children:(0,i.jsx)(s.img,{alt:"forgot_password.gif",src:n(5319).A+"",width:"1920",height:"1080"})}),"\n",(0,i.jsxs)(s.p,{children:["If you cannot reset your password following the above steps, please contact IT Support at ",(0,i.jsx)(s.code,{children:"<EMAIL>"})," for direct assistance."]}),"\n",(0,i.jsx)(s.h2,{id:"getting-started",children:"Getting Started"}),"\n",(0,i.jsx)(s.p,{children:"After successfully logging in, you will be redirected to the main page of the Bee Logistics system. From here, you can:"}),"\n",(0,i.jsx)(s.h3,{id:"customize-personal-information-and-account-settings",children:"Customize Personal Information and Account Settings"}),"\n",(0,i.jsx)(s.p,{children:"After logging in, you can customize your personal information and account settings to optimize your system experience:"}),"\n",(0,i.jsxs)(s.ol,{children:["\n",(0,i.jsxs)(s.li,{children:["\n",(0,i.jsxs)(s.p,{children:["Update personal information: (",(0,i.jsx)("span",{style:{backgroundColor:"#ffeb3b"},children:"Pending"}),")"]}),"\n",(0,i.jsxs)(s.ul,{children:["\n",(0,i.jsx)(s.li,{children:"Display name"}),"\n",(0,i.jsx)(s.li,{children:"Primary email address"}),"\n",(0,i.jsx)(s.li,{children:"Contact information (phone number, address)"}),"\n",(0,i.jsx)(s.li,{children:"Profile picture"}),"\n"]}),"\n"]}),"\n",(0,i.jsxs)(s.li,{children:["\n",(0,i.jsx)(s.p,{children:"Set up connections with applications:"}),"\n",(0,i.jsxs)(s.ul,{children:["\n",(0,i.jsx)(s.li,{children:"Email/Outlook: To receive notifications and send emails directly from the system"}),"\n",(0,i.jsx)(s.li,{children:"Zalo: Integrate messaging and notifications via Zalo"}),"\n",(0,i.jsx)(s.li,{children:"Teams: Sync calendar and notifications with Microsoft Teams"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,i.jsxs)(s.p,{children:["See the detailed video guide below:\n",(0,i.jsx)(s.img,{alt:"update_email.gif",src:n(2762).A+"",width:"1920",height:"1080"})]}),"\n",(0,i.jsxs)(s.ol,{start:"3",children:["\n",(0,i.jsxs)(s.li,{children:["Notification settings: (",(0,i.jsx)("span",{style:{backgroundColor:"#ffeb3b"},children:"Pending"}),")","\n",(0,i.jsxs)(s.ul,{children:["\n",(0,i.jsx)(s.li,{children:"Customize the types of notifications you want to receive"}),"\n",(0,i.jsx)(s.li,{children:"Choose notification methods (email, Zalo, or both)"}),"\n",(0,i.jsx)(s.li,{children:"Adjust notification frequency"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,i.jsx)(s.h3,{id:"list-of-functional-modules",children:"List of Functional Modules"}),"\n",(0,i.jsx)(s.p,{children:(0,i.jsx)(s.img,{alt:"module",src:n(6003).A+"",width:"2559",height:"1319"})}),"\n",(0,i.jsx)(s.p,{children:"Start using the tools and features to manage your logistics work."}),"\n",(0,i.jsx)(s.p,{children:"Below is a list of apps with specialized business functions, including:"}),"\n",(0,i.jsxs)(s.ol,{children:["\n",(0,i.jsxs)(s.li,{children:["\n",(0,i.jsx)(s.p,{children:"My Project, Spreadsheet: Tools for task tracking and file management, replacing traditional Excel files"}),"\n"]}),"\n",(0,i.jsxs)(s.li,{children:["\n",(0,i.jsx)(s.p,{children:"OKR: Track objectives and key results"}),"\n"]}),"\n",(0,i.jsxs)(s.li,{children:["\n",(0,i.jsx)(s.p,{children:"TMS: Software for managing transport bills and fleets"}),"\n"]}),"\n",(0,i.jsxs)(s.li,{children:["\n",(0,i.jsx)(s.p,{children:"Logistics Prices - Pricing Tools: Manage prices from Lines/Airlines/Subcontractors"}),"\n"]}),"\n",(0,i.jsxs)(s.li,{children:["\n",(0,i.jsx)(s.p,{children:"Logistics Sales: Manage inquiries, quotations, and IB."}),"\n"]}),"\n"]}),"\n",(0,i.jsx)(s.p,{children:"For further assistance, please refer to the detailed guides in the Documentation section or contact"})]})}function h(e={}){const{wrapper:s}={...(0,t.R)(),...e.components};return s?(0,i.jsx)(s,{...e,children:(0,i.jsx)(d,{...e})}):d(e)}},2762:(e,s,n)=>{n.d(s,{A:()=>i});const i=n.p+"assets/images/update_email-896a31fa0c0ff2d67162b16ca657c40e.gif"},3840:(e,s,n)=>{n.d(s,{A:()=>i});const i=n.p+"assets/images/forgot_password-030e1e811d820716ed87f98754bab1c8.png"},5319:(e,s,n)=>{n.d(s,{A:()=>i});const i=n.p+"assets/images/forgot_pass-45b9752259e7c2fb852b34c47292a5c1.gif"},6003:(e,s,n)=>{n.d(s,{A:()=>i});const i=n.p+"assets/images/app_list-0e5e8c32e8a077fbf50d57e45878f466.png"},7175:(e,s,n)=>{n.d(s,{A:()=>i});const i=n.p+"assets/images/login-0e14d78159952d438a54a8b21b17004d.gif"}}]);