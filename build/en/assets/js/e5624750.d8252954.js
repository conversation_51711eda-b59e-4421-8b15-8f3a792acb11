"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[681],{494:(e,n,s)=>{s.d(n,{R:()=>o,x:()=>l});var i=s(6372);const r={},t=i.createContext(r);function o(e){const n=i.useContext(t);return i.useMemo(function(){return"function"==typeof e?e(n):{...n,...e}},[n,e])}function l(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(r):e.components||r:o(e.components),i.createElement(t.Provider,{value:n},e.children)}},3621:(e,n,s)=>{s.r(n),s.d(n,{assets:()=>c,contentTitle:()=>o,default:()=>h,frontMatter:()=>t,metadata:()=>l,toc:()=>a});var i=s(216),r=s(494);const t={sidebar_position:1,sidebar:!1,hide_table_of_contents:!0,displayed_sidebar:"userSidebar"},o="Overview",l={id:"datatp-crm/user/intro",title:"Overview",description:"System Objectives",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/datatp-crm/user/intro.md",sourceDirName:"datatp-crm/user",slug:"/datatp-crm/user/intro",permalink:"/en/docs/datatp-crm/user/intro",draft:!1,unlisted:!1,tags:[],version:"current",sidebarPosition:1,frontMatter:{sidebar_position:1,sidebar:!1,hide_table_of_contents:!0,displayed_sidebar:"userSidebar"},sidebar:"userSidebar"},c={},a=[{value:"<strong>System Objectives</strong>",id:"system-objectives",level:4}];function d(e){const n={blockquote:"blockquote",h1:"h1",h4:"h4",header:"header",li:"li",ol:"ol",p:"p",strong:"strong",ul:"ul",...(0,r.R)(),...e.components};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(n.header,{children:(0,i.jsx)(n.h1,{id:"overview",children:"Overview"})}),"\n",(0,i.jsx)(n.h4,{id:"system-objectives",children:(0,i.jsx)(n.strong,{children:"System Objectives"})}),"\n",(0,i.jsxs)(n.ol,{children:["\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsx)(n.p,{children:(0,i.jsx)(n.strong,{children:"Centralized Database Management"})}),"\n",(0,i.jsxs)(n.p,{children:["The CRM system manages a centralized pricing database for ",(0,i.jsx)(n.strong,{children:"Sea"}),", ",(0,i.jsx)(n.strong,{children:"Air"}),", and ",(0,i.jsx)(n.strong,{children:"Trucking (Domestic/CBT)"})," services."]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsx)(n.p,{children:(0,i.jsx)(n.strong,{children:"Business Process Management"})}),"\n",(0,i.jsx)(n.p,{children:"The system provides tools for measuring and managing the sales process, including:"}),"\n",(0,i.jsxs)(n.blockquote,{children:["\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsx)(n.li,{children:"Customer information management."}),"\n",(0,i.jsx)(n.li,{children:"Inquiry and quotation management."}),"\n",(0,i.jsx)(n.li,{children:"KPI tracking for individual employees and branches."}),"\n"]}),"\n"]}),"\n",(0,i.jsx)(n.p,{children:"CRM helps reduce quotation time by storing pricing data for frequently used routes and services. These tools support sales teams in:"}),"\n",(0,i.jsxs)(n.blockquote,{children:["\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsx)(n.li,{children:"Quick price searching."}),"\n",(0,i.jsx)(n.li,{children:"Automated quotation creation and sending."}),"\n",(0,i.jsx)(n.li,{children:"Automatic conversion of sales pricing data to the BFSOne system."}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsx)(n.p,{children:(0,i.jsx)(n.strong,{children:"Online Sales & Marketing Channel"})}),"\n",(0,i.jsxs)(n.p,{children:["The system integrates ",(0,i.jsx)(n.strong,{children:"online sales and marketing channels"})," with features including:"]}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsx)(n.li,{children:"From e-quote to booking, track & trace."}),"\n",(0,i.jsx)(n.li,{children:"Lead management for potential customers."}),"\n",(0,i.jsx)(n.li,{children:"Collection of customer feedback and reviews."}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,i.jsx)("hr",{}),"\n",(0,i.jsxs)(n.p,{children:["The system consists of three modules: ",(0,i.jsx)(n.strong,{children:"Pricing Tools"}),", ",(0,i.jsx)(n.strong,{children:"CRM"}),", and ",(0,i.jsx)(n.strong,{children:"Dashboard"}),"."]}),"\n",(0,i.jsxs)(n.ol,{children:["\n",(0,i.jsx)(n.li,{children:(0,i.jsx)(n.strong,{children:"Pricing Tools"})}),"\n"]}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsxs)(n.li,{children:["Input/Upload pricing data for ",(0,i.jsx)(n.strong,{children:"Sea"}),", ",(0,i.jsx)(n.strong,{children:"Air"}),", ",(0,i.jsx)(n.strong,{children:"Trucking (Domestic/CBT)"})," services."]}),"\n",(0,i.jsx)(n.li,{children:"Offices and partners can input prices directly into the system."}),"\n"]}),"\n",(0,i.jsxs)(n.ol,{start:"2",children:["\n",(0,i.jsx)(n.li,{children:(0,i.jsx)(n.strong,{children:"CRM"})}),"\n"]}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsxs)(n.li,{children:["Sales staff enter ",(0,i.jsx)(n.strong,{children:"inquiry"})," into the system. If pricing is available, the tool allows sales to create quotation and send them to customers via email."]}),"\n",(0,i.jsx)(n.li,{children:"If pricing is not available or needs confirmation, the system automatically sends emails to the relevant pricing department."}),"\n",(0,i.jsxs)(n.li,{children:["After receiving pricing, sales can create ",(0,i.jsx)(n.strong,{children:"quotation"})," and send them to customers, which can be applied to multiple booking."]}),"\n",(0,i.jsx)(n.li,{children:"When customers confirm booking, sales create Internal Booking and the system uses API integration with OF1 to create booking and automatically update pricing in OF1."}),"\n"]}),"\n",(0,i.jsxs)(n.ol,{start:"3",children:["\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsx)(n.p,{children:(0,i.jsx)(n.strong,{children:"Dashboard"})}),"\n",(0,i.jsx)(n.p,{children:"The system provides detailed reports including:"}),"\n",(0,i.jsxs)(n.blockquote,{children:["\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsx)(n.li,{children:"Number of inquiry (filterable by requirements)."}),"\n",(0,i.jsx)(n.li,{children:"Number of quotation and successful booking."}),"\n",(0,i.jsx)(n.li,{children:"Volume statistics and KPI for each position."}),"\n"]}),"\n"]}),"\n",(0,i.jsxs)(n.p,{children:["The ",(0,i.jsx)(n.strong,{children:"Dashboard"})," helps centralize data management and supports accurate allocation and optimization of company resources."]}),"\n"]}),"\n"]})]})}function h(e={}){const{wrapper:n}={...(0,r.R)(),...e.components};return n?(0,i.jsx)(n,{...e,children:(0,i.jsx)(d,{...e})}):d(e)}}}]);