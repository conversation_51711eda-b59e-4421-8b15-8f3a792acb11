"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[2212],{98:(e,s,t)=>{t.d(s,{A:()=>r});const r=t.p+"assets/images/create_customer_form-bd3bd89384ecf994259e71fe4f06f55f.png"},494:(e,s,t)=>{t.d(s,{R:()=>i,x:()=>d});var r=t(6372);const n={},c=r.createContext(n);function i(e){const s=r.useContext(c);return r.useMemo(function(){return"function"==typeof e?e(s):{...s,...e}},[s,e])}function d(e){let s;return s=e.disableParentContext?"function"==typeof e.components?e.components(n):e.components||n:i(e.components),r.createElement(c.Provider,{value:s},e.children)}},518:(e,s,t)=>{t.d(s,{A:()=>r});const r=t.p+"assets/images/create_customer_refund_form_2-5a46810d372f66c47f01330d13083b6e.png"},799:(e,s,t)=>{t.d(s,{A:()=>r});const r=t.p+"assets/images/create_customer_form_done-8352afad41ffd1905aadbcb521d055f7.png"},1289:(e,s,t)=>{t.d(s,{A:()=>r});const r=t.p+"assets/images/create_customer_refund-03fd0fc8f052461ba7a5a2182536dd18.gif"},1725:(e,s,t)=>{t.d(s,{A:()=>r});const r=t.p+"assets/images/create_customer_refund_form_1-53b15c6ac8f23ef817c08ea060cd0a8f.png"},2231:(e,s,t)=>{t.d(s,{A:()=>r});const r=t.p+"assets/images/fetch_customer_form-8bd0f7a79ee28e49a4e49e3a08919785.gif"},2734:(e,s,t)=>{t.d(s,{A:()=>r});const r=t.p+"assets/images/enter_agent_list-6f214b36b80eec9221da8b16b9b22539.gif"},2746:(e,s,t)=>{t.d(s,{A:()=>r});const r=t.p+"assets/images/enter_coloader_list-06d02a23fced02cc97718598fe464c52.gif"},3076:(e,s,t)=>{t.d(s,{A:()=>r});const r=t.p+"assets/images/check_tax_code-b705029ce754a077ec6d76db558b9419.png"},3100:(e,s,t)=>{t.d(s,{A:()=>r});const r=t.p+"assets/images/search_customers-76b82b56f7fda71a037403a6011d391a.png"},4432:(e,s,t)=>{t.d(s,{A:()=>r});const r=t.p+"assets/images/check_exist_by_taxcode-afc960fa52c86a8458e4d9c73d50593d.gif"},6252:(e,s,t)=>{t.d(s,{A:()=>r});const r=t.p+"assets/images/create_customer-8763b52ad6150336995d96bc332541b6.gif"},7785:(e,s,t)=>{t.r(s),t.d(s,{assets:()=>a,contentTitle:()=>i,default:()=>h,frontMatter:()=>c,metadata:()=>d,toc:()=>o});var r=t(216),n=t(494);const c={sidebar_position:11,hide_table_of_contents:!0,displayed_sidebar:"userSidebar"},i="Partner Management",d={id:"datatp-crm/user/crm/references/customer_management",title:"Partner Management",description:"Guide for managing customer information (Customer/Agent) in the CRM system.",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/datatp-crm/user/crm/references/customer_management.md",sourceDirName:"datatp-crm/user/crm/references",slug:"/datatp-crm/user/crm/references/customer_management",permalink:"/en/docs/datatp-crm/user/crm/references/customer_management",draft:!1,unlisted:!1,tags:[],version:"current",sidebarPosition:11,frontMatter:{sidebar_position:11,hide_table_of_contents:!0,displayed_sidebar:"userSidebar"},sidebar:"userSidebar"},a={},o=[{value:"Access Customer List Screen",id:"access-customer-list-screen",level:3},{value:"Request to Create New Customer",id:"request-to-create-new-customer",level:4},{value:"For customers with existing codes in the system",id:"for-customers-with-existing-codes-in-the-system",level:4},{value:"Request to Create New Customer (Refund).",id:"request-to-create-new-customer-refund",level:4},{value:"Check if Partner already exists in the system",id:"check-if-partner-already-exists-in-the-system",level:4},{value:"Search Customer",id:"search-customer",level:4},{value:"Access Agent List Screen",id:"access-agent-list-screen",level:3},{value:"Access Coloader List Screen",id:"access-coloader-list-screen",level:3}];function l(e){const s={code:"code",em:"em",h1:"h1",h3:"h3",h4:"h4",header:"header",img:"img",li:"li",p:"p",strong:"strong",ul:"ul",...(0,n.R)(),...e.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(s.header,{children:(0,r.jsx)(s.h1,{id:"partner-management",children:"Partner Management"})}),"\n",(0,r.jsx)(s.p,{children:"Guide for managing customer information (Customer/Agent) in the CRM system."}),"\n",(0,r.jsx)(s.h3,{id:"access-customer-list-screen",children:"Access Customer List Screen"}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:["From the screen list, click ",(0,r.jsx)(s.code,{children:"Partners"})," -> select ",(0,r.jsx)(s.code,{children:"Customers"})," tab."]}),"\n"]}),"\n",(0,r.jsx)(s.p,{children:(0,r.jsx)(s.img,{alt:"enter_customers.gif",src:t(9367).A+"",width:"2101",height:"971"})}),"\n",(0,r.jsx)(s.h4,{id:"request-to-create-new-customer",children:"Request to Create New Customer"}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:["Click ",(0,r.jsx)(s.code,{children:"+ New Customer"})," on the toolbar."]}),"\n"]}),"\n",(0,r.jsx)(s.p,{children:(0,r.jsx)(s.img,{alt:"create_customer.gif",src:t(6252).A+"",width:"1718",height:"837"})}),"\n",(0,r.jsx)(s.p,{children:(0,r.jsx)(s.img,{alt:"create_customer_form.png",src:t(98).A+"",width:"1440",height:"727"})}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:["Click ",(0,r.jsx)(s.strong,{children:(0,r.jsx)(s.code,{children:"Request Partner"})})," to submit customer creation request and wait for approval."]}),"\n"]}),"\n",(0,r.jsx)(s.p,{children:(0,r.jsx)(s.img,{alt:"create_customer_form_done.png",src:t(799).A+"",width:"2520",height:"326"})}),"\n",(0,r.jsx)("div",{style:{background:"#fff3cd",color:"#856404",padding:"12px 16px",borderRadius:"6px",border:"1px solid #ffeeba",margin:"16px 0"},children:(0,r.jsxs)(s.p,{children:[(0,r.jsx)("strong",{children:"Note:"})," After customer approval, the code format will be automatically updated [CSxxxxx].\n",(0,r.jsx)("br",{})," An email will be sent to sales notifying whether the customer is approved or rejected."]})}),"\n",(0,r.jsx)(s.h4,{id:"for-customers-with-existing-codes-in-the-system",children:"For customers with existing codes in the system"}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:["Click ",(0,r.jsx)(s.code,{children:"New Customer"})]}),"\n",(0,r.jsxs)(s.li,{children:["Enter ",(0,r.jsx)("mark",{children:"Partner Code"})," or ",(0,r.jsx)("mark",{children:"Tax Code"})," in the search box to find Partner and wait for the system to load information and update notification."]}),"\n",(0,r.jsxs)(s.li,{children:["After the system shows success notification, close the screen. (Do not click ",(0,r.jsx)(s.code,{children:"Request Partner"}),")"]}),"\n"]}),"\n",(0,r.jsx)(s.p,{children:(0,r.jsx)(s.img,{alt:"fetch_customer_form.gif",src:t(2231).A+"",width:"1662",height:"927"})}),"\n",(0,r.jsx)(s.h4,{id:"request-to-create-new-customer-refund",children:"Request to Create New Customer (Refund)."}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:["Click ",(0,r.jsx)(s.code,{children:"+ New Customer (Refund)"})," on the toolbar."]}),"\n"]}),"\n",(0,r.jsx)(s.p,{children:(0,r.jsx)(s.img,{alt:"create_customer_refund.gif",src:t(1289).A+"",width:"2105",height:"968"})}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsx)(s.li,{children:"Method 1: Fill Customers Info by yourself (Refund)."}),"\n"]}),"\n",(0,r.jsx)(s.p,{children:(0,r.jsx)(s.img,{alt:"create_customer_refund_form_1.png",src:t(1725).A+"",width:"1906",height:"875"})}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsx)(s.li,{children:"Method 2: Input existing customer code in the Database, the system will automatically fill in the corresponding fields:"}),"\n"]}),"\n",(0,r.jsx)(s.p,{children:(0,r.jsx)(s.img,{alt:"create_customer_refund_form_2.png",src:t(518).A+"",width:"1906",height:"875"})}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:["Click ",(0,r.jsx)(s.strong,{children:(0,r.jsx)(s.code,{children:"Request Partner (Refund)"})})," to submit Customer (Refund) creation request and wait for approval."]}),"\n"]}),"\n",(0,r.jsx)(s.h4,{id:"check-if-partner-already-exists-in-the-system",children:"Check if Partner already exists in the system"}),"\n",(0,r.jsx)(s.p,{children:(0,r.jsx)(s.img,{alt:"check_tax_code.png",src:t(3076).A+"",width:"1905",height:"876"})}),"\n",(0,r.jsxs)(s.p,{children:["Enter Tax Code or Name then click the ",(0,r.jsx)(s.code,{children:"Check"})," button to verify."]}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:["If Customer doesn't exist in the system, the screen displays: ",(0,r.jsx)(s.em,{children:(0,r.jsx)(s.strong,{children:"No partners found with the provided tax code."})})]}),"\n",(0,r.jsx)(s.li,{children:"If Customer already exists, the screen displays existing Customer information."}),"\n"]}),"\n",(0,r.jsx)(s.p,{children:(0,r.jsx)(s.img,{alt:"check_exist_by_taxcode.gif",src:t(4432).A+"",width:"2104",height:"970"})}),"\n",(0,r.jsx)(s.h4,{id:"search-customer",children:"Search Customer"}),"\n",(0,r.jsx)(s.p,{children:(0,r.jsx)(s.img,{alt:"search_customers.png",src:t(3100).A+"",width:"1905",height:"876"})}),"\n",(0,r.jsx)(s.h3,{id:"access-agent-list-screen",children:"Access Agent List Screen"}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:["From the screen list, click ",(0,r.jsx)(s.code,{children:"Partners"})," -> select ",(0,r.jsx)(s.code,{children:"Agent List"})," tab."]}),"\n"]}),"\n",(0,r.jsx)(s.p,{children:(0,r.jsx)(s.img,{alt:"enter_agent_list.gif",src:t(2734).A+"",width:"2108",height:"973"})}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:["Operations for ",(0,r.jsx)(s.code,{children:"Create New Agent"})," and ",(0,r.jsx)(s.code,{children:"Manage Agent"})," are similar to ",(0,r.jsx)(s.code,{children:"Customer"}),"."]}),"\n"]}),"\n",(0,r.jsx)(s.h3,{id:"access-coloader-list-screen",children:"Access Coloader List Screen"}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:["From the screen list, click ",(0,r.jsx)(s.code,{children:"Partners"})," -> select ",(0,r.jsx)(s.code,{children:"Agent List"})," tab."]}),"\n"]}),"\n",(0,r.jsx)(s.p,{children:(0,r.jsx)(s.img,{alt:"enter_coloader_list.gif",src:t(2746).A+"",width:"2109",height:"966"})}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:["Operations for ",(0,r.jsx)(s.code,{children:"Create New Coloader"})," and ",(0,r.jsx)(s.code,{children:"Manage Coloader"})," are similar to ",(0,r.jsx)(s.code,{children:"Customer"}),"."]}),"\n"]})]})}function h(e={}){const{wrapper:s}={...(0,n.R)(),...e.components};return s?(0,r.jsx)(s,{...e,children:(0,r.jsx)(l,{...e})}):l(e)}},9367:(e,s,t)=>{t.d(s,{A:()=>r});const r=t.p+"assets/images/enter_customers-5f71c82a69d9f80775ae2951c3fe1bf1.gif"}}]);