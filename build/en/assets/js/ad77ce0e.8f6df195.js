"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[2111],{168:(e,n,r)=>{r.r(n),r.d(n,{assets:()=>o,contentTitle:()=>c,default:()=>h,frontMatter:()=>t,metadata:()=>l,toc:()=>d});var i=r(216),s=r(494);const t={sidebar_position:1,hide_table_of_contents:!0,displayed_sidebar:"developerSidebar"},c="Changelog",l={id:"datatp-hr/developer/CHANGELOG",title:"Changelog",description:"All notable changes to this project will be documented in this file.",source:"@site/docs/datatp-hr/developer/CHANGELOG.md",sourceDirName:"datatp-hr/developer",slug:"/datatp-hr/developer/CHANGELOG",permalink:"/en/docs/datatp-hr/developer/CHANGELOG",draft:!1,unlisted:!1,tags:[],version:"current",sidebarPosition:1,frontMatter:{sidebar_position:1,hide_table_of_contents:!0,displayed_sidebar:"developerSidebar"},sidebar:"developerSidebar",previous:{title:"CRM Features",permalink:"/en/docs/document-ie/developer/features"},next:{title:"DataTP HR Backlog",permalink:"/en/docs/datatp-hr/developer/BACKLOG"}},o={},d=[{value:"[Unreleased]",id:"unreleased",level:3},{value:"[]",id:"",level:3}];function a(e){const n={code:"code",h1:"h1",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",ul:"ul",...(0,s.R)(),...e.components};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(n.header,{children:(0,i.jsx)(n.h1,{id:"changelog",children:"Changelog"})}),"\n",(0,i.jsx)(n.p,{children:"All notable changes to this project will be documented in this file."}),"\n",(0,i.jsx)(n.h3,{id:"unreleased",children:"[Unreleased]"}),"\n",(0,i.jsxs)(n.ol,{children:["\n",(0,i.jsx)(n.li,{children:"[Nhat]"}),"\n"]}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsx)(n.li,{children:"B\u1ed5 sung auto save khi edit trong m\xe0n h\xecnh KPI/KPI Template"}),"\n",(0,i.jsx)(n.li,{children:"C\u1eadp nh\u1eadt d\u1eef li\u1ec7u Account Th\xeam account m\u1edbi/Xo\xe1 account nh\xe2n s\u1ef1 \u0111\xe3 ngh\u1ec9"}),"\n",(0,i.jsx)(n.li,{children:"Th\xeam tr\u01b0\u1eddng Leader cho KPI/Template, c\xf3 quy\u1ec1n t\u01b0\u01a1ng t\u1ef1 v\u1edbi Manager\n\u0110\u1ed1i v\u1edbi KPI c\xf3 Leader, b\u01b0\u1edbc duy\u1ec7t KPI sau c\xf9ng ph\u1ea3i th\xeam 1 b\u01b0\u1edbc duy\u1ec7t b\u1edfi leader (leader => manager => director)"}),"\n",(0,i.jsxs)(n.li,{children:["Init d\u1eef li\u1ec7u KPI Q2 cho c\xe1c chi nh\xe1nh\n",(0,i.jsx)(n.code,{children:"Script:"}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["migrate",":run"," --script hr/UpdateData.groovy"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["server:migrate",":run"," --script hr/CheckAccount.groovy --company bee"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["server:migrate",":run"," --script hr/CheckAccount.groovy --company beehph"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["server:migrate",":run"," --script hr/CheckAccount.groovy --company beehan"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["server:migrate",":run"," --script hr/CheckAccount.groovy --company beedad"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["server:migrate",":run"," --script hr/CheckAccount.groovy --company beehcm"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["server:migrate",":run"," --script hr/InitKpiTemplate.groovy --company bee"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["server:migrate",":run"," --script hr/InitKpiTemplate.groovy --company beehph"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["server:migrate",":run"," --script hr/InitKpiTemplate.groovy --company beehan"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["server:migrate",":run"," --script hr/InitKpiTemplate.groovy --company beedad"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["server:migrate",":run"," --script hr/InitKpiTemplate.groovy --company beehcm"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["server:migrate",":run"," --script hr/InitKpi.groovy --company bee"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["server:migrate",":run"," --script hr/InitKpi.groovy --company beehph"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["server:migrate",":run"," --script hr/InitKpi.groovy --company beehan"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["server:migrate",":run"," --script hr/InitKpi.groovy --company beedad"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsxs)(n.p,{children:["server:migrate",":run"," --script hr/InitKpi.groovy --company beehcm"]}),"\n"]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,i.jsx)(n.h3,{id:"",children:"[]"})]})}function h(e={}){const{wrapper:n}={...(0,s.R)(),...e.components};return n?(0,i.jsx)(n,{...e,children:(0,i.jsx)(a,{...e})}):a(e)}},494:(e,n,r)=>{r.d(n,{R:()=>c,x:()=>l});var i=r(6372);const s={},t=i.createContext(s);function c(e){const n=i.useContext(t);return i.useMemo(function(){return"function"==typeof e?e(n):{...n,...e}},[n,e])}function l(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:c(e.components),i.createElement(t.Provider,{value:n},e.children)}}}]);