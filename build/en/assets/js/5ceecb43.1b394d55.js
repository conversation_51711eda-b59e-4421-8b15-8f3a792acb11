"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[1501],{248:(e,s,r)=>{r.d(s,{A:()=>n});const n=r.p+"assets/images/company_pricing_dashboard_2-3bac3476838e5a0ba76fcac0558a8e53.png"},494:(e,s,r)=>{r.d(s,{R:()=>t,x:()=>o});var n=r(6372);const i={},a=n.createContext(i);function t(e){const s=n.useContext(a);return n.useMemo(function(){return"function"==typeof e?e(s):{...s,...e}},[s,e])}function o(e){let s;return s=e.disableParentContext?"function"==typeof e.components?e.components(i):e.components||i:t(e.components),n.createElement(a.Provider,{value:s},e.children)}},2220:(e,s,r)=>{r.d(s,{A:()=>n});const n=r.p+"assets/images/company_pricing_dashboard-15f2640cc09f718d7ee31b42040b7ee2.gif"},2447:(e,s,r)=>{r.d(s,{A:()=>n});const n=r.p+"assets/images/company_pricing_dashboard-275a23340d6b923e9d01457b887dbcb0.png"},3882:(e,s,r)=>{r.d(s,{A:()=>n});const n=r.p+"assets/images/company_crm_dashboard_1-373ffdb143a732ef096edeb2a9303147.gif"},3970:(e,s,r)=>{r.d(s,{A:()=>n});const n=r.p+"assets/images/company_crm_dashboard-e376f8ee01496b8b4228799989f050a5.gif"},4113:(e,s,r)=>{r.d(s,{A:()=>n});const n=r.p+"assets/images/company_pricing_dashboard_3-6105ca65ea4599156f31cdb7bc131b15.png"},5812:(e,s,r)=>{r.r(s),r.d(s,{assets:()=>c,contentTitle:()=>t,default:()=>h,frontMatter:()=>a,metadata:()=>o,toc:()=>d});var n=r(216),i=r(494);const a={sidebar_position:2,sidebar:!1,hide_table_of_contents:!0,displayed_sidebar:"userSidebar"},t="Dashboard",o={id:"datatp-crm/user/crm/dashboard",title:"Dashboard",description:"Company Dashboard",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/datatp-crm/user/crm/dashboard.md",sourceDirName:"datatp-crm/user/crm",slug:"/datatp-crm/user/crm/dashboard",permalink:"/en/docs/datatp-crm/user/crm/dashboard",draft:!1,unlisted:!1,tags:[],version:"current",sidebarPosition:2,frontMatter:{sidebar_position:2,sidebar:!1,hide_table_of_contents:!0,displayed_sidebar:"userSidebar"},sidebar:"userSidebar"},c={},d=[{value:"<strong>Company Dashboard</strong>",id:"company-dashboard",level:3},{value:"1. <strong>CRM Overview</strong>",id:"1-crm-overview",level:4},{value:"2. <strong>Pricing Dashboard</strong>",id:"2-pricing-dashboard",level:4},{value:"3. <strong>Partner Overview</strong>",id:"3-partner-overview",level:4},{value:"<strong>Admin Dashboard</strong>",id:"admin-dashboard",level:3},{value:"1. <strong>CRM Overview</strong>",id:"1-crm-overview-1",level:4},{value:"2. <strong>Pricing Overview</strong>",id:"2-pricing-overview",level:4}];function l(e){const s={a:"a",code:"code",em:"em",h1:"h1",h3:"h3",h4:"h4",header:"header",img:"img",li:"li",p:"p",strong:"strong",ul:"ul",...(0,i.R)(),...e.components},{Details:a}=s;return a||function(e,s){throw new Error("Expected "+(s?"component":"object")+" `"+e+"` to be defined: you likely forgot to import, pass, or provide it.")}("Details",!0),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(s.header,{children:(0,n.jsx)(s.h1,{id:"dashboard",children:"Dashboard"})}),"\n",(0,n.jsx)(s.h3,{id:"company-dashboard",children:(0,n.jsx)(s.strong,{children:"Company Dashboard"})}),"\n",(0,n.jsxs)(s.h4,{id:"1-crm-overview",children:["1. ",(0,n.jsx)(s.strong,{children:"CRM Overview"})]}),"\n",(0,n.jsx)(s.p,{children:(0,n.jsx)(s.img,{alt:"company_crm_dashboard.gif",src:r(3970).A+"",width:"1901",height:"922"})}),"\n",(0,n.jsxs)(s.p,{children:[(0,n.jsx)(s.strong,{children:"1.1. Daily Task"}),": View sales staff activities, track progress, update status.\n",(0,n.jsx)(s.img,{alt:"company_crm_dashboard_3.png",src:r(6879).A+"",width:"1905",height:"128"})]}),"\n",(0,n.jsxs)(s.ul,{children:["\n",(0,n.jsxs)(s.li,{children:[(0,n.jsx)(s.em,{children:"All Tasks"})," : List of sales staff tasks."]}),"\n",(0,n.jsxs)(s.li,{children:[(0,n.jsx)(s.em,{children:"Leads/ Customers"})," : List of tasks related to customers and potential customers."]}),"\n",(0,n.jsxs)(s.li,{children:[(0,n.jsx)(s.em,{children:"Users Monitoring"})," : Monitor user operations on CRM including metrics such as number of price searches, most searched routes, etc."]}),"\n",(0,n.jsxs)(s.li,{children:[(0,n.jsx)(s.em,{children:"Export Excel"})," : Export Daily Task report to excel (Pending)."]}),"\n"]}),"\n",(0,n.jsxs)(s.p,{children:[(0,n.jsx)(s.strong,{children:"1.2. Salesman Activity Tracker"}),".  Summary of activity metrics for each employee, including:"]}),"\n",(0,n.jsxs)(s.ul,{children:["\n",(0,n.jsxs)(s.li,{children:[(0,n.jsx)(s.em,{children:"Saleman/ Branch"})," : Sales staff information and branch they work for."]}),"\n",(0,n.jsxs)(s.li,{children:[(0,n.jsx)(s.em,{children:"Total Tasks"})," : Total number of tasks - aggregated from ",(0,n.jsx)(s.code,{children:" Daily Tasks (In Progress / Total)"}),"."]}),"\n",(0,n.jsxs)(s.li,{children:[(0,n.jsx)(s.em,{children:"Meet Customer Tasks"})," : Total number of meeting customers tasks - aggregated from ",(0,n.jsx)(s.code,{children:" Daily Tasks"}),"."]}),"\n",(0,n.jsxs)(s.li,{children:[(0,n.jsx)(s.em,{children:"New Leads/ New Customers"}),": Total number of new customers (Create CS or Permission from old CS) and new leads (Created on CRM system)."]}),"\n",(0,n.jsxs)(s.li,{children:[(0,n.jsx)(s.em,{children:"Total Requests"}),": Total inquiry requests collected from the system. (Including requests for price checking sent to pricing and requests automatically created when creating quotations)."]}),"\n",(0,n.jsxs)(s.li,{children:[(0,n.jsx)(s.em,{children:"No Response Req."}),": Number of requests not yet responded to by customers, not yet updated to Win/Mismatch/... status."]}),"\n",(0,n.jsxs)(s.li,{children:[(0,n.jsx)(s.em,{children:"Overdue Req. Count."}),": Number of times the system sent reminder emails for late customer response updates."]}),"\n",(0,n.jsxs)(s.li,{children:[(0,n.jsx)(s.em,{children:"Overdue Req. Count."}),": Number of times the system sent reminder emails for late customer response updates."]}),"\n",(0,n.jsxs)(s.li,{children:[(0,n.jsx)(s.em,{children:"Quotation Count."}),": Number of times quotations were exported from the system."]}),"\n",(0,n.jsxs)(s.li,{children:[(0,n.jsx)(s.em,{children:"Booking Count."}),": Number of times internal booking was successfully created."]}),"\n"]}),"\n",(0,n.jsx)("hr",{}),"\n",(0,n.jsxs)(s.p,{children:["By default, the software will load the current month's report. Select report time by clicking the ",(0,n.jsx)(s.code,{children:"This Month"})," button -> select custom time\n",(0,n.jsx)(s.img,{alt:"company_crm_dashboard_2.png",src:r(9942).A+"",width:"1893",height:"469"})]}),"\n",(0,n.jsxs)(s.p,{children:["For some metrics, you can click to view detailed data.\n",(0,n.jsx)(s.img,{alt:"company_crm_dashboard_1.gif",src:r(3882).A+"",width:"1901",height:"1066"})]}),"\n",(0,n.jsxs)(s.h4,{id:"2-pricing-dashboard",children:["2. ",(0,n.jsx)(s.strong,{children:"Pricing Dashboard"})]}),"\n",(0,n.jsxs)(s.p,{children:["View status reports, win rate/volume ",(0,n.jsx)(s.code,{children:"Inquiry"})," by different dimensions such as service, route, salesman, pricing."]}),"\n",(0,n.jsxs)(s.p,{children:["On the toolbar, select ",(0,n.jsx)(s.code,{children:"Pricing Dashboard"}),"."]}),"\n",(0,n.jsx)(s.p,{children:(0,n.jsx)(s.img,{alt:"company_pricing_dashboard.gif",src:r(2220).A+"",width:"1904",height:"1063"})}),"\n",(0,n.jsx)(s.p,{children:"Support function bar includes:"}),"\n",(0,n.jsxs)(s.ul,{children:["\n",(0,n.jsxs)(s.li,{children:[(0,n.jsx)(s.em,{children:"Type Of Service"})," : Filter by service type."]}),"\n",(0,n.jsxs)(s.li,{children:[(0,n.jsx)(s.em,{children:"Bee HPH"})," : Select company/branch to view data (Default displays branch according to user login)."]}),"\n",(0,n.jsxs)(s.li,{children:[(0,n.jsx)(s.em,{children:"This Month"})," : Select report time. (Default loads current month's report)."]}),"\n",(0,n.jsxs)(s.li,{children:[(0,n.jsx)(s.em,{children:"View all"})," : View raw data, all Inquiry Requests data."]}),"\n"]}),"\n",(0,n.jsxs)(s.p,{children:[(0,n.jsx)(s.strong,{children:"1.1. Volume Performance"}),": View volume summary by each service type.\n",(0,n.jsx)(s.img,{alt:"company_pricing_dashboard_1.png",src:r(7107).A+"",width:"1883",height:"468"})]}),"\n",(0,n.jsxs)(s.p,{children:[(0,n.jsx)(s.strong,{children:"1.2. Department/ Pricing Performance"}),": View volume summary by each department, or by each Pricing Specialist.\n",(0,n.jsx)(s.img,{alt:"company_pricing_dashboard_2.png",src:r(248).A+"",width:"1248",height:"334"})]}),"\n",(0,n.jsxs)(s.p,{children:[(0,n.jsx)(s.strong,{children:"1.3. Top Volume/ Top Route"}),": View volume summary, win rate by each route.\n",(0,n.jsx)(s.img,{alt:"company_pricing_dashboard_3.png",src:r(4113).A+"",width:"1897",height:"832"})]}),"\n",(0,n.jsxs)(s.h4,{id:"3-partner-overview",children:["3. ",(0,n.jsx)(s.strong,{children:"Partner Overview"})]}),"\n",(0,n.jsx)(s.p,{children:(0,n.jsx)(s.a,{href:"/docs/datatp-crm/user/crm/references/partners",children:"See detailed instructions here"})}),"\n",(0,n.jsx)(s.h3,{id:"admin-dashboard",children:(0,n.jsx)(s.strong,{children:"Admin Dashboard"})}),"\n",(0,n.jsxs)(a,{children:[(0,n.jsx)("summary",{children:"Admin - CRM Overview"}),(0,n.jsxs)(s.h4,{id:"1-crm-overview-1",children:["1. ",(0,n.jsx)(s.strong,{children:"CRM Overview"})]}),(0,n.jsxs)(s.ul,{children:["\n",(0,n.jsxs)(s.li,{children:["Similar to ",(0,n.jsx)(s.code,{children:"Company Dashboard - CRM Overview"}),", but shows the overall view of the entire company, not limited by branch."]}),"\n"]}),(0,n.jsx)(s.p,{children:(0,n.jsx)(s.img,{alt:"company_crm_dashboard.png",src:r(8757).A+"",width:"1913",height:"1081"})})]}),"\n",(0,n.jsxs)(a,{children:[(0,n.jsx)("summary",{children:"Admin - Pricing Overview"}),(0,n.jsxs)(s.h4,{id:"2-pricing-overview",children:["2. ",(0,n.jsx)(s.strong,{children:"Pricing Overview"})]}),(0,n.jsx)(s.p,{children:(0,n.jsx)(s.img,{alt:"company_crm_dashboard.png",src:r(8757).A+"",width:"1913",height:"1081"})}),(0,n.jsxs)(s.ul,{children:["\n",(0,n.jsxs)(s.li,{children:["Similar to ",(0,n.jsx)(s.code,{children:"Company Dashboard - Pricing Dashboard"}),", but shows the overall view of the entire company, not limited by branch."]}),"\n"]}),(0,n.jsx)(s.p,{children:(0,n.jsx)(s.img,{alt:"company_pricing_dashboard.png",src:r(2447).A+"",width:"1919",height:"1080"})})]})]})}function h(e={}){const{wrapper:s}={...(0,i.R)(),...e.components};return s?(0,n.jsx)(s,{...e,children:(0,n.jsx)(l,{...e})}):l(e)}},6879:(e,s,r)=>{r.d(s,{A:()=>n});const n=r.p+"assets/images/company_crm_dashboard_3-0f62975b33385282e4427135f34e8a0b.png"},7107:(e,s,r)=>{r.d(s,{A:()=>n});const n=r.p+"assets/images/company_pricing_dashboard_1-eefa0fa011a9c08c26c920a3b16ad5a9.png"},8757:(e,s,r)=>{r.d(s,{A:()=>n});const n=r.p+"assets/images/company_crm_dashboard-f42c2fe488ea2207a1f61db00fd9ebc3.png"},9942:(e,s,r)=>{r.d(s,{A:()=>n});const n=r.p+"assets/images/company_crm_dashboard_2-552e8e27759107a735b6b969677fdce9.png"}}]);