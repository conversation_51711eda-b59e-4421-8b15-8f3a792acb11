"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[4002],{494:(t,e,n)=>{n.d(e,{R:()=>c,x:()=>u});var s=n(6372);const o={},r=s.createContext(o);function c(t){const e=s.useContext(r);return s.useMemo(function(){return"function"==typeof t?t(e):{...e,...t}},[e,t])}function u(t){let e;return e=t.disableParentContext?"function"==typeof t.components?t.components(o):t.components||o:c(t.components),s.createElement(r.Provider,{value:e},t.children)}},6301:(t,e,n)=>{n.r(e),n.d(e,{assets:()=>i,contentTitle:()=>c,default:()=>p,frontMatter:()=>r,metadata:()=>u,toc:()=>a});var s=n(216),o=n(494);const r={},c=void 0,u={id:"datatp-tms/user/intro",title:"intro",description:"",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/datatp-tms/user/intro.md",sourceDirName:"datatp-tms/user",slug:"/datatp-tms/user/intro",permalink:"/en/docs/datatp-tms/user/intro",draft:!1,unlisted:!1,tags:[],version:"current",frontMatter:{}},i={},a=[];function d(t){return(0,s.jsx)(s.Fragment,{})}function p(t={}){const{wrapper:e}={...(0,o.R)(),...t.components};return e?(0,s.jsx)(e,{...t,children:(0,s.jsx)(d,{...t})}):d()}}}]);