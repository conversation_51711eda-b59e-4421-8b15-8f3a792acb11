"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[6403],{494:(e,t,n)=>{n.d(t,{R:()=>c,x:()=>u});var o=n(6372);const r={},s=o.createContext(r);function c(e){const t=o.useContext(s);return o.useMemo(function(){return"function"==typeof e?e(t):{...t,...e}},[t,e])}function u(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(r):e.components||r:c(e.components),o.createElement(s.Provider,{value:t},e.children)}},4960:(e,t,n)=>{n.r(t),n.d(t,{assets:()=>i,contentTitle:()=>c,default:()=>l,frontMatter:()=>s,metadata:()=>u,toc:()=>d});var o=n(216),r=n(494);const s={},c=void 0,u={id:"document-ie/user/intro",title:"intro",description:"",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/document-ie/user/intro.md",sourceDirName:"document-ie/user",slug:"/document-ie/user/intro",permalink:"/en/docs/document-ie/user/intro",draft:!1,unlisted:!1,tags:[],version:"current",frontMatter:{}},i={},d=[];function a(e){return(0,o.jsx)(o.Fragment,{})}function l(e={}){const{wrapper:t}={...(0,r.R)(),...e.components};return t?(0,o.jsx)(t,{...e,children:(0,o.jsx)(a,{...e})}):a()}}}]);