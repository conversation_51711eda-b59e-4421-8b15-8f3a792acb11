"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[7012],{494:(e,t,n)=>{n.d(t,{R:()=>c,x:()=>l});var r=n(6372);const s={},i=r.createContext(s);function c(e){const t=r.useContext(i);return r.useMemo(function(){return"function"==typeof e?e(t):{...t,...e}},[t,e])}function l(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:c(e.components),r.createElement(i.Provider,{value:t},e.children)}},2010:(e,t,n)=>{n.d(t,{A:()=>r});const r=n.p+"assets/images/price_fcl_analysis_1-5f2cb6205ee2c5b3f1642b490d6beb60.png"},4354:(e,t,n)=>{n.d(t,{A:()=>r});const r=n.p+"assets/images/search_bar-879da5e89613493ee359168b50abc7eb.png"},5510:(e,t,n)=>{n.d(t,{A:()=>r});const r=n.p+"assets/images/rate_finder_func-7d09a3110cce27cde127727fa6c18236.png"},7662:(e,t,n)=>{n.d(t,{A:()=>r});const r=n.p+"assets/images/price_fcl_analysis-aa7c56b7843d698b4a504d08b5eca897.png"},8143:(e,t,n)=>{n.d(t,{A:()=>r});const r=n.p+"assets/images/quick_rate_finder-bf066744f34c658be8ff72b760ccb836.png"},8200:(e,t,n)=>{n.r(t),n.d(t,{assets:()=>o,contentTitle:()=>c,default:()=>d,frontMatter:()=>i,metadata:()=>l,toc:()=>a});var r=n(216),s=n(494);const i={sidebar_position:2,toc:!1,hide_table_of_contents:!0,displayed_sidebar:"userSidebar"},c="Search Prices",l={id:"datatp-crm/user/crm/references/search_prices",title:"Search Prices",description:"Click Quick Rate Finder on the toolbar to access the price search screen.",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/datatp-crm/user/crm/references/search_prices.md",sourceDirName:"datatp-crm/user/crm/references",slug:"/datatp-crm/user/crm/references/search_prices",permalink:"/en/docs/datatp-crm/user/crm/references/search_prices",draft:!1,unlisted:!1,tags:[],version:"current",sidebarPosition:2,frontMatter:{sidebar_position:2,toc:!1,hide_table_of_contents:!0,displayed_sidebar:"userSidebar"},sidebar:"userSidebar"},o={},a=[{value:"3. Function buttons.",id:"3-function-buttons",level:3},{value:"4. View FCL Export price analysis/fluctuations",id:"4-view-fcl-export-price-analysisfluctuations",level:3}];function h(e){const t={a:"a",code:"code",h1:"h1",h3:"h3",header:"header",img:"img",li:"li",ol:"ol",p:"p",strong:"strong",ul:"ul",...(0,s.R)(),...e.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(t.header,{children:(0,r.jsx)(t.h1,{id:"search-prices",children:"Search Prices"})}),"\n",(0,r.jsxs)(t.p,{children:["Click ",(0,r.jsx)(t.code,{children:"Quick Rate Finder"})," on the toolbar to access the price search screen."]}),"\n",(0,r.jsx)(t.p,{children:(0,r.jsx)(t.img,{alt:"rate_finder.png",src:n(8143).A+"",width:"1920",height:"864"})}),"\n",(0,r.jsx)(t.p,{children:"By default, the system will display prices that are currently valid (expiration date is greater than the current date)."}),"\n",(0,r.jsxs)(t.p,{children:["At the top are the types of transportation including ",(0,r.jsx)(t.code,{children:"Sea Freight (FCL)"}),", ",(0,r.jsx)(t.code,{children:"Sea Freight (LCL)"}),", ",(0,r.jsx)(t.code,{children:"Air Freight"}),", and ",(0,r.jsx)(t.code,{children:"Trucking"}),"."]}),"\n",(0,r.jsx)(t.p,{children:"Please select the type of transportation that suits your needs."}),"\n",(0,r.jsx)(t.p,{children:(0,r.jsx)(t.img,{alt:"search_bar.png",src:n(4354).A+"",width:"1919",height:"412"})}),"\n",(0,r.jsxs)(t.p,{children:["The software provides search criteria such as: ",(0,r.jsx)(t.strong,{children:"(1)"})]}),"\n",(0,r.jsxs)(t.ul,{children:["\n",(0,r.jsxs)(t.li,{children:["\n",(0,r.jsxs)(t.p,{children:[(0,r.jsx)(t.strong,{children:"Origin of Shipment"})," (Port of loading)"]}),"\n"]}),"\n",(0,r.jsxs)(t.li,{children:["\n",(0,r.jsxs)(t.p,{children:[(0,r.jsx)(t.strong,{children:"Destination of Shipment"})," (Port of discharge)\nFor US routes, inland ports are not entered here.\nInstead, you can enter them in the quick search or use the filter feature on each column as instructed below."]}),"\n"]}),"\n",(0,r.jsxs)(t.li,{children:["\n",(0,r.jsxs)(t.p,{children:[(0,r.jsx)(t.strong,{children:"Ready to Load"})," (Date cargo is ready for shipment) - the software will use this date to search for valid prices."]}),"\n",(0,r.jsxs)(t.p,{children:["The valid price will be shown when: ",(0,r.jsx)(t.code,{children:"Effect Date \u2264 Selected Date \u2264 Valid Date"})]}),"\n"]}),"\n",(0,r.jsxs)(t.li,{children:["\n",(0,r.jsxs)(t.p,{children:[(0,r.jsx)(t.strong,{children:"Import / Export"})," (Import or export cargo)"]}),"\n"]}),"\n",(0,r.jsxs)(t.li,{children:["\n",(0,r.jsxs)(t.p,{children:[(0,r.jsx)(t.strong,{children:"General search bar"})," ",(0,r.jsx)(t.strong,{children:"(2)"}),": Enter any keyword to search, the system will automatically filter ",(0,r.jsx)(t.strong,{children:"all"})," data in the table."]}),"\n"]}),"\n",(0,r.jsxs)(t.li,{children:["\n",(0,r.jsxs)(t.p,{children:[(0,r.jsx)(t.strong,{children:"Filter by each column"})," ",(0,r.jsx)(t.strong,{children:"(3)"})," (as shown below)"]}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(t.p,{children:"Each column has a filter icon on the right, click to popup the filter.\nClick the label of the data to filter that value or tick each value to filter by multiple values at once."}),"\n",(0,r.jsx)(t.p,{children:(0,r.jsx)(t.img,{alt:"search_bar.png",src:n(9094).A+"",width:"1007",height:"680"})}),"\n",(0,r.jsx)(t.p,{children:"Filtering/searching data is effective immediately when you perform the action."}),"\n",(0,r.jsx)(t.p,{children:"Demo:"}),"\n",(0,r.jsx)(t.p,{children:(0,r.jsx)(t.a,{href:"https://youtu.be/bVEDsg4hpmE",children:"https://youtu.be/bVEDsg4hpmE"})}),"\n",(0,r.jsx)("div",{style:{position:"relative",paddingBottom:"56.25%",height:0,overflow:"hidden",maxWidth:"100%",height:"auto"},children:(0,r.jsx)("iframe",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%"},src:"https://www.youtube.com/embed/bVEDsg4hpmE",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})}),"\n",(0,r.jsx)(t.h3,{id:"3-function-buttons",children:"3. Function buttons."}),"\n",(0,r.jsx)(t.p,{children:(0,r.jsx)(t.img,{alt:"extra_func.png",src:n(5510).A+"",width:"1922",height:"627"})}),"\n",(0,r.jsxs)(t.ul,{children:["\n",(0,r.jsxs)(t.li,{children:[(0,r.jsx)(t.strong,{children:"XLSX Export"}),": export price data to excel file (the system limits to 20 lines/export)."]}),"\n"]}),"\n",(0,r.jsx)(t.p,{children:(0,r.jsx)(t.a,{href:"https://youtu.be/iqiYuLvF_yg",children:"https://youtu.be/iqiYuLvF_yg"})}),"\n",(0,r.jsx)("div",{style:{position:"relative",paddingBottom:"56.25%",height:0,overflow:"hidden",maxWidth:"100%",height:"auto"},children:(0,r.jsx)("iframe",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%"},src:"https://www.youtube.com/embed/iqiYuLvF_yg",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})}),"\n",(0,r.jsxs)(t.ul,{children:["\n",(0,r.jsxs)(t.li,{children:["\n",(0,r.jsxs)(t.p,{children:[(0,r.jsx)(t.strong,{children:"Request Pricing"}),": You can send a price check request (mail) to the Pricing Team."]}),"\n",(0,r.jsxs)(t.p,{children:["For usage details, please refer to ",(0,r.jsx)(t.a,{href:"/docs/datatp-crm/user/crm/references/mail_request",children:"instructions here"}),"."]}),"\n"]}),"\n",(0,r.jsxs)(t.li,{children:["\n",(0,r.jsxs)(t.p,{children:[(0,r.jsx)(t.strong,{children:"Export a Quote"}),": Quickly export a quote to an Excel file:"]}),"\n",(0,r.jsxs)(t.ul,{children:["\n",(0,r.jsx)(t.li,{children:"Select the price to export"}),"\n",(0,r.jsx)(t.li,{children:'Click the "Export a Quote" button'}),"\n",(0,r.jsx)(t.li,{children:"The Excel file will be downloaded to your computer"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(t.li,{children:["\n",(0,r.jsxs)(t.p,{children:[(0,r.jsx)(t.strong,{children:"Request a Quote"}),": See details here: ",(0,r.jsx)(t.a,{href:"/docs/datatp-crm/user/crm/overview",children:"Quotation"})]}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(t.h3,{id:"4-view-fcl-export-price-analysisfluctuations",children:"4. View FCL Export price analysis/fluctuations"}),"\n",(0,r.jsx)(t.p,{children:"You can view price fluctuations over time by:"}),"\n",(0,r.jsxs)(t.ol,{children:["\n",(0,r.jsxs)(t.li,{children:["\n",(0,r.jsxs)(t.p,{children:["On the ",(0,r.jsx)(t.code,{children:"Quick Rate Finder"})," screen, select the transportation type as ",(0,r.jsx)(t.code,{children:"Sea Freight (FCL)"})," -> ",(0,r.jsx)(t.code,{children:"Export"})]}),"\n"]}),"\n",(0,r.jsxs)(t.li,{children:["\n",(0,r.jsxs)(t.p,{children:["On each price row, at the end there is an ",(0,r.jsx)(t.code,{children:"Analysis"})," button -> Click this button to view the price analysis of that Line/Route."]}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(t.p,{children:(0,r.jsx)(t.img,{alt:"./img/price_fcl_analysis.png",src:n(7662).A+"",width:"1922",height:"1081"})}),"\n",(0,r.jsx)(t.p,{children:"The screen will display:"}),"\n",(0,r.jsxs)(t.ul,{children:["\n",(0,r.jsxs)(t.li,{children:["\n",(0,r.jsx)(t.p,{children:"List of prices before and after the current time"}),"\n"]}),"\n",(0,r.jsxs)(t.li,{children:["\n",(0,r.jsx)(t.p,{children:"The price you are selecting will be highlighted in yellow. (Action in step 2)"}),"\n"]}),"\n",(0,r.jsxs)(t.li,{children:["\n",(0,r.jsx)(t.p,{children:'The "Comparison" column shows the % difference compared to the selected price'}),"\n"]}),"\n",(0,r.jsxs)(t.li,{children:["\n",(0,r.jsx)(t.p,{children:"To compare prices between different carriers, you can leave the Carrier column blank in the filter screen. This helps you easily evaluate and choose the best price among the Lines."}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(t.p,{children:(0,r.jsx)(t.img,{alt:"./img/price_fcl_analysis.png",src:n(2010).A+"",width:"1915",height:"1083"})}),"\n",(0,r.jsx)(t.p,{children:(0,r.jsx)(t.strong,{children:"Demo:"})}),"\n",(0,r.jsx)(t.p,{children:(0,r.jsx)(t.a,{href:"https://youtu.be/ysdnwKeK9PM",children:"https://youtu.be/ysdnwKeK9PM"})}),"\n",(0,r.jsx)("div",{style:{position:"relative",paddingBottom:"56.25%",height:0,overflow:"hidden",maxWidth:"100%",height:"auto"},children:(0,r.jsx)("iframe",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%"},src:"https://www.youtube.com/embed/ysdnwKeK9PM",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})})]})}function d(e={}){const{wrapper:t}={...(0,s.R)(),...e.components};return t?(0,r.jsx)(t,{...e,children:(0,r.jsx)(h,{...e})}):h(e)}},9094:(e,t,n)=>{n.d(t,{A:()=>r});const r=n.p+"assets/images/search_bar_1-70437028259b224a329f4bfd93c09ff5.png"}}]);