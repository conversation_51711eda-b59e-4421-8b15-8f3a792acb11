"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[810],{420:(e,s,t)=>{t.d(s,{A:()=>n});const n=t.p+"assets/images/mail_request_list-9f9fc284ed086441d01caff933ec3459.png"},494:(e,s,t)=>{t.d(s,{R:()=>c,x:()=>l});var n=t(6372);const i={},r=n.createContext(i);function c(e){const s=n.useContext(r);return n.useMemo(function(){return"function"==typeof e?e(s):{...s,...e}},[s,e])}function l(e){let s;return s=e.disableParentContext?"function"==typeof e.components?e.components(i):e.components||i:c(e.components),n.createElement(r.Provider,{value:s},e.children)}},642:(e,s,t)=>{t.r(s),t.d(s,{assets:()=>a,contentTitle:()=>c,default:()=>h,frontMatter:()=>r,metadata:()=>l,toc:()=>d});var n=t(216),i=t(494);const r={sidebar_position:3,toc:!1,hide_table_of_contents:!0,displayed_sidebar:"userSidebar"},c="Inquiry Request",l={id:"datatp-crm/user/crm/references/mail_request",title:"Inquiry Request",description:"Feature to send price check requests via email when no suitable price is found on Pricing Tools.",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/datatp-crm/user/crm/references/mail_request.md",sourceDirName:"datatp-crm/user/crm/references",slug:"/datatp-crm/user/crm/references/mail_request",permalink:"/en/docs/datatp-crm/user/crm/references/mail_request",draft:!1,unlisted:!1,tags:[],version:"current",sidebarPosition:3,frontMatter:{sidebar_position:3,toc:!1,hide_table_of_contents:!0,displayed_sidebar:"userSidebar"},sidebar:"userSidebar"},a={},d=[{value:"Select the appropriate request type",id:"select-the-appropriate-request-type",level:3},{value:"Fill in the request information",id:"fill-in-the-request-information",level:3},{value:"Email",id:"email",level:4},{value:"Attachment",id:"attachment",level:4},{value:"Manage sent requests",id:"manage-sent-requests",level:2},{value:"Available functions on the screen.",id:"available-functions-on-the-screen",level:4},{value:"Main functions",id:"main-functions",level:3}];function o(e){const s={a:"a",blockquote:"blockquote",code:"code",em:"em",h1:"h1",h2:"h2",h3:"h3",h4:"h4",header:"header",img:"img",li:"li",ol:"ol",p:"p",strong:"strong",ul:"ul",...(0,i.R)(),...e.components};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(s.header,{children:(0,n.jsx)(s.h1,{id:"inquiry-request",children:"Inquiry Request"})}),"\n",(0,n.jsxs)(s.p,{children:["Feature to send price check requests via email when no suitable price is found on ",(0,n.jsx)(s.strong,{children:"Pricing Tools"}),"."]}),"\n",(0,n.jsxs)(s.ol,{children:["\n",(0,n.jsx)(s.li,{children:"Search for the price for the route to be quoted"}),"\n",(0,n.jsxs)(s.li,{children:["Click ",(0,n.jsx)(s.code,{children:"Request Pricing"})," on the toolbar if there is no suitable result"]}),"\n"]}),"\n",(0,n.jsx)(s.p,{children:(0,n.jsx)(s.img,{alt:"mail_request.png",src:t(6367).A+"",width:"1681",height:"967"})}),"\n",(0,n.jsx)(s.h3,{id:"select-the-appropriate-request-type",children:"Select the appropriate request type"}),"\n",(0,n.jsxs)(s.p,{children:[(0,n.jsx)(s.strong,{children:"FCL Import"}),"\n",(0,n.jsx)(s.img,{alt:"mail_request_type_1.png",src:t(9960).A+"",width:"1918",height:"1075"})]}),"\n",(0,n.jsxs)(s.p,{children:[(0,n.jsx)(s.strong,{children:"FCL Export"}),"\n",(0,n.jsx)(s.img,{alt:"mail_request_type_2.png",src:t(1939).A+"",width:"1916",height:"1076"})]}),"\n",(0,n.jsxs)(s.p,{children:[(0,n.jsx)(s.strong,{children:"Trucking"}),"\n",(0,n.jsx)(s.img,{alt:"mail_request_type_3.png",src:t(2106).A+"",width:"1915",height:"1083"})]}),"\n",(0,n.jsx)(s.h3,{id:"fill-in-the-request-information",children:"Fill in the request information"}),"\n",(0,n.jsx)(s.h4,{id:"email",children:"Email"}),"\n",(0,n.jsxs)(s.ul,{children:["\n",(0,n.jsxs)(s.li,{children:[(0,n.jsx)(s.strong,{children:"From"}),": Sender's email"]}),"\n",(0,n.jsxs)(s.li,{children:[(0,n.jsx)(s.strong,{children:"TO"}),": Pricing team email ",(0,n.jsx)(s.em,{children:"(auto-filled)"})]}),"\n",(0,n.jsxs)(s.li,{children:[(0,n.jsx)(s.strong,{children:"TO (External)"}),": Recipient's email outside the system"]}),"\n",(0,n.jsxs)(s.li,{children:[(0,n.jsx)(s.strong,{children:"CC"}),": CC email within the system"]}),"\n",(0,n.jsxs)(s.li,{children:[(0,n.jsx)(s.strong,{children:"CC (External)"}),": CC email outside the system"]}),"\n"]}),"\n",(0,n.jsx)(s.h4,{id:"attachment",children:"Attachment"}),"\n",(0,n.jsxs)(s.ul,{children:["\n",(0,n.jsx)(s.li,{children:"Drag and drop or select file (up to 35MB)"}),"\n"]}),"\n",(0,n.jsx)(s.p,{children:(0,n.jsx)(s.img,{alt:"mail_request_from.png",src:t(5298).A+"",width:"1444",height:"846"})}),"\n",(0,n.jsxs)(s.blockquote,{children:["\n",(0,n.jsxs)(s.p,{children:["Click ",(0,n.jsx)(s.code,{children:"Send Request"})," after completion"]}),"\n"]}),"\n",(0,n.jsx)(s.h2,{id:"manage-sent-requests",children:"Manage sent requests"}),"\n",(0,n.jsxs)(s.p,{children:["View the list of sent requests: ",(0,n.jsx)(s.strong,{children:"Transactions"})," > tab ",(0,n.jsx)(s.strong,{children:"Inquiry"})," ",(0,n.jsx)(s.strong,{children:"(1)"})]}),"\n",(0,n.jsx)(s.p,{children:(0,n.jsx)(s.img,{alt:"mail_request_list.png",src:t(420).A+"",width:"1921",height:"905"})}),"\n",(0,n.jsx)(s.h4,{id:"available-functions-on-the-screen",children:"Available functions on the screen."}),"\n",(0,n.jsx)(s.h3,{id:"main-functions",children:"Main functions"}),"\n",(0,n.jsxs)(s.ol,{children:["\n",(0,n.jsxs)(s.li,{children:["\n",(0,n.jsx)(s.p,{children:(0,n.jsx)(s.strong,{children:"Update feedback and status of Inquiry Request"})}),"\n",(0,n.jsxs)(s.ul,{children:["\n",(0,n.jsx)(s.li,{children:"Update customer feedback and request status (Win/ Feedback/ No price match/ ... )."}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,n.jsx)(s.p,{children:(0,n.jsx)(s.img,{alt:"request_change_status.png",src:t(1929).A+"",width:"1916",height:"896"})}),"\n",(0,n.jsxs)(s.p,{children:[(0,n.jsx)(s.em,{children:"Demo video"}),": ",(0,n.jsx)(s.a,{href:"https://youtu.be/8DMXdL3haIc",children:"https://youtu.be/8DMXdL3haIc"})]}),"\n",(0,n.jsx)("div",{style:{position:"relative",paddingBottom:"56.25%",height:0,overflow:"hidden",maxWidth:"100%",height:"auto"},children:(0,n.jsx)("iframe",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%"},src:"https://www.youtube.com/embed/8DMXdL3haIc",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})}),"\n",(0,n.jsx)("br",{}),"\n",(0,n.jsxs)(s.ol,{start:"2",children:["\n",(0,n.jsxs)(s.li,{children:[(0,n.jsx)(s.strong,{children:"Resend request"}),"\n",(0,n.jsxs)(s.ul,{children:["\n",(0,n.jsx)(s.li,{children:"Click the bullet icon to copy information from the old request"}),"\n",(0,n.jsx)(s.li,{children:"Edit and send as a new request"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,n.jsx)(s.p,{children:(0,n.jsx)(s.img,{alt:"resend_mail.png",src:t(8583).A+"",width:"1920",height:"1078"})})]})}function h(e={}){const{wrapper:s}={...(0,i.R)(),...e.components};return s?(0,n.jsx)(s,{...e,children:(0,n.jsx)(o,{...e})}):o(e)}},1929:(e,s,t)=>{t.d(s,{A:()=>n});const n=t.p+"assets/images/request_change_status-0dbc459b9588934cf5538e789ab1846e.png"},1939:(e,s,t)=>{t.d(s,{A:()=>n});const n=t.p+"assets/images/mail_request_type_2-a15d5fd49a277e34907dfaf92b084529.png"},2106:(e,s,t)=>{t.d(s,{A:()=>n});const n=t.p+"assets/images/mail_request_type_3-c36b02086e42ab15ccd333214d38ee58.png"},5298:(e,s,t)=>{t.d(s,{A:()=>n});const n=t.p+"assets/images/mail_request_form-9637169814928ec51381d6edc59a2cda.png"},6367:(e,s,t)=>{t.d(s,{A:()=>n});const n=t.p+"assets/images/mail_request-8eda5978f7a30a44d34bc45cd3240b52.png"},8583:(e,s,t)=>{t.d(s,{A:()=>n});const n=t.p+"assets/images/resend_mail-27c4f7811cf9a5dd355bf36e0614cbc9.png"},9960:(e,s,t)=>{t.d(s,{A:()=>n});const n=t.p+"assets/images/mail_request_type_1-0862e5dfcdcdc791b1bb2a1d75d94bc7.png"}}]);