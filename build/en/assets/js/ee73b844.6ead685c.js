"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[1977],{494:(e,t,n)=>{n.d(t,{R:()=>o,x:()=>a});var s=n(6372);const r={},i=s.createContext(r);function o(e){const t=s.useContext(i);return s.useMemo(function(){return"function"==typeof e?e(t):{...t,...e}},[t,e])}function a(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(r):e.components||r:o(e.components),s.createElement(i.Provider,{value:t},e.children)}},8720:(e,t,n)=>{n.r(t),n.d(t,{assets:()=>d,contentTitle:()=>o,default:()=>u,frontMatter:()=>i,metadata:()=>a,toc:()=>c});var s=n(216),r=n(494);const i={sidebar_position:8,sidebar:!1,hide_table_of_contents:!0,displayed_sidebar:"userSidebar"},o="KPI",a={id:"datatp-hr/user/kpi/kpi",title:"KPI",description:"Quy tr\xecnh nghi\u1ec7p v\u1ee5:",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/datatp-hr/user/kpi/kpi.md",sourceDirName:"datatp-hr/user/kpi",slug:"/datatp-hr/user/kpi/",permalink:"/en/docs/datatp-hr/user/kpi/",draft:!1,unlisted:!1,tags:[],version:"current",sidebarPosition:8,frontMatter:{sidebar_position:8,sidebar:!1,hide_table_of_contents:!0,displayed_sidebar:"userSidebar"},sidebar:"userSidebar"},d={},c=[];function p(e){const t={h1:"h1",header:"header",img:"img",p:"p",...(0,r.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(t.header,{children:(0,s.jsx)(t.h1,{id:"kpi",children:"KPI"})}),"\n",(0,s.jsx)(t.p,{children:"Quy tr\xecnh nghi\u1ec7p v\u1ee5:"}),"\n",(0,s.jsx)(t.p,{children:(0,s.jsx)(t.img,{alt:"./img/kpi_flow.png",src:n(8960).A+"",width:"1356",height:"484"})})]})}function u(e={}){const{wrapper:t}={...(0,r.R)(),...e.components};return t?(0,s.jsx)(t,{...e,children:(0,s.jsx)(p,{...e})}):p(e)}},8960:(e,t,n)=>{n.d(t,{A:()=>s});const s=n.p+"assets/images/kpi_flow-da90a46122d339574785902b87e33135.png"}}]);