"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[5276],{494:(e,n,t)=>{t.d(n,{R:()=>c,x:()=>i});var a=t(6372);const s={},r=a.createContext(s);function c(e){const n=a.useContext(r);return a.useMemo(function(){return"function"==typeof e?e(n):{...n,...e}},[n,e])}function i(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:c(e.components),a.createElement(r.Provider,{value:n},e.children)}},1052:(e,n,t)=>{t.d(n,{A:()=>a});const a=t.p+"assets/images/lead_create_agent_form-3a9f7cacdaa2ec8c96e57e9d8521191a.png"},2123:(e,n,t)=>{t.d(n,{A:()=>a});const a=t.p+"assets/images/lead_create_customer_form-eea8229ca2e403bca7f0df4189db5a28.png"},2347:(e,n,t)=>{t.d(n,{A:()=>a});const a=t.p+"assets/images/lead_create_customer-12ea4fa99dc9f416080b594758af2aae.gif"},2482:(e,n,t)=>{t.d(n,{A:()=>a});const a=t.p+"assets/images/lead_enter_lead_list-0569fd20c522d366ddf96668f195d7d2.gif"},6499:(e,n,t)=>{t.d(n,{A:()=>a});const a=t.p+"assets/images/lead_create_customer_validate-b48e23f492638055bafd114cc8c7f041.png"},8363:(e,n,t)=>{t.d(n,{A:()=>a});const a=t.p+"assets/images/lead_list-3e5a0c55a3fb94c2ee13341fc50f516f.png"},9095:(e,n,t)=>{t.r(n),t.d(n,{assets:()=>d,contentTitle:()=>c,default:()=>h,frontMatter:()=>r,metadata:()=>i,toc:()=>o});var a=t(216),s=t(494);const r={sidebar_position:10,hide_table_of_contents:!0,displayed_sidebar:"userSidebar"},c="Lead/ Agent Potential Management",i={id:"datatp-crm/user/crm/references/lead_management",title:"Lead/ Agent Potential Management",description:"Instructions for managing potential customer (Lead) information in the CRM system.",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/datatp-crm/user/crm/references/lead_management.md",sourceDirName:"datatp-crm/user/crm/references",slug:"/datatp-crm/user/crm/references/lead_management",permalink:"/en/docs/datatp-crm/user/crm/references/lead_management",draft:!1,unlisted:!1,tags:[],version:"current",sidebarPosition:10,frontMatter:{sidebar_position:10,hide_table_of_contents:!0,displayed_sidebar:"userSidebar"},sidebar:"userSidebar"},d={},o=[{value:"Access the Leads list screen.",id:"access-the-leads-list-screen",level:3},{value:"Enter Customer Lead/Agent Approach information",id:"enter-customer-leadagent-approach-information",level:3},{value:"Create new Customer Lead",id:"create-new-customer-lead",level:4},{value:"Create new Agent Approach",id:"create-new-agent-approach",level:4}];function l(e){const n={code:"code",h1:"h1",h3:"h3",h4:"h4",header:"header",img:"img",li:"li",p:"p",ul:"ul",...(0,s.R)(),...e.components};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.header,{children:(0,a.jsx)(n.h1,{id:"lead-agent-potential-management",children:"Lead/ Agent Potential Management"})}),"\n",(0,a.jsx)(n.p,{children:"Instructions for managing potential customer (Lead) information in the CRM system."}),"\n",(0,a.jsxs)(n.ul,{children:["\n",(0,a.jsx)(n.li,{children:"Track and manage potential customer information"}),"\n",(0,a.jsx)(n.li,{children:"Convert Lead to Customer"}),"\n",(0,a.jsx)(n.li,{children:"Track interaction history"}),"\n"]}),"\n",(0,a.jsx)(n.h3,{id:"access-the-leads-list-screen",children:"Access the Leads list screen."}),"\n",(0,a.jsxs)(n.ul,{children:["\n",(0,a.jsxs)(n.li,{children:["On the list screen, click ",(0,a.jsx)(n.code,{children:"Partners"})," -> select the ",(0,a.jsx)(n.code,{children:"Customer Leads"})," Tab."]}),"\n"]}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.img,{alt:"lead_enter_lead_list.gif",src:t(2482).A+"",width:"1721",height:"839"})}),"\n",(0,a.jsxs)(n.ul,{children:["\n",(0,a.jsx)(n.li,{children:"Customer Lead screen:"}),"\n"]}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.img,{alt:"lead_list.png",src:t(8363).A+"",width:"2932",height:"1538"})}),"\n",(0,a.jsx)(n.h3,{id:"enter-customer-leadagent-approach-information",children:"Enter Customer Lead/Agent Approach information"}),"\n",(0,a.jsx)(n.h4,{id:"create-new-customer-lead",children:"Create new Customer Lead"}),"\n",(0,a.jsxs)(n.ul,{children:["\n",(0,a.jsxs)(n.li,{children:["Click the ",(0,a.jsx)(n.code,{children:"+ Customer Lead"})," button to create a new Customer Lead."]}),"\n"]}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.img,{alt:"lead_create_customer.gif",src:t(2347).A+"",width:"1655",height:"934"})}),"\n",(0,a.jsxs)(n.ul,{children:["\n",(0,a.jsx)(n.li,{children:"New Customer Lead creation screen:"}),"\n"]}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.img,{alt:"lead_create_customer_form.png",src:t(2123).A+"",width:"3354",height:"1864"})}),"\n",(0,a.jsx)("div",{style:{background:"#fff3cd",color:"#856404",padding:"12px 16px",borderRadius:"6px",border:"1px solid #ffeeba",margin:"16px 0"},children:(0,a.jsxs)(n.p,{children:[(0,a.jsx)("strong",{children:"Note:"})," When entering the tax code, you may encounter a warning screen as below..",(0,a.jsx)("br",{}),"\nThis is a warning that the Lead already exists in the system and is being followed by another salesman."]})}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.img,{alt:"lead_create_customer_validate.png",src:t(6499).A+"",width:"2512",height:"1114"})}),"\n",(0,a.jsx)(n.h4,{id:"create-new-agent-approach",children:"Create new Agent Approach"}),"\n",(0,a.jsxs)(n.ul,{children:["\n",(0,a.jsxs)(n.li,{children:["Click the ",(0,a.jsx)(n.code,{children:"+ Agent Approach"})," button to create a new Agent Approach."]}),"\n"]}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.img,{alt:"lead_create_agent.gif",src:t(9234).A+"",width:"1661",height:"933"})}),"\n",(0,a.jsxs)(n.ul,{children:["\n",(0,a.jsx)(n.li,{children:"New Agent Approach creation screen:"}),"\n"]}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.img,{alt:"lead_create_agent_form.png",src:t(1052).A+"",width:"2932",height:"1538"})})]})}function h(e={}){const{wrapper:n}={...(0,s.R)(),...e.components};return n?(0,a.jsx)(n,{...e,children:(0,a.jsx)(l,{...e})}):l(e)}},9234:(e,n,t)=>{t.d(n,{A:()=>a});const a=t.p+"assets/images/lead_create_agent-6034bcbccc73054b72a3854da6d1e2e7.gif"}}]);