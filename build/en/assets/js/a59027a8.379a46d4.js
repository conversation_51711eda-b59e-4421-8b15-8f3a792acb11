"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[36],{494:(n,e,t)=>{t.d(e,{R:()=>s,x:()=>r});var l=t(6372);const i={},c=l.createContext(i);function s(n){const e=l.useContext(c);return l.useMemo(function(){return"function"==typeof n?n(e):{...e,...n}},[e,n])}function r(n){let e;return e=n.disableParentContext?"function"==typeof n.components?n.components(i):n.components||i:s(n.components),l.createElement(c.Provider,{value:e},n.children)}},5673:(n,e,t)=>{t.r(e),t.d(e,{assets:()=>h,contentTitle:()=>s,default:()=>a,frontMatter:()=>c,metadata:()=>r,toc:()=>d});var l=t(216),i=t(494);const c={sidebar_position:1,hide_table_of_contents:!0,displayed_sidebar:"developerSidebar"},s="DataTP CRM Backlog",r={id:"datatp-crm/developer/BACKLOG",title:"DataTP CRM Backlog",description:"Tasks",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/datatp-crm/developer/BACKLOG.md",sourceDirName:"datatp-crm/developer",slug:"/datatp-crm/developer/BACKLOG",permalink:"/en/docs/datatp-crm/developer/BACKLOG",draft:!1,unlisted:!1,tags:[],version:"current",sidebarPosition:1,frontMatter:{sidebar_position:1,hide_table_of_contents:!0,displayed_sidebar:"developerSidebar"},sidebar:"developerSidebar",next:{title:"Changelog",permalink:"/en/docs/datatp-crm/developer/CHANGELOG"}},h={},d=[{value:"Tasks",id:"tasks",level:2},{value:"Current Sprint",id:"current-sprint",level:2},{value:"Backlog",id:"backlog",level:2}];function o(n){const e={em:"em",h1:"h1",h2:"h2",header:"header",li:"li",ol:"ol",p:"p",strong:"strong",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,i.R)(),...n.components};return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(e.header,{children:(0,l.jsx)(e.h1,{id:"datatp-crm-backlog",children:"DataTP CRM Backlog"})}),"\n",(0,l.jsx)(e.h2,{id:"tasks",children:"Tasks"}),"\n",(0,l.jsxs)(e.table,{children:[(0,l.jsx)(e.thead,{children:(0,l.jsxs)(e.tr,{children:[(0,l.jsx)(e.th,{style:{textAlign:"left"},children:"Category"}),(0,l.jsx)(e.th,{style:{textAlign:"left"},children:"Title"}),(0,l.jsx)(e.th,{style:{textAlign:"left"},children:"Status"}),(0,l.jsx)(e.th,{style:{textAlign:"left"},children:"Timeline"}),(0,l.jsx)(e.th,{style:{textAlign:"left"},children:"PIC"})]})}),(0,l.jsxs)(e.tbody,{children:[(0,l.jsxs)(e.tr,{children:[(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"Feature"}),(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"Task 1"}),(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"In Progress"}),(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"2025-06-20"}),(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"Dan"})]}),(0,l.jsxs)(e.tr,{children:[(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"Bug"}),(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"Fix issue"}),(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"To Do"}),(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"2025-06-25"}),(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"Nhat"})]}),(0,l.jsxs)(e.tr,{children:[(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"Enhancement"}),(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"Improve UI"}),(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"Done"}),(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"2025-06-15"}),(0,l.jsx)(e.td,{style:{textAlign:"left"},children:"An"})]})]})]}),"\n",(0,l.jsx)(e.p,{children:(0,l.jsx)(e.strong,{children:"Sprint Rules:"})}),"\n",(0,l.jsxs)(e.ul,{children:["\n",(0,l.jsx)(e.li,{children:"Sprint duration: 1 week (Monday-Sunday)"}),"\n",(0,l.jsx)(e.li,{children:"Daily status updates required"}),"\n",(0,l.jsx)(e.li,{children:"Incomplete tasks return to backlog for next sprint prioritization"}),"\n"]}),"\n",(0,l.jsx)(e.h2,{id:"current-sprint",children:"Current Sprint"}),"\n",(0,l.jsx)(e.p,{children:(0,l.jsx)(e.em,{children:"Tasks planned for current week. Incomplete tasks will return to backlog at the end of the sprint."})}),"\n",(0,l.jsxs)(e.ol,{children:["\n",(0,l.jsx)(e.li,{children:"[An] #pricing - Company Pricing Dashboard:"}),"\n"]}),"\n",(0,l.jsxs)(e.ul,{children:["\n",(0,l.jsx)(e.li,{children:"ch\u1ee9c n\u0103ng l\u1ecdc theo Type Of Service cho to\xe0n b\u1ed9 d\u1eef li\u1ec7u, cho Top Route Performance."}),"\n",(0,l.jsx)(e.li,{children:"export excel t\u1eebng section/ b\u1ea3ng."}),"\n"]}),"\n",(0,l.jsxs)(e.ol,{start:"2",children:["\n",(0,l.jsxs)(e.li,{children:["\n",(0,l.jsx)(e.p,{children:"[An] C\u1eadp nh\u1eadt th\xf4ng tin c\xe1c tr\u01b0\u1eddng industry_sector (t\u1eeb excel), date_created, date_modified (t\u1eeb BFSOne) cho partner.\n-> C\u1eadp nh\u1eadt xong, vi\u1ebft cron \u0111\u1ec3 sync l\u1ea1i v\u1edbi h\u1ec7 th\u1ed1ng bee_legacy (b\u1ea3o \u0110\xe0n l\xe0m)"}),"\n"]}),"\n",(0,l.jsxs)(e.li,{children:["\n",(0,l.jsx)(e.p,{children:"[An] #partner - Update Partner Data:"}),"\n",(0,l.jsxs)(e.ul,{children:["\n",(0,l.jsx)(e.li,{children:"C\u1eadp nh\u1eadt th\xf4ng tin c\xe1c tr\u01b0\u1eddng industry_sector (t\u1eeb excel)"}),"\n",(0,l.jsx)(e.li,{children:"C\u1eadp nh\u1eadt date_created, date_modified (t\u1eeb BFSOne) cho partner."}),"\n",(0,l.jsx)(e.li,{children:"Vi\u1ebft cron \u0111\u1ec3 sync l\u1ea1i v\u1edbi h\u1ec7 th\u1ed1ng bee_legacy (b\u1ea3o \u0110\xe0n l\xe0m)"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,l.jsx)(e.h2,{id:"backlog",children:"Backlog"}),"\n",(0,l.jsx)(e.p,{children:(0,l.jsx)(e.em,{children:"Future tasks"})}),"\n",(0,l.jsxs)(e.ol,{children:["\n",(0,l.jsx)(e.li,{children:"Company Pricing Dashboard:"}),"\n"]}),"\n",(0,l.jsxs)(e.ul,{children:["\n",(0,l.jsx)(e.li,{children:"ch\u1ee9c n\u0103ng l\u1ecdc theo Type Of Service cho to\xe0n b\u1ed9 d\u1eef li\u1ec7u, cho Top Route Performance."}),"\n",(0,l.jsx)(e.li,{children:"export excel t\u1eebng section/ b\u1ea3ng."}),"\n"]}),"\n",(0,l.jsxs)(e.ol,{start:"2",children:["\n",(0,l.jsx)(e.li,{children:"Nghi\xean c\u1ee9u, ch\u1ec9nh l\u1ea1i shell script cho postgres-admin.sh"}),"\n"]}),"\n",(0,l.jsxs)(e.ul,{children:["\n",(0,l.jsx)(e.li,{children:"Cho ph\xe9p th\u1ef1c hi\u1ec7n c\xe1c l\u1ec7nh t\u1ea1o db user, db user readonly, new db, drop db, ... cho c\xe1c d\u1ef1 \xe1n v\u1eeba t\xe1ch."}),"\n"]}),"\n",(0,l.jsxs)(e.ol,{start:"3",children:["\n",(0,l.jsx)(e.li,{children:"Nghi\xean c\u1ee9u chuy\u1ec3n qua code java tr\xean VS Code."}),"\n",(0,l.jsx)(e.li,{children:"Enhance document web UI Framework."}),"\n"]}),"\n",(0,l.jsxs)(e.ul,{children:["\n",(0,l.jsx)(e.li,{children:"Nghi\xean c\u1ee9u \u0111\u1ec3 t\u1eadp trung docs/ guide tr\xean t\u1eebng d\u1ef1 \xe1n v\xe0 c\xf3 c\xe1ch \u0111\u1ec3 merge, collect c\xe1c docs v\xe0 build l\xean."}),"\n",(0,l.jsx)(e.li,{children:"T\u1ea1o khung, template, quy \u01b0\u1edbc folder chung cho t\u1eebng d\u1ef1 \xe1n."}),"\n",(0,l.jsx)(e.li,{children:"Ngo\xe0i docs (cho users) , th\xeam c\xe1c m\u1ee5c wiki (gi\u1ea3i th\xedch ch\u1ee9c n\u0103ng, nghi\u1ec7p v\u1ee5, thu\u1eadn ng\u1eef \u0111\u1ec3 cho dev t\xecm hi\u1ec3u ph\u1ea7n m\u1ec1m), changelog (l\u01b0u l\u1ecbch s\u1eed thay \u0111\u1ed5i theo c\xe1c phi\xean b\u1ea3n Released)"}),"\n"]}),"\n",(0,l.jsxs)(e.ol,{start:"5",children:["\n",(0,l.jsx)(e.li,{children:"X\u1eed l\xfd d\u1eef li\u1ec7u partners lost cho HCM"}),"\n"]}),"\n",(0,l.jsxs)(e.ul,{children:["\n",(0,l.jsx)(e.li,{children:"D\u1eef li\u1ec7u T\xe2m g\u1eedi d\u1ea1ng excel, g\u1ed3m c\xe1c kh\xe1ch h\xe0ng \u0111\xe3 l\xe2u ko ph\xe1t sinh booking. Y/c dev ki\u1ec3m tra/ check l\u1ea1i v\u1edbi h\u1ec7 th\u1ed1ng, n\u1ebfu kh\xf4ng ph\xe1t sinh booking trong 1 n\u0103m g\u1ea7n \u0111\xe2y => Import v\xe0o CRM."}),"\n"]}),"\n",(0,l.jsxs)(e.ol,{start:"6",children:["\n",(0,l.jsx)(e.li,{children:"Theo d\xf5i c\xf4ng vi\u1ec7c h\xe0ng xu\u1ea5t."}),"\n"]}),"\n",(0,l.jsxs)(e.ul,{children:["\n",(0,l.jsx)(e.li,{children:"Mail confirm gi\xe1 (gi\xe1 c\xf3 s\u1eb5n tr\xean b\u1ea3ng gi\xe1 - t\xednh n\u0103ng cho ph\xe9p sales g\u1eedi mail \u0111\u1ec3 confirm v\u1edbi pricing v\u1ec1 gi\xe1 \u0111\u1ec3 b\xe1o l\u1ea1i v\u1edbi kh\xe1ch h\xe0ng )\n(C\xe1c quy tr\xecnh, tham kh\u1ea3o h\xe0ng nh\u1eadp \u0111ang l\xe0m)"}),"\n"]}),"\n",(0,l.jsxs)(e.ol,{start:"7",children:["\n",(0,l.jsx)(e.li,{children:"#sale_dashboard Enhance b\xe1o c\xe1o Saleman Activities Tracker - Mrs. Minh Sales HPH"}),"\n"]}),"\n",(0,l.jsxs)(e.ul,{children:["\n",(0,l.jsx)(e.li,{children:"C\u1ed1 \u0111\u1ecbnh row header v\xe0 c\u1ed9t \u0111\u1ea7u ti\xean."}),"\n",(0,l.jsx)(e.li,{children:"Ki\u1ec3m tra l\u1ea1i ch\u1ec9 s\u1ed1 Kh\xe1ch h\xe0ng m\u1edbi/ lead m\u1edbi."}),"\n",(0,l.jsx)(e.li,{children:"Th\xeam c\u1ed9t s\u1ed1 quotation \u0111\u01b0\u1ee3c t\u1ea1o theo t\u1eebng sales."}),"\n",(0,l.jsx)(e.li,{children:"S\u1eafp x\u1ebfp th\u1ee9 t\u1ef1 c\xe1c c\u1ed9t"}),"\n"]}),"\n",(0,l.jsxs)(e.ol,{start:"8",children:["\n",(0,l.jsx)(e.li,{children:"Review to\xe0n b\u1ed9 query tr\xean module CRM (chu\u1ea9n b\u1ecb cho vi\u1ec7c t\xe1ch database)"}),"\n",(0,l.jsx)(e.li,{children:"C\u1eadp nh\u1eadt th\xf4ng tin c\xe1c tr\u01b0\u1eddng industry_sector (t\u1eeb excel), date_created, date_modified (t\u1eeb BFSOne) cho partner.\n-> C\u1eadp nh\u1eadt xong, vi\u1ebft cron \u0111\u1ec3 sync l\u1ea1i v\u1edbi h\u1ec7 th\u1ed1ng bee_legacy (b\u1ea3o \u0110\xe0n l\xe0m)"}),"\n",(0,l.jsx)(e.li,{children:"#sale_daily_tasks: [Mrs Minh Sale HPH] - create xong, th\xec a k tich v\xe0o c\xe1i send zalo \u0111\u01b0\u1ee3c n\u1eefa.\nEnhance: cho ph\xe9p sales set/ ch\u1ec9nh gi\u1edd th\xf4ng b\xe1o sau khi tasks \u0111\u01b0\u1ee3c t\u1ea1o. Hi\u1ec7n t\u1ea1i ch\u1ec9 set \u0111\u01b0\u1ee3c l\xfac t\u1ea1o, t\u1ea1o xong b\u1ecb lock v\xe0 kh\xf4ng cho set.\nM\u1ed7i khi update tasks, check notification time n\u1ebfu > now th\xec schedule message \u0111\u1ec3 g\u1eedi cho ng\u01b0\u1eddi d\xf9ng."}),"\n"]})]})}function a(n={}){const{wrapper:e}={...(0,i.R)(),...n.components};return e?(0,l.jsx)(e,{...n,children:(0,l.jsx)(o,{...n})}):o(n)}}}]);