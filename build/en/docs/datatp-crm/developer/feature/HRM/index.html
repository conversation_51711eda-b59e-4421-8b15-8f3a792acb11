<!doctype html>
<html lang="en-GB" dir="ltr" class="docs-wrapper plugin-docs plugin-id-default docs-version-current docs-doc-page docs-doc-id-datatp-crm/developer/feature/HRM" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.5.2">
<title data-rh="true">HRM, Workflow, Project Features. | DataTP Cloud Documentation</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:url" content="https://docs.beelogistics.cloud/en/docs/datatp-crm/developer/feature/HRM"><meta data-rh="true" property="og:locale" content="en_GB"><meta data-rh="true" property="og:locale:alternate" content="vi_VN"><meta data-rh="true" name="docusaurus_locale" content="en"><meta data-rh="true" name="docsearch:language" content="en"><meta data-rh="true" name="docusaurus_version" content="current"><meta data-rh="true" name="docusaurus_tag" content="docs-default-current"><meta data-rh="true" name="docsearch:version" content="current"><meta data-rh="true" name="docsearch:docusaurus_tag" content="docs-default-current"><meta data-rh="true" property="og:title" content="HRM, Workflow, Project Features. | DataTP Cloud Documentation"><meta data-rh="true" name="description" content="1. Task Request."><meta data-rh="true" property="og:description" content="1. Task Request."><link data-rh="true" rel="icon" href="/en/img/favicon.ico"><link data-rh="true" rel="canonical" href="https://docs.beelogistics.cloud/en/docs/datatp-crm/developer/feature/HRM"><link data-rh="true" rel="alternate" href="https://docs.beelogistics.cloud/docs/datatp-crm/developer/feature/HRM" hreflang="vi-VN"><link data-rh="true" rel="alternate" href="https://docs.beelogistics.cloud/en/docs/datatp-crm/developer/feature/HRM" hreflang="en-GB"><link data-rh="true" rel="alternate" href="https://docs.beelogistics.cloud/docs/datatp-crm/developer/feature/HRM" hreflang="x-default"><link rel="stylesheet" href="/en/assets/css/styles.a320300f.css">
<script src="/en/assets/js/runtime~main.ad289977.js" defer="defer"></script>
<script src="/en/assets/js/main.a898494c.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<script>!function(){function t(t){document.documentElement.setAttribute("data-theme",t)}var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();t(null!==e?e:"light")}(),function(){try{const n=new URLSearchParams(window.location.search).entries();for(var[t,e]of n)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><div role="region" aria-label="Skip to main content"><a class="skipToContent_sO_d" href="#__docusaurus_skipToContent_fallback">Skip to main content</a></div><nav aria-label="Main" class="navbar navbar--fixed-top"><div class="navbar__inner"><div class="navbar__items"><button aria-label="Toggle navigation bar" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/en/"><div class="navbar__logo"><img src="/en/img/logo.png" alt="DataTP Cloud Logo" class="themedComponent_ybjk themedComponent--light_zoQ6"><img src="/en/img/logo.png" alt="DataTP Cloud Logo" class="themedComponent_ybjk themedComponent--dark_iuml"></div></a><a class="navbar__item navbar__link" href="/en/docs/shared/user/system">User Guides<!-- --></a><a class="navbar__item navbar__link" href="/en/docs/shared/developer/SETUP">Developer<!-- --></a></div><div class="navbar__items navbar__items--right"><div class="navbar__item dropdown dropdown--hoverable dropdown--right"><a href="#" aria-haspopup="true" aria-expanded="false" role="button" class="navbar__link"><svg viewBox="0 0 24 24" width="20" height="20" aria-hidden="true" class="iconLanguage_M5xz"><path fill="currentColor" d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"></path></svg>English<!-- --></a><ul class="dropdown__menu"><li><a href="/docs/datatp-crm/developer/feature/HRM" target="_self" rel="noopener noreferrer" class="dropdown__link" lang="vi-VN">Tiếng Việt<!-- --></a></li><li><a href="/en/docs/datatp-crm/developer/feature/HRM" target="_self" rel="noopener noreferrer" class="dropdown__link dropdown__link--active" lang="en-GB">English<!-- --></a></li></ul></div><div class="toggle_vwHj colorModeToggle_BRnA"><button class="clean-btn toggleButton_pCMA toggleButtonDisabled_e4z_" type="button" disabled="" title="Switch between dark and light mode (currently light mode)" aria-label="Switch between dark and light mode (currently light mode)" aria-live="polite"><svg viewBox="0 0 24 24" width="24" height="24" class="lightToggleIcon_O8M2"><path fill="currentColor" d="M12,9c1.65,0,3,1.35,3,3s-1.35,3-3,3s-3-1.35-3-3S10.35,9,12,9 M12,7c-2.76,0-5,2.24-5,5s2.24,5,5,5s5-2.24,5-5 S14.76,7,12,7L12,7z M2,13l2,0c0.55,0,1-0.45,1-1s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S1.45,13,2,13z M20,13l2,0c0.55,0,1-0.45,1-1 s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S19.45,13,20,13z M11,2v2c0,0.55,0.45,1,1,1s1-0.45,1-1V2c0-0.55-0.45-1-1-1S11,1.45,11,2z M11,20v2c0,0.55,0.45,1,1,1s1-0.45,1-1v-2c0-0.55-0.45-1-1-1C11.45,19,11,19.45,11,20z M5.99,4.58c-0.39-0.39-1.03-0.39-1.41,0 c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0s0.39-1.03,0-1.41L5.99,4.58z M18.36,16.95 c-0.39-0.39-1.03-0.39-1.41,0c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0c0.39-0.39,0.39-1.03,0-1.41 L18.36,16.95z M19.42,5.99c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06c-0.39,0.39-0.39,1.03,0,1.41 s1.03,0.39,1.41,0L19.42,5.99z M7.05,18.36c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06 c-0.39,0.39-0.39,1.03,0,1.41s1.03,0.39,1.41,0L7.05,18.36z"></path></svg><svg viewBox="0 0 24 24" width="24" height="24" class="darkToggleIcon_hKRZ"><path fill="currentColor" d="M9.37,5.51C9.19,6.15,9.1,6.82,9.1,7.5c0,4.08,3.32,7.4,7.4,7.4c0.68,0,1.35-0.09,1.99-0.27C17.45,17.19,14.93,19,12,19 c-3.86,0-7-3.14-7-7C5,9.07,6.81,6.55,9.37,5.51z M12,3c-4.97,0-9,4.03-9,9s4.03,9,9,9s9-4.03,9-9c0-0.46-0.04-0.92-0.1-1.36 c-0.98,1.37-2.58,2.26-4.4,2.26c-2.98,0-5.4-2.42-5.4-5.4c0-1.81,0.89-3.42,2.26-4.4C12.92,3.04,12.46,3,12,3L12,3z"></path></svg></button></div><div class="navbarSearchContainer_zC86"></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="main-wrapper mainWrapper_btEJ"><div class="docsWrapper_LEG8"><button aria-label="Scroll back to top" class="clean-btn theme-back-to-top-button backToTopButton_yO2w" type="button"></button><div class="docRoot_QXfX"><aside class="theme-doc-sidebar-container docSidebarContainer_YfKm"><div class="sidebarViewport_kETH"><div class="sidebar_Jdpv"><nav aria-label="Docs sidebar" class="menu thin-scrollbar menu_kjpJ"><ul class="theme-doc-sidebar-menu menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/en/docs/shared/developer/SETUP">Setup<!-- --></a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/en/docs/shared/developer/CHANGELOG">Changelog<!-- --></a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/en/docs/shared/developer/BACKLOG">Backlog<!-- --></a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret menu__link--active" role="button" aria-expanded="true" href="/en/docs/datatp-crm/developer/BACKLOG">CRM</a></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/en/docs/datatp-crm/developer/BACKLOG">DataTP CRM Backlog<!-- --></a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/en/docs/datatp-crm/developer/CHANGELOG">Changelog<!-- --></a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/en/docs/datatp-crm/developer/crm_flow">CRM Flowchart<!-- --></a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret menu__link--active" role="button" aria-expanded="true" tabindex="0" href="/en/docs/datatp-crm/developer/feature/crm">feature</a></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-3 menu__list-item"><a class="menu__link" tabindex="0" href="/en/docs/datatp-crm/developer/feature/crm">CRM Features<!-- --></a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-3 menu__list-item"><a class="menu__link menu__link--active" aria-current="page" tabindex="0" href="/en/docs/datatp-crm/developer/feature/HRM">HRM, Workflow, Project Features.<!-- --></a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-3 menu__list-item"><a class="menu__link" tabindex="0" href="/en/docs/datatp-crm/developer/feature/MAIL-TICKET">MAIL-TICKET<!-- --></a></li></ul></li></ul></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" href="/en/docs/document-ie/developer/CHANGELOG">Document IE</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" href="/en/docs/datatp-hr/developer/CHANGELOG">HR</a></div></li></ul></nav><button type="button" title="Collapse sidebar" aria-label="Collapse sidebar" class="button button--secondary button--outline collapseSidebarButton_Vuw3"><svg width="20" height="20" aria-hidden="true" class="collapseSidebarButtonIcon_Vp19"><g fill="#7a7a7a"><path d="M9.992 10.023c0 .2-.062.399-.172.547l-4.996 7.492a.982.982 0 01-.828.454H1c-.55 0-1-.453-1-1 0-.2.059-.403.168-.551l4.629-6.942L.168 3.078A.939.939 0 010 2.528c0-.548.45-.997 1-.997h2.996c.352 0 .649.18.828.45L9.82 9.472c.11.148.172.347.172.55zm0 0"></path><path d="M19.98 10.023c0 .2-.058.399-.168.547l-4.996 7.492a.987.987 0 01-.828.454h-3c-.547 0-.996-.453-.996-1 0-.2.059-.403.168-.551l4.625-6.942-4.625-6.945a.939.939 0 01-.168-.55 1 1 0 01.996-.997h3c.348 0 .649.18.828.45l4.996 7.492c.11.148.168.347.168.55zm0 0"></path></g></svg></button></div></div></aside><main class="docMainContainer_hcuh"><div class="container padding-top--md padding-bottom--lg"><div class="row"><div class="col docItemCol_F4eA"><div class="docItemContainer_jkBE"><article><nav class="theme-doc-breadcrumbs breadcrumbsContainer_ON7I" aria-label="Breadcrumbs"><ul class="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><li class="breadcrumbs__item"><a aria-label="Home page" class="breadcrumbs__link" href="/en/"><svg viewBox="0 0 24 24" class="breadcrumbHomeIcon_H14Z"><path d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z" fill="currentColor"></path></svg></a></li><li class="breadcrumbs__item"><span class="breadcrumbs__link">CRM</span><meta itemprop="position" content="1"></li><li class="breadcrumbs__item"><span class="breadcrumbs__link">feature</span><meta itemprop="position" content="2"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item breadcrumbs__item--active"><span class="breadcrumbs__link" itemprop="name">HRM, Workflow, Project Features.</span><meta itemprop="position" content="3"></li></ul></nav><div class="tocCollapsible_zskR theme-doc-toc-mobile tocMobile_v3PZ"><button type="button" class="clean-btn tocCollapsibleButton_Pxai">On this page<!-- --></button></div><div class="theme-doc-markdown markdown"><header><h1>HRM, Workflow, Project Features.</h1></header>
<!-- --><h3 class="anchor anchorWithStickyNavbar_Sb3Y" id="1-task-request">1. Task Request.<!-- --><a href="#1-task-request" class="hash-link" aria-label="Direct link to 1. Task Request." title="Direct link to 1. Task Request.">​</a></h3>
<!-- --><ul>
<!-- --><li>Người triển khai: Đàn, An, Nhật.</li>
<!-- --><li>Người request: chị Quyên, Tấm.</li>
<!-- --></ul>
<!-- --><ol>
<!-- --><li>
<!-- --><p>Đặt vấn đề:</p>
<!-- --><div class="codeBlockContainer_lHyG theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_o3WU"><pre tabindex="0" class="prism-code language-text codeBlock_SsTB thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_GnfZ"><span class="token-line" style="color:#393A34"><span class="token plain">Tại chỗ team kế toán muốn xây dựng cái request lỗi như kiểu mình đki phép hay duyệt ngày công ấy, để các bộ phận nếu phát sinh lỗi thì sẽ request trên phần mềm, kế toán sẽ căn cứ vào đó ngoài việc xử lý phát sinh, cũng sẽ là tổng hợp để cuối tháng report và tính vào lương hiệu quả sau này.</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">Những trường thông tin cần để nhận request cơ bản trước.</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  1. Tên ngưởi request.</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  2. Bộ phận/ VP Vệ Tinh.</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  3. Số file</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  4. loại request (mở file, sửa hoá đơn ...);</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  5. lý do request (text) ...</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  6. Tên dịch vụ/ khách hàng.</span><br></span></code></pre><div class="buttonGroup_bRv8"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_U7Rx" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_RLFU"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_aQBr"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><p>Hiện tại, các yêu cầu như sửa hóa đơn, hoặc mở file được trao đổi qua email hoặc nhóm chat (Zalo, Teams), dẫn đến:</p>
<!-- --><p>Hạn chế:
Quá tải thông tin trong nhóm chat, khó theo dõi lịch sử yêu cầu, khó tính KPI.
Thiếu giao diện tổng quan để xem trạng thái, loại yêu cầu, hoặc quy trình phê duyệt.
Không có thông báo tự động khi trạng thái yêu cầu thay đổi (ví dụ: từ &quot;New&quot; sang &quot;Approved&quot;).
Khó tích hợp với các phân hệ khác như HRM, Kế toán.</p>
<!-- --></li>
<!-- --><li>
<!-- --><p>Giải pháp:</p>
<!-- --><ul>
<!-- --><li>Quản lý Quy trình (Workflow Management)</li>
<!-- --><li>Nghiệp vụ liên quan:<!-- -->
<!-- --><ul>
<!-- --><li>Request mở file cập nhật thông tin.</li>
<!-- --><li>Request chỉnh sửa hoá đơn, settle, ...</li>
<!-- --><li>Request mua trang thiết bị.</li>
<!-- --><li>Request tăng ca, nghỉ phép, ...</li>
<!-- --></ul>
<!-- --></li>
<!-- --><li>Chi tiết:
định nghĩa các bước phê duyệt hoặc xử lý cho từng loại yêu cầu.<!-- -->
<!-- --><div class="codeBlockContainer_lHyG theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_o3WU"><pre tabindex="0" class="prism-code language-text codeBlock_SsTB thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_GnfZ"><span class="token-line" style="color:#393A34"><span class="token plain">- Tạo màn hình riêng cho Request Management.</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">- Định nghĩa các loại request (mở file, sửa hoá đơn, ...).</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">- Định nghĩa các trạng thái của request (new, completed, rejected, ...).</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">- Định nghĩa các vai trò tham gia (requestor, assignee, approver, ...)</span><br></span></code></pre><div class="buttonGroup_bRv8"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_U7Rx" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_RLFU"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_aQBr"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><ul>
<!-- --><li>Gửi request -&gt; Gửi mail người liên quan -&gt; Completed/ Rejected.</li>
<!-- --><li>Phân quyền/ Scope:<!-- -->
<!-- --><ul>
<!-- --><li>Requestor: User tạo request.</li>
<!-- --><li>Approver: User được giao việc xử lý request.</li>
<!-- --><li>Observer: User được phép xem request.</li>
<!-- --><li>Data Scope: Màn hình Requests có thể nhóm theo từng bộ phận (tree) hoặc không (dạng bảng).</li>
<!-- --><li>Cùng bộ phận có thể xem requests của nhau, người tham gia đều xem được request.</li>
<!-- --></ul>
<!-- --></li>
<!-- --></ul>
<!-- --></li>
<!-- --></ul>
<!-- --></li>
<!-- --><li>
<!-- --><p>Tasks:</p>
<!-- --><ul>
<!-- --><li>[Dan] - Design code, chuẩn bị code, QA.</li>
<!-- --><li>[Nhat] - Function gửi mail:
Khi tạo request -&gt; Gửi mail cho người liên quan -&gt; Approved/ Rejected qua mail -&gt;
hiển thị màn hình thông báo cho người dùng. -&gt; Gửi kết quả Approved/Rejected qua mail cho người liên quan.</li>
<!-- --><li>[An] - Implement code, Backend, Frontend.</li>
<!-- --></ul>
<!-- --></li>
<!-- --></ol></div></article><nav class="pagination-nav docusaurus-mt-lg" aria-label="Docs pages"><a class="pagination-nav__link pagination-nav__link--prev" href="/en/docs/datatp-crm/developer/feature/crm"><div class="pagination-nav__sublabel">Previous<!-- --></div><div class="pagination-nav__label">CRM Features</div></a><a class="pagination-nav__link pagination-nav__link--next" href="/en/docs/datatp-crm/developer/feature/MAIL-TICKET"><div class="pagination-nav__sublabel">Next<!-- --></div><div class="pagination-nav__label">MAIL-TICKET</div></a></nav></div></div><div class="col col--3"><div class="tableOfContents_mBHc thin-scrollbar theme-doc-toc-desktop"><ul class="table-of-contents table-of-contents__left-border"><li><a href="#1-task-request" class="table-of-contents__link toc-highlight">1. Task Request.</a></li></ul></div></div></div></div></main></div></div></div><footer class="footer footer--dark"><div class="container container-fluid"><div class="footer__bottom text--center"><div class="footer__copyright">Copyright © 2025 DataTP Cloud.</div></div></div></footer></div>
</body>
</html>