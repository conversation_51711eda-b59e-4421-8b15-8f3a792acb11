package migration.server.crm

import cloud.datatp.fforwarder.core.common.ChargeType
import cloud.datatp.fforwarder.sales.booking.BookingLogic
import cloud.datatp.fforwarder.sales.booking.entity.Booking
import lib.data.RunnableReporter
import lib.data.ServiceRunnable
import lib.data.ServiceRunnableSet
import net.datatp.module.company.entity.CompanyConfig
import net.datatp.module.data.db.ExternalDataSourceManager
import net.datatp.module.data.db.SqlMapRecord
import net.datatp.module.data.db.SqlQueryManager
import net.datatp.module.data.db.SqlSelectView
import net.datatp.module.data.db.entity.ICompany
import net.datatp.module.data.db.util.DBConnectionUtil
import net.datatp.module.data.db.util.DeleteGraphBuilder
import net.datatp.module.groovy.ServerScriptContext
import net.datatp.security.client.ClientContext
import net.datatp.util.dataformat.DataSerializer
import net.datatp.util.ds.MapObject
import net.datatp.util.text.StringUtil

import javax.sql.DataSource
import java.util.stream.Collectors

public class DeleteBookingSet extends ServiceRunnableSet {
    public DeleteBookingSet() {
        super("""Delete Booking""");

        ServiceRunnable dataInit = new ServiceRunnable("Delete booking") {
            @Override
            public void run(RunnableReporter reporter, ServerScriptContext scriptCtx) {
                BookingLogic bookingLogic = scriptCtx.getService(BookingLogic.class);
                ClientContext client = scriptCtx.getClientCtx();
                ICompany company = client.getCompany();
                SqlQueryManager sqlQueryManager = scriptCtx.getService(SqlQueryManager.class);
                DataSource ds = bookingLogic.getCrmDataSource();

                final String dataDir = scriptCtx.getDataDir();
                String SCRIPT_DIR = dataDir + "/crm/sql";
                String fileName = "SelectBooking.groovy";
                SqlQueryManager.QueryContext queryContext = sqlQueryManager.create(SCRIPT_DIR, fileName);
                Binding binding = new Binding();
                SqlSelectView view = queryContext.createSqlSelectView(ds, binding);
                List<SqlMapRecord> records = view.getSqlMapRecords();
                List<Long> bookingIds = records.stream()
                        .map(record -> record.getLong("id", null))
                        .collect(Collectors.toList());
                for (Long bookingId : bookingIds) {
                    Booking booking = bookingLogic.getBooking(client, company, bookingId);
                    if (booking == null) continue;
                    if (booking.getChargeId() != null) {
                        if (booking.getChargeType() == ChargeType.SEA) bookingLogic.deleteSeaCharges(client, booking.getChargeId());
                        else if (booking.getChargeType() == ChargeType.AIR) bookingLogic.deleteAirCharges(client, booking.getChargeId());
                    }
                }
                DBConnectionUtil connectionUtil = new DBConnectionUtil(bookingLogic.getCrmDataSource());
                DeleteGraphBuilder deleteGraphBuilder = new DeleteGraphBuilder(connectionUtil, company.getId(), Booking.class, bookingIds);
                int count = deleteGraphBuilder.runDelete();
                connectionUtil.commit();
                connectionUtil.close();
            }
        };

        addRunnable(dataInit);
    }

}

ServerScriptContext scriptCtx = (ServerScriptContext) CONTEXT;
RunnableReporter reporter = new RunnableReporter("service", "V1_0_0_2024-10-09")
DeleteBookingSet migration = new DeleteBookingSet();
migration.run(reporter, scriptCtx);