package migration.server.hr


import javax.sql.DataSource

import org.slf4j.Logger
import org.slf4j.LoggerFactory

import lib.data.RunnableReporter
import lib.data.ServiceRunnable
import lib.data.ServiceRunnableSet
import net.datatp.module.account.AccountLogic
import net.datatp.module.account.entity.Account
import net.datatp.module.communication.CommunicationMessageLogic
import net.datatp.module.communication.CommunicationMessageService
import net.datatp.module.communication.entity.Message
import net.datatp.module.communication.entity.MessageDeliverType
import net.datatp.module.communication.entity.TargetRecipient
import net.datatp.module.data.db.SqlMapRecord
import net.datatp.module.data.db.SqlQueryManager
import net.datatp.module.data.db.SqlSelectView
import net.datatp.module.groovy.ServerScriptContext
import net.datatp.security.client.ClientContext
import net.datatp.util.dataformat.DataSerializer
import net.datatp.util.text.StringUtil

class SyncBFSOneUsername extends ServiceRunnableSet {
    private static final Logger log = LoggerFactory.getLogger(SyncBFSOneUsername.class);
    private static String label = "------------Sync BFSUsername for Account LoginId --------------"

    public SyncBFSOneUsername() {
      super(label);
      ServiceRunnable syncService = new ServiceRunnable(label) {
          @Override
          public void run(RunnableReporter reporter, ServerScriptContext scriptCtx) {
            ClientContext client = scriptCtx.getClientCtx();
            AccountLogic accountLogic = scriptCtx.getService(AccountLogic.class);
            
            final String dataDir = scriptCtx.getDataDir();
            String SCRIPT_DIR = dataDir + "/hr/sql";
            String fileName = "SearchAccountWithBFSOneUsernameExists.groovy";
            DataSource ds = accountLogic.getPrimaryDataSource();
            SqlQueryManager.QueryContext queryContext = accountLogic.getSqlQueryManager().create(SCRIPT_DIR, fileName);
            Binding binding = new Binding();
            SqlSelectView view = queryContext.createSqlSelectView(ds, binding);
            List<SqlMapRecord> accountsWithBFSOneUsername = view.renameColumWithJavaConvention().getSqlMapRecords();
            
            List<NotiModel> notiModels = new ArrayList();
            
            String[] ignoreLoginIds = [
              "tony.nguyen",
              "dan",
              "helena.vnsgn",
              "kelly.vnsgn",
              "<EMAIL>",
              "<EMAIL>",
              "<EMAIL>",
              "<EMAIL>",
              "airexp.vndad",
              "fcltrucking.north",
              "project.north",
              "lcltrucking.north",
              "expconsol.vnhph",
              "rs.baskkaran",
              "miamia.vnsgn",
              "kristina.vnsgn",
              "tts.sales",
              "tts.sales1",
              "tt1.procurement.vnhph",
              "airexp.vnhan",
              "seaexp.vnhan",
              "<EMAIL>",
              "alina",
              "vinhpnq"
            ] as String[];
            
            List<String> usernameUpdateFails = new ArrayList();
            List<String> loginIdUpdateFails = new ArrayList();
            
            int batchSize = 10;
            
//            for (int i = 0; i < accountsWithBFSOneUsername.size(); i += batchSize) {
//              int endIndex = Math.min(i + batchSize, accountsWithBFSOneUsername.size());
//              List<SqlMapRecord> batch = accountsWithBFSOneUsername.subList(i, endIndex);
//              
//              log.info("Processing batch {}/{}: {} records",
//                (i / batchSize) + 1,
//                (int) Math.ceil(accountsWithBFSOneUsername.size() / (double) batchSize),
//                batch.size()
//              );
//              
//              for (SqlMapRecord record : batch) {
//                String bfsUsername = record.getString("bfsoneUsername");
//                if (StringUtil.isEmpty(bfsUsername)) continue;
//                
//                String oldLoginId = record.getString("loginId");
//                if (ignoreLoginIds.contains(oldLoginId)) continue;
//                
//                String email = record.getString("email");
//                
//                try {
////                  Account account = accountLogic.getAccountByLoginId(client, oldLoginId);
////                  account.setLoginId(bfsUsername);
////                  
////                  account.set(client);
////                  accountLogic.getAccountRepo().save(account);
//                  
//                  notiModels.add(new NotiModel(email, oldLoginId, bfsUsername));
//                } catch (e) {
//                  usernameUpdateFails.add(bfsUsername);
//                  loginIdUpdateFails.add(oldLoginId);
//                }
//              }
//            }
            Account account = accountLogic.getAccountByLoginId(client, "nhat.le");
            account.setLoginId("qngnhat");

            account.set(client);
            accountLogic.getAccountRepo().save(account);
            notiModels.add(new NotiModel("<EMAIL>", "qngnhat"));
            
//            println "TRY TO UPDATE " + accountsWithBFSOneUsername.size();
//            println "IGNORED " + (accountsWithBFSOneUsername.size() - notiModels.size());
            println "UPDATED " + notiModels.size()
            println "FAILED " + loginIdUpdateFails.size();
            
            DataSerializer.JSON.dump(usernameUpdateFails)
            DataSerializer.JSON.dump(loginIdUpdateFails)
            
            sendNotificationToUser(scriptCtx, notiModels);
          }
          
          private void sendNotificationToUser(ServerScriptContext scriptCtx, List<NotiModel> notiModels) {
            ClientContext client = scriptCtx.getClientCtx();
            CommunicationMessageService messageService = scriptCtx.getService(CommunicationMessageService.class);
            AccountLogic accountLogic = scriptCtx.getService(AccountLogic.class);
            
            Account senderAccount = accountLogic.getAccountByEmail(client, "<EMAIL>");
            
            for (NotiModel model : notiModels) {
              Message message = new Message(senderAccount.getId());
              message.setSubject("[Quan trọng] Thay đổi thông tin đăng nhập");
              
              String content = """
                <div style="font-family: Arial, sans-serif; max-width: 600px; padding: 20px;">
                  <p style="font-size: 16px; color: #333; margin-bottom: 20px;">
                    Xin chào,
                  </p>

                  <p style="font-size: 16px; color: #333; line-height: 1.6; margin-bottom: 20px;">
                    Để đạt được sự nhất quán trong phương thức đăng nhập của người dùng giữa 2 hệ thống DataTP Cloud và BFSOne.
                  </p>
                  <p style="font-size: 16px; color: #333; line-height: 1.6; margin-bottom: 20px;">
                    Chúng tôi đã cập nhật thông tin đăng nhập trên hệ thống DataTP Cloud giống với thông tin đăng nhập của bạn trên hệ thống BFSOne.
                  </p>
                  <p style="font-size: 16px; color: #333; line-height: 1.6; margin-bottom: 20px;">
                    Mật khẩu đăng nhập của hệ thông DataTP Cloud vẫn được giữ nguyên.
                  </p>
                  <p style="font-size: 16px; color: #333; line-height: 1.6; margin-bottom: 20px;">
                    Dưới đây là thông tin đăng nhập mới của bạn trên hệ thống <span style="color: #0066cc; font-weight: bold; background-color: #f0f7ff; padding: 2px 5px; border-radius: 3px;">DATATP Cloud</span>:
                  </p>
            
                  <div style="background-color: #f5f5f5; border-left: 4px solid #ffd700; padding: 20px; margin: 20px 0;">
                    <p style="margin: 0; font-size: 16px;">
                      <strong style="color: #333;">Tên đăng nhập:</strong>
                      <span style="color: #0066cc; font-weight: bold;">${model.getNewLoginId()}</span>
                    </p>
                  </div>
            
                  <p style="font-size: 16px; color: #333; line-height: 1.6;">
                    Vui lòng truy cập <a href="https://beelogistics.cloud" style="color: #0066cc; text-decoration: none;">https://beelogistics.cloud</a> để kiểm tra lại thông tin đăng nhập tài khoản của bạn.
                  </p>
                  <p style="font-size: 16px; color: #333; line-height: 1.6; margin-bottom: 20px;">
                    Mọi thắc mắc xin gửi về bộ phận IT OF1: <EMAIL> hoặc <EMAIL>
                  </p>
                </div>
              """;
              message.setContent(content);
              TargetRecipient targetRecipient = new TargetRecipient(MessageDeliverType.Email, model.getEmail(), model.getEmail());
              targetRecipient.setForwardEmail(false);
              message.withRecipient(targetRecipient);
              
              messageService.sendMessage(client, client.getCompany(), message);
            }
          }
      };
      addRunnable(syncService);
    }
}

class NotiModel {
  String email;
  String newLoginId;
  
  NotiModel(String email, String newLoginId) {
    this.email = email;
    this.newLoginId = newLoginId;
  }
  
  String getEmail() { return this.email; }
  String getOldLoginId() { return oldLoginId; }
  String getNewLoginId() { return newLoginId; }
}

ServerScriptContext scriptCtx = (ServerScriptContext) CONTEXT;
RunnableReporter reporter = new RunnableReporter("service", "latest");

SyncBFSOneUsername runnableSet = new SyncBFSOneUsername();
runnableSet.run(reporter, scriptCtx);
return "DONE!!!";