package migration.db.hr

import java.sql.Connection

import jakarta.persistence.Column
import lib.data.DBMigrationRunnable
import lib.data.DBMigrationRunnableSet
import lib.data.RunnableReporter
import net.datatp.cli.ShellApplicationContext
import net.datatp.module.data.db.util.DBConnectionUtil


public class UpdateDataSet extends DBMigrationRunnableSet {

  public UpdateDataSet() {
    super("""Update Data HR""");

    DBMigrationRunnable crmMigration = new DBMigrationRunnable("""Update Data HR""") {
      @Override
      public void run(RunnableReporter reporter, DBConnectionUtil connUtil) {
        connUtil.execute("DELETE FROM account_account WHERE id IN (9730, 11552, 53585, 10961, 11706, 11893, 11927, 11579, 10977, 10980, 11401, 53647, 53553);");
        connUtil.execute("DELETE FROM company_hr_employee WHERE account_id IN (9730, 11552, 53585, 10961, 11706, 11893, 11927, 11579, 10977, 10980, 11401, 53647, 53553);");
      }
    };
    addRunnable(crmMigration);
  }
}

ShellApplicationContext shellContext = (ShellApplicationContext) SHELL_CONTEXT;
Connection conn = shellContext.getPrimaryDBConnection();
DBConnectionUtil connUtil = new DBConnectionUtil(conn);
RunnableReporter reporter  = new RunnableReporter("dbmigration", "latest")

UpdateDataSet runnableSet = new UpdateDataSet();
runnableSet.run(reporter, connUtil);

connUtil.close();
return "DONE!!!"