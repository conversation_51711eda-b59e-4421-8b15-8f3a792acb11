package hr.sql

def buildQuery() {
    String query = """
      SELECT DISTINCT ON (a.id) a.*, e.bfsone_username 
      FROM account_account a
      JOIN company_hr_employee e ON e.account_id = a.id
      WHERE a.login_id != e.bfsone_username OR LOWER(a.login_id) != LOWER(e.bfsone_username) AND a.email IS NOT NULL AND a.login_id IS NOT NULL AND e.bfsone_username IS NOT NULL;
    """;
    return query;
}

buildQuery();