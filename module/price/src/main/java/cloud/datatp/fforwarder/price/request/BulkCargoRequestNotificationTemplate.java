package cloud.datatp.fforwarder.price.request;

import cloud.datatp.fforwarder.price.common.BulkCargoShipmentDetail;
import cloud.datatp.fforwarder.price.entity.BulkCargoInquiryRequest;

public class BulkCargoRequestNotificationTemplate {

  public static String buildMailMessage(BulkCargoInquiryRequest request, String remoteUser, String reason) {
    BulkCargoShipmentDetail shipmentDetail = request.getShipmentDetail();

    return String.format("""
            <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 15px; background-color: #f9fafb;">
                <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <h1 style="color: #1f2937; font-size: 20px; margin: 0 0 16px 0; padding-bottom: 12px; border-bottom: 1px solid #e5e7eb;">
                        BULK CARGO INQUIRY REQUEST - REF: %s
                    </h1>
                    <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 16px 0;">
                        <p style="margin: 0 0 8px 0; color: #374151;">
                            <strong style="color: #1f2937;">👨‍💼 Sales:</strong> %s
                        </p>
                        <p style="margin: 0; color: #374151;">
                            <strong style="color: #1f2937;">📧 Email:</strong> %s
                        </p>
                    </div>
                    %s
                    <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 16px 0;">
                        <p style="margin: 0 0 8px 0; color: #374151;">
                            <strong style="color: #1f2937;">📋 Subject:</strong> %s
                        </p>
                        <p style="margin: 0 0 8px 0; color: #374151;">
                            <strong style="color: #1f2937;">💬 Feedback:</strong> %s
                        </p>
                        <p style="margin: 0 0 8px 0; color: #374151;">
                            <strong style="color: #1f2937;">📝 Pricing Note:</strong> %s
                        </p>
                        <p style="margin: 0 0 8px 0; color: #374151;">
                            <strong style="color: #1f2937;">👨‍💼 Pricing By:</strong> %s
                        </p>
                        <p style="margin: 0 0 8px 0; color: #374151;">
                            <strong style="color: #1f2937;">📊 Status:</strong> %s
                        </p>
                        <p style="margin: 0 0 8px 0; color: #374151;">
                            <strong style="color: #1f2937;">🗳 Reason:</strong> %s
                        </p>
                        <p style="margin: 0; color: #374151;">
                            <strong style="color: #1f2937;">👤 Updated by:</strong> %s
                        </p>
                    </div>
                    <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                        <p style="color: #6b7280; font-size: 14px; margin: 0;">
                            This is an automated notification from the CRM Task Management System.
                        </p>
                    </div>
                </div>
            </div>
        """,
      request.getCode(),
      request.getSalemanLabel(),
      request.getSalemanEmail(),
      buildShipmentDetailsHtml(shipmentDetail),
      request.getMailSubject(),
      request.getFeedback() != null ? request.getFeedback() : "N/A",
      request.getPricingNote() != null ? request.getPricingNote() : "N/A",
      request.getPricingLabel() != null ? request.getPricingLabel() : "N/A",
      request.getStatus() != null ? request.getStatus().getLabel() : "N/A",
      !reason.isEmpty() ? reason : "N/A",
      remoteUser
    );
  }

  public static String buildMailPricingNoteMessage(BulkCargoInquiryRequest request, String remoteUser) {
    BulkCargoShipmentDetail shipmentDetail = request.getShipmentDetail();

    return String.format("""
            <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 15px; background-color: #f9fafb;">
                <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <h1 style="color: #1f2937; font-size: 20px; margin: 0 0 16px 0; padding-bottom: 12px; border-bottom: 1px solid #e5e7eb;">
                        BULK CARGO INQUIRY REQUEST - REF: %s
                    </h1>
                    <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 16px 0;">
                        <p style="margin: 0 0 8px 0; color: #374151;">
                            <strong style="color: #1f2937;">👨‍💼 Sales:</strong> %s
                        </p>
                        <p style="margin: 0; color: #374151;">
                            <strong style="color: #1f2937;">📧 Email:</strong> %s
                        </p>
                    </div>
                    %s
                    <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 16px 0;">
                        <p style="margin: 0 0 8px 0; color: #374151;">
                            <strong style="color: #1f2937;">📋 Subject:</strong> %s
                        </p>
                        <p style="margin: 0 0 8px 0; color: #374151;">
                            <strong style="color: #1f2937;">💬 Feedback:</strong> %s
                        </p>
                        <p style="margin: 0 0 8px 0; color: #374151;">
                            <strong style="color: #1f2937;">📝 Pricing Note:</strong> %s
                        </p>
                        <p style="margin: 0 0 8px 0; color: #374151;">
                            <strong style="color: #1f2937;">👨‍💼 Pricing By:</strong> %s
                        </p>
                        <p style="margin: 0 0 8px 0; color: #374151;">
                            <strong style="color: #1f2937;">📊 Status:</strong> %s
                        </p>
                        <p style="margin: 0; color: #374151;">
                            <strong style="color: #1f2937;">👤 Updated by:</strong> %s
                        </p>
                    </div>
                    <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                        <p style="color: #6b7280; font-size: 14px; margin: 0;">
                            This is an automated notification from the CRM Task Management System.
                        </p>
                    </div>
                </div>
            </div>
        """,
      request.getCode(),
      request.getSalemanLabel(),
      request.getSalemanEmail(),
      buildShipmentDetailsHtml(shipmentDetail),
      request.getMailSubject(),
      request.getFeedback() != null ? request.getFeedback() : "N/A",
      request.getPricingNote() != null ? request.getPricingNote() : "N/A",
      request.getPricingLabel() != null ? request.getPricingLabel() : "N/A",
      request.getStatus() != null ? request.getStatus().getLabel() : "N/A",
      remoteUser
    );
  }

  private static String buildShipmentDetailsHtml(BulkCargoShipmentDetail shipment) {
    if (shipment == null ||
      (shipment.getQuantity() == 0 &&
        shipment.getVolume() == 0 &&
        shipment.getStowageFactor() == 0)) {
      return "";
    }

    StringBuilder html = new StringBuilder();
    html.append("""
      <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 16px 0;">
          <p style="margin: 0 0 8px 0; color: #374151;">
              <strong style="color: #1f2937;">📦 Shipment Details:</strong>
          </p>
          <ul style="margin: 0; padding-left: 20px; color: #374151;">
      """);

    if (shipment.getQuantity() > 0) {
      html.append(String.format("<li>Quantity: %.2f Metric Ton</li>", shipment.getQuantity()));
    }
    if (shipment.getVolume() > 0) {
      html.append(String.format("<li>Volume: %.2f CBM</li>", shipment.getVolume()));
    }
    if (shipment.getStowageFactor() > 0) {
      html.append(String.format("<li>Stowage Factor: %.2f CBM/MT</li>", shipment.getStowageFactor()));
    }

    html.append("</ul></div>");
    return html.toString();
  }

}