package cloud.datatp.fforwarder.price.entity;

import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.message.entity.MessageType;
import cloud.datatp.fforwarder.price.common.BulkCargoShipmentDetail;
import cloud.datatp.fforwarder.price.common.BulkCargoShipmentDetail.BulkCargoType;
import cloud.datatp.fforwarder.price.common.BulkCargoShipmentDetail.StackableType;
import cloud.datatp.fforwarder.price.common.ShipmentDetail.Stackable;
import cloud.datatp.fforwarder.core.common.Purpose;
import cloud.datatp.fforwarder.price.request.BulkCargoMessagePlugin;
import cloud.datatp.fforwarder.price.request.BulkCargoRequestNotificationTemplate;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonSetter;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;

import java.io.Serial;
import java.util.*;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.account.entity.Account;
import net.datatp.module.communication.AttachmentResource;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.security.client.ClientContext;
import net.datatp.util.bean.BeanUtil;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@Entity
@Table(
  name = BulkCargoInquiryRequest.TABLE_NAME,
  indexes = {
    @Index(columnList = "code"),
    @Index(columnList = "status"),
    @Index(columnList = "request_date"),
    @Index(columnList = "pricing_date"),
    @Index(columnList = "saleman_account_id"),
    @Index(columnList = "pricing_account_id"),
  }
)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter
@Setter
public class BulkCargoInquiryRequest extends CompanyEntity {

  @Serial
  private static final long serialVersionUID = 1L;
  public static final String TABLE_NAME = "lgc_price_bulk_cargo_inquiry_request";
  public static final String SEQUENCE = "lgc:lgc_price_bulk_cargo_inquiry_request";

  public enum BulkCargoProcessStatus {
    PENDING, CHECKING, NEED_SUPPORT, DROPPED, OFFERED, PERFORMING, CANCELLED, DONE;

    static public BulkCargoProcessStatus parse(String token) {
      if (StringUtil.isEmpty(token)) return PENDING;
      if (CHECKING.toString().equalsIgnoreCase(token.trim())) return CHECKING;
      if (NEED_SUPPORT.toString().equalsIgnoreCase(token.trim())) return NEED_SUPPORT;
      if (DROPPED.toString().equalsIgnoreCase(token.trim())) return DROPPED;
      if (OFFERED.toString().equalsIgnoreCase(token.trim())) return OFFERED;
      if (PERFORMING.toString().equalsIgnoreCase(token.trim())) return PERFORMING;
      if (CANCELLED.toString().equalsIgnoreCase(token.trim())) return CANCELLED;
      if (DONE.toString().equalsIgnoreCase(token.trim())) return DONE;
      return PENDING;
    }

    public String getLabel() {
      return switch (this) {
        case PENDING -> "Pending";
        case CHECKING -> "Checking";
        case NEED_SUPPORT -> "Need Support";
        case DROPPED -> "Dropped";
        case OFFERED -> "Offered";
        case PERFORMING -> "Performing";
        case CANCELLED -> "Cancelled";
        case DONE -> "Done";
      };
    }
  }

  public enum BulkCargoInquiryStatus {
    PRICE_MISMATCH, NO_FIRM, NO_RESPONSE, FIXED, IN_PROGRESS, REJECTED;

    static public BulkCargoInquiryStatus parse(String token) {
      if (StringUtil.isEmpty(token)) return IN_PROGRESS;
      if (PRICE_MISMATCH.toString().equalsIgnoreCase(token.trim())) return PRICE_MISMATCH;
      if (NO_FIRM.toString().equalsIgnoreCase(token.trim())) return NO_FIRM;
      if (FIXED.toString().equalsIgnoreCase(token.trim())) return FIXED;
      if (NO_RESPONSE.toString().equalsIgnoreCase(token.trim())) return NO_RESPONSE;
      if (REJECTED.toString().equalsIgnoreCase(token.trim())) return REJECTED;
      return IN_PROGRESS;
    }

    public boolean isChecking() {
      return this == IN_PROGRESS;
    }

    public String getLabel() {
      return switch (this) {
        case FIXED -> "Fixed";
        case PRICE_MISMATCH -> "Mismatch";
        case NO_FIRM -> "No Firm";
        case NO_RESPONSE -> "No Response'";
        case IN_PROGRESS -> "Checking";
        case REJECTED -> "Reject";
      };
    }
  }

  @Column(name = "code", nullable = false, length = 50)
  private String code;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "request_date", nullable = false)
  private Date requestDate = new Date();

  @Enumerated(EnumType.STRING)
  @Column(name = "process_status")
  private BulkCargoProcessStatus processStatus = BulkCargoProcessStatus.CHECKING;

  @Enumerated(EnumType.STRING)
  @Column(name = "status", nullable = false)
  private BulkCargoInquiryStatus status = BulkCargoInquiryStatus.IN_PROGRESS;

  @Enumerated(EnumType.STRING)
  @Column(name = "purpose")
  private Purpose purpose;

  @Column(name = "client_partner_id")
  private Long clientPartnerId;

  @Column(name = "client_label")
  private String clientLabel;

  @Column(name = "saleman_branch_name")
  private String salemanBranchName;

  @Column(name = "saleman_account_id")
  private Long salemanAccountId;

  @Column(name = "saleman_label")
  private String salemanLabel;

  @Column(name = "saleman_email")
  private String salemanEmail;

  @Column(name = "saleman_phone")
  private String salemanPhone;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "pricing_date")
  private Date pricingDate;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "closed_date")
  private Date closedDate;

  @Column(name = "pricing_account_id")
  private Long pricingAccountId;

  @Column(name = "pricing_label")
  private String pricingLabel;

  @Column(name = "from_location_code")
  private String fromLocationCode;

  @Column(name = "from_location_label")
  private String fromLocationLabel;

  @Column(name = "to_location_code")
  private String toLocationCode;

  @Column(name = "to_location_label")
  private String toLocationLabel;

  @Column(name = "note", length = 2 * 1024)
  private String note;

  @Column(name = "feedback", length = 2 * 1024)
  private String feedback;

  @Column(name = "pricing_note", length = 2 * 1024)
  private String pricingNote;

  @Embedded
  private BulkCargoShipmentDetail shipmentDetail = new BulkCargoShipmentDetail();

  public BulkCargoInquiryRequest withPricingAccount(Account account) {
    if (pricingDate == null) this.pricingDate = new Date();
    this.pricingAccountId = account.getId();
    this.pricingLabel = account.getFullName();
    return this;
  }

  @Transient
  @Column(name = "mail_message", length = 32 * 1024)
  private String mailMessage;

  @Column(name = "mail_subject", length = 32 * 1024)
  private String mailSubject;

  @Column(name = "mail_to")
  private String to;

  @Column(name = "mail_cc")
  private String cc;

  @Transient
  List<AttachmentResource> attachments = new ArrayList<>();

  public BulkCargoInquiryRequest mergeFromMapObject(MapObject sel) {
    List<String> fields = List.of(
      "requestDate",
      "clientPartnerId", "clientLabel",
      "salemanAccountId", "salemanLabel", "salemanBranchName", "salemanEmail", "salemanPhone",
      "pricingAccountId", "pricingLabel", "pricingDate",
      "fromLocationCode", "fromLocationLabel", "toLocationCode", "toLocationLabel",
      "feedback", "note", "pricingNote", "mailSubject"
    );

    List<String> shipmentFields = List.of(
      "termOfService", "laydaysDate", "cancellingDate", "targetRate",
      "volume", "quantity", "unit", "commodity", "descOfGoods", "allowCombine",
      "cargoProceeding", "loadRate", "dischargeRate", "stowageFactor"
    );

    BeanUtil.updateFieldsFromMap(this, sel, fields);
    this.shipmentDetail = Objects.ensureNotNull(shipmentDetail, BulkCargoShipmentDetail::new);
    BeanUtil.updateFieldsFromMap(this.shipmentDetail, sel, shipmentFields);

    if (sel.containsKey("processStatus")) {
      String processStatusStr = sel.getString("processStatus", null);
      if (!StringUtil.isEmpty(processStatusStr)) {
        this.processStatus = BulkCargoProcessStatus.parse(processStatusStr);
        if (processStatus == BulkCargoProcessStatus.DONE ||
          processStatus == BulkCargoProcessStatus.DROPPED ||
          processStatus == BulkCargoProcessStatus.CANCELLED) {
          closedDate = new Date();
        }
      }
    }

    if (sel.containsKey("status")) {
      String pricingStatusStr = sel.getString("status", null);
      if (!StringUtil.isEmpty(pricingStatusStr)) {
        this.status = BulkCargoInquiryStatus.parse(pricingStatusStr);
      }
    }

    if (sel.containsKey("cargoType")) {
      String cargoTypeStr = sel.getString("cargoType", null);
      if (!StringUtil.isEmpty(cargoTypeStr)) {
        this.shipmentDetail.setCargoType(BulkCargoType.valueOf(cargoTypeStr));
      }
    }
    if (sel.containsKey("stackable")) {
      String stackableStr = sel.getString("stackable", null);
      if (!StringUtil.isEmpty(stackableStr)) {
        Stackable stackable = Stackable.valueOf(stackableStr);
        this.shipmentDetail.setStackable(stackable);
      }
    }
    if (sel.containsKey("stackableType")) {
      String stackableTypeStr = sel.getString("stackableType", null);
      if (!StringUtil.isEmpty(stackableTypeStr)) {
        StackableType stackableType = StackableType.valueOf(stackableTypeStr);
        this.shipmentDetail.setStackableType(stackableType);
      }
    }

    if (sel.containsKey("purpose")) {
      String purposeStr = sel.getString("purpose", null);
      if (!StringUtil.isEmpty(purposeStr)) {
        this.purpose = Purpose.parse(purposeStr);
      }
    }
    return this;
  }

  public CRMMessageSystem toSalemanRejectMessage(ClientContext client, String pricingEmail, String reason) {
    CRMMessageSystem message = new CRMMessageSystem();
    message.setContent(BulkCargoRequestNotificationTemplate.buildMailMessage(this, client.getRemoteUser(), reason));
    message.setScheduledAt(new Date());
    message.setMessageType(MessageType.MAIL);
    message.setReferenceId(id);
    message.setReferenceType(BulkCargoInquiryRequest.TABLE_NAME);
    message.setPluginName(BulkCargoMessagePlugin.PLUGIN_TYPE);
    message.setRecipients(new HashSet<>(java.util.Collections.singletonList(salemanEmail)));
    MapObject metadata = new MapObject();
    metadata.put("fromEmail", pricingEmail);
    metadata.put("subject", String.format("CRM - Bulk Cargo Request [REF: %s - %s]", code, BulkCargoInquiryRequest.BulkCargoInquiryStatus.REJECTED.getLabel()));
    metadata.put("to", java.util.Collections.singletonList(salemanEmail));
    metadata.put("ccList", getCcList());
    message.setMetadata(metadata);
    return message;
  }

  public CRMMessageSystem toSalemanPricingNoteMessage(ClientContext client, String pricingEmail) {
    CRMMessageSystem message = new CRMMessageSystem();
    message.setContent(BulkCargoRequestNotificationTemplate.buildMailPricingNoteMessage(this, client.getRemoteUser()));
    message.setScheduledAt(new Date());
    message.setMessageType(MessageType.MAIL);
    message.setReferenceId(id);
    message.setReferenceType(BulkCargoInquiryRequest.TABLE_NAME);
    message.setPluginName(BulkCargoMessagePlugin.PLUGIN_TYPE);
    message.setRecipients(new HashSet<>(java.util.Collections.singletonList(salemanEmail)));
    MapObject metadata = new MapObject();
    metadata.put("fromEmail", pricingEmail);
    metadata.put("subject", String.format("CRM - Bulk Cargo Request [REF: %s - Pricing Note]", code));
    metadata.put("to", java.util.Collections.singletonList(salemanEmail));
    metadata.put("ccList", getCcList());
    message.setMetadata(metadata);
    return message;
  }

  public void computeMailSubject() {
    if (StringUtil.isNotEmpty(mailSubject)) return;
    String termOfService = "[TERMS]";
    if (this.shipmentDetail != null && this.shipmentDetail.getTermOfService() != null) {
      termOfService = this.shipmentDetail.getTermOfService();
    }
    if (StringUtil.isEmpty(toLocationLabel)) toLocationLabel = "[POD]";
    if (StringUtil.isEmpty(fromLocationLabel)) fromLocationLabel = "[POL]";

    StringBuilder builder = new StringBuilder();
    builder.append("REF:").append(code);
    builder.append("/ ");
    builder.append(termOfService).append(" ");

    builder.append(fromLocationLabel).append(" ");
    builder.append("TO").append(" ");
    builder.append(toLocationLabel).append(" ");

    if (StringUtil.isNotEmpty(clientLabel)) {
      builder.append(" - ").append(clientLabel);
    }
    mailSubject = builder.toString();
  }

  public void setTo(String text) {
    if (text == null) {
      this.to = null;
    } else {
      text = text.toLowerCase();
      Set<String> variantSet = StringUtil.toStringHashSet(text);
      this.to = StringUtil.join(variantSet);
    }
  }

  public void withTo(String text) {
    if (StringUtil.isEmpty(to)) {
      setTo(text);
    } else {
      text = text.toLowerCase();
      Set<String> origin = StringUtil.toStringHashSet(to);
      Set<String> variantSet = StringUtil.toStringHashSet(text);
      origin.addAll(variantSet);
      setTo(new ArrayList<>(origin));
    }
  }

  @JsonGetter("to")
  public List<String> getToList() {
    if (to == null || to.isEmpty()) return new ArrayList<>();
    return Arrays.stream(to.split(","))
      .map(String::trim)
      .collect(Collectors.toList());
  }

  @JsonSetter("to")
  public void setTo(List<String> toList) {
    if (toList == null || toList.isEmpty()) this.to = null;
    else this.to = toList.stream().map(String::trim).collect(Collectors.joining(","));
  }

  public void setCc(String text) {
    if (text == null) {
      this.cc = null;
    } else {
      text = text.toLowerCase();
      Set<String> variantSet = StringUtil.toStringHashSet(text);
      this.cc = StringUtil.join(variantSet);
    }
  }

  @JsonGetter("cc")
  public List<String> getCcList() {
    if (cc == null || cc.isEmpty()) return new ArrayList<>();
    return Arrays.stream(cc.split(","))
      .map(String::trim)
      .collect(Collectors.toList());
  }

  @JsonSetter("cc")
  public void setCc(List<String> ccList) {
    if (ccList == null || ccList.isEmpty()) this.cc = null;
    else this.cc = ccList.stream().map(String::trim).collect(Collectors.joining(","));
  }
  // -------------- mail detail  ----------------

}