package cloud.datatp.fforwarder.price;

import cloud.datatp.fforwarder.core.common.Purpose;
import cloud.datatp.fforwarder.core.common.TransportationMode;
import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.core.message.CRMMessageLogic;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.template.CRMUserRoleLogic;
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;
import cloud.datatp.fforwarder.price.entity.InquiryRequest;
import cloud.datatp.fforwarder.price.entity.InquiryRequest.InquiryStatus;
import cloud.datatp.fforwarder.price.repository.InquiryRequestRepository;
import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.bot.BotService;
import net.datatp.module.communication.MailMessage;
import net.datatp.module.company.CompanyConfigLogic;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.company.entity.CompanyConfig;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.core.security.entity.AppPermission;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.graphapi.GraphApiService;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Getter
@Component
public class InquiryRequestLogic extends CRMDaoService {

  @Autowired
  private CompanyConfigLogic companyConfigLogic;

  @Autowired
  private SecurityLogic securityLogic;

  @Autowired
  private CompanyLogic companyLogic;

  @Autowired
  private InquiryRequestRepository inquiryRequestRepo;

  @Autowired
  private GraphApiService graphApiService;

  @Autowired
  private SeqService seqService;

  @Autowired
  private BotService botService;

  @Autowired
  private CRMMessageLogic crmMessageLogic;

  @Autowired
  private CRMUserRoleLogic crmUserRoleLogic;

  @PostConstruct
  private void onInit() {
    seqService.createIfNotExists(InquiryRequest.SEQUENCE, 10);
  }

  public InquiryRequest getInquiryRequest(ClientContext client, ICompany company, Long requestId) {
    return inquiryRequestRepo.findById(requestId).get();
  }

  public InquiryRequest getInquiryRequest(ClientContext client, ICompany company, String requestCode) {
    return inquiryRequestRepo.getByCode(company.getId(), requestCode);
  }

  public InquiryRequest initInquiryRequest(ClientContext client, ICompany company, InquiryRequest request) {
    CrmUserRole crmUserRole = crmUserRoleLogic.getByAccountId(client, client.getAccountId());
    Objects.assertNotNull(crmUserRole, "Crm user role not found!!!, account id: " + client.getAccountId());
    request.setSalemanAccountId(crmUserRole.getAccountId());
    request.setSalemanLabel(crmUserRole.getFullName());
    request.setSalemanBranchName(crmUserRole.getCompanyBranchName());
    request.setSalemanJobTitle(crmUserRole.getPosition());
    request.setSalemanPhone(crmUserRole.getPhone());
    request.setSalemanEmail(crmUserRole.getEmail());

    if (request.getCargoReadyDate() == null) request.setCargoReadyDate(new Date());
    Purpose purpose = request.getPurpose();
    if (purpose == null) {
      request.setPurpose(Purpose.EXPORT);
      purpose = Purpose.EXPORT;
    }
    if (request.getMode() == null) request.setMode(TransportationMode.SEA_FCL);

    //set default mail to in request
    StringBuilder builder = new StringBuilder("mail.request.pricing.");
    builder.append(request.getMode().toString().toLowerCase());
    if (!TransportationMode.isTruckTransport(request.getMode())) {
      builder.append(".");
      builder.append(purpose.toString().toLowerCase(Locale.ROOT));
    }
    CompanyConfig companyConfig = companyConfigLogic.getCompanyConfigByCompanyId(client, company.getId());
    String mailTo = companyConfig.getAttributeAsString(builder.toString(), null);
    if (StringUtil.isNotEmpty(mailTo)) request.withTo(mailTo);
    return request;
  }

  public List<MapObject> saveInquiryRequestRecords(ClientContext client, ICompany company, List<MapObject> requests) {
    if (Collections.isNotEmpty(requests)) {
      for (MapObject request : requests) {
        try {
          InquiryRequest priceCheckRequest = new InquiryRequest();
          Long id = request.getLong("id", null);
          if (id != null) priceCheckRequest = getInquiryRequest(client, company, id);
          InquiryStatus oldInquiryStatus = InquiryStatus.parse(priceCheckRequest.getStatus().toString());
          priceCheckRequest = priceCheckRequest.computeFromMapObject(request);

          final boolean ownerEdit = client.getAccountId() == priceCheckRequest.getSalemanAccountId();
          InquiryStatus newInquiryStatus = InquiryStatus.parse(request.getString("status", null));

          if (!priceCheckRequest.isNew() && ownerEdit && !oldInquiryStatus.equals(newInquiryStatus)) {
            if(StringUtil.isNotEmpty(priceCheckRequest.getTo())) {
              CRMMessageSystem message = priceCheckRequest.toSalemanChangeStatusMessage(client);
              crmMessageLogic.scheduleMessage(client, message);
            }
          }

          if (!ownerEdit && newInquiryStatus.equals(InquiryStatus.REJECTED) && !priceCheckRequest.isNew()) {
            CrmUserRole crmUserRole = crmUserRoleLogic.getByAccountId(client, client.getAccountId());
            Objects.assertNotNull(crmUserRole, "Crm user role not found!!!, account id: " + client.getAccountId());
            CRMMessageSystem message = priceCheckRequest.toPricingRejectMessage(client, crmUserRole.getEmail());
            crmMessageLogic.scheduleMessage(client, message);
          }

          if (StringUtil.isEmpty(priceCheckRequest.getTo())) {
            CrmUserRole crmUserRole = crmUserRoleLogic.getByAccountId(client, client.getAccountId());
            Objects.assertNotNull(crmUserRole, "Crm user role not found!!!, account id: " + client.getAccountId());
            priceCheckRequest.setTo(crmUserRole.getEmail());
          }
          InquiryRequest savedRequest = saveInquiryRequest(client, company, priceCheckRequest);
          request.put("id", savedRequest.getId());
        } catch(Exception ex) {
          log.error("Error saving inquiry request: {}", ex.getMessage(), ex);
        }
      }
    }
    return requests;
  }

  public InquiryRequest saveInquiryRequest(ClientContext client, ICompany company, InquiryRequest request) {
    if (request.isNew() || StringUtil.isEmpty(request.getCode())) {
      String prefix = request.getPurpose().getAbb() + request.getMode().getBfsAbb() + DateUtil.asCompactDateId(new Date()).substring(2);
      String code = prefix + String.format("%04d", seqService.nextSequence(InquiryRequest.SEQUENCE));
      request.setCode(code);
    }

    request.computeMailSubject();
    request.computeVolumeReport();

    if (StringUtil.isEmpty(request.getSalemanBranchName())) {
      request.setSalemanBranchName(client.getCompanyCode());
    }

    if (request.getRequestDate() == null) {
      request.setRequestDate(new Date());
    }
    if (request.getSalemanAccountId() == null) {
      CrmUserRole crmUserRole = crmUserRoleLogic.getByAccountId(client, client.getAccountId());
      Objects.assertNotNull(crmUserRole, "Crm user role not found!!!, account id: " + client.getAccountId());
      request.setSalemanAccountId(crmUserRole.getAccountId());
      request.setSalemanLabel(crmUserRole.getFullName());
      request.setSalemanBranchName(crmUserRole.getCompanyBranchName());
      request.setSalemanJobTitle(crmUserRole.getPosition());
      request.setSalemanPhone(crmUserRole.getPhone());
      request.setSalemanEmail(crmUserRole.getEmail());
    }
    if (request.isNew()) {
      request.set(client, company);
    }
    return inquiryRequestRepo.save(request);
  }

  public InquiryRequest sendInquiryRequest(ClientContext client, ICompany company, InquiryRequest request) {
    InquiryRequest priceCheckRequest = saveInquiryRequest(client, company, request);
    MailMessage mailMessage = new MailMessage();
    mailMessage.setMessage(priceCheckRequest.getMailMessage());
    mailMessage.setFrom(priceCheckRequest.getSalemanEmail());
    mailMessage.setSubject(priceCheckRequest.getMailSubject());
    mailMessage.setTo(priceCheckRequest.getToList());
    mailMessage.setCc(priceCheckRequest.getCcList());
    mailMessage.setAttachments(request.getAttachments());
    graphApiService.sendEmailWithHtmlFormat(client, company, mailMessage);
    return priceCheckRequest;
  }

  public List<SqlMapRecord> inquiryRequestReport(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    AppPermission pricePermission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-prices");
    AppPermission salePermission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-sales");
    if (pricePermission == null && salePermission == null) return java.util.Collections.emptyList();

    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/price/groovy/InquiryRequestSql.groovy";
    sqlParams.addParam("accessAccountId", client.getAccountId());

    ICompany targetCompany = client.getCompany();
    if (sqlParams.hasParam("companyCode") && StringUtil.isNotEmpty(sqlParams.getString("companyCode"))) {
      String companyCode = sqlParams.getString("companyCode");
      targetCompany = companyLogic.getCompany(client, companyCode);
      Objects.assertNotNull(targetCompany, "Company not found!!!, code : " + companyCode);
    }
    sqlParams.addParam("companyId", targetCompany.getId());

    if (salePermission.isGroupScope()) {
      String coreScriptDir = appEnv.addonPath("core", "groovy");
      String coreScriptFile = "net/datatp/module/hr/groovy/EmployeeSql.groovy";
      List<SqlMapRecord> accountIds = searchPlatformDbRecords(client, coreScriptDir, coreScriptFile, "FindEmployeeIdsByManagerId", sqlParams);
      List<Long> participantAccountIds = accountIds.stream()
        .map(record -> record.getLong("accountId", null))
        .collect(Collectors.toList());
      sqlParams.addParam("salemanAccountIds", participantAccountIds);
    }

    List<SqlMapRecord> inquiryRequests = searchDbRecords(client, scriptDir, scriptFile, "InquiryRequestReport", sqlParams);

    if (inquiryRequests.isEmpty()) {
      return inquiryRequests;
    }

    Set<Long> accountIds = inquiryRequests.stream()
      .map(record -> record.getLong("salemanAccountId"))
      .filter(Objects::nonNull)
      .collect(Collectors.toSet());

    Set<Long> companyIds = inquiryRequests.stream()
      .map(record -> record.getLong("companyId"))
      .filter(Objects::nonNull)
      .collect(Collectors.toSet());

    Map<String, SqlMapRecord> departmentMap = getCompanyAndDepartmentInfo(client, accountIds, companyIds);

    return mergeInquiryRequestWithAllInfo(inquiryRequests, departmentMap);
  }

  private Map<String, SqlMapRecord> getCompanyAndDepartmentInfo(ClientContext client, Set<Long> accountIds, Set<Long> companyIds) {
    if (accountIds.isEmpty() || companyIds.isEmpty()) {
      return new HashMap<>();
    }

    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/price/groovy/InquiryRequestSql.groovy";

    SqlQueryParams params = new SqlQueryParams();
    params.addParam("companyIds", new ArrayList<Long>(companyIds));
    params.addParam("accountIds", new ArrayList<Long>(accountIds));

    List<SqlMapRecord> resultList = searchPlatformDbRecords(client, scriptDir, scriptFile, "InquiryRequestCompanyAndDepartmentInfo", params);

    return resultList.stream()
      .collect(Collectors.toMap(
        record -> record.getString("accountId") + "_" + record.getLong("companyId"),
        record -> record,
        (existing, replacement) -> existing
      ));
  }

  private List<SqlMapRecord> mergeInquiryRequestWithAllInfo(List<SqlMapRecord> inquiryRequests, Map<String, SqlMapRecord> departmentMap) {

    return inquiryRequests.stream()
      .map(request -> {
        SqlMapRecord mergedRecord = new SqlMapRecord();
        mergedRecord.putAll(request);

        Long companyId = request.getLong("companyId");
        String accountId = request.getString("salemanAccountId");

        if (accountId != null && companyId != null) {
          String deptKey = accountId + "_" + companyId;
          SqlMapRecord deptInfo = departmentMap.get(deptKey);

          if (deptInfo != null) {
            mergedRecord.put("companyLabel", deptInfo.getString("companyLabel"));
            mergedRecord.put("departmentLabel", deptInfo.getString("departmentLabel"));
            mergedRecord.put("departmentName", deptInfo.getString("departmentName"));
            mergedRecord.put("parentDepartmentLabel", deptInfo.getString("parentDepartmentLabel"));
          }
        }

        return mergedRecord;
      })
      .collect(Collectors.toList());
  }

  public List<SqlMapRecord> searchInquiryRequests(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/price/groovy/InquiryRequestSql.groovy";
    sqlParams.addParam("accessAccountId", client.getAccountId());
    ICompany targetCompany = client.getCompany();
    if (sqlParams.hasParam("companyCode") && StringUtil.isNotEmpty(sqlParams.getString("companyCode"))) {
      String companyCode = sqlParams.getString("companyCode");
      targetCompany = companyLogic.getCompany(client, companyCode);
      Objects.assertNotNull(targetCompany, "Company not found!!!, code : " + companyCode);
    }
    sqlParams.addParam("companyId", targetCompany.getId());
    AppPermission pricePermission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-prices");
    AppPermission salePermission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-sales");
    if (pricePermission == null && salePermission == null) return java.util.Collections.emptyList();
    if (!sqlParams.hasParam("space")) sqlParams.addParam("space", "User");
    if (sqlParams.hasParam("filterMode") && StringUtil.isNotEmpty(sqlParams.getString("filterMode"))) {
      CrmUserRole crmUserRole = crmUserRoleLogic.getByAccountId(client, client.getAccountId());
      Objects.assertNotNull(crmUserRole, "Crm user role not found!!!, account id: " + client.getAccountId());
      sqlParams.addParam("mailPattern", crmUserRole.getEmail());
    }
    return searchDbRecords(client, scriptDir, scriptFile, "SearchInquiryRequest", sqlParams);
  }

  public List<SqlMapRecord> searchInquiryRequestSpace(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/price/groovy/InquiryRequestSql.groovy";
    sqlParams.addParam("accessAccountId", client.getAccountId());
    sqlParams.addParam("companyId", client.getCompanyId());
    AppPermission pricePermission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-prices");
    AppPermission salePermission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-sales");
    if (pricePermission == null && salePermission == null) return java.util.Collections.emptyList();
    if (!sqlParams.hasParam("space")) sqlParams.addParam("space", "User");
    return searchDbRecords(client, scriptDir, scriptFile, "SearchInquiryRequestSpace", sqlParams);
  }

  public boolean deleteInquiryRequests(ClientContext client, ICompany company, List<Long> ids) {
    inquiryRequestRepo.deleteAllById(ids);
    return true;
  }

  public List<InquiryRequest> findByCompany(ClientContext client, ICompany company) {
    return inquiryRequestRepo.findByCompany(company.getId());
  }

  public List<InquiryRequest> findNoResponseInquiryRequests(ClientContext client, ICompany company) {
    return inquiryRequestRepo.findNoResponseInquiryRequests(company.getId());
  }

  public void updateToNoResponse(ClientContext client, ICompany company, List<Long> requestIds) {
    inquiryRequestRepo.updateToNoResponse(requestIds);
  }
}