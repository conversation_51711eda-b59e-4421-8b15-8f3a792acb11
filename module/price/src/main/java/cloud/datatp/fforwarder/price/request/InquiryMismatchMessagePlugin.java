package cloud.datatp.fforwarder.price.request;

import cloud.datatp.fforwarder.core.message.CRMMessageLogic;
import cloud.datatp.fforwarder.core.message.MailMessageProvider;
import cloud.datatp.fforwarder.core.message.MessageServicePlugin;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.message.entity.MessageStatus;
import cloud.datatp.fforwarder.core.template.CRMUserRoleLogic;
import cloud.datatp.fforwarder.price.InquiryRequestLogic;
import cloud.datatp.fforwarder.price.entity.InquiryRequest;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.company.CompanyLogic;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class InquiryMismatchMessagePlugin extends MessageServicePlugin {
  public static final String PLUGIN_TYPE = "inquiry-mismatch"; // set message at 8 A.M

  @Autowired
  private InquiryRequestLogic inquiryRequestLogic;

  @Autowired
  private CompanyLogic companyLogic;

  @Autowired
  private CRMMessageLogic crmMessageLogic;

  @Autowired
  private CRMUserRoleLogic crmUserRoleLogic;

  @Autowired
  private MailMessageProvider mailMessageProvider;

  protected InquiryMismatchMessagePlugin() {
    super(PLUGIN_TYPE);
  }

  @Override
  public CRMMessageSystem onPreSend(ClientContext client, CRMMessageSystem message) {
    try {
      Objects.assertNotNull(message.getReferenceType(), "Message has no reference type");
      Objects.assertNotNull(message.getReferenceId(), "Message has no reference id");
      InquiryRequest request = inquiryRequestLogic.getInquiryRequest(client, null, message.getReferenceId());
      Objects.assertNotNull(request, "Inquiry request {} is not found", message.getReferenceId());
      InquiryRequest.InquiryStatus status = request.getStatus();
      log.info("📧 Preparing mismatch email for inquiry request [code={}, status={}] at {}\n", request.getCode(), status, DateUtil.asCompactDateTime(new Date()));

      if(InquiryRequest.InquiryStatus.PRICE_MISMATCH != status) {
        log.info("❌ Cancelling message [id={}] due to invalid status: {}", message.getId(), status);
        message.setStatus(MessageStatus.CANCELLED);
        message = crmMessageLogic.saveMessage(client, message);
      }
      return message;
    } catch (Exception e) {
      log.error("❌ Error during pre-send processing for message [id={}]: {}", message.getId(), e.getMessage(), e);
      message.setStatus(MessageStatus.CANCELLED);
      return crmMessageLogic.saveMessage(client, message);
    }
  }

  @Override
  public void onPostSend(ClientContext client, CRMMessageSystem message) {}

  @Override
  public void onSendError(ClientContext client, CRMMessageSystem message, Exception error) {}

}