package cloud.datatp.fforwarder.core.message;

import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.message.entity.MessageStatus;
import cloud.datatp.fforwarder.core.message.entity.MessageType;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.app.AppEnv;
import net.datatp.module.communication.MailMessage;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.graphapi.GraphApiService;
import net.datatp.module.zalo.ZaloLogic;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.MapObject;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MailMessageProvider implements MessageProvider {

  @Autowired
  private ZaloLogic zaloLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private CompanyLogic companyLogic;

  @Autowired
  GraphApiService graphApiService;

  @Autowired
  CRMMessageLogic crmMessageLogic;

  @Autowired
  private AppEnv appEnv;

  @Override
  public MessageType getType() {
    return MessageType.MAIL;
  }

  @Override
  public boolean supports(CRMMessageSystem message) {
    return message.getMessageType() == MessageType.MAIL;
  }

  @Override
  public CRMMessageSystem send(ClientContext client, CRMMessageSystem message) throws Exception {

    CRMMessageSystem messageSystem = crmMessageLogic.getMessageSystemRepo().getReferenceById(message.getId());
    if (messageSystem.getStatus().equals(MessageStatus.CANCELLED)) {
      log.info("❌ Message [id={}] is cancelled", messageSystem.getId());
      return messageSystem;
    }

    Set<String> recipients = messageSystem.getRecipients();

    MapObject metadata = messageSystem.getMetadata();
    String subject = metadata.getString("subject", "CRM - Mail Message System");
    String from = metadata.getString("fromEmail", "<EMAIL>");
    try {
      if (StringUtil.isBlank(subject)) {
        throw RuntimeError.UnknownError("Subject is empty");
      }
      if (StringUtil.isBlank(from)) {
        throw RuntimeError.UnknownError("From is empty");
      }

      List<String> ccList = metadata.getStringList("ccList");
      if (ccList == null) {
        ccList = new ArrayList<>();
      }
      ccList = ccList.stream()
        .map(String::toLowerCase)
        .distinct()
        .collect(Collectors.toList());

      if (recipients == null || recipients.isEmpty()) {
        throw RuntimeError.UnknownError("Recipients is empty");
      }
      MailMessage mailMessage = new MailMessage();
      mailMessage.setMessage(messageSystem.getContent());
      mailMessage.setFrom(from);
      mailMessage.setSubject(subject);
      mailMessage.setTo(new ArrayList<>(recipients));
      mailMessage.setCc(ccList);

      if (appEnv.isProdEnv()) {
        graphApiService.sendEmailWithHtmlFormat(client, null, mailMessage);
        log.info("📧 Sent mail id={} to {} recipients, cc: {}",
          messageSystem.getId(), recipients.size(), ccList.size());
      } else {
        graphApiService.sendEmailWithHtmlFormat(client, null, mailMessage);
        log.info("DEV MODE - Mail would be sent: id={}, from={}, subject={}, to={}, cc={}, content: \n{}",
          messageSystem.getId(), from, subject, recipients, ccList, messageSystem.getContent());
      }

      messageSystem.markAsSent();
      return crmMessageLogic.saveMessage(client, messageSystem);
    } catch (Exception ex) {
      log.error("📧❌ Failed to send mail id={} {}", messageSystem.getId(), ex.getMessage(), ex);
      messageSystem.markAsFailed(ex.getLocalizedMessage());
      return crmMessageLogic.saveMessage(client, messageSystem);
    }
  }

}