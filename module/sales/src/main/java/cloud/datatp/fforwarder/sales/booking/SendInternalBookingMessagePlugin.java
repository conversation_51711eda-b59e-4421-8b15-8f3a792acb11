package cloud.datatp.fforwarder.sales.booking;

import cloud.datatp.fforwarder.core.common.TransportationMode;
import cloud.datatp.fforwarder.core.message.MailMessageProvider;
import cloud.datatp.fforwarder.core.message.MessageServicePlugin;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.sales.booking.dto.BookingModel;
import cloud.datatp.fforwarder.sales.inquiry.entity.SpecificServiceInquiry;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.company.CompanyLogic;
import net.datatp.security.client.ClientContext;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SendInternalBookingMessagePlugin extends MessageServicePlugin {
  public static final String PLUGIN_TYPE = "send-internal-booking";

  @Autowired
  private CompanyLogic companyLogic;

  @Autowired
  private BookingLogic bookingLogic;

  @Autowired
  private MailMessageProvider mailMessageProvider;

  @Autowired
  private AccountLogic accountLogic;

  protected SendInternalBookingMessagePlugin() {
    super(PLUGIN_TYPE);
  }

  public void onPostSend(ClientContext client, CRMMessageSystem message) {
  }

  public static String buildZaloMessage(BookingModel booking) {
    SpecificServiceInquiry inquiry = booking.getInquiry();
    StringBuilder message = new StringBuilder();
    message.append(String.format("""
            INTERNAL BOOKING - REF: %s
            ━━━━━━━━━━━━━━━━━━━━
            👨‍💼 Sales: %s
            
            📦 Shipment Details:
            🔢 Booking No: %s
            🏢 From: %s
            🏢 To: %s
            📝 Description: %s
            """,
      booking.getBfsoneReference(),
      booking.getSenderLabel(),
      booking.getBookingNumber(),
      inquiry.getFromLocationLabel(),
      inquiry.getToLocationLabel(),
      inquiry.getDescOfGoods()
    ));

    // Add container types for SeaFCL
    if (TransportationMode.isSeaFCLTransport(inquiry.getMode())) {
      String containerTypes = inquiry.getContainerTypes();
      if (StringUtil.isNotEmpty(containerTypes)) {
        message.append(String.format("""
                
                📦 Container Types:
                🚢 %s
                """,
          containerTypes
        ));
      }
    }

    // Add weight/volume info if available
    if (inquiry.getGrossWeightKg() > 0 || inquiry.getVolumeCbm() > 0) {
      message.append(String.format("""
                
                📊 Measurement:
                ⚖️ Weight: %.2f KG
                📏 Volume: %.2f CBM
                """,
        inquiry.getGrossWeightKg(),
        inquiry.getVolumeCbm()
      ));
    }

    // Add shipment info if available 
    String hawb = booking.getHawbNo();
    String mawb = booking.getMawbNo();
    message.append(String.format("""
                
                🚢 Transport Info:
                📄 HAWB: %s
                📄 MAWB: %s
                """,
      hawb != null && !hawb.trim().isEmpty() ? hawb : "N/A",
      mawb != null && !mawb.trim().isEmpty() ? mawb : "N/A"
    ));

    message.append("""
            
            ⚡️ Vui lòng kiểm tra và xử lý Internal Booking này.
            ━━━━━━━━━━━━━━━━━━━━
            """);

    return message.toString();
  }

  public static String buildMailMessage(BookingModel booking) {
    StringBuilder containerSection = new StringBuilder();
    final SpecificServiceInquiry inquiry = booking.getInquiry();
    if (TransportationMode.isSeaFCLTransport(inquiry.getMode())) {
      String containerTypes = inquiry.getContainerTypes();
      if (StringUtil.isNotEmpty(containerTypes)) {
        containerSection.append("""
            <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 16px 0;">
                <p style="margin: 0 0 8px 0; color: #374151;">
                    <strong style="color: #1f2937;">📦 Container Types:</strong> %s
                </p>
            </div>
            """.formatted(containerTypes));
      }
    }

    StringBuilder measurementSection = new StringBuilder();
    if (inquiry.getGrossWeightKg() > 0 || inquiry.getVolumeCbm() > 0) {
      measurementSection.append("""
          <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 16px 0;">
              <p style="margin: 0 0 8px 0; color: #374151;">
                  <strong style="color: #1f2937;">📊 Measurement:</strong>
              </p>
              <ul style="margin: 0; padding-left: 20px; color: #374151;">
          """);
      if (inquiry.getGrossWeightKg() > 0) {
        measurementSection.append(String.format("<li>Weight: %.2f KG</li>", inquiry.getGrossWeightKg()));
      }
      if (inquiry.getVolumeCbm() > 0) {
        measurementSection.append(String.format("<li>Volume: %.2f CBM</li>", inquiry.getVolumeCbm()));
      }
      measurementSection.append("</ul></div>");
    }

    StringBuilder transportSection = new StringBuilder();
    String hawb = booking.getHawbNo();
    String mawb = booking.getMawbNo();
    transportSection.append(String.format("""
          <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 16px 0;">
              <p style="margin: 0 0 8px 0; color: #374151;">
                  <strong style="color: #1f2937;">🚢 Transport Info:</strong>
              </p>
              <ul style="margin: 0; padding-left: 20px; color: #374151;">
                  <li>HAWB: %s</li>
                  <li>MAWB: %s</li>
              </ul>
          </div>
          """,
      hawb != null && !hawb.trim().isEmpty() ? hawb : "N/A",
      mawb != null && !mawb.trim().isEmpty() ? mawb : "N/A"
    ));

    return String.format("""
        <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 15px; background-color: #f9fafb;">
            <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h1 style="color: #1f2937; font-size: 20px; margin: 0 0 16px 0; padding-bottom: 12px; border-bottom: 1px solid #e5e7eb;">
                    INTERNAL BOOKING - REF: %s
                </h1>
                
                <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 16px 0;">
                    <p style="margin: 0; color: #374151;">
                        <strong style="color: #1f2937;">👨‍💼 Sales:</strong> %s
                    </p>
                </div>

                <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 16px 0;">
                    <p style="margin: 0 0 8px 0; color: #374151;">
                        <strong style="color: #1f2937;">📦 Shipment Details:</strong>
                    </p>
                    <ul style="margin: 0; padding-left: 20px; color: #374151;">
                        <li>Booking No: %s</li>
                        <li>From: %s</li>
                        <li>To: %s</li>
                        <li>Description: %s</li>
                    </ul>
                </div>

                %s
                %s
                %s

                <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                    <p style="color: #6b7280; font-size: 14px; margin: 0;">
                        This is an automated notification from the CRM Task Management System.
                    </p>
                </div>
            </div>
        </div>
        """,
      booking.getBfsoneReference(),
      booking.getSenderLabel(),
      booking.getBookingNumber(),
      inquiry.getFromLocationLabel(),
      inquiry.getToLocationLabel(),
      inquiry.getDescOfGoods(),
      containerSection.toString(),
      measurementSection.toString(),
      transportSection.toString()
    );
  }
}