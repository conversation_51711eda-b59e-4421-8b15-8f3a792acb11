package cloud.datatp.fforwarder.sales.booking.dto;

import cloud.datatp.fforwarder.core.common.ChargeType;
import cloud.datatp.fforwarder.core.common.FreightTerm;
import cloud.datatp.fforwarder.core.common.TransportationMode;
import cloud.datatp.fforwarder.core.partner.entity.CRMPartner;
import cloud.datatp.fforwarder.price.common.ChargeTarget;
import cloud.datatp.fforwarder.price.common.ClientPartnerType;
import cloud.datatp.fforwarder.price.entity.CustomClearanceType;
import cloud.datatp.fforwarder.sales.booking.dto.SellingRate.Type;
import cloud.datatp.fforwarder.sales.booking.entity.Booking;
import cloud.datatp.fforwarder.sales.booking.entity.BookingAirTransportCharge;
import cloud.datatp.fforwarder.sales.booking.entity.BookingCustomClearance;
import cloud.datatp.fforwarder.sales.booking.entity.BookingSeaTransportCharge;
import cloud.datatp.fforwarder.sales.booking.entity.BookingTruckTransportCharge;
import cloud.datatp.fforwarder.sales.common.BookingShipmentInfo;
import cloud.datatp.fforwarder.sales.common.ContainerType;
import cloud.datatp.fforwarder.sales.common.ContainerType.ContainerTypeUnit;
import cloud.datatp.fforwarder.sales.common.entity.CustomerAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerAirTransportAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerCustomClearanceAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerSeaTransportAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerSeaTransportCharge.SeaType;
import cloud.datatp.fforwarder.sales.common.entity.CustomerTruckTransportAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerTruckTransportCharge.TruckType;
import cloud.datatp.fforwarder.sales.common.entity.QuotationCharge;
import cloud.datatp.fforwarder.sales.inquiry.entity.Container;
import cloud.datatp.fforwarder.sales.inquiry.entity.SpecificServiceInquiry;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.data.db.entity.EditMode;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@NoArgsConstructor
@Getter @Setter
public class BookingModel {

  private Long    id;
  private String bfsoneReference;
  private String bookingNumber;

  private String mawbNo;
  private String hawbNo;

  private String  shipmentType;

  @Enumerated(EnumType.STRING)
  private FreightTerm paymentTerm;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date planTimeArrival;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date planTimeDeparture;

  private String note;

  /* ------------ Sender (Saleman) / Receiver (Cus/ Docs) -------------- */
  private Long    senderAccountId;
  private String  senderBFSOneCode;
  private String  senderLabel;

  private Long    receiverAccountId;
  private String  receiverEmployeeLabel;
  private String  receiverBFSOneCode;

  /* -------------------------------Customer ID, Coloader ID, Agent ID------------------------------*/
  private Long carrierPartnerId;
  private String carrierLabel;

  private Long handlingAgentPartnerId;
  private String handlingAgentLabel;

  /* -------------------------------Association/ Reference Entity------------------------------*/
  private SpecificServiceInquiry inquiry;
  private Long sQuotationChargeId;

  private List<SellingRate> sellingRates = new ArrayList<>();

  public Booking toBooking(Booking template) {
    template = Objects.ensureNotNull(template, Booking::new);
    Objects.assertNotNull(this.inquiry, "Inquiry is not found");

    if (this.inquiry != null) {
      template.setInquiryId(inquiry.getId());
    }
    if (template.isNew()) {
      template.setBookingNumber(bookingNumber);
      template.setBookingDate(new Date());
    }

    template.setReceiverAccountId(receiverAccountId);
    template.setReceiverLabel(receiverEmployeeLabel);
    template.setReceiverBFSOneCode(receiverBFSOneCode);

    template.setPaymentTerm(paymentTerm);
    template.setShipmentType(shipmentType);
    template.setHawbNo(hawbNo);
    template.setMawbNo(mawbNo);
    template.setSQuotationChargeId(sQuotationChargeId);
    return template;
  }

  public List<BookingTruckTransportCharge> computeTruckTransportCharges(Booking template) {
    TransportationMode mode = inquiry.getMode();

    if (TransportationMode.isSeaTransport(mode)) template.setChargeType(ChargeType.SEA);
    else if (TransportationMode.isAirTransport(mode)) template.setChargeType(ChargeType.AIR);
    else throw RuntimeError.UnknownError("Not support yet!!!");

    Map<TransportationMode, List<SellingRate>> sellingRateByGroup = sellingRates.stream()
      .collect(Collectors.groupingBy(SellingRate::getGroup));

    List<SellingRate> freightRates = sellingRateByGroup.getOrDefault(mode, new ArrayList<>());
    Map<Type, List<SellingRate>> sellingGroup = freightRates.stream().collect(Collectors.groupingBy(SellingRate::getType));

    List<SellingRate> truckingRates = sellingGroup.getOrDefault(Type.TRUCKING, new ArrayList<>());

    List<BookingTruckTransportCharge> truckCharges = new ArrayList<>();
    if (Collections.isNotEmpty(truckingRates)) {
      Map<ChargeTarget, List<SellingRate>> collect = truckingRates.stream().collect(Collectors.groupingBy(SellingRate::getTarget));
      for (ChargeTarget target : collect.keySet()) {
        List<SellingRate> sellingRates = collect.get(target);
        BookingTruckTransportCharge truckCharge = computeTruckTransportCharge(target, template);
        for (SellingRate rate : sellingRates) {
          String unit = rate.getUnit();
          ContainerType containerType = ContainerTypeUnit.match(unit);

          CustomerTruckTransportAdditionalCharge addCharge = new CustomerTruckTransportAdditionalCharge();
          addCharge.setLabel(rate.getName());
          addCharge.setName(rate.getCode());
          addCharge.setCurrency(rate.getCurrency());
          addCharge.setUnitPrice(rate.getUnitPrice());
          addCharge.setNote(rate.getNote());
          addCharge.setExchangeRate(rate.getExchangeRate());
          if (containerType != null) {
            truckCharge.setTruckType(TruckType.CONTAINER);
            addCharge.setUnit(containerType.getLabel());
          } else {
            truckCharge.setTruckType(TruckType.REGULAR);
            addCharge.setUnit(unit);
          }
          addCharge.setTaxRate(rate.getTaxRate());
          addCharge.setFinalCharge(rate.getTotalAmount());
          addCharge.setFinalCharge(rate.getTotalAmount());
          addCharge.setDomesticCurrency(rate.getDomesticCurrency());
          addCharge.setDomesticFinalCharge(rate.getDomesticTotalAmount());
          truckCharge.withAdditionalCharge(addCharge);

        }

        truckCharges.add(truckCharge);
      }
    }

    return truckCharges;
  }

  private BookingTruckTransportCharge computeTruckTransportCharge(ChargeTarget target, Booking template) {
    BookingTruckTransportCharge charge = new BookingTruckTransportCharge();
    charge.setEditMode(EditMode.VALIDATED);
    charge.setValidFrom(new Date());
    charge.setValidTo(new Date());
    charge.setCurrency("VND");
    TransportationMode mode = inquiry.getMode();
    charge.setTruckType(TruckType.CONTAINER);
    if (TransportationMode.isAirTransport(mode)) charge.setTruckType(TruckType.REGULAR);
    if (TransportationMode.isTruckRegular(mode)) charge.setTruckType(TruckType.REGULAR);

    if (target.equals(ChargeTarget.ORIGIN)) {
      charge.setPickupAddress(inquiry.getPickupAddress());
      charge.setDeliveryLocationCode(inquiry.getFromLocationCode());
      charge.setDeliveryLocationLabel(inquiry.getFromLocationLabel());
    } else {
      charge.setPickupLocationCode(inquiry.getToLocationCode());
      charge.setPickupLocationLabel(inquiry.getToLocationLabel());
      charge.setDeliveryLocationLabel(inquiry.getDeliveryAddress());
    }

    charge.setRoute(charge.getPickupLocationCode() + "-" + charge.getDeliveryLocationCode());
    if (StringUtil.isEmpty(charge.getCarrierLabel())) charge.setCarrierRoute(charge.getRoute() + "-N/A");
    else charge.setCarrierRoute(charge.getRoute() + "-" + charge.getCarrierLabel());
    charge.setAssigneeLabel(inquiry.getSalemanLabel());
    return charge;
  }

  public List<BookingCustomClearance> computeCustomClearances(Booking template) {
    TransportationMode mode = inquiry.getMode();

    if (TransportationMode.isSeaTransport(mode)) template.setChargeType(ChargeType.SEA);
    else if (TransportationMode.isAirTransport(mode)) template.setChargeType(ChargeType.AIR);
    else throw RuntimeError.UnknownError("Not support yet!!!");

    Map<TransportationMode, List<SellingRate>> sellingRateByGroup = sellingRates.stream()
      .collect(Collectors.groupingBy(SellingRate::getGroup));

    List<SellingRate> freightRates = sellingRateByGroup.getOrDefault(mode, new ArrayList<>());
    Map<Type, List<SellingRate>> sellingGroup = freightRates.stream().collect(Collectors.groupingBy(SellingRate::getType));

    List<SellingRate> customRates = sellingGroup.getOrDefault(Type.CUSTOM, new ArrayList<>());
    List<BookingCustomClearance> customCharges = new ArrayList<>();
    if (Collections.isNotEmpty(customCharges)) {
      Map<ChargeTarget, List<SellingRate>> collect = customRates.stream().collect(Collectors.groupingBy(SellingRate::getTarget));
      for (ChargeTarget target : collect.keySet()) {
        List<SellingRate> sellingRates = collect.get(target);
        BookingCustomClearance customClearance =  new BookingCustomClearance();
        customClearance.setType(CustomClearanceType.SEA_FCL);
        customClearance.setValidFrom(new Date());
        customClearance.setValidTo(new Date());
        customClearance.setCurrency("VND");
        customClearance.setAssigneeLabel(inquiry.getSalemanLabel());

        for (SellingRate rate : sellingRates) {
          String unit = rate.getUnit();
          ContainerType containerType = ContainerTypeUnit.match(unit);
          CustomerCustomClearanceAdditionalCharge addCharge = new CustomerCustomClearanceAdditionalCharge();
          addCharge.setLabel(rate.getName());
          addCharge.setName(rate.getCode());
          addCharge.setCurrency(rate.getCurrency());
          addCharge.setUnitPrice(rate.getUnitPrice());
          addCharge.setNote(rate.getNote());
          addCharge.setTaxRate(rate.getTaxRate());
          addCharge.setFinalCharge(rate.getTotalAmount());
          addCharge.setDomesticCurrency(rate.getDomesticCurrency());
          addCharge.setDomesticFinalCharge(rate.getDomesticTotalAmount());

          double total = rate.getQuantity() * rate.getUnitPrice();
          double totalTax = total * rate.getTaxRate();
          addCharge.setTotal(total);
          addCharge.setTotalTax(totalTax);
          addCharge.setFinalCharge(total + totalTax);

          if (containerType != null) {
            addCharge.setUnit(containerType.getLabel());
          } else {
            addCharge.setUnit(unit);
          }
          customClearance.withAdditionalCharge(addCharge);
        }
        customCharges.add(customClearance);
      }
    }
    return customCharges;
  }

  public BookingAirTransportCharge computeAirCharge(BookingAirTransportCharge charge, QuotationCharge quote) {
    TransportationMode mode = inquiry.getMode();

    Map<TransportationMode, List<SellingRate>> sellingRateByGroup = sellingRates.stream()
      .collect(Collectors.groupingBy(SellingRate::getGroup));

    List<SellingRate> freightRates = sellingRateByGroup.getOrDefault(mode, new ArrayList<>());
    Map<Type, List<SellingRate>> sellingGroup = freightRates.stream().collect(Collectors.groupingBy(SellingRate::getType));

    List<CustomerAdditionalCharge> addCharges = createAddCharges(sellingGroup.getOrDefault(Type.LOCAL_CHARGE, new ArrayList<>()));

    charge = Objects.ensureNotNull(charge, BookingAirTransportCharge::new);
    final BookingShipmentInfo shipmentInfo = getBookingShipmentInfo();

    charge.setShipmentInfo(shipmentInfo);
    charge.setFromLocationCode(inquiry.getFromLocationCode());
    charge.setFromLocationLabel(inquiry.getFromLocationLabel());
    charge.setToLocationCode(inquiry.getToLocationCode());
    charge.setToLocationLabel(inquiry.getToLocationLabel());

    if(ClientPartnerType.isAgent(inquiry.getClientPartnerType())) {
      charge.setPayerPartnerId(inquiry.getHandlingAgentPartnerId());
      charge.setPayerFullName(inquiry.getHandlingAgentLabel());
    } else {
      charge.setPayerPartnerId(inquiry.getClientPartnerId());
      charge.setPayerFullName(inquiry.getClientLabel());
    }

    if (quote != null) {
      charge.setChargeableWeightInKG(inquiry.getGrossWeightKg());
      charge.setPurpose(quote.getPurpose());
      charge.setRefCurrency(quote.getRefCurrency());
      charge.setCurrency(quote.getCurrency());
      charge.setNote(quote.getNote());
      charge.setValidFrom(new Date());
      charge.setValidTo(quote.getValidity());
    }

    List<SellingRate> rates = sellingGroup.getOrDefault(Type.AIRFREIGHT, new ArrayList<>());
    if (!rates.isEmpty()) {
      Objects.assertTrue(rates.size() == 1, "Only one rate is allowed for Air Freight");
      SellingRate.convertToAirFreight(charge, rates.get(0));
    }
    List<CustomerAirTransportAdditionalCharge> mappedAddCharges = addCharges.stream()
      .map(CustomerAirTransportAdditionalCharge::new)
      .collect(Collectors.toList());
    charge.setTemporaryAdditionalCharges(mappedAddCharges);

    return charge;
  }

  public BookingSeaTransportCharge computeSeaCharge(BookingSeaTransportCharge charge, QuotationCharge quote) {
    TransportationMode mode = inquiry.getMode();

    Map<TransportationMode, List<SellingRate>> sellingRateByGroup = sellingRates.stream()
      .collect(Collectors.groupingBy(SellingRate::getGroup));

    List<SellingRate> freightRates = sellingRateByGroup.getOrDefault(mode, new ArrayList<>());
    Map<Type, List<SellingRate>> sellingGroup = freightRates.stream().collect(Collectors.groupingBy(SellingRate::getType));

    List<CustomerAdditionalCharge> addCharges = createAddCharges(sellingGroup.getOrDefault(Type.LOCAL_CHARGE, new ArrayList<>()));

    if (charge == null) {
      charge = new BookingSeaTransportCharge();
      if (TransportationMode.isSeaFCLTransport(mode)) charge.setType(SeaType.FCL);
      else charge.setType(SeaType.LCL);
    }
    BookingShipmentInfo shipmentInfo = getBookingShipmentInfo();
    charge.setShipmentInfo(shipmentInfo);

    charge.setFromLocationCode(inquiry.getFromLocationCode());
    charge.setFromLocationLabel(inquiry.getFromLocationLabel());
    charge.setToLocationCode(inquiry.getToLocationCode());
    charge.setToLocationLabel(inquiry.getToLocationLabel());

    if(ClientPartnerType.isAgent(inquiry.getClientPartnerType())) {
      charge.setPayerPartnerId(inquiry.getHandlingAgentPartnerId());
      charge.setPayerFullName(inquiry.getHandlingAgentLabel());
    } else {
      charge.setPayerPartnerId(inquiry.getClientPartnerId());
      charge.setPayerFullName(inquiry.getClientLabel());
    }

    if (quote != null) {
      charge.setChargeableVolumeInCBM(inquiry.getVolumeCbm());
      charge.setPurpose(inquiry.getPurpose());
      charge.setRefCurrency(quote.getRefCurrency());
      charge.setCurrency(quote.getCurrency());
      charge.setNote(quote.getNote());
      charge.setValidFrom(new Date());
      charge.setValidTo(quote.getValidity());
    }

    List<SellingRate> oceanFreights = sellingGroup.getOrDefault(Type.SEAFREIGHT, new ArrayList<>());
    if (!oceanFreights.isEmpty()) {
      if (TransportationMode.isSeaFCLTransport(mode)) {
        SellingRate.convertToOceanFreightFCL(charge, oceanFreights);
      } else {
        Objects.assertTrue(oceanFreights.size() == 1, "Only one rate is allowed for LCL");
        SellingRate.convertToOceanFreightLCL(charge, oceanFreights.get(0));
      }
    }

    List<CustomerSeaTransportAdditionalCharge> mappedAddCharges = addCharges.stream()
      .map(CustomerSeaTransportAdditionalCharge::new)
      .collect(Collectors.toList());

    charge.setTemporaryAdditionalCharges(mappedAddCharges);

    return charge;
  }

  private BookingShipmentInfo getBookingShipmentInfo() {
    BookingShipmentInfo shipmentInfo = new BookingShipmentInfo();
    shipmentInfo.setCarrierPartnerId(carrierPartnerId);
    shipmentInfo.setCarrierLabel(carrierLabel);
    shipmentInfo.setPlanHBCode(hawbNo);
    shipmentInfo.setPlanMBCode(mawbNo);
    shipmentInfo.setPlanTimeArrival(planTimeArrival);
    shipmentInfo.setPlanTimeDeparture(planTimeDeparture);
    return shipmentInfo;
  }

  public List<CustomerAdditionalCharge> createAddCharges(List<SellingRate> sellingRates) {
    List<CustomerAdditionalCharge> holder = new ArrayList<>();
    for (SellingRate sel : sellingRates) {
      CustomerAdditionalCharge addCharge = new CustomerAdditionalCharge();
      addCharge.setPayerFullName(sel.getPayerPartnerLabel());
      addCharge.setPayerPartnerId(sel.getPayerPartnerId());
      addCharge.setName(sel.getCode());
      addCharge.setLabel(sel.getName());
      addCharge.setQuantity(sel.getQuantity());
      addCharge.setUnitPrice(sel.getUnitPrice());
      addCharge.setUnit(sel.getUnit());
      addCharge.setCurrency(sel.getCurrency());
      addCharge.setTaxRate(sel.getTaxRate());
      addCharge.setNote(sel.getNote());
      addCharge.setExchangeRate(sel.getExchangeRate());

      double total = sel.getQuantity() * sel.getUnitPrice();
      addCharge.setTotal(total);
      addCharge.setTotalTax(total * sel.getTaxRate());
      addCharge.setFinalCharge(sel.getTotalAmount());
      addCharge.setDomesticFinalCharge(sel.getDomesticTotalAmount());
      holder.add(addCharge);
    }
    return holder;
  }

  public MapObject toBFSOneIBooking(CRMPartner customer) {
    Objects.assertNotNull(customer, "Customer is not found when create Internal Booking!");
    MapObject ibooking = new MapObject();
    if (StringUtil.isEmpty(bfsoneReference)) ibooking.put("CreatedDate", DateUtil.asBFSOneFormat(new Date()));
    ibooking.set("ServiceType", inquiry.getTypeOfService());
    ibooking.set("BkgID", bfsoneReference);

    ibooking.set("ReceiveUserID", "");
    ibooking.set("SendUserID", "");

    ibooking.set("CustomerID", customer.getBfsonePartnerCode());
    ibooking.set("ColoaderID", "");
    ibooking.set("AgentID", "");
    ibooking.set("ShipperName", customer.getPrintCustomConfirmBillInfo());
    ibooking.set("ConsigneeID", "");
    ibooking.set("ConsigneeName", customer.getPrintCustomConfirmBillInfo());

    ibooking.set("ShipmentType", shipmentType);
    ibooking.set("POLCode", inquiry.getFromLocationCode());
    ibooking.set("POLName", inquiry.getFromLocationLabel());
    ibooking.set("PODCode", inquiry.getToLocationCode());
    ibooking.set("PODName", inquiry.getToLocationLabel());
    if(StringUtil.isNotEmpty(inquiry.getFinalDestination())) {
      ibooking.set("DeliveryPlace", inquiry.getFinalDestination());
    } else {
      ibooking.set("DeliveryPlace", inquiry.getToLocationLabel());
    }

    ibooking.set("CargoPickupAt", inquiry.getPickupAddress());
    ibooking.set("CargoDeliveryAt", inquiry.getDeliveryAddress());
    ibooking.set("ETA", DateUtil.asBFSOneFormat(planTimeArrival));
    ibooking.set("ETD", DateUtil.asBFSOneFormat(planTimeDeparture));
    ibooking.set("Flight_Vessel", "");
    ibooking.set("Voyage", "");
    ibooking.set("Flight_Vessel_Date", DateUtil.asBFSOneFormat(planTimeArrival));
    ibooking.set("Commodity", inquiry.getCommodity());
    ibooking.set("DescriptionOfGoods", inquiry.getDescOfGoods());
    ibooking.set("GW", inquiry.getGrossWeightKg());
    ibooking.set("Packages", inquiry.getPackageQty());
    ibooking.set("UnitOfPackage", inquiry.getContainerTypes());
    ibooking.set("CBM", inquiry.getVolumeCbm());
    ibooking.set("MAWBNO", mawbNo);
    ibooking.set("HAWBNO", hawbNo);
    ibooking.set("BookingNo", bookingNumber);
    String safeNote = note != null && note.length() > 399
      ? note.substring(0, 399)
      : note;

    ibooking.set("Note", safeNote);

    // container list
    List<MapObject> containers = new ArrayList<>();

    for (Container container : inquiry.getContainers()) {
      MapObject rec = new MapObject();
      ContainerType containerType = ContainerTypeUnit.match(container.getContainerType());
      if (containerType != null) {
        rec.set("ContainerType", containerType.getLabel());
      } else {
        rec.set("ContainerType", container.getContainerType());
      }
      rec.set("Quantity", container.getQuantity());
      rec.set("ContainerNo", "");
      rec.set("ContainerSeal", "");
      containers.add(rec);
    }
    ibooking.set("Containers", containers);
    List<MapObject> collected = new ArrayList<>();
    for (SellingRate rate : this.sellingRates) {
      MapObject rec = rate.toBFSOneFee(customer);
      collected.add(rec);
    }
    ibooking.set("SellingRates", collected);
    return ibooking;
  }

}