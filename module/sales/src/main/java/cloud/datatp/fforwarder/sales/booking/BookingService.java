package cloud.datatp.fforwarder.sales.booking;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cloud.datatp.fforwarder.sales.booking.entity.Booking;
import lombok.Getter;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.service.BaseComponent;
import net.datatp.security.client.ClientContext;

@Service("BookingService")
public class BookingService extends BaseComponent {

  @Autowired
  @Getter
  private BookingLogic bookingLogic;

  @Transactional(readOnly = true)
  public Booking getBooking(ClientContext client, Long bookingId) {
    return bookingLogic.getBooking(client, bookingId);
  }
  
  @Transactional
  public Booking sendBFSOneIBooking(ClientContext client, Booking booking) {
    return bookingLogic.sendBFSOneIBooking(client, booking);
  }

  @Transactional
  public Booking resendBFSOneIBooking(ClientContext client, Booking booking) {
    return bookingLogic.resendBFSOneIBooking(client, booking);
  }
  
  @Transactional
  public Booking saveBooking(ClientContext client, ICompany company, Booking booking) {
    return bookingLogic.saveBooking(client, company, booking);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchBookings(ClientContext client, ICompany company, SqlQueryParams params) {
    return bookingLogic.searchBookings(client, company, params);
  }

  @Transactional
  public boolean changeBookingStorageState(ClientContext client, ChangeStorageStateRequest req) {
    return bookingLogic.changeBookingStorageState(client, req);
  }

  @Transactional
  public int deleteBookings(ClientContext client, ICompany company, List<Long> bookingIds) {
    return bookingLogic.deleteBookings(client, company, bookingIds);
  }

  @Transactional
  public Booking newBooking(ClientContext client, Booking booking) {
    return bookingLogic.newBooking(client, booking);
  }

}