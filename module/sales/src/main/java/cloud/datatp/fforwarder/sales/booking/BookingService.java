package cloud.datatp.fforwarder.sales.booking;

import cloud.datatp.fforwarder.sales.booking.dto.BookingModel;
import cloud.datatp.fforwarder.sales.booking.entity.Booking;
import java.util.List;
import lombok.Getter;
import net.datatp.module.core.security.AuthorizationCipherTool;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.service.BaseComponent;
import net.datatp.security.client.ClientContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("BookingService")
public class BookingService extends BaseComponent {

  @Autowired
  @Getter
  private BookingLogic bookingLogic;

  @Autowired
  private AuthorizationCipherTool authCipherTool;

  @Transactional(readOnly = true)
  public Booking getBookingModel(ClientContext client, Long bookingId) {
    return bookingLogic.getBookingModel(client, bookingId);
  }

  @Transactional
  public BookingModel sendBFSOneIBooking(ClientContext client, ICompany company, BookingModel bookingModel) {
    return bookingLogic.sendBFSOneIBooking(client, company, bookingModel);
  }

  @Transactional
  public BookingModel sendBFSOneIBookingNew(ClientContext client, Booking booking) {
    return bookingLogic.sendBFSOneIBookingNew(client, booking);
  }

  @Transactional
  public BookingModel resendBFSOneIBooking(ClientContext client, ICompany company, BookingModel bookingModel) {
    return bookingLogic.resendBFSOneIBooking(client, company, bookingModel);
  }
  
  @Transactional
  public BookingModel saveBookingModel(ClientContext client, ICompany company, BookingModel bookingModel) {
    return bookingLogic.saveBookingModel(client, company, bookingModel);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchBookings(ClientContext client, ICompany company, SqlQueryParams params) {
    return bookingLogic.searchBookings(client, company, params);
  }

  @Transactional
  public boolean changeBookingStorageState(ClientContext client, ChangeStorageStateRequest req) {
    return bookingLogic.changeBookingStorageState(client, req);
  }

  @Transactional
  public int deleteBookings(ClientContext client, ICompany company, List<Long> bookingIds) {
    return bookingLogic.deleteBookings(client, company, bookingIds);
  }

  @Transactional
  public BookingModel newBookingModel(ClientContext clientCtx, BookingModel model) {
    return bookingLogic.newBookingModel(clientCtx, model);
  }

}