package cloud.datatp.fforwarder.sales.booking;

import cloud.datatp.fforwarder.price.InquiryRequestLogic;
import cloud.datatp.fforwarder.price.entity.InquiryRequest;
import cloud.datatp.fforwarder.sales.booking.dto.BookingModel;
import cloud.datatp.fforwarder.sales.inquiry.entity.SpecificServiceInquiry;
import net.datatp.module.bot.BotEvent;
import net.datatp.module.bot.BotEventHandler;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.monitor.SourceType;
import net.datatp.security.client.ClientContext;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;

//@Component
public class BookingBotSaveBookingEventHandler extends BotEventHandler {
  
  @Autowired
  private BookingLogic bookingLogic;
  
  @Autowired
  private InquiryRequestLogic requestLogic;
  
  public BookingBotSaveBookingEventHandler() {
    super(BookingBotSaveBookingEvent.EVENT_NAME);
  }
  
  @Override
  public void handle(ClientContext client, ICompany company, SourceType sourceType, BotEvent<?> event) {
    BookingModel bookingModel = (BookingModel) event.getData();
    
    bookingModel = bookingLogic.saveBookingModel(client, company, bookingModel);

    // * update status request to won
    SpecificServiceInquiry inquiry = bookingModel.getInquiry();
    String referenceCode = inquiry.getReferenceCode();
    if (StringUtil.isNotEmpty(referenceCode)) {
      InquiryRequest inquiryRequest = requestLogic.getInquiryRequest(client, company, referenceCode);
      if (inquiryRequest != null) {
        inquiryRequest.setStatus(InquiryRequest.InquiryStatus.SUCCESS);
        requestLogic.saveInquiryRequest(client, company, inquiryRequest);
      }
    }

  }
}