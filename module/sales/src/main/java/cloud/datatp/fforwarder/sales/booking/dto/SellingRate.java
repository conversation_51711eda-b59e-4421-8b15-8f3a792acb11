package cloud.datatp.fforwarder.sales.booking.dto;

import cloud.datatp.fforwarder.core.common.TransportationMode;
import cloud.datatp.fforwarder.core.partner.entity.CRMPartner;
import cloud.datatp.fforwarder.price.common.ChargeTarget;
import cloud.datatp.fforwarder.sales.booking.entity.BookingAirTransportCharge;
import cloud.datatp.fforwarder.sales.booking.entity.BookingCustomClearance;
import cloud.datatp.fforwarder.sales.booking.entity.BookingSeaTransportCharge;
import cloud.datatp.fforwarder.sales.booking.entity.BookingTruckTransportCharge;
import cloud.datatp.fforwarder.sales.common.ContainerType;
import cloud.datatp.fforwarder.sales.common.ContainerType.ContainerTypeUnit;
import cloud.datatp.fforwarder.sales.common.entity.CustomerAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerCustomClearanceAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerSeaTransportCharge.SeaType;
import cloud.datatp.fforwarder.sales.common.entity.CustomerTruckTransportAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.quote.CustomerAirPriceGroup;
import cloud.datatp.fforwarder.sales.common.quote.CustomerSeaFCLPriceGroup;
import cloud.datatp.fforwarder.sales.common.quote.CustomerSeaLCLPriceGroup;
import cloud.datatp.fforwarder.sales.inquiry.entity.Container;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.StringUtil;

@NoArgsConstructor
@Getter
@Setter
public class SellingRate {

  public static enum Type {SEAFREIGHT, AIRFREIGHT, TRUCKING, CUSTOM, LOCAL_CHARGE;}

  private TransportationMode group;
  private Type type;
  private ChargeTarget target = ChargeTarget.ORIGIN;

  private Long payerPartnerId;
  private String payerPartnerCode;
  private String payerPartnerLabel;

  private String code;
  private String name;
  private double quantity;
  private String unit;

  private double unitPrice;
  private String currency;

  private double exchangeRate;

  private double taxRate;
  private double totalAmount;

  private String domesticCurrency = "VND";
  private double domesticUnitPrice;
  private double domesticTotalAmount;

  private String note;

  public static List<SellingRate> sellingRateCreator(List<Container> containers, BookingSeaTransportCharge seaCharge) {
    SeaType type = seaCharge.getType();
    TransportationMode group = TransportationMode.SEA_LCL;
    if (type.equals(SeaType.FCL)) group = TransportationMode.SEA_FCL;
    List<SellingRate> holder = sellingRateCreator(group, seaCharge.getTemporaryAdditionalCharges());

    double exchangeRate = seaCharge.getExchangeRate();

    if (seaCharge.getType().equals(SeaType.FCL) && seaCharge.getFclPriceGroup() != null) {
      MapObject priceGroup = seaCharge.getFclPriceGroup().toMapObject();
      for (Container sel : containers) {
        String containerTypeAsStr = sel.getContainerType();
        ContainerType containerType = ContainerTypeUnit.match(containerTypeAsStr);
        String priceLevel = containerType.toFCLPriceLevel();
        Double priceValue = priceGroup.getDouble(priceLevel, 0D);
        if (priceValue > 0) {
          SellingRate sellingRate = new SellingRate();
          sellingRate.setPayerPartnerId(seaCharge.getPayerPartnerId());
          sellingRate.setPayerPartnerLabel(seaCharge.getPayerFullName());
          sellingRate.setGroup(group);
          sellingRate.setType(Type.SEAFREIGHT);
          sellingRate.setCode("S_OF");
          sellingRate.setName("SEAFREIGHT");
          sellingRate.setUnit(containerType.getLabel());
          sellingRate.setUnitPrice(priceValue);
          sellingRate.setQuantity(sel.getQuantity());
          sellingRate.setTaxRate(seaCharge.getTaxRate());
          sellingRate.setCurrency(seaCharge.getCurrency());
          sellingRate.setNote(seaCharge.getNote());
          sellingRate.setExchangeRate(exchangeRate);
          sellingRate.setDomesticCurrency("VND");
          sellingRate.setPayerPartnerId(seaCharge.getPayerPartnerId());
          sellingRate.setPayerPartnerLabel(seaCharge.getPayerFullName());

          //TODO: Dan - handle domestic unit price
          double totalAmount = sel.getQuantity() * priceValue;
          if (seaCharge.getTaxRate() > 0) totalAmount = totalAmount + (totalAmount * seaCharge.getTaxRate());
          sellingRate.setTotalAmount(totalAmount);
          double dUnitPrice = priceValue * exchangeRate;
          sellingRate.setDomesticUnitPrice(dUnitPrice);
          sellingRate.setDomesticTotalAmount(dUnitPrice * sel.getQuantity());
          holder.add(sellingRate);
        }
      }
    } else if (seaCharge.getLclPriceGroup() != null){
      CustomerSeaLCLPriceGroup priceGroup = seaCharge.getLclPriceGroup();
      double selectedPrice = priceGroup.getSelectedPrice();
      if (selectedPrice != 0) {
        SellingRate sellingRate = getSellingRate(seaCharge, group, selectedPrice);
        holder.add(sellingRate);
      }
    }
    return holder;
  }

  private static SellingRate getSellingRate(BookingSeaTransportCharge seaCharge, TransportationMode group, double selectedPrice) {
    SellingRate sellingRate = new SellingRate();
    sellingRate.setPayerPartnerId(seaCharge.getPayerPartnerId());
    sellingRate.setPayerPartnerLabel(seaCharge.getPayerFullName());
    sellingRate.setGroup(group);
    sellingRate.setType(Type.SEAFREIGHT);
    sellingRate.setCode("S_OF");
    sellingRate.setName("SEAFREIGHT");
    sellingRate.setUnitPrice(selectedPrice);
    sellingRate.setUnit("CBM");
    sellingRate.setQuantity(seaCharge.getChargeableVolumeInCBM());
    sellingRate.setCurrency(seaCharge.getCurrency());
    sellingRate.setTaxRate(seaCharge.getTaxRate());
    sellingRate.setExchangeRate(seaCharge.getExchangeRate());
    sellingRate.setNote(seaCharge.getNote());
    sellingRate.setDomesticCurrency("VND");
    sellingRate.setPayerPartnerId(seaCharge.getPayerPartnerId());
    sellingRate.setPayerPartnerLabel(seaCharge.getPayerFullName());
    double totalAmount = seaCharge.getChargeableVolumeInCBM() * selectedPrice;
    if (seaCharge.getTaxRate() > 0) totalAmount = totalAmount + (totalAmount * seaCharge.getTaxRate());
    sellingRate.setTotalAmount(totalAmount);
    double dUnitPrice = selectedPrice * seaCharge.getExchangeRate();
    sellingRate.setDomesticUnitPrice(dUnitPrice);
    sellingRate.setDomesticTotalAmount(dUnitPrice * seaCharge.getChargeableVolumeInCBM());
    return sellingRate;
  }

  private static SellingRate getSellingRate(BookingAirTransportCharge airCharge, TransportationMode GROUP, double selectedPrice) {
    SellingRate sellingRate = new SellingRate();
    sellingRate.setGroup(GROUP);
    sellingRate.setType(Type.AIRFREIGHT);
    sellingRate.setCode("S_AF");
    sellingRate.setName("AIRFREIGHT");
    sellingRate.setUnitPrice(selectedPrice);
    sellingRate.setUnit("KGS");
    sellingRate.setQuantity(airCharge.getChargeableWeightInKG());
    sellingRate.setCurrency(airCharge.getCurrency());
    sellingRate.setTaxRate(airCharge.getTaxRate());
    sellingRate.setNote(airCharge.getNote());
    sellingRate.setExchangeRate(airCharge.getExchangeRate());
    sellingRate.setDomesticCurrency("VND");
    sellingRate.setPayerPartnerId(airCharge.getPayerPartnerId());
    sellingRate.setPayerPartnerLabel(airCharge.getPayerFullName());
    double totalAmount = airCharge.getChargeableWeightInKG() * selectedPrice;
    if (airCharge.getTaxRate() > 0) totalAmount = totalAmount + (totalAmount * airCharge.getTaxRate());
    sellingRate.setTotalAmount(totalAmount);
    double dUnitPrice = selectedPrice * airCharge.getExchangeRate();
    sellingRate.setDomesticUnitPrice(dUnitPrice);
    sellingRate.setDomesticTotalAmount(dUnitPrice * airCharge.getChargeableWeightInKG());
    return sellingRate;
  }

  public SellingRate(TransportationMode group, CustomerAdditionalCharge addCharge) {
    this.group = group;
    this.type = Type.LOCAL_CHARGE;
    this.payerPartnerId = addCharge.getPayerPartnerId();
    this.payerPartnerLabel = addCharge.getPayerFullName();
    this.code = addCharge.getName();
    this.name = addCharge.getLabel();
    this.quantity = addCharge.getQuantity();
    this.unitPrice = addCharge.getUnitPrice();
    this.unit = addCharge.getUnit();
    this.currency = addCharge.getCurrency();
    this.exchangeRate = addCharge.getExchangeRate();
    this.taxRate = addCharge.getTaxRate();
    this.note = addCharge.getNote();

    if (addCharge.getFinalCharge() == 0) {
      double totalAmount = addCharge.getQuantity() * addCharge.getUnitPrice();
      if (addCharge.getTaxRate() > 0) totalAmount = totalAmount + (totalAmount * addCharge.getTaxRate());
      this.totalAmount = totalAmount;
      this.domesticTotalAmount = totalAmount * exchangeRate;
    } else {
      this.totalAmount = addCharge.getFinalCharge();
      this.domesticTotalAmount = addCharge.getDomesticFinalCharge();
    }
    this.domesticUnitPrice = addCharge.getUnitPrice() * exchangeRate;
    this.domesticCurrency = "VND";
  }

  public static List<SellingRate> sellingRateCreator(BookingAirTransportCharge airCharge) {
    TransportationMode GROUP = TransportationMode.AIR;
    List<SellingRate> holder = sellingRateCreator(GROUP, airCharge.getTemporaryAdditionalCharges());

    CustomerAirPriceGroup priceGroup = airCharge.getPriceGroup();
    if (priceGroup != null) {
      double selectedPrice = priceGroup.getSelectedPrice();
      if (selectedPrice != 0) {
        SellingRate sellingRate = getSellingRate(airCharge, GROUP, selectedPrice);
        holder.add(sellingRate);
      }
    }
    return holder;
  }

  public static List<SellingRate> sellingRateTruckingCreator(List<BookingTruckTransportCharge> truckCharges) {
    if (Collections.isEmpty(truckCharges)) return new ArrayList<>();
    List<CustomerTruckTransportAdditionalCharge> truckAddCharges = truckCharges.stream()
      .flatMap(sel -> sel.getAdditionalCharges().stream())
      .collect(Collectors.toList());
    return SellingRate.sellingRateCreator(TransportationMode.TRUCK_CONTAINER, truckAddCharges);
  }

  public static List<SellingRate> sellingRateCustomCreator(List<BookingCustomClearance> customClearances) {
    if (Collections.isEmpty(customClearances)) return new ArrayList<>();
    List<CustomerCustomClearanceAdditionalCharge> customAddCharges = customClearances.stream()
      .flatMap(sel -> sel.getAdditionalCharges().stream())
      .collect(Collectors.toList());
    return SellingRate.sellingRateCreator(TransportationMode.UNKNOWN, customAddCharges);
  }

  public static <T extends CustomerAdditionalCharge> List<SellingRate> sellingRateCreator(TransportationMode group, List<T> addCharges) {
    List<SellingRate> holder = new ArrayList<>();
    for (T addCharge : addCharges) {
      SellingRate sellingRate = new SellingRate(group, addCharge);
      holder.add(sellingRate);
    }
    return holder;
  }

  // --------------- convert Selling Rate to BookingTransportCharge --------------
  public static BookingSeaTransportCharge convertToOceanFreightFCL(BookingSeaTransportCharge template, List<SellingRate> rates) {
    if (rates == null || rates.isEmpty()) return template;
    Objects.ensureNotNull(template, BookingSeaTransportCharge::new);
    boolean isFCL = rates.stream().allMatch(sel -> TransportationMode.isSeaFCLTransport(sel.getGroup()));
    Objects.assertTrue(isFCL, "All rates must be FCL");
    Optional<SellingRate> first = rates.stream().filter(sel -> sel.getPayerPartnerId() == null).findFirst();
    if (first.isPresent()) {
      SellingRate sellingRate = first.get();
      template.setPayerPartnerId(sellingRate.getPayerPartnerId());
      template.setPayerFullName(sellingRate.getPayerPartnerLabel());
      template.setExchangeRate(sellingRate.getExchangeRate());
      template.setTaxRate(sellingRate.getTaxRate());
      template.setNote(sellingRate.getNote());
    }

    //TODO: Dan - review this code, note should be in price level
    MapObject priceGroupMap = new MapObject();
    for (SellingRate rate : rates) {
      String containerUnit = rate.getUnit();
      ContainerType containerType = ContainerTypeUnit.match(containerUnit);
      String priceLevel = containerType.toFCLPriceLevel();
      priceGroupMap.put(priceLevel, rate.getUnitPrice());
    }
    CustomerSeaFCLPriceGroup priceGroup = new CustomerSeaFCLPriceGroup().computeFrom(priceGroupMap);
    template.setFclPriceGroup(priceGroup);
    return template;
  }

  public static BookingSeaTransportCharge convertToOceanFreightLCL(BookingSeaTransportCharge template, SellingRate rate) {
    Objects.ensureNotNull(template, BookingSeaTransportCharge::new);
    if (rate == null) return template;
    if (template.getPayerPartnerId() == null) {
      template.setPayerPartnerId(rate.getPayerPartnerId());
      template.setPayerFullName(rate.getPayerPartnerLabel());
    }
    template.setExchangeRate(rate.getExchangeRate());
    template.setTaxRate(rate.getTaxRate());
    template.setNote(rate.getNote());

    CustomerSeaLCLPriceGroup priceGroup = new CustomerSeaLCLPriceGroup();
    double unitPrice = rate.getUnitPrice();
    priceGroup.computePriceType(rate.getQuantity());
    priceGroup.setSelectedPrice(unitPrice);
    template.setLclPriceGroup(priceGroup);
    return template;
  }

  public static BookingAirTransportCharge convertToAirFreight(BookingAirTransportCharge template, SellingRate rate) {
    Objects.ensureNotNull(template, BookingAirTransportCharge::new);
    if (rate == null) return template;
    if (template.getPayerPartnerId() == null) {
      template.setPayerPartnerId(rate.getPayerPartnerId());
      template.setPayerFullName(rate.getPayerPartnerLabel());
    }
    template.setChargeableWeightInKG(rate.getQuantity());
    template.setExchangeRate(rate.getExchangeRate());
    template.setTaxRate(rate.getTaxRate());
    template.setNote(rate.getNote());
    CustomerAirPriceGroup priceGroup = new CustomerAirPriceGroup();
    double unitPrice = rate.getUnitPrice();
    priceGroup.computePriceType(rate.getQuantity());
    priceGroup.setSelectedPrice(unitPrice);
    template.setPriceGroup(priceGroup);
    return template;
  }

  public MapObject toBFSOneFee(CRMPartner customer) {
    MapObject rec = new MapObject();
    if (StringUtil.isNotEmpty(payerPartnerCode) && !payerPartnerCode.equals(customer.getBfsonePartnerCode())) {
      rec.set("PartnerID_KB", payerPartnerCode);
      rec.set("PartnerName_KB", payerPartnerLabel);
      rec.set("KB", true);
    }

    rec.set("FeeName", name);
    rec.set("FeeCode", code);
    rec.set("FeeName", name);
    rec.set("Quantity", quantity);
    rec.set("Unit", unit);
    rec.set("Tax", taxRate * 100);
    rec.set("Notes", note);

    if (domesticUnitPrice > 0) {
      rec.set("UnitPrice", domesticUnitPrice);
      rec.set("Currency", "VND");
      rec.set("TotalAmount", domesticTotalAmount);
    } else {
      rec.set("UnitPrice", unitPrice);
      rec.set("Currency", currency);
      rec.set("TotalAmount", totalAmount);
    }
    return rec;
  }

}