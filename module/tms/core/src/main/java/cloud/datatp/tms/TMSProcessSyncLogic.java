package cloud.datatp.tms;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cloud.datatp.fleet.vehicle.VehicleFleetLogic;
import cloud.datatp.fleet.vehicle.VehicleLogic;
import cloud.datatp.fleet.vehicle.VehicleTripGoodsTrackingLogic;
import cloud.datatp.fleet.vehicle.entity.VehicleFleet;
import cloud.datatp.fleet.vehicle.entity.VehicleTrip;
import cloud.datatp.fleet.vehicle.entity.VehicleTripGoodsTracking;
import cloud.datatp.fleet.vehicle.entity.VehicleTripGoodsTracking.VehicleTripGoodsTrackingStatus;
import cloud.datatp.tms.bill.TMSBillLogic;
import cloud.datatp.tms.bill.entity.TMSBill;
import cloud.datatp.tms.bill.models.TMSProcessingRequest;
import cloud.datatp.tms.input.plugin.TMSWebhookModel;
import cloud.datatp.tms.ops.TMSOperationsLogic;
import cloud.datatp.tms.ops.entity.TMSOperations;
import cloud.datatp.tms.ops.entity.TMSOperations.TMSOperationsStatus;
import cloud.datatp.tms.partner.TMSPartnerLogic;
import cloud.datatp.tms.partner.entity.TMSPartner;
import cloud.datatp.tms.partner.entity.TMSWebhookConfig;
import cloud.datatp.vendor.TMSVendorBillLogic;
import cloud.datatp.vendor.entity.TMSVendorAttachment;
import cloud.datatp.vendor.entity.TMSVendorBill;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.module.core.security.AuthorizedToken;
import net.datatp.module.core.security.api.ApiAuthorizationService;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.input.InputProcessStatus;
import net.datatp.module.data.input.models.InputDataProcessResult;
import net.datatp.module.data.input.plugin.InputRecordProcessResult;
import net.datatp.module.data.output.DataTPWebhookClient;
import net.datatp.security.client.ClientContext;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.MapObject;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.StringUtil;

@Slf4j
@Component
public class TMSProcessSyncLogic extends DAOService {
  
  @Autowired
  private VehicleTripGoodsTrackingLogic goodsTrackingLogic;
  
  @Autowired
  private TMSOperationsLogic operationsLogic;
  
  @Autowired
  private VehicleLogic vehicleLogic;

  @Autowired
  private VehicleFleetLogic vehicleFleetLogic;
  
  @Autowired
  private TMSBillLogic tmsBillLogic;
  
  @Autowired
  private TMSVendorBillLogic vendorBillLogic;
  
  @Autowired
  private TMSPartnerLogic partnerLogic;
  
  @Autowired
  private CompanyLogic companyLogic;

  @Autowired
  private TMSOperationsLogic opsLogic;
  
  @Autowired
  private ApiAuthorizationService apiAuthorizationService;
  
  public InputDataProcessResult syncVendorBillAccessToken(ClientContext client, ICompany company, Long tmsBillId) {
    TMSBill bill = tmsBillLogic.getTMSBillById(client, company, tmsBillId);
    return syncVendorBillAccessToken(client, company, bill);
  }
  
  public InputDataProcessResult syncVendorBillAccessToken(ClientContext client, ICompany company, TMSBill bill) {
    String originalCustomerCode = bill.getOriginalCustomerCode();
    if(originalCustomerCode == null) return null;
    
    String tmsAuthorization = createAccessTokenByTMSBill(client, company, bill);
    
    TMSPartner customer = partnerLogic.getByWebhookCompanyCode(client, company, originalCustomerCode);
    if(!(customer != null && customer.getWebhookConfig() != null && customer.getWebhookConfig().isAutoPush())) return null;
    TMSWebhookConfig config = customer.getWebhookConfig();
    MapObject rec = new MapObject();
    rec.add("id", bill.getOriginVendorBillId());
    rec.add("billId", bill.getRefId());
    rec.add("vendorAuthorization", tmsAuthorization);
    rec.add("update", "AUTHORIZATION");
    
    TMSWebhookModel model = new TMSWebhookModel(config, "vendor-bill");
    model.addRecord(rec);
    return model.processResult("sync-tms-vendor-bill-access-token", "Sync Vendor Bill Access Token", false);
  }
  
  public String createAccessTokenByTMSBill(ClientContext client, ICompany company, TMSBill bill) {
    TMSPartner customer = partnerLogic.getByWebhookCompanyCode(client, company, bill.getOriginalCustomerCode());
    if(!(customer != null && customer.getWebhookConfig() != null)) return null;
    TMSWebhookConfig config = customer.getWebhookConfig();
    AuthorizedToken token = new AuthorizedToken();
    token.setResourceHandler("resource:tms-goods-tracking");
    token.withAllowedResourceId("tmsBillId", bill.getId());
    return apiAuthorizationService.createAuthorizedToken(client, company, config.getTokenId(), token);
  }
  
  public String createAccessTokenByTMSBillPrint(ClientContext client, ICompany company, Long tokenId, List<Long> billIds) {
    AuthorizedToken token = new AuthorizedToken();
    token.setResourceHandler("resource:tms-bill-print");
    token.withAllowedResourceId("tmsBillIds", billIds);
    return apiAuthorizationService.createAuthorizedToken(client, company, tokenId, token);
  }
  
  public List<TMSVendorBill> syncVendorBillWithVehicleTripGoodsTrackings(ClientContext client, ICompany company, Long [] tmsBillIds) {
    Map<Long, TMSWebhookModel>  tmsCustomerWebhookMaps = new HashMap<>();
    List<TMSVendorBill> vendorBills = new ArrayList<>();
    List<TMSBill> tmsBills = tmsBillLogic.findTMSBills(client, company, tmsBillIds);
    for(TMSBill bill : tmsBills) {
      TMSVendorBill vendorBill = null;
      if(bill.getOriginVendorBillId() == null) {
        vendorBill = vendorBillLogic.getByTMSBillId(client, company, bill.getId());
        if(vendorBill == null) {
          vendorBill = new TMSVendorBill();
          vendorBill = vendorBill.updateByTMSBill(bill);
          vendorBill = vendorBillLogic.saveVendorBill(client, company, vendorBill);
        }
        bill.setOriginVendorBillId(vendorBill.getId());
        bill = tmsBillLogic.saveTMSBill(client, company, bill);
      } else {
        vendorBill = vendorBillLogic.getVendorBillById(client, company, bill.getOriginVendorBillId());
        if(vendorBill == null) vendorBill = new TMSVendorBill();
      }
      vendorBill.getBillTrackings().clear();
      List<VehicleTripGoodsTracking> trackings = goodsTrackingLogic.findVehicleTripGoodTrackingByTMSBillId(client, company, bill.getId());
      for(VehicleTripGoodsTracking tracking : trackings) {
        VehicleTrip trip = vehicleLogic.getVehicleTrip(client, company, tracking.getVehicleTripId());
        vendorBill.updateByGoodsTrackingAndTrip(tracking, trip);
      }
      vendorBill.calculateTotalCost();
      
      String originalCustomerCode = bill.getOriginalCustomerCode();
      if(originalCustomerCode != null) {
        //sync by webhook
        TMSPartner customer = partnerLogic.getByWebhookCompanyCode(client, company, originalCustomerCode);
        if(customer != null && customer.getWebhookConfig() != null && customer.getWebhookConfig().isAutoPush()) {
          TMSWebhookConfig config = customer.getWebhookConfig();
          MapObject rec = new MapObject();
          rec.add("billId", bill.getRefId());
          rec.add("billCode", bill.getRefCode());
          rec.add("label", bill.getLabel());
          rec.add("billTrackings", vendorBill.getBillTrackings());
          rec.add("id", bill.getOriginVendorBillId());
          rec.add("update", "TRACKING");
          
          Long customerId = customer.getId();
          if(tmsCustomerWebhookMaps.containsKey(customerId)) {
            tmsCustomerWebhookMaps.get(customerId).addRecord(rec);
          } else {
            TMSWebhookModel model = new TMSWebhookModel(config, "vendor-bill");
            model.addRecord(rec);
            tmsCustomerWebhookMaps.put(customerId, model);
          }
          continue;
        }
      }
      
      if(!vendorBill.isNew()) {
        vendorBill = vendorBillLogic.saveVendorBill(client, company, vendorBill);
      } else {
        TMSVendorBill update = vendorBillLogic.getVendorBill(client, company, bill.getOriginVendorBillId());
        if(update == null) continue;
        update.updateByGoodsTrackingFiels(vendorBill);
        Company ownerCompanyBill = companyLogic.getCompany(client, update.getCompanyId());
        vendorBill = vendorBillLogic.saveVendorBill(client, ownerCompanyBill, update);
      }
      vendorBills.add(vendorBill);
    }
  //sync by webhook
    for(TMSWebhookModel model : tmsCustomerWebhookMaps.values()) {
      model.processResult("sync-tms-vendor-bill-by-goods-trackings", "Sync Vendor Bill By Goods Tracking", false);
    }
    return vendorBills;
  }
  
  public List<TMSVendorBill> syncVendorBillWithTMSOperations(ClientContext client, ICompany company, Long [] ids) {
    List<TMSVendorBill> vendorBills = new ArrayList<>();
    Map<Long, TMSWebhookModel>  tmsCustomerWebhookMaps = new HashMap<>();
    for(Long id : ids) {
      TMSOperations ops = operationsLogic.getOperationsById(client, company, id);
      Long tmsBillId = ops.getTmsBillId();
      if(tmsBillId == null) continue;
      TMSBill bill = tmsBillLogic.getTMSBillById(client, company, tmsBillId);
      String originalCustomerCode = bill.getOriginalCustomerCode();
      if(originalCustomerCode != null) {
        //conpute sync by webhook
        TMSPartner customer = partnerLogic.getByWebhookCompanyCode(client, company, originalCustomerCode);
        if(customer != null && customer.getWebhookConfig() != null && customer.getWebhookConfig().isAutoPush()) {
          TMSWebhookConfig config = customer.getWebhookConfig();
          MapObject rec = new MapObject();
          rec.add("billId", bill.getRefId());
          rec.add("billCode", bill.getRefCode());
          rec.add("id", bill.getOriginVendorBillId());
          rec.add("label", bill.getLabel());
          rec.add("opsAccountId", ops.getOpsAccountId());
          rec.add("opsAccountFullName", ops.getOpsAccountFullName());
          rec.add("opsMobile", ops.getOpsAccountMobile());
          rec.add("opsIdentificationNo", ops.getIdentificationNo());
          rec.add("opsNote", ops.getNote());
          rec.add("opsStatus", ops.getStatus());
          rec.add("update", "OPS");
          
          Long customerId = customer.getId();
          if(tmsCustomerWebhookMaps.containsKey(customerId)) {
            tmsCustomerWebhookMaps.get(customerId).addRecord(rec);
          } else {
            TMSWebhookModel model = new TMSWebhookModel(config, "vendor-bill");
            model.addRecord(rec);
            tmsCustomerWebhookMaps.put(customerId, model);
          }
          continue;
        }
      }
      
      //sync vendor internal;
      if(bill.getOriginVendorBillId() == null) continue;
      TMSVendorBill vendorBill = vendorBillLogic.getVendorBill(client, company, bill.getOriginVendorBillId());
      vendorBill.updateByTMSOperations(ops);
      
      Company ownerCompanyBill = companyLogic.getCompany(client, vendorBill.getCompanyId());
      vendorBill = vendorBillLogic.saveVendorBill(client, ownerCompanyBill, vendorBill);
      vendorBills.add(vendorBill);
    }
    
    //sync by webhook
    for(TMSWebhookModel model : tmsCustomerWebhookMaps.values()) {
      model.processResult("sync-tms-vendor-bill-by-ops", "Sync Vendor Bill By Ops", false);
    }
    return vendorBills;
  }
  
  public  List<TMSBill> syncVendorBillWithTMSBills(ClientContext client, ICompany company, Long [] tmsBillIds) {
    List<TMSBill> sourceBills = tmsBillLogic.findTMSBills(client, company, tmsBillIds);
    Map<Long, TMSWebhookModel>  tmsCustomerWebhookMaps = new HashMap<>();
    for(TMSBill sourceBill : sourceBills) {
      TMSVendorBill vendorBill = vendorBillLogic.getByTMSBillId(client, company, sourceBill.getId());
      if(vendorBill == null) continue;
      vendorBill = vendorBill.updateByTMSBill(sourceBill);
      vendorBill = vendorBillLogic.saveVendorBill(client, company, vendorBill);

      VehicleFleet vehicleFleet = vehicleFleetLogic.getVehicleFleetById(client, company, vendorBill.getVendorId());
      if(!(vendorBill.isSendOps() && vendorBill.isSendVendor())) return sourceBills;
      TMSWebhookConfig config = vehicleFleet.getWebhookConfig();
      if(config != null && config.isAutoPush() && StringUtil.isNotBlank(config.getAccessToken())) {
        MapObject rec = DataSerializer.JSON.fromString(DataSerializer.JSON.toString(sourceBill), MapObject.class);
        Long vendorId = vehicleFleet.getId();
        if(tmsCustomerWebhookMaps.containsKey(vendorId)) {
          tmsCustomerWebhookMaps.get(vendorId).addRecord(rec);
        } else {
          TMSWebhookModel model = new TMSWebhookModel(config, "tms-bill");
          model.addRecord(rec);
          tmsCustomerWebhookMaps.put(vendorId, model);
        }
      }
    }
    
  //sync by webhook
    for(TMSWebhookModel model : tmsCustomerWebhookMaps.values()) {
      model.processResult("sync-tms-bill", "Sync TMS Bill", false);
    }
    return sourceBills;
  }
  
  public List<TMSBill> processingRequestTMSBillsAndPushWebhook(ClientContext client, ICompany company, TMSProcessingRequest request) {
    if(request.getTmsBillIds() == null) throw RuntimeError.UnknownError("Invalid Request, ids is empty!!!");
    List<TMSBill> bills = tmsBillLogic.findTMSBills(client, company, request.getTMSBillIdsToArray());
    
    if(request.getProcessVendorId() == null) return null;
    VehicleFleet vehicleFleet = vehicleFleetLogic.getVehicleFleetById(client, company, request.getProcessVendorId());
    if(vehicleFleet == null) throw RuntimeError.UnknownError("You Need Choose Vendor Field!!!");
    
    //create this company
    
    List<MapObject> tmsBillMaps = new ArrayList<>();
    
    String tmsBillPrintToken = null;
    if(vehicleFleet.getWebhookConfig() != null && vehicleFleet.getWebhookConfig().getTokenId() != null) {
      tmsBillPrintToken = createAccessTokenByTMSBillPrint(client, company, vehicleFleet.getWebhookConfig().getTokenId(), request.getTmsBillIds());
    }

    for(TMSBill tmsBill : bills) {
      TMSVendorBill vendorBill = vendorBillLogic.getByTMSBillId(client, company, tmsBill.getId());
      if(vendorBill == null) {
        vendorBill = new TMSVendorBill();
      }
      vendorBill = vendorBill.updateByTMSBill(tmsBill);
      if(request.isCreateTMSOps()) {
        vendorBill.setOpsStatus(TMSOperationsStatus.NEED_CONFIRM);
        vendorBill.setSendOps(true);
      }
      if(request.isCreateVehicleTripGoodsTracking()) {
        vendorBill.setSendVendor(true);
        vendorBill.setStatus(VehicleTripGoodsTrackingStatus.CONFIRMED);
      }
      vendorBill = vendorBillLogic.saveVendorBill(client, company, vendorBill);
      List<TMSVendorAttachment> vendorAtts = vendorBillLogic.vendorBillAttachFileByMail(client, company, vendorBill.getId(), request.getMessage());
      
      tmsBill.getTmsBillForwarderTransport().setVendorId(vehicleFleet.getId());
      tmsBill.getTmsBillForwarderTransport().setVendorFullName(vehicleFleet.getLabel());
      tmsBill = tmsBillLogic.saveTMSBill(client, company, tmsBill);
      
      MapObject rec = DataSerializer.JSON.fromString(DataSerializer.JSON.toString(tmsBill), MapObject.class);
      rec.add("sourceCompanyCode", company.getCode());
      rec.add("createVehicleTripGoodsTracking", request.isCreateVehicleTripGoodsTracking());
      rec.add("combineTrip", request.isCombineTrip());
      rec.add("createTMSOps", request.isCreateTMSOps());
      rec.add("typeOfTransport", request.getTypeOfTransport());
      rec.add("markColor", request.getMarkColor());
      rec.add("vendorBillId", vendorBill.getId());
      rec.add("totalVendorAttachFiles", vendorAtts.size());
     
      if(vehicleFleet.getWebhookConfig() != null && vehicleFleet.getWebhookConfig().getTokenId() != null) {
        TMSWebhookConfig config = vehicleFleet.getWebhookConfig();
        AuthorizedToken token   = new AuthorizedToken();
        token.setResourceHandler("resource:vendor-bill-attachments");
        token.withAllowedResourceId("vendorBillId", vendorBill.getId());
        String authorizedToken   = apiAuthorizationService.createAuthorizedToken(client, company, config.getTokenId(), token);
        rec.add("syncVendorBillAuthorizationToken", authorizedToken);
        if(tmsBillPrintToken != null)rec.add("tmsBillPrintAuthorizationToken", tmsBillPrintToken);
      }
      tmsBillMaps.add(rec);
    }
    
    TMSWebhookConfig config = vehicleFleet.getWebhookConfig();
    if(config != null && config.isAutoPush() && StringUtil.isNotBlank(config.getAccessToken())) {
      config.setPlugin("tms-bill");
      config.setModule("logistics");
      DataTPWebhookClient webhookClient = 
          new DataTPWebhookClient(config.getBaseRestUrl(), config.getInputCompanyCode(), config.getModule(), config.getPlugin())
          .initHttpClient(config.getAccessToken());
      InputDataProcessResult result = webhookClient.pushRecords("sync-tms-bill", "Sync TMS Bill", tmsBillMaps, false);
      List<InputRecordProcessResult> inputRecordProcessResult = result.getInputRecordProcessResults();
      processResults(client, company, inputRecordProcessResult);
      
      if(result.getProcessStatus().equals(InputProcessStatus.FAIL)) throw RuntimeError.UnknownError(result.getError());
      return bills;
    }
    
    if(request.isCreateVehicleTripGoodsTracking()) {
      goodsTrackingLogic.createVehicleTripGoodsTrackings(client, company, bills, request.getTypeOfTransport(), request.getMarkColor(), true, request.isCombineTrip());
    }
    if(request.isCreateTMSOps()) {
      opsLogic.requestOPS(client, company, request.getTmsBillIds());
    }
    return bills;
  }
  
  private void processResults(ClientContext client, ICompany company, List<InputRecordProcessResult> processResults) {
    if(processResults == null) return;
    List<TMSVendorBill> vendorBills = new ArrayList<>();
    for(InputRecordProcessResult result : processResults) {
      if(StringUtil.isBlank(result.getJsonResult())) continue;
      MapObject mapResult = DataSerializer.JSON.fromString(result.getJsonResult(), MapObject.class);
      Long   vendorBillId      = mapResult.getLong("vendorBillId", null);
      String tmsAuthorization  = mapResult.getString("tmsAuthorization");
      TMSVendorBill vendorBill = vendorBillLogic.getVendorBill(client, company, vendorBillId);
      if(vendorBill != null) {
        vendorBill.setVendorAuthorization(tmsAuthorization);
        vendorBill.setPushStatus(result.getStatus());
        vendorBill.set(client, company);
        vendorBills.add(vendorBill);
      }
    }
    vendorBillLogic.getRepo().saveAll(vendorBills);
  }
}
