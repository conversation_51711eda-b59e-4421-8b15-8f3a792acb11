package cloud.datatp.tms.groovy

import org.springframework.context.ApplicationContext

import net.datatp.lib.executable.ExecutableContext;
import net.datatp.lib.executable.Executor;
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.module.data.db.query.OptionFilter
import net.datatp.module.data.db.query.RangeFilter
import net.datatp.security.client.DataScope
import net.datatp.util.ds.MapObject;
import net.datatp.util.text.StringUtil

public class TMSBillSql extends Executor {
  static public class SearchTMSBill extends ExecutableSqlBuilder {
    private String JOIN_PERMISSION_FILTER(MapObject sqlParams) {
      if(!sqlParams.has("withCustomerPermission")) return "";
      String scopeStr = sqlParams.getString("dataScope", DataScope.Company.toString())
      DataScope scope = DataScope.valueOf(scopeStr);
      boolean isOwnerScope = DataScope.Owner.equals(scope);
      return """${isOwnerScope ? "" : "--"} INNER JOIN permission_filter per_filter ON per_filter.customer_id  = lgc_tms_bill.customer_id""";
    }
    private String WITH_PERMISSION_FILTER(MapObject sqlParams) {
      if(!sqlParams.has("withCustomerPermission")) return "";
      String scopeStr = sqlParams.getString("dataScope", DataScope.Company.toString())
      DataScope scope = DataScope.valueOf(scopeStr);
      boolean isOwnerScope = DataScope.Owner.equals(scope);
      if(isOwnerScope == true) {
        return """
          ,permission_filter AS (
            SELECT
              cus.id AS customer_id
            FROM lgc_tms_partner_permission per
            INNER JOIN lgc_tms_customer cus ON cus.tms_partner_id = per.tms_partner_id
            WHERE
              ${FILTER_BY_PARAM("per.company_id", "companyId", sqlParams)}
              ${AND_FILTER_BY_PARAM("per.user_id", "responsibleAccountId", sqlParams)}
            GROUP BY cus.id
          )
        """
      }
      return "";
    }
    
    public static String GET_MODERATOR_PERMISSION_FIELDS(Boolean moderatorPermission) {
      if(moderatorPermission)
        return """
          lgc_tms_bill.created_by,
          lgc_tms_bill.created_time,
          lgc_tms_bill.modified_by,
          lgc_tms_bill.modified_time,
          """;
      return "--NO MODERATOR PERMISSION";
    }
    
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams          = ctx.getParam("sqlParams");
      String searchPattern         = sqlParams.getString("searchPattern", null);
      Boolean filterByIds          = sqlParams.has("ids");
      String excludeRoundUsed      = sqlParams.get("excludeRoundUsed", null);
      String dataScope             = sqlParams.getString("dataScope");
      boolean isOwner              = DataScope.Owner.toString().equals(dataScope) && !sqlParams.has("withCustomerPermission");
      
      RangeFilter rangeFilter      = (RangeFilter) sqlParams.get("date_time");
      String filterDateTimeIsNull  = (rangeFilter == null || StringUtil.isBlank(rangeFilter.getToValue())) ? "OR date_time IS NULL" : "";
      boolean excludeFilterByDate  = rangeFilter == null || (StringUtil.isBlank(rangeFilter.getFromValue()) && StringUtil.isBlank(rangeFilter.getToValue())) ? false : true;
      if(StringUtil.isNotBlank(searchPattern)) {
        searchPattern = "%" + searchPattern + "%";
        excludeFilterByDate = false;
      }
      
      boolean internalBill = false;
      OptionFilter sourceFilter = sqlParams.get("source");
      if(sourceFilter != null) {
        String [] options = sourceFilter.getSelectOptions();
        if(options.contains("INTERNAL_BILL")) internalBill = true;
      }
      
//      tracking_group_by_tms_bill       AS (
//        SELECT
//          tms_bill_id,
//          COUNT(tms_bill_id)                                     AS total_trip,
//          sum(tracking.fixed_charge)                             AS tms_tracking_fixed_charge,
//          sum(tracking.extra_charge)                             AS tms_tracking_extra_charge,
//          sum(tracking.total_charge)                             AS tms_tracking_total_charge
//          FROM lgc_fleet_vehicle_trip_goods_tracking tracking
//          GROUP BY tms_bill_id
//    ),
      
      String query = """
        WITH 
          vendor_bill_attachment           AS (
            SELECT
              vendor_bill_id                                           AS vendor_bill_id,
              COUNT(vendor_bill_id)                                    AS vendor_attach_file_total
            FROM lgc_tms_vendor_attachment
            GROUP BY vendor_bill_id
          )
          ${WITH_PERMISSION_FILTER(sqlParams)}
        SELECT * FROM (
          SELECT
            lgc_tms_bill.created_time,
            lgc_tms_bill.created_by,
            lgc_tms_bill.modified_by,
            lgc_tms_bill.modified_time,
            lgc_tms_bill.push_bfs_one_vehicle_info,
            lgc_tms_bill.push_bfs_one_cost,
            lgc_tms_bill.id,
            lgc_tms_bill.code,
            lgc_tms_bill.ref_source,
            lgc_tms_bill.label,
            lgc_tms_bill.state,
            lgc_tms_bill.process_status,
            lgc_tms_bill.type,
            lgc_tms_bill.origin_vendor_bill_id,
            lgc_tms_bill.tms_bill_print_authorization_token,
            lgc_tms_bill.sync_vendor_bill_authorization_token,
            lgc_tms_bill.origin_total_vendor_attach_files,
            lgc_tms_bill.tms_bill_source,
            lgc_tms_bill.delivery_plan                              AS date_time,
            TO_CHAR(lgc_tms_bill.delivery_plan, 'yyyy/MM/dd')       AS formarted_date_time,
            lgc_tms_bill.time,
            lgc_tms_bill.vehicle_info_lock_date,
            lgc_tms_bill.description,
            lgc_tms_bill.status_issue,
            lgc_tms_bill.collect,
            lgc_tms_bill.is_combine,
            lgc_tms_bill.office,
            lgc_tms_bill.container_in_hand_plan_id,
            lgc_tms_bill.estimate_time,
            lgc_tms_bill.modified_estimate_time,
            lgc_tms_bill.delayed_time,
            lgc_tms_bill.responsible_account_id,
            lgc_tms_bill.responsible_full_name,
            lgc_tms_bill.vehicle_info_exporter,
            lgc_tms_bill.fee_exporter,
            lgc_tms_bill.close_payment,
      
            lgc_tms_bill.version,
            lgc_tms_bill.storage_state,
            lgc_tms_bill.company_id,
            lgc_tms_bill.customer_id,
            lgc_tms_customer.tms_partner_id AS customer_partner_id,
            lgc_tms_bill.customer_full_name,
      
            lgc_tms_bill.sender_full_name,
            lgc_tms_bill.sender_contact,
            lgc_tms_bill.sender_address,
            lgc_tms_bill.sender_inv_address,
            lgc_tms_bill.sender_location_id,
            lgc_tms_bill.sender_partner_address_id,
      
            lgc_tms_bill.receiver_full_name,
            lgc_tms_bill.receiver_contact,
            lgc_tms_bill.receiver_address,
            lgc_tms_bill.receiver_inv_address,
            lgc_tms_bill.receiver_location_id,
            lgc_tms_bill.receiver_partner_address_id,
      
            lgc_tms_bill.quantity,
            lgc_tms_bill.quantity_unit,
            lgc_tms_bill.volume,
            lgc_tms_bill.volume_as_text,
            lgc_tms_bill.volume_unit,
            lgc_tms_bill.weight,
            lgc_tms_bill.weight_unit,
            lgc_tms_bill.goods_type,
            lgc_tms_bill.goods_description,
            lgc_tms_bill.job_tracking_id,
            lgc_tms_bill.verify_job_tracking_error,
      
            lgc_tms_bill_forwarder_transport.mode,
            lgc_tms_bill_forwarder_transport.eta_cut_off_time,
            lgc_tms_bill_forwarder_transport.truck_type,
            lgc_tms_bill_forwarder_transport.container_no,
            lgc_tms_bill_forwarder_transport.seal_no,
            lgc_tms_bill_forwarder_transport.carrier_full_name,
            lgc_tms_bill_forwarder_transport.carrier_id,
            lgc_tms_bill_forwarder_transport.vendor_full_name,
            lgc_tms_bill_forwarder_transport.vendor_id,
            vehicle_fleet.bfs_one_code                          as vendor_bfs_one_code,
            lgc_tms_bill_forwarder_transport.declaration_number,
            lgc_tms_bill_forwarder_transport.booking_code,
            lgc_tms_bill_forwarder_transport.hwb_no,
            lgc_tms_bill_forwarder_transport.verify_hbl_no,
            lgc_tms_bill_forwarder_transport.truck_no,
            lgc_tms_bill_forwarder_transport.verify_vehicle_info,
            lgc_tms_bill_forwarder_transport.verify_vehicle_info_note,
            CASE
              WHEN process_status = 'PLAN' THEN 1
              WHEN process_status = 'PENDING' THEN 2
              WHEN process_status = 'PROCESSING' THEN 3
              ELSE 4
            END AS status_number,
            CASE
              WHEN lgc_tms_bill_forwarder_transport.mode = 'EXPORT_FCL' THEN 1
              WHEN lgc_tms_bill_forwarder_transport.mode = 'EXPORT_LCL' THEN 2
              WHEN lgc_tms_bill_forwarder_transport.mode = 'EXPORT_AIR' THEN 3
              WHEN lgc_tms_bill_forwarder_transport.mode = 'IMPORT_FCL' THEN 4
              WHEN lgc_tms_bill_forwarder_transport.mode = 'IMPORT_LCL' THEN 5
              WHEN lgc_tms_bill_forwarder_transport.mode = 'IMPORT_AIR' THEN 6
              ELSE 7
            END AS mode_number,
            
            lgc_tms_bill_fee.id AS bill_fee_id,
            lgc_tms_bill_fee.payment_method,
            lgc_tms_bill_fee.currency,
            lgc_tms_bill_fee.cod,
            lgc_tms_bill_fee.goods_insurance,
            lgc_tms_bill_fee.shipment_charge,
            lgc_tms_bill_fee.extra_shipment_charge,
            lgc_tms_bill_fee.total_tax,
            lgc_tms_bill_fee.total_shipment_charge,
            lgc_tms_bill_fee.final_shipment_charge,
            lgc_tms_bill_fee.final_charge,
            lgc_tms_bill_fee.adjust_goods_insurance,
            lgc_tms_bill_fee.adjust_extra_shipment_charge,
            lgc_tms_bill_fee.adjust_shipment_charge,
            lgc_tms_bill_fee.adjust_total_tax,
            lgc_tms_bill_fee.adjust_total_shipment_charge,
            lgc_tms_bill_fee.adjust_final_shipment_charge,
            lgc_tms_bill_fee.adjust_final_charge,
            lgc_tms_bill_fee.fixed_payment,
            lgc_tms_bill_fee.extra_payment,
            lgc_tms_bill_fee.total_payment,
            lgc_tms_bill_fee.total_payment_tax,
            lgc_tms_bill_fee.final_payment,
            lgc_tms_bill_fee.payment_note,
            lgc_tms_bill_fee.profit,
            lgc_tms_bill_fee.pay_on_behalf_customer_id,
            lgc_tms_bill_fee.pay_on_behalf_customer_name,
            lgc_tms_bill_fee.verify_payment_info,
            lgc_tms_bill_fee.verify_payment_note,
      
            lgc_job_tracking.job_tracking_project_id,
            lgc_job_tracking.step_done_count,
            lgc_job_tracking.last_step_name as current_step,
            
            --Goods Tracking Fee Fields
            --tracking_group_by_tms_bill.total_trip,
            --tracking_group_by_tms_bill.tms_tracking_fixed_charge,
            --tracking_group_by_tms_bill.tms_tracking_extra_charge,
            --tracking_group_by_tms_bill.tms_tracking_total_charge,
            
            --Vendor fields
            lgc_tms_vendor_bill.id                      AS vendor_bill_id,
            lgc_tms_vendor_bill.vendor_authorization    AS vendor_authorization,
            lgc_tms_vendor_bill.upload_error            AS upload_error,
            lgc_tms_vendor_bill.fixed                   AS vendor_fixed,
            lgc_tms_vendor_bill.extra                   AS vendor_extra,
            lgc_tms_vendor_bill.cost                    AS vendor_cost,
            lgc_tms_vendor_bill.feedback                AS feedback,
            lgc_tms_vendor_bill.update_container_no     AS update_container_no,
            lgc_tms_vendor_bill.update_truck_info       AS update_truck_info,
            lgc_tms_vendor_bill.update_costing          AS update_costing,
            lgc_tms_vendor_bill.container_no            AS vendor_bill_container_no,
            lgc_tms_vendor_bill.seal_no                 AS vendor_bill_seal_no,

            lgc_tms_vendor_bill.send_vendor             AS vendor_bill_send_vendor,
            lgc_tms_vendor_bill.ops_note                AS vendor_ops_note,
            lgc_tms_vendor_bill.ops_status              AS vendor_ops_status,
            lgc_tms_vendor_bill.send_ops                AS vendor_bill_send_ops,
            lgc_tms_vendor_bill.ops_account_full_name   AS vendor_ops_account_full_name,
            lgc_tms_vendor_bill.ops_mobile              AS vendor_ops_mobile,
            lgc_tms_vendor_bill.ops_identification_no   AS vendor_ops_identification_no,
            
            -- operations fields
            lgc_tms_operations.status                   AS ops_status,
            lgc_tms_operations.id                       AS ops_id,
            lgc_tms_operations.note                     AS ops_note,
            lgc_tms_operations.ops_account_full_name    AS ops_account_full_name,
            lgc_tms_operations.ops_account_mobile       AS ops_account_mobile,
            lgc_tms_operations.identification_no        AS ops_identification_no,
      
            -- RU fields
            lgc_tms_round_used.id AS round_used_id,
            lgc_tms_round_used.status AS round_used_status,
            lgc_tms_vendor_bill.vendor_cost_status      AS vendor_cost_status,

            vendor_bill_attachment.vendor_attach_file_total AS vendor_attach_file_total,

            --Message fields,
            mm.status                                   AS message_status,
            mm.id                                       AS message_id

          FROM lgc_tms_bill
          LEFT JOIN lgc_tms_bill_forwarder_transport
           ON lgc_tms_bill_forwarder_transport.id           = lgc_tms_bill.tms_bill_forwarder_transport_id
          LEFT JOIN lgc_tms_bill_fee
           ON lgc_tms_bill_fee.id                           = lgc_tms_bill.tms_bill_fee_id
          LEFT JOIN lgc_job_tracking
            ON lgc_job_tracking.id                          = lgc_tms_bill.job_tracking_id
          LEFT JOIN lgc_tms_customer
            ON lgc_tms_customer.id = lgc_tms_bill.customer_id
          LEFT JOIN lgc_tms_operations
            ON lgc_tms_operations.tms_bill_id               = lgc_tms_bill.id
          LEFT JOIN lgc_tms_round_used
            ON lgc_tms_round_used.tms_bill_id               = lgc_tms_bill.id
          LEFT JOIN lgc_tms_vendor_bill
            ON lgc_tms_vendor_bill.tms_bill_id              = lgc_tms_bill.id


          LEFT JOIN lgc_fleet_vehicle_fleet vehicle_fleet
            ON vehicle_fleet.id                                  = lgc_tms_bill_forwarder_transport.vendor_id
     

          --LEFT JOIN tracking_group_by_tms_bill ON tracking_group_by_tms_bill.tms_bill_id       = lgc_tms_bill.id

          LEFT JOIN vendor_bill_attachment 
            ON vendor_bill_attachment.vendor_bill_id        = lgc_tms_vendor_bill.id

          LEFT JOIN message_message mm 
            ON mm.id                                        = lgc_tms_bill.message_id

          ${JOIN_PERMISSION_FILTER(sqlParams)}
        ) as lgc_tms_bill
        WHERE
          ${FILTER_BY_STORAGE_STATE(sqlParams)}
          ${internalBill ? "" : "--"}AND (ref_source <> 'lgc_tms_bill' OR ref_source is null)
          ${AND_FILTER_BY_OPTION("plan_status", "plan", sqlParams)}
          ${AND_FILTER_BY_PARAM("companyId", sqlParams)}
          ${AND_FILTER_BY_PARAM("customerId", sqlParams)}
          ${AND_FILTER_BY_PARAM("vendorId", sqlParams)}
          ${AND_FILTER_BY_PARAM("carrierId", sqlParams)}
          ${AND_FILTER_BY_PARAM("hwb_no", "hblNos", sqlParams)}
          ${AND_FILTER_BY_PARAM("label", "fileNos", sqlParams)}
          ${AND_SEARCH_BY(["label", "bookingCode", "containerNo"], searchPattern)}
          ${isOwner ? AND_FILTER_BY_PARAM("lgc_tms_bill.responsible_account_id", "responsibleAccountId", sqlParams) : ""}
          ${excludeFilterByDate ? "" : "--"} AND (${FILTER_BY_RANGE('lgc_tms_bill.date_time', 'date_time', sqlParams)} ${filterDateTimeIsNull})
          ${filterByIds ? """ AND id IN ${sqlParams.getString("ids").replace("[", "(").replace("]", ")")}""" : ""}
          ${excludeRoundUsed ? " AND round_used_id IS NULL" : ""}
          ORDER BY formarted_date_time DESC, collect, customer_full_name, booking_code, label, mode_number, time
          ${MAX_RETURN(sqlParams)};
      """;
      return query;
    }
  }
  
  static public class FindVendorBillByTMSBillIds extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      
      String query = """
        SELECT  
         bt.vendor_bill_id,
         bt.vehicle_type,
         bt.license_plate,
         bt.driver_full_name,
         bt.driver_mobile,
         bt.driver_identification_no,
         bt.description,
         ltvb.tms_bill_id
        FROM lgc_tms_vendor_bill_tracking bt
        INNER JOIN lgc_tms_vendor_bill ltvb on bt.vendor_bill_id = ltvb.id
        WHERE
        ${FILTER_BY_STORAGE_STATE("ltvb", sqlParams)}
        
      """
      return query
    }
  }

  static public class FindStopLocationsByTMSBillIds extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");

      String query = """
        SELECT  
         *
        FROM lgc_tms_bill_stop_location 
        WHERE
          ${FILTER_BY_PARAM('tms_bill_id', 'tmsBillIds', sqlParams)}       
      """
      return query
    }
  }
  
  static public class FindTruckTrackingByTMSBillIds extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      String query = """
          SELECT 
            ltb.id,
            ltb.label, 
            ltb.delivery_plan,
            ltb.sender_inv_address,
            ltb.receiver_inv_address,
            ltbft.hwb_no, ltbft.verify_hbl_no,
            lfvtgt.vehicle_type, lfvt.vehicle_label, lfvt.driver_full_name, lfvt.mobile,
            ltbft.container_no
          FROM lgc_tms_bill ltb
          INNER JOIN lgc_tms_bill_forwarder_transport ltbft       ON ltbft.id = ltb.tms_bill_forwarder_transport_id
          INNER JOIN lgc_fleet_vehicle_trip_goods_tracking lfvtgt ON lfvtgt.tms_bill_id = ltb.id
          INNER JOIN lgc_fleet_vehicle_trip lfvt                  ON lfvt.id = lfvtgt.vehicle_trip_id
          WHERE
            ${FILTER_BY_PARAM("ltb.company_id", "companyId", sqlParams)}
            ${AND_FILTER_BY_PARAM('ltb.id', 'tmsBillIds', sqlParams)}
      """
      return query
    }
  }
  
  static public class FindVendorTrackingByTMSBillIds extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      String query = """
        SELECT 
              ltb.id,
              ltb.label,
              ltb.delivery_plan,
              ltb.sender_inv_address,
              ltb.receiver_inv_address,
              ltbft.hwb_no,
              ltbft.verify_hbl_no,
              ltbft.truck_type          AS vehicle_type , 
              ltvbt.license_plate       AS vehicle_label, 
              ltvbt.driver_full_name,
              ltvbt.driver_mobile       AS mobile,
              ltbft.container_no
        FROM lgc_tms_bill ltb
        INNER JOIN lgc_tms_bill_forwarder_transport ltbft on ltbft.id = ltb.tms_bill_forwarder_transport_id
        INNER JOIN lgc_tms_vendor_bill ltvb on ltvb.tms_bill_id = ltb.id
        INNER JOIN lgc_tms_vendor_bill_tracking ltvbt on ltvbt.vendor_bill_id = ltvb.id
        WHERE
          ${FILTER_BY_PARAM("ltb.company_id", "companyId", sqlParams)}
          ${AND_FILTER_BY_PARAM('ltb.id', 'tmsBillIds', sqlParams)}
      """
      return query
    }
  }
  
  static public class FindTMSBillFeeByTMSBillIds extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      String query = """
          SELECT 
            bill.id,
            bill.label,
            bill.office,
            transport.hwb_no,
            item.cash_flow ,
            item.label AS fee_name,
            item.code  AS fee_code,
            item.tms_partner_id,
            item.tms_partner_name,
            fleet.bfs_one_code,
            item.unit,
            item.quantity,
            item.price,
            item.currency,
            item.tax,
            item.total_cost,
            fleet.fleet_resource,
            item.ref_entity_id    AS tracking_id
          FROM lgc_tms_bill bill
          INNER JOIN lgc_tms_bill_forwarder_transport transport on transport.id = bill.tms_bill_forwarder_transport_id
          LEFT JOIN lgc_tms_bill_fee fee on fee.id = bill.tms_bill_fee_id
          LEFT JOIN lgc_tms_bill_cost_item item on item.tms_bill_fee_id = fee.id
          LEFT JOIN  lgc_fleet_vehicle_fleet fleet on fleet.id = item.tms_partner_id
          INNER JOIN lgc_tms_vendor_bill vendor_bill ON vendor_bill.tms_bill_id = bill.id
          WHERE
          ${FILTER_BY_PARAM("bill.company_id", "companyId", sqlParams)}
          ${AND_FILTER_BY_PARAM('bill.id', 'tmsBillIds', sqlParams)}
          """
          return query
    }
  }
  
  static public class FindTMSBillTripInfoByIds extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams  = ctx.getParam("sqlParams");
      
      String query = """
          SELECT 
            bill.label,
            bill.sender_contact,
            bill.sender_address,
            bill.receiver_contact,
            bill.receiver_address,
            bill.customer_full_name,
            bill.customer_id,
            bill.weight,
            bill.weight_unit,
            bill.quantity,
            bill.quantity_unit,
            bill.volume_as_text as volume,
            f_transport.mode,
            bill.delivery_plan,
            f_transport.booking_code,
            f_transport.container_no,
            f_transport.seal_no,
            vehicle_label,
            driver_full_name,
            identification_no,
            mobile 
          FROM lgc_tms_bill bill 
          INNER JOIN lgc_tms_bill_forwarder_transport f_transport    ON f_transport.id = bill.tms_bill_forwarder_transport_id
          INNER JOIN lgc_fleet_vehicle_trip_goods_tracking tracking  ON tracking.tms_bill_id = bill.id
          LEFT JOIN lgc_fleet_vehicle_trip trip                     ON trip.id = tracking.vehicle_trip_id
          WHERE
          ${FILTER_BY_OPTION('bill.storage_state', 'storageState', sqlParams, ['ACTIVE'])}
          ${AND_FILTER_BY_PARAM('bill.company_id', 'companyId', sqlParams)}
          ${AND_FILTER_BY_PARAM('bill.id', 'tmsBillIds', sqlParams)}
          ORDER BY bill.delivery_plan DESC, bill.label, bill.created_time DESC
          ${MAX_RETURN(sqlParams)}
          """
          return query;
    }
  }
  
  
  public TMSBillSql() {
    register(new SearchTMSBill())
    register(new FindVendorBillByTMSBillIds())
    register(new FindStopLocationsByTMSBillIds())
    register(new FindTruckTrackingByTMSBillIds())
    register(new FindVendorTrackingByTMSBillIds())
    register(new FindTMSBillFeeByTMSBillIds())
    register(new FindTMSBillTripInfoByIds())
  }
}
