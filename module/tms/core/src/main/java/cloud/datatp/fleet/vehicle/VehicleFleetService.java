package cloud.datatp.fleet.vehicle;

import java.util.List;

import net.datatp.util.ds.MapObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cloud.datatp.fleet.vehicle.entity.Transporter;
import cloud.datatp.fleet.vehicle.entity.TransporterMembership;
import cloud.datatp.fleet.vehicle.entity.Vehicle;
import cloud.datatp.fleet.vehicle.entity.VehicleFleet;
import cloud.datatp.fleet.vehicle.entity.VehicleFleetCoordinator;
import cloud.datatp.fleet.vehicle.entity.VehicleFleetMembership;
import lombok.Getter;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.security.client.ClientContext;

@Service("VehicleFleetService")
public class VehicleFleetService {
  @Autowired @Getter
  private VehicleFleetLogic vehicleFleetLogic;
  
  @Autowired @Getter
  private TransporterLogic transporterLogic;

  @Autowired @Getter
  private VehicleLogic vehicleLogic;

  //Transport Fleet
  @Transactional(readOnly = true)
  public VehicleFleet getVehicleFleet(ClientContext client, ICompany company, String code) {
    return vehicleFleetLogic.getVehicleFleet(client, company, code);
  }
  
  @Transactional(readOnly = true)
  public VehicleFleet getVehicleFleetById(ClientContext client, ICompany company, Long id) {
    return vehicleFleetLogic.getVehicleFleetById(client, company, id);
  }

  @Transactional(readOnly = true)
  public VehicleFleet getVehicleFleetByOwnerAccountId(ClientContext client, ICompany company, Long ownerAccountId) {
    return vehicleFleetLogic.getVehicleFleetByOwnerAccountId(client, company, ownerAccountId);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchVehicleFleets(ClientContext clientCtx, ICompany company, SqlQueryParams params) {
    return vehicleFleetLogic.searchVehicleFleets(clientCtx, company, params);
  }

  @Transactional
  public boolean changeVehicleFleetStorageState(ClientContext client, ChangeStorageStateRequest req) {
    return vehicleFleetLogic.changeVehicleFleetStorageState(client, req);
  }
  
  @Transactional
  public VehicleFleet saveVehicleFleet(ClientContext client, ICompany company, VehicleFleet vFleet) {
    return vehicleFleetLogic.saveVehicleFleet(client, company, vFleet);
  }


  @Transactional
  public List<Vehicle> processVehicles(ClientContext client, ICompany company, VehicleFleet fleet, List<Vehicle> vehicles) {
    return vehicleLogic.processVehicles(client, company, fleet, vehicles);
  }

  //Transport Membership
  @Transactional
  public VehicleFleetMembership addVehicleFleetMembership(ClientContext client, ICompany company, VehicleFleetMembership membership) {
    return vehicleFleetLogic.addVehicleFleetMembership(client, company, membership);
  }

  @Transactional
  public List<VehicleFleetMembership> addVehicleFleetMembershipList(ClientContext client, ICompany company, List<VehicleFleetMembership> memberships) {
    return vehicleFleetLogic.addVehicleFleetMembershipList(client, company, memberships);
  }

  @Transactional
  public boolean removeVehicleFleetMemberships(ClientContext client, ICompany company, List<VehicleFleetMembership> memberships) {
    return vehicleFleetLogic.removeVehicleFleetMemberships(client, company, memberships);
  }
  
  // Transport Fleet Coordinator
  @Transactional(readOnly = true)
  public VehicleFleetCoordinator getVehicleFleetCoordinator(ClientContext client, ICompany company, String code) {
    return vehicleFleetLogic.getVehicleFleetCoordinator(client, company, code);
  }
  
  @Transactional(readOnly = true)
  public VehicleFleetCoordinator getVehicleFleetCoordinatorById(ClientContext client, ICompany company, Long id) {
    return vehicleFleetLogic.getVehicleFleetCoordinatorById(client, company, id);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchVehicleFleetCoordinators(ClientContext clientCtx, ICompany company, SqlQueryParams params) {
    return vehicleFleetLogic.searchVehicleFleetCoordinators(clientCtx, company, params);
  }

  @Transactional
  public boolean changeVehicleFleetCoordinatorsStorageState(ClientContext client, ChangeStorageStateRequest req) {
    return vehicleFleetLogic.changeVehicleFleetCoordinatorsStorageState(client, req);
  }
  
  @Transactional
  public VehicleFleetCoordinator saveVehicleFleetCoordinator(ClientContext client, ICompany company, VehicleFleetCoordinator coordinator) {
    return vehicleFleetLogic.saveCoordinator(client, company, coordinator);
  }
  
  // Transporter
  @Transactional(readOnly = true)
  public Transporter loadTransporterByCode(ClientContext client, ICompany company, String code) {
    return transporterLogic.loadTransporterByCode(client, company, code);
  }
  
  @Transactional(readOnly = true)
  public Transporter loadTransporterById(ClientContext client, ICompany company, Long id) {
    return transporterLogic.loadTransporterById(client, company, id);
  }
  
  @Transactional
  public Transporter saveTransporter(ClientContext client, ICompany company, Transporter transporter) {
    return transporterLogic.saveTransporter(client, company, transporter);
  }

  @Transactional
  public List<Transporter> processTransporters(ClientContext client, ICompany company, VehicleFleet fleet, List<Transporter> transporters) {
    return transporterLogic.processTransporters(client, company, fleet, transporters);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchTransporters(ClientContext client, ICompany company, SqlQueryParams params) {
    return transporterLogic.searchTransporters(client, company, params);
  }

  @Transactional
  public Boolean changeTransportersStorageState(ClientContext client, ChangeStorageStateRequest req) {
    return transporterLogic.changeTransportersStorageState(client, req);
  }

  @Transactional
  public Boolean deleteTransporters(ClientContext client, ICompany company, List<Long> ids) {
    return transporterLogic.deleteTransporters(client, company, ids);
  }

  @Transactional
  public boolean removeTransporterMemberships(ClientContext client, ICompany company, List<TransporterMembership> memberships) {
    return transporterLogic.removeTransporterMemberships(client, company, memberships);
  }

  @Transactional
  public TransporterMembership addTransporterMembership(ClientContext client, ICompany company, TransporterMembership membership) {
    return transporterLogic.addTransporterMembership(client, company, membership);
  }

  @Transactional
  public List<TransporterMembership> addTransporterMembershipList(ClientContext client, ICompany company, List<TransporterMembership> memberships) {
    return transporterLogic.addTransporterMembershipList(client, company, memberships);
  }
}