package cloud.datatp.tms.bill;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cloud.datatp.tms.bill.entity.TMSBill;
import cloud.datatp.tms.bill.entity.TMSBillCustomer;
import cloud.datatp.tms.bill.entity.TMSBillFee;
import cloud.datatp.tms.bill.entity.TMSBillType;
import cloud.datatp.tms.partner.TMSCustomerLogic;
import cloud.datatp.tms.partner.TMSPartnerLogic;
import cloud.datatp.tms.partner.entity.TMSCustomer;
import cloud.datatp.tms.partner.entity.TMSPartner;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.entity.StorageState;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;

@Component
public class TMSBillBFSOneLogic {
  @Autowired
  private TMSBillLogic tmsBillLogic;

  @Autowired
  private TMSPartnerLogic tmsPartnerLogic;

  @Autowired
  private TMSCustomerLogic customerLogic;

  @Autowired
  private AccountLogic accountLogic;

  public List<TMSBill> convertBFSOneDataToTMSBills(ClientContext client, ICompany company, List<MapObject> bfsOneRecords) {
    List<TMSBill> bills = new ArrayList<>();
    for (MapObject sel : bfsOneRecords) {
      bills.add(convertBFSOneDataToTMSBill(client, company, sel));
    }
    return bills;
  }

  public TMSBill convertBFSOneDataToTMSBill(ClientContext client, ICompany company, MapObject bfsOneRecord) {
    String source = "BFSOne";
    TMSBill bill = new TMSBill(TMSBillType.FORWARDER);
    bill.withRefFieldSBFSOneData(bfsOneRecord);
    bill.setRefSource(source);

    Account account = accountLogic.getAccountById(client, client.getAccountId());

    if (account != null) {
      bill.setResponsibleAccountId(account.getId());
      bill.setResponsibleFullName(account.getFullName());
    }
    bill.setTmsBillFee(new TMSBillFee(bill.getCode()));
    bill.convertBFSOneData(bfsOneRecord);
    TMSBillCustomer tmsBillCustomer = bill.getCustomer();
    if (tmsBillCustomer == null) {
      tmsBillCustomer = new TMSBillCustomer();
    }

    String taxCode = bfsOneRecord.getString("shipperTaxCode");
    List<TMSPartner> partners = tmsPartnerLogic.findByTaxCode(client, company, taxCode);
    if (Collections.isNotEmpty(partners) && partners.size() == 1) {
      TMSPartner partner = partners.get(0);
      TMSCustomer customer = customerLogic.loadTMSCustomerByPartnerId(client, company, partner.getId());
      tmsBillCustomer.setCustomerFullName(partner.getShortName());
      if (customer != null) tmsBillCustomer.setCustomerId(customer.getId());
    }
    if (tmsBillCustomer.getCustomerFullName() == null) {
      tmsBillCustomer.setCustomerFullName(bfsOneRecord.getString("shipperName"));
    }

    TMSBillFee fee = bill.getTmsBillFee();
    if (fee != null) {
      fee.setPayOnBehalfCustomerId(tmsBillCustomer.getCustomerId());
      fee.setPayOnBehalfCustomerName(tmsBillCustomer.getCustomerFullName());
    }

    bill = tmsBillLogic.saveTMSBill(client, company, bill);
    return bill;
  }
}