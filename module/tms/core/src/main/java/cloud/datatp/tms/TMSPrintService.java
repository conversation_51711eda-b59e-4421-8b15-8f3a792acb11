package cloud.datatp.tms;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cloud.datatp.fleet.vehicle.VehicleFleetLogic;
import cloud.datatp.fleet.vehicle.entity.VehicleFleet;
import cloud.datatp.gps.GPSTrackingLogic;
import cloud.datatp.tms.bill.TMSBillLogic;
import cloud.datatp.tms.bill.entity.TMSBill;
import cloud.datatp.tms.bill.entity.TMSBillForwarderTransport;
import cloud.datatp.tms.bill.entity.TMSBillRating;
import cloud.datatp.tms.bill.models.SubcontractorReportingParams;
import cloud.datatp.tms.partner.TMSCustomerLogic;
import cloud.datatp.tms.partner.entity.TMSPartner;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.ProfileLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.account.entity.UserProfile;
import net.datatp.module.app.AppEnv;
import net.datatp.module.communication.entity.Message;
import net.datatp.module.communication.entity.Message.Status;
import net.datatp.module.communication.entity.MessageDeliverType;
import net.datatp.module.communication.entity.TargetRecipient;
import net.datatp.module.company.tmpl.TmplService;
import net.datatp.module.core.security.AuthorizedToken;
import net.datatp.module.core.security.api.ApiAuthorizationService;
import net.datatp.module.data.db.RecordGroupByMap;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.http.get.GETContent;
import net.datatp.module.http.get.GETTmpStoreHandler;
import net.datatp.module.http.get.StoreInfo;
import net.datatp.module.resource.entity.OwnerType;
import net.datatp.module.resource.misc.ContactLogic;
import net.datatp.module.resource.misc.entity.AccountContact;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;
import net.datatp.util.text.TokenUtil;

@Service(value = "TMSPrintService")
public class TMSPrintService {
  @Autowired
  AppEnv appEnv;

  @Autowired
  private TmplService tmplService;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private ProfileLogic profileLogic;

  @Autowired @Getter
  private ContactLogic contactLogic;

  @Autowired
  private TMSBillLogic tmsBillLogic;

  @Autowired
  private TMSCustomerLogic tmsCustomerLogic;

  @Autowired
  private VehicleFleetLogic vehicleFleetLogic;

  @Autowired
  private GPSTrackingLogic  gpsTrackingLogic;

  @Autowired
  private GETTmpStoreHandler tmpStoreHandler;

  @Autowired
  private ApiAuthorizationService apiAuthorizationService;

  private final Map<String, String> companyStoragePathMap = new HashMap<>();

  private String URL;

  @PostConstruct
  public void onInit() {
    if(appEnv.isProdEnv()) {
      URL = "https://beelogistics.cloud/";
    } else {
      URL = "http://localhost:7080/";
    }
  }

  private Long   TOKEN_ID         = 35484L;
  private String RESOURCE_HANDLER = "resource:tms-vendor-bill-tracking";
  private String createAuthorizedToken(ClientContext client, ICompany company, List<Long> ids) {
    AuthorizedToken template = new AuthorizedToken();
    template.setResourceHandler(RESOURCE_HANDLER);
    template.setAllowedResourceIds(new MapObject("tmsBillIds", ids));
    return apiAuthorizationService.createAuthorizedToken(client, company, TOKEN_ID, template);
  }

  private MapObject groupByAddress(MapObject model, List<TMSBill> bills) {
    TMSBill bill = bills.get(0);
    if(bill.getTmsBillForwarderTransport().isDomestic()) return model;
    if(bill.getTmsBillForwarderTransport().isExport()) {
      RecordGroupByMap<String, TMSBill> addressMap = new RecordGroupByMap<>(bills, sel -> sel.getSender().getSenderAddress());
      model.add("addressMap", addressMap.getAll());
    }
    if(bill.getTmsBillForwarderTransport().isImport()) {
      RecordGroupByMap<String, TMSBill> addressMap = new RecordGroupByMap<>(bills, sel -> sel.getReceiver().getReceiverAddress());
      model.add("addressMap", addressMap.getAll());
    }

    return model;
  }

  private MapObject groupByCustomer(ClientContext client, ICompany company, MapObject model, List<TMSBill> bills) {
    List<MapObject> groups = new ArrayList<>();
    TMSBill bill = bills.get(0);
    Account employee = accountLogic.getAccountById(client, bill.getResponsibleAccountId());
    RecordGroupByMap<String, TMSBill> groupByCustomer = new RecordGroupByMap<>(bills, sel -> sel.getCustomer().getCustomerFullName());
    for(List<TMSBill> sel : groupByCustomer.getAll().values()) {
      List<Long> ids = Collections.transform(sel, s-> s.getId());
      String token = createAuthorizedToken(client, company, ids);
      String trackingUrl = URL + "api/ui/" + token;
      StoreInfo info = gpsTrackingLogic.genQrCode(client, trackingUrl);
      String urlQrCode = URL+ "get/private/store/" + info.getStoreId();

      MapObject group = new MapObject();
      group.add("bills", sel);
      group.add("employee", employee);
      group.add("urlQrCode", urlQrCode);
      groupByAddress(group, sel);
      addCustomerInfo(client, company, group, sel);
      groups.add(group);
    }
    model.add("groups", groups);
    return model;
  }

  private MapObject addCustomerInfo(ClientContext client, ICompany company, MapObject model, List<TMSBill> bills) {
    TMSBill bill = bills.get(0);
    if(bill.getCustomer() != null && bill.getCustomer().getCustomerId() != null ) {
      TMSPartner partner = tmsCustomerLogic.loadTMSPartnerByCustomerId(client, company, bill.getCustomer().getCustomerId());
      if(partner != null) {
        model.add("payOnBehalfCustomer", partner);
      } else {
        model.add("customer", new TMSPartner(bill.getCustomer().getCustomerFullName(), bill.getCustomer().getCustomerCode()));
      }
    }
    Long payOnBehalfCustomerId = bill.getTmsBillFee().getPayOnBehalfCustomerId();
    if(payOnBehalfCustomerId != null) {
      TMSPartner partner = tmsCustomerLogic.loadTMSPartnerByCustomerId(client, company, payOnBehalfCustomerId);
      model.add("payOnBehalfCustomer", partner);
    }
    return model;
  }

  public StoreInfo createReceiptOfDeliveryPrint(ClientContext client, ICompany company, String format, Long [] ids) {
    GETContent getContent = getContentPOD(client, company, format, ids);
    return tmpStoreHandler.store(client, getContent, true);
  }

  public GETContent createPODPrint(ClientContext client, ICompany company, String format, Long [] ids) {
    return getContentPOD(client, company, format, ids);
  }

  public GETContent getContentPOD(ClientContext client, ICompany company, String format, Long [] ids) {
    MapObject model = new MapObject();
    model.add("resStorage", getResourceStoragePath(client, company));

    List<TMSBill> bills = tmsBillLogic.findTMSBills(client, company, ids);
    if(Collections.isNotEmpty(bills)) {
      if(bills.size() > 1) {
        groupByCustomer(client, company, model, bills);
      } else {
        String token = createAuthorizedToken(client, company, Arrays.asList(ids));
        String trackingUrl = URL + "api/ui/" + token;
        StoreInfo info = gpsTrackingLogic.genQrCode(client, trackingUrl);
        String urlQrCode = URL+ "get/private/store/" + info.getStoreId();
        Account employee = accountLogic.getAccountById(client, bills.get(0).getResponsibleAccountId());
        model.add("employee", employee);
        model.add("urlQrCode", urlQrCode);
        addCustomerInfo(client, company, model, bills);
        groupByAddress(model, bills);
        model.add("bills", bills);
      }
    }
    String hbsLocation = "db:print:tms/receipt-of-delivery.hbs";
    byte[] data = tmplService.render(company, hbsLocation, format, model);
    String id   = TokenUtil.idWithDateTime("receipt-of-delivery");
    return new GETContent(id + "." + format, data);
  }

  public StoreInfo createSubcontractorReportingPrint(ClientContext client, ICompany company, String format,  SubcontractorReportingParams params) {
    MapObject model = new MapObject();
    model.add("resStorage", getResourceStoragePath(client, company));
    model.add("comment", params.getComment());

    List<TMSBill> billsDb = tmsBillLogic.findTMSBills(client, company, params.getTmsBillIds());

    List<MapObject> groups = new ArrayList<>();
    RecordGroupByMap<String, TMSBill> groupByVendorMap = new RecordGroupByMap<>(billsDb, sel -> sel.getTmsBillForwarderTransport().getVendorFullName());
    for(List<TMSBill> bills : groupByVendorMap.getAll().values()) {
      MapObject group = new MapObject();
      Double totalAveragePoint = 0D;

      List<MapObject> billModels = new ArrayList<>();
      for(TMSBill bill : bills) {
        MapObject billModel = new MapObject();
        TMSBillRating tmsBillRating = tmsBillLogic.getTMSBillRatingByTMSBillId(client, company, bill.getId());
        billModel.add("bill", bill);
        billModel.add("tmsBillRating", tmsBillRating);

        billModels.add(billModel);
        totalAveragePoint += tmsBillRating.getAveragePoint();
      }
      totalAveragePoint = totalAveragePoint/bills.size();
      group.add("totalAveragePoint", totalAveragePoint);
      group.add("rating", "C");
      if(totalAveragePoint >= 8) group.add("rating", "B");
      if(totalAveragePoint >= 9) group.add("rating", "A");
      if(totalAveragePoint >= 10) group.add("rating", "S");
      group.add("bills", billModels);
      groups.add(group);
    }

    model.add("groups", groups);
    String hbsLocation = "db:print:tms/subcontractor-reporting.hbs";
    byte[] data = tmplService.render(company, hbsLocation, format, model);
    GETContent getContent = new GETContent("subcontractor-reporting." + format, data);
    return tmpStoreHandler.store(client, getContent, true);
  }

  public Message createCustomerServiceEmail(ClientContext client, ICompany company, Long [] ids) {
    List<TMSBill> bills = tmsBillLogic.findTMSBills(client, company, ids);

    MapObject model = new MapObject();
    model.add("resStorage", getResourceStoragePath(client, company));
    Account employee = accountLogic.getEditable(client, client.getRemoteUser());
    model.add("employee", employee);
    TMSBill bill = bills.get(0);
    if(bill.getCustomer().getCustomerId() != null ) {
      TMSPartner partner = tmsCustomerLogic.loadTMSPartnerByCustomerId(client, company, bill.getCustomer().getCustomerId());
      model.add("payOnBehalfCustomer", partner);
    }
    Long payOnBehalfCustomerId = bill.getTmsBillFee().getPayOnBehalfCustomerId();
    if(payOnBehalfCustomerId != null) {
      TMSPartner partner = tmsCustomerLogic.loadTMSPartnerByCustomerId(client, company, payOnBehalfCustomerId);
      model.add("payOnBehalfCustomer", partner);
    }
    model.add("bills", bills);

    String pagePath = "companydb:email:tms/request-handling-goods.hbs";
    String content = tmplService.render(company, pagePath, model);
    Message message = new Message(client.getAccountId()) ;
    message.setContent(content);
    message.setStatus(Status.Delivered);

    message.setSubject(createSubjectMessage(bill, bills.size() == 1));
    return message;
  }

  private String createSubjectMessage(TMSBill bill, boolean showGoods) {
    String subject = bill.getLabel();
    if (bill.getCustomer() != null && bill.getCustomer().getCustomerFullName() != null) {
      subject += " - " + bill.getCustomer().getCustomerFullName()  ;
    }

    TMSBillForwarderTransport fTransport = bill.getTmsBillForwarderTransport();
    if(fTransport == null) return subject;

    if (fTransport.getBookingCode() != null) {
      subject += " - " + bill.getTmsBillForwarderTransport().getBookingCode();
    }
    if (fTransport.getMode() != null) {
      subject += " - " + bill.getTmsBillForwarderTransport().getModeLabel();
    }
    if(!showGoods)  return subject;

    if(fTransport.isFcl()) {
      String container   = fTransport.getContainerNo();
      String truckType   = fTransport.getTruckType();
      subject += "//" +  container + " " + truckType;
    } else {
      double quantity     = bill.getTmsBillGoods().getQuantity();
      String quantityUnit = bill.getTmsBillGoods().getQuantityUnit();
      quantityUnit        = StringUtil.isBlank(quantityUnit) ? "PKG" : quantityUnit;
      subject += "//" +  quantity + " " + quantityUnit;
    }
    return subject;
  }

  public Message createCustomerFCLService(ClientContext client, ICompany company, Long [] ids) {
    List<TMSBill> bills = tmsBillLogic.findTMSBills(client, company, ids);
    boolean visibleTruckNo = false;
    for(TMSBill bill : bills) {
      String truckNo = bill.getTmsBillForwarderTransport().getTruckNo();
      if(StringUtil.isNotEmpty(truckNo)) {
        visibleTruckNo = true;
        break;
      }
    }
    TMSBill bill = bills.get(0);
    MapObject model = new MapObject();
    model.add("bills", bills);
    model.add("visibleTruckNo", visibleTruckNo);
    String pagePath = "companydb:email:tms/send-customer-fcl.hbs";
    String content = tmplService.render(company, pagePath, model);
    Message message = new Message(client.getAccountId()) ;
    message.setContent(content);
//    message.setESignature(renderESignature(client, company));
    message.setStatus(Status.Delivered);
    message.setSubject(createSubjectMessage(bill, bills.size() == 1));

    if(bill.getCustomer() != null) {
      Long customerId = bill.getCustomer().getCustomerId();
      TMSPartner partner = tmsCustomerLogic.loadTMSPartnerByCustomerId(client, company, customerId);
      setRecipientsMessage(client, company, message, partner);
    }
    return message;
  }

  private void setRecipientsMessage(ClientContext client, ICompany company, Message message, VehicleFleet fleet) {
    if(fleet != null && Collections.isNotEmpty(fleet.getEmails())) {
      List<TargetRecipient> targetEmails = new ArrayList<>();
      for(String email : fleet.getEmails()) {
        if(StringUtil.isBlank(email)) continue;
        TargetRecipient rec = new TargetRecipient(MessageDeliverType.Email, email);
        rec.setRecipientDisplayName(email);
        targetEmails.add(rec);
      }
      message.setRecipients(targetEmails);
    }
  }

  private void setRecipientsMessage(ClientContext client, ICompany company, Message message, TMSPartner partner) {
    if(partner != null && Collections.isNotEmpty(partner.getEmails())) {
      List<TargetRecipient> targetEmails = new ArrayList<>();
      for(String email : partner.getEmails()) {
        if(StringUtil.isBlank(email)) continue;
        TargetRecipient rec = new TargetRecipient(MessageDeliverType.Email, email);
        rec.setRecipientDisplayName(email);
        targetEmails.add(rec);
      }
      message.setRecipients(targetEmails);
    }
  }

  public MapObject createCustomerServiceEmailv2(ClientContext client, ICompany company, Long [] ids) {
    Account owner = accountLogic.getActiveAccountByLoginId(client, client.getRemoteUser());
    MapObject result = new MapObject("owner", owner);
    List<TMSBill> bills = tmsBillLogic.findTMSBills(client, company, ids);
//    String token = createAuthorizedToken(client, company, Arrays.asList(ids));
//    String trackingUrl = URL + "api/ui/" + token;
    TMSBill bill = bills.get(0);
    MapObject model = new MapObject();
    model.add("resStorage", getResourceStoragePath(client, company));
    model.add("vender", bill.getTmsBillForwarderTransport().getVendorFullName());
    model.add("pic", bill.getResponsibleFullName());
//    model.add("trackingUrl", trackingUrl);
    groupByCustomer(client, company, model, bills);
    String pagePath = "companydb:email:tms/request-handling-goods/request-handling-goods.hbs";
    String content = tmplService.render(company, pagePath, model);
    Message message = new Message() ;
    message.setSenderAccountId(client.getAccountId());
    message.setContent(content);
//    message.setESignature(renderESignature(client, company));
    message.setStatus(Status.Ready);
    message.setSubject(createSubjectMessage(bill, bills.size() == 1));

    for(TMSBill sel : bills) {
      if(sel.getTmsBillForwarderTransport() != null && sel.getTmsBillForwarderTransport().getVendorId() != null) {
        Long vendorId = sel.getTmsBillForwarderTransport().getVendorId();
        VehicleFleet vehicleFleet = vehicleFleetLogic.getVehicleFleetById(client, company, vendorId);
        setRecipientsMessage(client, company , message, vehicleFleet);
        result.add("vendor", vehicleFleet);
        break;
      }
    }
    result.add("message", message);
    return result;
  }

  private String renderESignature(ClientContext client, ICompany company) {
    UserProfile profile = profileLogic.getUserProfile(client, client.getAccountId());
    AccountContact contact = contactLogic.getPrimaryContact(client, OwnerType.Account, profile.getAccountId());
    MapObject ClientContext = new MapObject();
    ClientContext.add("profile", profile);
    if(contact != null && contact.getPhone() != null) {
      for(String sel : contact.getPhone()) {
        ClientContext.add("phone", sel);
        break;
      }
    }
    String signaturePath = "companydb:email:tms/request-handling-goods/e-signature.hbs";
    return tmplService.render(company, signaturePath, ClientContext);
  }

  public Message createVendorAccountingEmail(ClientContext client, ICompany company, MapObject params) {
    UserProfile profile = profileLogic.getUserProfile(client, client.getAccountId());
    MapObject model = new MapObject();
    model.add("resStorage", getResourceStoragePath(client, company));
    model.add("pic", profile.getFullName());
    model.add("profile", profile);
    model.add("vendorFullName", params.getString("vendorFullName"));
    List<TargetRecipient> targetRecipients = new ArrayList<>();
    Long vendorId = params.getLong("vendorId", null);
    if(vendorId != null ) {
      VehicleFleet fleet = vehicleFleetLogic.getVehicleFleetById(client, company, vendorId);
      Set<String> emails = fleet.getEmails();
      for(String email : emails) {
        if(StringUtil.isBlank(email)) continue;
        TargetRecipient recipient = new TargetRecipient(MessageDeliverType.Email, email);
        recipient.setRecipientDisplayName(email);
        targetRecipients.add(recipient);
      }

//      model.add("invoice", partner);
//      if(StringUtil.isNotBlank(partner.getInvoiceCompanyTaxCode()) ) {
//        TMSPartner payOnBehalfCustomer = new TMSPartner();
//        payOnBehalfCustomer.setLabel(partner.getInvoiceCompanyLabel());
//        payOnBehalfCustomer.setTaxCode(partner.getInvoiceCompanyTaxCode());
//        payOnBehalfCustomer.setAddress(partner.getInvoiceCompanyAddress());
//        model.add("invoice", payOnBehalfCustomer);
//      }
    }

    Date fromDate = params.getDate("fromDate", new Date());
    Date toDate = params.getDate("toDate", new Date());
    String sFromDate =  DateUtil.asCompactDate(fromDate);
    String sToDate =  DateUtil.asCompactDate(toDate);
    model.add("fromDate", sFromDate);
    model.add("toDate", sToDate);

    String pagePath = "companydb:email:tms/vendor-accounting.hbs";
    String content = tmplService.render(company, pagePath, model);
    Message message = new Message(client.getAccountId()) ;
    message.setContent(content);
//    message.setESignature(renderESignature(client, company));
    message.setStatus(Status.Delivered);
    message.setSubject("Bảng Kê " + sFromDate + "-" + sToDate);

    TargetRecipient recipient = new TargetRecipient(MessageDeliverType.Email, "<EMAIL>");
    recipient.setRecipientDisplayName("<EMAIL>");
    targetRecipients.add(recipient);
    message.setRecipients(targetRecipients);
    return message;
  }
  
  public Message createCustomerTripInfoEmail(ClientContext client, ICompany company, List<Long> tmsBillIds) {
    List<SqlMapRecord> bills = tmsBillLogic.findTMSBillTripInfoByIds(client, company, tmsBillIds);
    Map<String, List<SqlMapRecord>> groupByMode = new LinkedHashMap<>();
    for(SqlMapRecord bill : bills) {
      String mode        = bill.getString("mode", "");
      String containerNo = bill.getString("containerNo", "");
      String key = "LCL";
      
      if(mode.contains("FCL") || (!mode.contains("LCL") && StringUtil.isNotBlank(containerNo))) {
        key = "FCL";
      }
      if(groupByMode.containsKey(key)) {
        groupByMode.get(key).add(bill);
      } else {
        groupByMode.put(key, Arrays.asList(bill));
      }
    }

    MapObject model = new MapObject();
    model.add("resStorage", getResourceStoragePath(client, company));
    Account employee = accountLogic.getEditable(client, client.getRemoteUser());
    model.add("employee", employee);
    model.add("data", groupByMode);

    String pagePath = "companydb:email:tms/tms-bill-trip-info.hbs";
    String content = tmplService.render(company, pagePath, model);
    Message message = new Message(client.getAccountId()) ;
    message.setContent(content);
    message.setStatus(Status.Delivered);
    
    Long customerId = bills.get(0).getLong("customerId");
    if(customerId != null) {
      TMSPartner  partner = tmsCustomerLogic.loadTMSPartnerByCustomerId(client, company, customerId);
      //TODO: dung chung
      if(partner != null && Collections.isNotEmpty( partner.getEmails())) {
        List<TargetRecipient> targetEmails = new ArrayList<>();
        for(String email : partner.getEmails()) {
          if(StringUtil.isBlank(email)) continue;
          TargetRecipient rec = new TargetRecipient(MessageDeliverType.Email, email);
          rec.setRecipientDisplayName(email);
          targetEmails.add(rec);
        }
        message.setRecipients(targetEmails);
      }
    }
    return message;
  }

  public String getResourceStoragePath(ClientContext client, ICompany company) {
    String resStoragePath = companyStoragePathMap.get(company.getCode());
    if(resStoragePath == null) {
      synchronized(companyStoragePathMap) {
//        CompanyStorage storage = storageService.createCompanyStorage(client, company.getCode());
        String resourceDir = appEnv.addonPath("logistics", "templates/resources/" + company.getCode() + "/images");
//        resStoragePath = "file:" + storage.getLocalPath() + "/resources";
//        if(!storage.hasNode("resources/images")) {
//          File src = new File(resourceDir);
//          storage.copyFromLocal(src.getAbsolutePath(), "resources/images");
//        }
        companyStoragePathMap.put(company.getCode(), resourceDir);
      }
    }
    return resStoragePath;
  }
}