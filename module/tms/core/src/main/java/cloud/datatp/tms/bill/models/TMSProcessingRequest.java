package cloud.datatp.tms.bill.models;

import java.util.ArrayList;
import java.util.List;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.communication.entity.Message;

@Getter @Setter @NoArgsConstructor
public class TMSProcessingRequest {
  private List<Long> tmsBillIds = new ArrayList<>();
  private Message message;
  private boolean createVehicleTripGoodsTracking;
  private boolean combineTrip;
  private boolean createTMSOps;
  private Long processVendorId;
  private String typeOfTransport;
  private String markColor;

  
  public Long [] getTMSBillIdsToArray() {
    return tmsBillIds.toArray(new Long[tmsBillIds.size()]);
  }
}
