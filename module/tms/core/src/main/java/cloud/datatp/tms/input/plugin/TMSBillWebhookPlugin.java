package cloud.datatp.tms.input.plugin;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cloud.datatp.fleet.vehicle.VehicleTripGoodsTrackingLogic;
import cloud.datatp.tms.TMSProcessSyncLogic;
import cloud.datatp.tms.bill.TMSBillLogic;
import cloud.datatp.tms.bill.entity.TMSBill;
import cloud.datatp.tms.bill.entity.TMSBillType;
import cloud.datatp.tms.ops.TMSOperationsLogic;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.input.InputProcessStatus;
import net.datatp.module.data.input.entity.InputConfig;
import net.datatp.module.data.input.entity.InputRecord;
import net.datatp.module.data.input.entity.InputRecordVersion;
import net.datatp.module.data.input.plugin.DataInputPlugin;
import net.datatp.module.data.input.plugin.InputRecordProcessResult;
import net.datatp.module.data.input.plugin.WebhookInputMethodPlugin;
import net.datatp.security.client.ClientContext;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.MapObject;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@Component
public class TMSBillWebhookPlugin extends WebhookInputMethodPlugin {
  @Autowired
  private TMSBillLogic                  tmsBillLogic;
  
  @Autowired
  private TMSProcessSyncLogic           tmsProcessSyncLogic;
  
  @Autowired
  private VehicleTripGoodsTrackingLogic goodsTrackingLogic;
  
  @Autowired
  private TMSOperationsLogic            operationsLogic;
  @Override
  public InputConfig initInputConfig(DataInputPlugin plugin, InputConfig inputConfig) {
    inputConfig.setCode("sync-tms-bill-" + DateUtil.asCompactDateTimeId(new Date()));
    return inputConfig;
  }
  
  protected InputRecordProcessResult processRecord(
      ClientContext client, ICompany company, InputConfig inputConfig, InputRecord record, InputRecordVersion recordVersion) {
    String    refSource                          = TMSBill.TABLE_NAME;
    String    lastDataJson                       = recordVersion.getRawDataJson();
    MapObject billMap                            = DataSerializer.JSON.fromString(lastDataJson, MapObject.class);
    boolean   createVehicleTripGoodsTracking     = billMap.getBoolean("createVehicleTripGoodsTracking", false);
    boolean   combineTrip                        = billMap.getBoolean("combineTrip", false);
    boolean   createTMSOps                       = billMap.getBoolean("createTMSOps", false);
    Integer   totalAttchFiles                    = billMap.getInteger("totalVendorAttachFiles", 0);
    Long      vendorBillId                       = billMap.getLong("vendorBillId", null);
    String    syncVendorBillAuthorizationToken   = billMap.getString("syncVendorBillAuthorizationToken");
    String    tmsBillPrintAuthorizationToken     = billMap.getString("tmsBillPrintAuthorizationToken");
    String    sourceCompanyCode                  = billMap.getString("sourceCompanyCode");
    String    typeOfTransport                    = billMap.getString("typeOfTransport");
    String    markColor                          = billMap.getString("markColor");
    
    TMSBill source    = DataSerializer.JSON.fromString(DataSerializer.JSON.toString(billMap), TMSBill.class);
    Long    refId     = source.getId();
    String  refCode   = source.getCode();
    TMSBill bill      = null;
    
    if(refId != null) {
      bill = tmsBillLogic.getTMSBillByRefId(client, company, refSource, refId.toString());
    } else {
      bill = tmsBillLogic.getTMSBillByRefCode(client, company, refSource, refCode.toString());
    }
    TMSBill clone = source.clone();
    if(bill == null) {
      bill = new TMSBill(TMSBillType.FORWARDER);
      bill.setRefSource(refSource);
      bill.setRefId(refId != null ? refId.toString() : null);
      bill.setRefCode(refCode);
      bill.genCode(company.getCode(), refId);
    }
    bill.merge(clone);
    bill.withOriginVendorBill(vendorBillId, totalAttchFiles);
    bill.withSyncVendorBillAuthorizationToken(syncVendorBillAuthorizationToken);
    bill.withTMSBillPrintAuthorizationToken(tmsBillPrintAuthorizationToken);
    if(StringUtil.isNotBlank(sourceCompanyCode)) bill.setOriginalCustomerCode(sourceCompanyCode);
    bill = tmsBillLogic.saveTMSBill(client, company, bill);
    
    if(createVehicleTripGoodsTracking) {
      goodsTrackingLogic.createVehicleTripGoodsTrackings(client, company, Arrays.asList(bill), typeOfTransport, markColor, true, combineTrip);
    }
    if(createTMSOps) {
      operationsLogic.requestOperations(client, company, bill.getId());
    }
    
    recordVersion.setProcessStatus(InputProcessStatus.SUCCESS);
    
    String tmsAuthorization = tmsProcessSyncLogic.createAccessTokenByTMSBill(client, company, bill);
    MapObject result = new MapObject("vendorBillId", vendorBillId, "tmsAuthorization", tmsAuthorization);
    
    return new InputRecordProcessResult(InputProcessStatus.SUCCESS, result);
  }
}