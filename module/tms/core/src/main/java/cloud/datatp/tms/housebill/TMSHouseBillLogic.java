package cloud.datatp.tms.housebill;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import net.datatp.module.account.ProfileLogic;
import net.datatp.module.resource.location.LocationLogic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cloud.datatp.bfsone.BFSOneDataLogic;
import cloud.datatp.tms.bill.TMSBillLogic;
import cloud.datatp.tms.bill.entity.TMSBill;
import cloud.datatp.tms.bill.entity.TMSBillGoods;
import cloud.datatp.tms.bill.entity.TMSBillType;
import cloud.datatp.tms.housebill.entity.TMSHouseBill;
import cloud.datatp.tms.housebill.entity.TMSHouseBillGoods;
import cloud.datatp.tms.housebill.entity.TMSImportAndExport;
import cloud.datatp.tms.housebill.repository.TMSHouseBillRepository;
import cloud.datatp.tms.partner.TMSCustomerLogic;
import cloud.datatp.tms.partner.TMSPartnerLogic;
import cloud.datatp.tms.partner.entity.TMSCustomer;
import cloud.datatp.tms.partner.entity.TMSPartner;
import net.datatp.lib.executable.ExecutableContext;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.app.AppEnv;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.entity.StorageState;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.service.ExecutableUnitManager;
import net.datatp.security.client.ClientContext;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.text.StringUtil;

@Component
public class TMSHouseBillLogic extends DAOService {
  @Autowired
  protected TMSHouseBillRepository repo;
  
  @Autowired
  protected TMSBillLogic tmsBillLogic;
  
  @Autowired
  protected AccountLogic accountLogic;
  
  @Autowired
  protected TMSCustomerLogic customerLogic;
  
  @Autowired
  protected TMSPartnerLogic  partnerLogic;
  
  @Autowired
  protected BFSOneDataLogic bfsOneDataLogic;
  
  @Autowired
  protected AppEnv appEnv;
  
  @Autowired
  protected ExecutableUnitManager executableUnitManager;

  @Autowired
  protected ProfileLogic profileLogic;

  @Autowired
  protected LocationLogic locationLogic;
  
  public TMSHouseBill getById(ClientContext client, ICompany company, Long id) {
    return repo.getById(company.getId(), id);
  }

  public boolean deleteByIds(ClientContext client, ICompany company, List<Long> ids) {
    repo.deleteAllById(ids);
    return true;
  }
  
  private void onPreSave(ClientContext client, ICompany company, TMSHouseBill hbl) {
    if(hbl.isNew()) return;
    
    List<TMSBill> bills = tmsBillLogic.findByHouseBill(client, company, hbl.getId());
    if(Collections.isEmpty(bills)) return;
    
    TMSHouseBill db = getById(client, company, hbl.getId());
    TMSImportAndExport importAndExport   = hbl.getImportAndExport();
    double quantity     = 0;
    String quantityUnit = "PKG";
    double weight     = 0;
    String weightUnit = "KGM";
    double volume     = 0;
    String volumeUnit = "CBM";
    String volumeNote = "";
    for(TMSBill bill : bills) {
      if(db != null && importAndExport.getWarehouseLocationId() != db.getImportAndExport().getWarehouseLocationId()) {
        if(hbl.isImport()) {
          bill.getSender().setSenderContact(importAndExport.getWarehouseContact());
          bill.getSender().setSenderAddress(importAndExport.getWarehouseLocationLabel());
          bill.getSender().setSenderLocationId(importAndExport.getWarehouseLocationId());
        }
        if(hbl.isExport()) {
          bill.getReceiver().setReceiverContact(importAndExport.getWarehouseContact());
          bill.getReceiver().setReceiverAddress(importAndExport.getWarehouseLocationLabel());
          bill.getReceiver().setReceiverLocationId(importAndExport.getWarehouseLocationId());
        }
      }
      
      TMSBillGoods goods = bill.getTmsBillGoods();
      quantity     += goods.getQuantity();
      quantityUnit  = goods.getQuantityUnit();
      weight       += goods.getWeight();
      weightUnit    = goods.getWeightUnit();
      volume       += goods.getVolume();
      volumeUnit    = goods.getVolumeUnit();
      volumeNote   += goods.getVolumeAsText() != null ? goods.getVolumeAsText() : "";
      
      bill = hbl.updateTMSBill(bill);
      bill.set(client, company);
    }
    tmsBillLogic.getTmsBillRepo().saveAll(bills);
    
    TMSHouseBillGoods hbGoods = hbl.getHouseBillGoods();
    hbGoods.setQuantity(quantity);
    hbGoods.setQuantityUnit(quantityUnit);
    hbGoods.setWeight(weight);
    hbGoods.setWeightUnit(weightUnit);
    hbGoods.setVolume(volume);
    hbGoods.setVolumeUnit(volumeUnit);
    hbGoods.setVolumeNote(volumeNote);
    List<TMSBill> filters = bills.stream().filter(sel -> sel.getDeliveryPlan() != null).toList();
    if(Collections.isNotEmpty(filters)) {
      TMSBill bill = java.util.Collections.max(filters, (a, b) -> a.getDeliveryTime().compareTo(b.getDeliveryTime()));
      hbl.setLastShippingDate(bill.getDeliveryTime());
    } else {
      hbl.setLastShippingDate(null);
    }
  }
  
  public TMSHouseBill saveTMSHouseBill(ClientContext client, ICompany company, TMSHouseBill hbl) {
  hbl.set(client, company);
  onPreSave(client, company, hbl);
  return repo.saveAndFlush(hbl);
  }

  public List<SqlMapRecord> searchTMSBills(ClientContext client, ICompany company, SqlQueryParams params) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    ExecutableContext ctx =
        new ExecutableContext()
            .withScriptEnv(scriptDir, "cloud/datatp/tms/housebill/SearchLogicUnit.java", "SearchTMSBill")
            .withParam(this).withParam(client).withParam(company).withParam(params).withParam(profileLogic).withParam(locationLogic);
    return (List<SqlMapRecord>) executableUnitManager.execute(ctx);
  }
  
  public List<TMSHouseBill> convertTMSBillToTMSHouseBill(ClientContext client, ICompany company, List<Long> tmsBillIds) {
    List<TMSBill> bills = tmsBillLogic.findTMSBills(client, company, tmsBillIds);
    List<TMSBill>             updateBills    = new ArrayList<>();
    List<TMSHouseBill>        hblBillResults = new ArrayList<>();
    Map<String, TMSHouseBill> hblBillMap     = new HashMap<>();
    for(TMSBill bill : bills) {
      
      if(bill.getTmsHouseBillId() != null) {
        TMSHouseBill hblDb = repo.getById(company.getId(), bill.getTmsHouseBillId());
        if(hblDb != null) {
          if(!StorageState.isActive(hblDb.getStorageState())) {
            hblDb.setStorageState(StorageState.ACTIVE);
            hblDb = saveTMSHouseBill(client, company, hblDb);
          }
          hblBillResults.add(hblDb);
          continue;
        }
      };
      TMSHouseBill hblBill = bill.toHouseBill();
      if(StringUtil.isBlank(hblBill.getHblNo()) || hblBill.getHblNo().length() < 5) {
        hblBill = saveTMSHouseBill(client, company, hblBill);
        bill.setTmsHouseBillId(hblBill.getId());
        tmsBillLogic.saveTMSBill(client, company, bill);
        hblBillResults.add(hblBill);
        continue;
      }
      
      TMSHouseBill hblDb = repo.getByHblNo(company.getId(), hblBill.getFileNo(), hblBill.getHblNo());
      if(hblDb != null) {
        bill.setTmsHouseBillId(hblDb.getId());
        tmsBillLogic.saveTMSBill(client, company, bill);
        hblBillResults.add(hblDb);
        continue;
      }
      //create hbl
      hblBill.set(client, company);
//      hblBill = saveTMSHouseBill(client, company, hblBill);
      String key = hblBill.getFileNo() + "/" + hblBill.getHblNo();
      hblBillMap.put(key, hblBill);
      updateBills.add(bill);
    }
    if(Collections.isNotEmpty(hblBillMap.values())) {
      List<TMSHouseBill> hblBills = repo.saveAll(hblBillMap.values());
      hblBillMap = new HashMap<>();
      for(TMSHouseBill hbl : hblBills) {
        String key = hbl.getFileNo() + "/" + hbl.getHblNo();
        hblBillMap.put(key, hbl);
      }
      
      for(TMSBill bill : updateBills) {
        if(bill.getTmsHouseBillId() != null) continue;
        String hblNo = bill.getLabel() + "/" + bill.getTmsBillForwarderTransport().getHwbNo();
        TMSHouseBill hbl = hblBillMap.get(hblNo);
        bill.setTmsHouseBillId(hbl.getId());
      }
      tmsBillLogic.getTmsBillRepo().saveAll(updateBills);
      hblBillResults.addAll(hblBills);
    }
    
    //calculate
    repo.flush();
    tmsBillLogic.getTmsBillRepo().flush();
    for(TMSHouseBill bill : hblBillResults) {
      saveTMSHouseBill(client, company, bill);
    }
    return hblBillResults;
  }
  
  public MapObject createTMSHouseBillWithBFSOneData(ClientContext client, ICompany company, String hblNo) {
    SqlQueryParams params = new SqlQueryParams();
    params.addParam("hblNo", hblNo);
    List<SqlMapRecord> bfsOneRecs = bfsOneDataLogic.searchBFSOneTransactionsData(client, company, params);
    if(Collections.isEmpty(bfsOneRecs)) return null;
    
    TMSHouseBill houseBill = new TMSHouseBill(bfsOneRecs.get(0));
    String shipperId = bfsOneRecs.get(0).getString("shipperId");
    
    try {
      TMSPartner partner = partnerLogic.getByBfsOneCode(client, company, shipperId);
      if(partner != null) {
        TMSCustomer customer = customerLogic.loadTMSCustomerByPartnerId(client, company, partner.getId());
        if(customer != null) {
          houseBill.setCustomerId(customer.getId());
          houseBill.setCustomerFullName(partner.getShortName());
        } else {
          houseBill.setError(shipperId + " Customer not found!");
        }
      } else {
        houseBill.setError(shipperId + " Partner not found!");
      }
    } catch (Exception e) {
      houseBill.setError(e.getMessage());
    }
    
    List<MapObject> billModels = new ArrayList<>();
    double quantity     = 0;
    String quantityUnit = "PKG";
    double weight     = 0;
    String weightUnit = "KGM";
    double volume     = 0;
    String volumeUnit = "CBM";
    String volumeNote = "";
    for(SqlMapRecord bfsRec : bfsOneRecs) {
      TMSBill bill = new TMSBill(TMSBillType.FORWARDER);
      bill.convertBFSOneData(bfsRec);
      TMSBillGoods goods = bill.getTmsBillGoods();
      quantity     += goods.getQuantity();
      quantityUnit  = goods.getQuantityUnit();
      weight       += goods.getWeight();
      weightUnit    = goods.getWeightUnit();
      volume       += goods.getVolume();
      volumeUnit    = goods.getVolumeUnit();
      volumeNote   += goods.getVolumeAsText() != null ? goods.getVolumeAsText() : "";
      bill = houseBill.updateTMSBill(bill);
      
      MapObject billModel = bill.toTMSBillMap();
      billModels.add(billModel);
    }
    TMSHouseBillGoods hbGoods = houseBill.getHouseBillGoods();
    hbGoods.setQuantity(quantity);
    hbGoods.setQuantityUnit(quantityUnit);
    hbGoods.setWeight(weight);
    hbGoods.setWeightUnit(weightUnit);
    hbGoods.setVolume(volume);
    hbGoods.setVolumeUnit(volumeUnit);
    hbGoods.setVolumeNote(volumeNote);
    
    MapObject result = new MapObject();
    result.add("houseBill", houseBill);
    result.add("bills", billModels);
    return result;
  }
  
  public boolean verifyHblNo(ClientContext client, ICompany company, String hblNo) {
    try {
      SqlQueryParams params = new SqlQueryParams();
      params.addParam("hblNos", Arrays.asList(hblNo));
      List<SqlMapRecord> bfsHbls   = bfsOneDataLogic.searchBFSOneHouseBill(client, company, params);
      if(Collections.isEmpty(bfsHbls)) {
        return false;
      } else {
        return true;
      }
    } catch (Exception e) {
      return false;
    }
  }
}