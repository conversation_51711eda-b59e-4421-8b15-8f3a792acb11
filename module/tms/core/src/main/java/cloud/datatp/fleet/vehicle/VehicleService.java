package cloud.datatp.fleet.vehicle;

import cloud.datatp.fleet.vehicle.api.VehicleTripTrackingPlugin;
import cloud.datatp.fleet.vehicle.entity.*;
import cloud.datatp.fleet.vehicle.models.Message;
import cloud.datatp.fleet.vehicle.models.VehicleTripGoodsTrackingModel;
import cloud.datatp.fleet.vehicle.models.VehicleTripProfitReportParams;
import cloud.datatp.fleet.vehicle.models.VehicleTripTrackingModel;
import cloud.datatp.gps.entity.GPSConfig;
import cloud.datatp.gps.provider.entity.GPSInfo;
import lombok.Getter;
import net.datatp.module.backend.Notification;
import net.datatp.module.core.security.AuthorizationCipherTool;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.EditMode;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.MapObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service("VehicleService")
public class VehicleService {

  @Autowired @Getter
  private VehicleLogic vehicleLogic;
  
  @Autowired @Getter
  private VehicleFleetLogic vehicleFleetLogic;

  @Autowired
  private VehicleExpenseLogic vehicleExpenseLogic;

  @Autowired
  private VehicleTripLogic tripLogic;

  @Autowired
  private DriverSalaryLogic        driverSalaryLogic;
  
  @Autowired
  private VehicleProfitReportLogic vehicleProfitReportLogic;

  @Autowired @Getter
  private VehicleTripGoodsTrackingLogic goodsTrackingLogic;
  
  @Autowired
  private AuthorizationCipherTool authCipherTool;
  
  @Autowired
  private VehicleTripTrackingPlugin tripTrackingPlugin;

  //Vehicle
  @Transactional(readOnly = true)
  public Vehicle getVehicle(ClientContext client, ICompany company, String code) {
    return vehicleLogic.getVehicle(client, company, code);
  }
  
  @Transactional(readOnly = true)
  public Vehicle getVehicleByLicensePlate(ClientContext client, ICompany company, String licensePlate) {
    return vehicleLogic.getVehicle(client, company, licensePlate);
  }
  
  @Transactional(readOnly = true)
  public Vehicle getVehicleById(ClientContext client, ICompany company, Long id) {
    return vehicleLogic.getVehicleById(client, company, id);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchVehicles(ClientContext clientCtx, ICompany company, SqlQueryParams params) {
    return vehicleLogic.searchVehicles(clientCtx, company, params);
  }

  @Transactional
  public boolean changeVehicleStorageState(ClientContext client, ChangeStorageStateRequest req) {
    return vehicleLogic.changeVehicleStorageState(client, req);
  }
  
  @Transactional
  public Vehicle saveVehicle(ClientContext client, ICompany company, Vehicle vehicle) {
    return vehicleLogic.saveVehicle(client, company, vehicle);
  }

  @Transactional
  public List<MapObject> saveVehicles(ClientContext client, ICompany company, List<MapObject> vehicles) {
    return vehicleLogic.saveVehicles(client, company, vehicles);
  }


  @Transactional(readOnly = true)
  public VehicleRefuel getVehicleRefuel(ClientContext client, ICompany company, Long id) {
    return vehicleLogic.getVehicleRefuel(client, company, id);
  }

  @Transactional
  public VehicleRefuel saveVehicleRefuel(ClientContext client, ICompany company, VehicleRefuel vehicleRefuel) {
    return vehicleLogic.saveVehicleRefuel(client, company, vehicleRefuel );
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchVehicleRefuels(ClientContext client, ICompany company, SqlQueryParams params) {
    return vehicleLogic.searchVehicleRefuels(client, company, params);
  }
  
  @Transactional(readOnly = true)
  public VehicleTrip getVehicleTrip(ClientContext client, ICompany company, Long id) {
    return vehicleLogic.getVehicleTrip(client, company, id);
  }

  @Transactional
  public VehicleTrip saveVehicleTrip(ClientContext client, ICompany company, VehicleTrip vehicleTrip) {
    return vehicleLogic.saveVehicleTrip(client, company, vehicleTrip);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchVehicleTrips(ClientContext client, ICompany company, SqlQueryParams params) {
    return vehicleLogic.searchVehicleTrips(client, company, params);
  }
  
  @Transactional
  public VehicleTrip newVehicleTrip(ClientContext client, ICompany company, Long trackingId){
    return vehicleLogic.newVehicleTrip(client, company, trackingId);
  }
  
  //VehicleTripGoodsTracking
  @Transactional(readOnly = true)
  public VehicleTripGoodsTracking getVehicleTripGoodsTrackingById(ClientContext client, ICompany company, Long id){
    return goodsTrackingLogic.getVehicleTripGoodsTrackingById(client, company, id);
  }
  
  @Transactional
  public VehicleTripGoodsTracking saveVehicleTripGoodsTracking(ClientContext client, ICompany company, VehicleTripGoodsTracking tracking){
    return goodsTrackingLogic.saveVehicleTripGoodsTrackingAndSyncVendorBill(client, company, tracking);
  }

  @Transactional
  public boolean deleteVehicleTripGoodsTrackings(ClientContext client, ICompany company, List<Long> ids){
    return goodsTrackingLogic.deleteVehicleTripGoodsTrackings(client, company, ids);
  }
  
  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchVehicleTripGoodsTrackings(ClientContext client, ICompany company, SqlQueryParams params){
    return goodsTrackingLogic.searchVehicleTripGoodsTrackings(client, company, params);
  }
  
  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchVehicleExpenseReport(ClientContext client, ICompany company, SqlQueryParams params){
    return vehicleLogic.searchVehicleExpenseReport(client, company, params);
  }
  
  @Transactional
  public VehicleTripTrackingModel createVehicleTripForTracking(ClientContext client, ICompany company, VehicleTripTrackingModel model){
    return goodsTrackingLogic.createVehicleTripForTracking(client, company, model);
  }

  @Transactional
  public boolean updateStatusVehicleTrips(ClientContext client, ICompany company, VehicleTrip.TaskStatus status, List<Long> ids){
    return vehicleLogic.updateStatusVehicleTrips(client, company, status, ids);
  }
  
  @Transactional
  public VehicleTripGoodsTracking removeVehicleTrip(ClientContext client, ICompany company, Long trackingId){
    return goodsTrackingLogic.removeVehicleTrip(client, company, trackingId);
  }
  //
  
  @Transactional(readOnly = true)
  public List<VehicleTripAttachment> findVehicleTripAttachments(ClientContext client, ICompany company, Long vehicleTripId) {
    List<VehicleTripAttachment> atts = vehicleLogic.findVehicleTripAttachments(client, company, vehicleTripId);
    VehicleTripAttachment.setStoreInfo(authCipherTool, client.getToken(), atts);
    return atts;
  }

  @Transactional
  public List<VehicleTripAttachment> saveVehicleTripAttachments(
      ClientContext client, ICompany company, Long vehicleTripId, List<VehicleTripAttachment> attachments) {
    List<VehicleTripAttachment> atts = vehicleLogic.saveVehicleTripAttachments(client, company, vehicleTripId, attachments, true);
    VehicleTripAttachment.setStoreInfo(authCipherTool, client.getToken(), atts);
    return atts;
  }

  @Transactional
  public List<Map<String,Object>> getDataTMSBillTripAttachment(ClientContext client, ICompany company, Long tmsBillId){
    return vehicleLogic.getDataTMSBillTripAttachment(client, company, tmsBillId);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchVehicleExpenses(ClientContext client, ICompany company, SqlQueryParams params){
    return vehicleExpenseLogic.searchVehicleExpenses(client, company, params);
  }

  @Transactional
  public List<VehicleExpense> saveVehicleExpenses(ClientContext client, ICompany company, List<VehicleExpense> vehicleExpenses){
    return vehicleExpenseLogic.saveVehicleExpenses(client, company, vehicleExpenses);
  }

  @Transactional
  public boolean deleteVehicleExpenses(ClientContext client, ICompany company, List<Long> ids){
    return vehicleExpenseLogic.deleteVehicleExpenses(client, company, ids);
  }

  @Transactional
  public VehicleExpense updateStatus(ClientContext client, ICompany company, VehicleExpense vehicleExpense, VehicleExpense.VehicleExpenseStatus status){
    return vehicleExpenseLogic.updateStatus(client, company, vehicleExpense, status);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchDriverSalaryReport(ClientContext client, ICompany company, SqlQueryParams params){
    return driverSalaryLogic.searchDriverSalaryReport(client, company, params);
  }
  
  @Transactional(readOnly = true)
  public DriverSalaryReport getDriverSalaryReport(ClientContext client, ICompany company, Long id){
    return driverSalaryLogic.getById(client, company, id);
  }
  
  @Transactional
  public boolean deleteDriverReports(ClientContext client, ICompany company, List<Long> ids){
    return driverSalaryLogic.deleteByIds(client, company, ids);
  }
  
  public List<DriverSalaryReport> createDriverReports(ClientContext client, ICompany company, VehicleTripProfitReportParams params) {
    return driverSalaryLogic.createDriverReports(client, company, params);
  }
//
  @Transactional
  public List<Message> sendMessage(ClientContext client, ICompany company, List<Message> messages){
    return tripLogic.sendMessage(client, company, messages);
  }

  @Transactional
  public List<Message> generateMessage(ClientContext client, ICompany company, List<VehicleTripGoodsTrackingModel> models) {
    return tripLogic.generateMessage(client, company, models);
  }

  @Transactional
  public VehicleTrip updateVehicleTripEditMode(ClientContext client, ICompany company, Long vehicleTripId, EditMode editMode) {
    return tripLogic.updateVehicleTripEditMode(client, company, vehicleTripId, editMode);
  }
  
  //
  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchVehicleProfitReport(ClientContext client, ICompany company, SqlQueryParams params){
    return vehicleProfitReportLogic.searchVehicleProfitReport(client, company, params);
  }
  
  @Transactional
  public List<VehicleProfitReport> saveVehicleProfitReports(ClientContext client, ICompany company, List<VehicleProfitReport> reports) {
    return vehicleProfitReportLogic.saveVehicleProfitReports(client, company, reports);
  }
  
  @Transactional
  public VehicleProfitReport saveVehicleProfitReport(ClientContext client, ICompany company, VehicleProfitReport report) {
    return vehicleProfitReportLogic.saveVehicleProfitReport(client, company, report);
  }
  
  @Transactional(readOnly = true)
  public VehicleProfitReport getVehicleProfitReport(ClientContext client, ICompany company, Long id){
    return vehicleProfitReportLogic.getById(client, company, id);
  }
  
  @Transactional
  public boolean deleteVehicleProfitReports(ClientContext client, ICompany company, List<Long> ids){
    return vehicleProfitReportLogic.deleteByIds(client, company, ids);
  }
  
  @Transactional
  public List<VehicleProfitReport> createVehicleProfitReports(ClientContext client, ICompany company, VehicleTripProfitReportParams params) {
    return vehicleProfitReportLogic.createVehicleProfitReports(client, company, params);
  }
  
  @Transactional
  public VehicleProfitReport updateVehicleProfitReport(ClientContext client, ICompany company, VehicleProfitReport report) {
    return vehicleProfitReportLogic.updateVehicleProfitReport(client, company, report);
  }
  
  @Transactional(readOnly = true)
  public String createAccessToken(ClientContext client, ICompany company, Long tokenId, String partnerName) {
    return vehicleLogic.createAccessToken(client, company, tokenId, partnerName);
  }
  
  @Transactional(readOnly = true)
  public List<GPSInfo> findVehicleLocationByPlates(ClientContext client, ICompany company, List<String> vehiclePlates) {
    return vehicleLogic.findVehicleLocationByPlates(client, company, vehiclePlates);
  }
  
  @Transactional(readOnly = true)
  public Notification getVehicleLocation(ClientContext client, ICompany company, String licensePlate) {
    return vehicleLogic.getVehicleLocationNotification(client, company, licensePlate);
  }
  
  @Transactional(readOnly = true)
  public Notification reportVehicleLocation(ClientContext client, ICompany company, Long vehicleTripId) {
    return vehicleLogic.reportVehicleLocationNotification(client, company, vehicleTripId);
  }
  
  @Transactional
  public List<Vehicle> connectGPS(ClientContext client, ICompany company, GPSConfig config) {
    return vehicleLogic.connectGPS(client, company, config);
  }
  
  @Transactional
  public List<Vehicle> addGPSConfigToVehicles(ClientContext client, ICompany company, GPSConfig config, List<Long> vehicleIds) {
    return vehicleLogic.addGPSConfigToVehicles(client, company, config, vehicleIds);
  }
  
  //Create autho token
  @Transactional(readOnly = true)
  public MapObject createAccessTokenByTrip(ClientContext client, ICompany company, Long tokenId, List<Long> tripIds) {
    return tripTrackingPlugin.createAccessTokenByTrip(client, company, tokenId, tripIds);
  }
}